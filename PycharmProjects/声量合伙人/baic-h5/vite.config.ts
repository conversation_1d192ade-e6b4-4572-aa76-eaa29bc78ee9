import { fileURLToPath, URL } from "node:url";
import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "unplugin-vue-components/resolvers";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import path from "path";
import mockDevServerPlugin from "vite-plugin-mock-dev-server";
import viteCompression from "vite-plugin-compression";
import { createHtmlPlugin } from "vite-plugin-html";
import { enableCDN } from "./build/cdn";

// 当前工作目录路径
const root: string = process.cwd();

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 环境变量
  const env = loadEnv(mode, root, "");
  return {
    base: env.VITE_PUBLIC_PATH || "/",
    plugins: [
      vue({
        template: {
          compilerOptions: {
            isCustomElement: tag => tag.startsWith("wx-") // 忽略所有以 wx- 开头的标签
          }
        }
      }),
      vueJsx(),
      mockDevServerPlugin(),
      AutoImport({
        dts: "src/typings/auto-import.d.ts",
        imports: [
          "vue",
          "vue-router",
          {
            vant: [
              "showToast",
              "showFailToast",
              "showSuccessToast",
              "showDialog",
              "showConfirmDialog",
              "showNotify",
              "showLoadingToast",
              "closeToast"
            ]
          }
        ],
        resolvers: [VantResolver()]
      }),
      // vant 组件自动按需引入
      Components({
        dts: "src/typings/components.d.ts",
        resolvers: [VantResolver()]
      }),
      // svg icon
      createSvgIconsPlugin({
        // 指定图标文件夹
        iconDirs: [path.resolve(root, "src/icons/svg")],
        // 指定 symbolId 格式
        symbolId: "icon-[dir]-[name]"
      }),
      // 生产环境 gzip 压缩资源
      viteCompression(),
      // 注入模板数据
      createHtmlPlugin({
        inject: {
          data: {
            ENABLE_ERUDA: env.VITE_ENABLE_ERUDA || "false"
          }
        }
      }),
      // 生产环境默认不启用 CDN 加速
      enableCDN(env.VITE_CDN_DEPS)
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url))
      }
    },
    server: {
      host: "0.0.0.0",
      port: Number(env.VITE_PORT),
      open: false,
      // 仅在 proxy 中配置的代理前缀， mock-dev-server 才会拦截并 mock
      // doc: https://github.com/pengzhanbo/vite-plugin-mock-dev-server
      proxy: {
        [env.VITE_BASE_API]: {
          target: env.VITE_PROXY_TARGET,
          changeOrigin: true,
          rewrite: path =>
            env.VITE_BASE_API_REWRITE === "true"
              ? path.replace(new RegExp("^" + env.VITE_BASE_API), "")
              : path,
          secure: false,
          configure: (proxy, options) => {
            // 配置此项可在响应头中看到请求的真实地址
            proxy.on("proxyRes", (proxyRes, req) => {
              proxyRes.headers["x-real-url"] =
                new URL(req.url || "", options.target as string)?.href || "";
            });
          }
        },
        // 任务公众号文章图片nginx映射
        [`${env.VITE_PUBLIC_PATH}mmbiz`]: {
          target: env.VITE_PROXY_TARGET,
          changeOrigin: true
        },
        // 预约试驾头像图片nginx映射
        [`${env.VITE_PUBLIC_PATH}thirdwx`]: {
          target: env.VITE_PROXY_TARGET,
          changeOrigin: true
        },
        // 预约试驾海报图片nginx映射
        [`${env.VITE_PUBLIC_PATH}ddev-baic`]: {
          target: env.VITE_PROXY_TARGET,
          changeOrigin: true
        }
      }
    },
    build: {
      rollupOptions: {
        output: {
          chunkFileNames: "static/js/[name]-[hash].js",
          entryFileNames: "static/js/[name]-[hash].js",
          assetFileNames: "static/[ext]/[name]-[hash].[ext]"
        }
      }
    }
  };
});
