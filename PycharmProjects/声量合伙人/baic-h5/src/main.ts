import store from "./store";
// normalize.css
import "normalize.css/normalize.css";
// 全局样式
import "./styles/index.less";
// tailwindcss
import "./styles/tailwind.css";
// svg icon
import "virtual:svg-icons-register";
import { initializeDarkMode } from "@/utils/dark-mode";
// 引入字体大小调整脚本
import "./utils/text-size-adjust.js";
import App from "./App.vue";
import router from "./router";
import { initWechatSDK } from "./utils/wechat";
import { debounceClick } from "./directives/debounceClick";
import { setupErrorMonitor } from "@/utils/errorMonitor";

initializeDarkMode();

// 初始化错误监控
setupErrorMonitor();
// 初始化微信 SDK
initWechatSDK(
  [
    "hideMenuItems",
    "showAllNonBaseMenuItem",
    "updateAppMessageShareData",
    "updateTimelineShareData",
    "onMenuShareTimeline",
    "onMenuShareAppMessage"
  ],
  ["wx-open-launch-weapp"]
)
  .then(() => {
    console.log("SDK 初始化成功，可调用微信 API");
  })
  .catch(error => {
    console.error("SDK 初始化失败:", error);
  });

const app = createApp(App);

// 设置Vue错误处理器
app.config.errorHandler = (error: Error, instance: any, info: string) => {
  console.error("Vue Error:", error, info);
  // 调用全局错误处理器
  if ((window as any).__VUE_ERROR_HANDLER__) {
    (window as any).__VUE_ERROR_HANDLER__(error, instance, info);
  }
};

app.use(store);
app.use(router);
// 注册全局指令
app.directive("debounce-click", debounceClick);

app.mount("#app");
