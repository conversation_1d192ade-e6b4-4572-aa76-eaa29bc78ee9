@import "./variables.less";

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  color: var(--van-text-color);
  background-color: var(--color-background);
}

html {
  height: 100%;
  box-sizing: border-box;
  font-size: 10px;
}

#app {
  height: 100%;
  width: 100%;
  max-width: 750px;
  margin: 0 auto;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

// see https://github.com/vuejs/vitepress/pull/2347
::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}
::view-transition-old(root) {
  z-index: 1;
}
::view-transition-new(root) {
  z-index: 9999;
}
.dark::view-transition-old(root) {
  z-index: 9999;
}
.dark::view-transition-new(root) {
  z-index: 1;
}
// .loop(@i, @property) when (@i > 0) {
//   .@{property}-@{i} {
//     @{property}: (@i * 1rem);
//   }
//   .loop(@i - 1, @property);
// }
// .loop(10, margin-left);
// .loop(10, margin-top);
// .loop(10, margin-right);
// .loop(10, margin-bottom);
// .loop(10, padding-left);
// .loop(10, padding-top);
// .loop(10, padding-right);
// .loop(10, padding-bottom);
