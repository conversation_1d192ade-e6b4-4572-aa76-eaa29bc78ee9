:root {
  --color-primary: #3366cc;
  /* 主要颜色 */
  --color-background: #f5f5f5;
  /* 背景颜色 */
  --color-block-background: #ffffff;
  /* 块背景颜色 */
  --color-divider: #ebedf0;
  /* 分割线颜色 */
  --color-overlay-background: rgba(0, 0, 0, 0.4);
  /* 遮罩层颜色 */
  --color-car-background: rgba(255, 255, 255, 0.55);
  /* 邀约试驾背景颜色 */
  --color-border: #f0f0f0;
  --rounded-md: 6px;
  /* default圆角 */
  --rounded-xl: 12px;
  /* large圆角 */
  --text-highlight: #000000;
  /* 高亮文本颜色 */
  --text-primary: rgba(0, 0, 0, 0.85);
  /* 主要文本颜色 */
  --text-secondary: rgba(0, 0, 0, 0.65);
  /* 次要文本颜色 */
  --text-describe: rgba(0, 0, 0, 0.45);
  /* 说明文本颜色 */
  --text-placeholder: rgba(0, 0, 0, 0.25);
  /* placeholder文本颜色 */
  --text-danger: #fa5151;
  /* 删除文本颜色 */
  --color-border: var(--van-border-color, #ebedf0);
  --font-size-h1: 18px;
  --font-size-h2: 16px;
  --font-size-h3: 14px;
  --font-size-caption: 12px;
  --font-size-small: 10px;
}
:root:root {
  --van-primary-color: var(--color-primary);
  --van-button-large-height: 48px;
  --van-button-default-height: 36px;
  --van-button-small-height: 28px;
  --van-button-radius: var(--rounded-md);
  --van-button-large-width: 327px;
  --van-field-label-color: var(--text-primary);
  --van-field-placeholder-text-color: var(--text-placeholder);
  --van-cell-font-size: 12px;
  --van-cell-value-font-size: 14px;
  --van-popup-round-radius: var(--rounded-xl);
  --van-overlay-background: var(--color-overlay-background);
  --van-tabbar-height: 56px;
  --van-tabbar-item-icon-size: 24px;
  --van-tabbar-item-font-size: 10px;
  --van-tabbar-item-text-color: var(--text-primary);
  --van-tabbar-item-active-color: var(--color-primary);
  --van-tabbar-item-line-height: 1.4;
  --van-radio-label-color: var(--text-primary);
  --van-radio-size: 16px;
  --van-radio-label-color: var(--text-describe);
  --van-cell-vertical-padding: 12px;
  --van-cell-horizontal-padding: 12px;
  --van-cell-right-icon-color: var(--text-describe);
  --van-cell-icon-size: var(--font-size-caption);
  --van-cell-border-color: #f0f0f0;
  --van-picker-option-text-color: var(--text-primary);
  --van-notice-bar-height: 42px;
  --van-dialog-radius: var(--rounded-xl);
  --van-list-text-color: var(--text-placeholder);
  --van-list-text-font-size: var(--font-size-caption);
}
body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  color: var(--van-text-color);
  background-color: var(--color-background);
  -webkit-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
  -moz-text-size-adjust: 100% !important;
}
html {
  height: 100%;
  box-sizing: border-box;
  font-size: 10px;
}
#app {
  height: 100%;
  width: 100%;
  max-width: 750px;
  margin: 0 auto;
}
a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}
::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}
::view-transition-old(root) {
  z-index: 1;
}
::view-transition-new(root) {
  z-index: 9999;
}
.dark::view-transition-old(root) {
  z-index: 9999;
}
.dark::view-transition-new(root) {
  z-index: 1;
}
