import { defineStore } from "pinia";
import { logout as logoutApi } from "@/api/login";

export const useAuthStore = defineStore("auth", {
  state: () => ({
    token: undefined,
    wxInfo: null,
    userInfo: null,
    posterInfo: null
  }),
  actions: {
    setToken({ token }) {
      this.token = token;
    },
    resetToken() {
      this.token = undefined;
    },
    setWx(wxInfo) {
      this.wxInfo = wxInfo;
    },
    setUser(user) {
      this.userInfo = user;
    },
    setPoster(posterInfo) {
      this.posterInfo = posterInfo;
    },
    resetWx() {
      this.wxInfo = undefined;
    },
    reset() {
      this.$reset();
      localStorage.removeItem("baic_h5_auth");
    },
    // 注销
    async logout(): Promise<void> {
      this.reset();
      await logoutApi();
    }
  },
  persist: {
    key: "baic_h5_auth"
  }
});
