<script setup lang="ts">
defineOptions({
  name: "Layout"
});
import tabbar from "@/components/tabbar/index.vue";
import { useCachedViewStoreHook } from "@/store/modules/cached-view";
// import { useDarkMode } from "@/composables/useToggleDarkMode";

const cachedViews = computed(() => {
  return useCachedViewStoreHook().cachedViewList;
});

const route = useRoute();
</script>

<!-- :theme="useDarkMode() ? 'dark' : 'light'" -->
<!-- , route.meta.isShowTab ? '' : 'van-safe-area-bottom' -->
<template>
  <div :class="['app-wrapper']">
    <van-config-provider :theme="'light'">
      <router-view v-slot="{ Component }">
        <keep-alive :include="cachedViews">
          <component :is="Component" />
        </keep-alive>
      </router-view>
      <tabbar v-if="route.meta.isShowTab" />
    </van-config-provider>
  </div>
</template>

<style lang="less" scoped>
@import "@/styles/mixin.less";

.app-wrapper {
  .clearfix();
  position: relative;
  height: 100%;
  width: 100%;
  overflow-x: auto;
  box-sizing: border-box;
}
</style>
