<template>
  <div class="rank-page">
    <!-- 头部标题和切换按钮 -->
    <div class="rank-header">
      <!-- <img src="@/assets/backIcon.png" class="back-icon" @click="router.push('/')" alt="返回" /> -->
      <div class="rank-title">
        <!-- 使用图片替代文本 -->
        <img
          :src="currentRankType === 'team' ? rankText2 : rankText1"
          alt="排行榜标题"
          class="rank-title-img"
        />
      </div>
      <button
        class="rank-type-switch"
        block
        @click="
          switchRankType(currentRankType === 'team' ? 'personal' : 'team')
        "
      >
        切换{{ currentRankType === "team" ? "个人" : "团队" }}排行榜
        <svg-icon name="rank-cut" class="exchange-icon" />
      </button>
    </div>
    <!-- 排行榜logo -->
    <div class="rank-logo">
      <img
        v-if="currentRankType === 'team'"
        src="@/assets/rank/person.png"
        alt="团队排行榜"
        class="logo-image"
      />
      <img
        v-else
        src="@/assets/rank/team.png"
        alt="个人排行榜"
        class="logo-image"
      />
    </div>
    <!-- 分类选择区域 -->
    <div class="rank-box">
      <div class="rank-categories">
        <van-tabs
          :key="`main-${currentRankType}`"
          v-model:active="activeMainCategory"
          class="tab-l1"
          background="linear-gradient(180deg, rgba(85, 141, 245, 0.08) 62%, rgba(142, 182, 255, 0) 100%), #FFFFFF"
          sticky
          :disabled="isLoading"
          @change="handleMainCategoryChange"
        >
          <van-tab
            v-for="category in mainCategories"
            :key="category.key"
            :title="category.name"
          >
            <!-- 子分类选择 -->
            <div class="tab-l2">
              <van-tabs
                :key="`sub-${currentRankType}-${category.key}`"
                v-model:active="activeSubCategory[category.key]"
                shrink
                :disabled="isLoading"
                @change="handleSubCategoryChange"
              >
                <van-tab
                  v-for="subCategory in category.subCategories"
                  :key="subCategory.key"
                  :title="subCategory.name"
                >
                  <!-- 标题行 -->
                  <div class="rank-header-row thin-border">
                    <div class="rank-number-header" style="width: 50px">
                      排名
                    </div>
                    <div class="rank-number-header" style="width: 75px">
                      {{ currentRankType === "team" ? "部门名称" : "用户名" }}
                    </div>
                    <div class="rank-number-header">
                      {{ activeMainCategory === 1 ? "当月热力值" : "当月积分" }}
                    </div>
                    <div class="rank-number-header">
                      {{ activeMainCategory === 1 ? "累计热力值" : "累计积分" }}
                    </div>
                  </div>
                  <!-- 排行榜列表 -->
                  <div class="rank-list">
                    <!-- 添加空数据提示 -->
                    <div
                      v-if="!isLoading && rankData.length === 0"
                      class="empty-data"
                    >
                      暂无排行榜数据
                    </div>
                    <div
                      v-for="(item, index) in rankData"
                      v-else
                      :key="index"
                      class="rank-item thin-border"
                    >
                      <!-- 现有的排行榜项目内容 -->
                      <div
                        class="rank-number"
                        style="width: 50px"
                        :class="`rank-${index + 1}`"
                      >
                        <!-- 现有内容 -->
                        <template v-if="index < 3">
                          <svg-icon
                            v-if="index + 1 === 1"
                            name="rank-1"
                            class="rank-img"
                          />
                          <svg-icon
                            v-if="index + 1 === 2"
                            name="rank-2"
                            class="rank-img"
                          />
                          <svg-icon
                            v-if="index + 1 === 3"
                            name="rank-3"
                            class="rank-img"
                          />
                        </template>
                        <template v-else>{{ index + 1 }}</template>
                      </div>
                      <div
                        class="rank-number"
                        style="width: 75px; word-break: break-all"
                      >
                        {{
                          currentRankType === "team"
                            ? item.deptName
                            : item.userName
                        }}
                      </div>
                      <div class="rank-number">
                        {{
                          activeMainCategory === 1
                            ? item.thermalValue
                            : item.monthPoint
                        }}
                      </div>
                      <div class="rank-number">
                        {{
                          activeMainCategory === 1
                            ? item.totalThermalValue
                            : item.totalPoint
                        }}
                      </div>
                    </div>
                  </div>
                </van-tab>
              </van-tabs>
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </div>
    <!-- 添加占位元素 -->
    <div
      v-if="currentRankType === 'personal' && myRanking"
      class="rank-placeholder"
    />
    <!-- 个人排名信息 -->
    <div
      v-if="
        currentRankType === 'personal' &&
        myRanking &&
        myRanking.ranking !== null
      "
      class="personal-rank-info"
    >
      <div class="personal-rank-item">
        <span v-if="myRanking.ranking === 1" style="width: 63px">
          <img src="@/assets/rank/1.png" alt="排名" class="rank-img" />
        </span>
        <span v-if="myRanking.ranking === 2" style="width: 63px">
          <img src="@/assets/rank/2.png" alt="排名" class="rank-img" />
        </span>
        <span v-if="myRanking.ranking === 3" style="width: 63px">
          <img src="@/assets/rank/3.png" alt="排名" class="rank-img" />
        </span>
        <span v-if="myRanking.ranking > 3" style="width: 60px">{{
          myRanking.ranking
        }}</span>
        <span style="width: 80px">{{ myRanking.userName }}</span>
        <span style="width: 80px">
          {{
            activeMainCategory === 1
              ? myRanking.thermalValue
              : myRanking.monthPoint
          }}
        </span>
        <span style="width: 80px">
          {{
            activeMainCategory === 1
              ? myRanking.totalThermalValue
              : myRanking.totalPoint
          }}
        </span>
      </div>
    </div>

    <!-- 添加加载状态显示 -->
    <van-overlay :show="isLoading" class="loading-overlay">
      <BaseLoadingIndicator
        class="loading-container"
        background="transparent"
        text="加载中..."
      />
    </van-overlay>
  </div>
</template>

<script setup lang="ts">
import rankText1 from "@/assets/rank/rankText1.png";
import rankText2 from "@/assets/rank/rankText2.png";
import {
  getPrivateForwardRanking,
  getPrivateOriginalRanking,
  getPrivateInviteRanking,
  getTeamForwardRanking,
  getTeamOriginalRanking,
  getTeamInviteRanking,
  getPrivateForwardTotalRanking,
  getPrivateOriginalTotalRanking,
  getPrivateInviteTotalRanking
} from "@/api/rank";

defineOptions({
  name: "Rank"
});

// 当前排行榜类型（团队/个人）
const currentRankType = ref("team");

// 主分类选项
const mainCategories = computed(() => [
  {
    key: "forward",
    name: "转发评论排行榜",
    subCategories:
      currentRankType.value === "team"
        ? [{ key: "baic", name: "北汽排行榜" }]
        : [
            { key: "department", name: "部门内排名" },
            { key: "total", name: "总排名" }
          ]
  },
  {
    key: "original",
    name: "原创排行榜",
    subCategories:
      currentRankType.value === "team"
        ? [{ key: "baic", name: "北汽排行榜" }]
        : [
            { key: "department", name: "部门内排名" },
            { key: "total", name: "总排名" }
          ]
  },
  {
    key: "invitation",
    name: "邀约排行榜",
    subCategories:
      currentRankType.value === "team"
        ? [{ key: "baic", name: "北汽排行榜" }]
        : [
            { key: "department", name: "部门内排名" },
            { key: "total", name: "总排名" }
          ]
  }
]);

// 当前选中的主分类
const activeMainCategory = ref(0);

// 各主分类下当前选中的子分类
const activeSubCategory = reactive({
  forward: 0,
  original: 0,
  invitation: 0
});

// 排行榜数据
const rankData = ref([]);

// 个人排行榜数据
const myRanking = ref(null);

// 添加 loading 状态
const isLoading = ref(false);

// 获取排行榜数据
const fetchRankData = async () => {
  if (isLoading.value) return; // 如果正在加载中，则直接返回

  isLoading.value = true;
  try {
    const currentCategory = mainCategories.value[activeMainCategory.value].key;
    const currentSubCategory =
      mainCategories.value[activeMainCategory.value].subCategories[
        activeSubCategory[currentCategory]
      ].key;
    rankData.value = [];
    let response;

    if (currentRankType.value === "team") {
      switch (currentCategory) {
        case "forward":
          response = await getTeamForwardRanking();
          break;
        case "original":
          response = await getTeamOriginalRanking();
          break;
        case "invitation":
          response = await getTeamInviteRanking();
          break;
      }
    } else {
      switch (currentCategory) {
        case "forward":
          response =
            currentSubCategory === "department"
              ? await getPrivateForwardRanking()
              : await getPrivateForwardTotalRanking();
          break;
        case "original":
          response =
            currentSubCategory === "department"
              ? await getPrivateOriginalRanking()
              : await getPrivateOriginalTotalRanking();
          break;
        case "invitation":
          response =
            currentSubCategory === "department"
              ? await getPrivateInviteRanking()
              : await getPrivateInviteTotalRanking();
          break;
      }
    }

    if (response?.data) {
      rankData.value =
        currentRankType.value === "team"
          ? response.data
          : response.data.rankingList;
      // 保存个人排名数据
      if (currentRankType.value === "personal") {
        myRanking.value = response.data.myRanking;
      } else {
        myRanking.value = null;
      }
    }
  } catch (error) {
    console.error("获取排行榜数据失败:", error);
  } finally {
    isLoading.value = false;
  }
};

// 切换排行榜类型
const switchRankType = (type: "team" | "personal") => {
  currentRankType.value = type;
  // 重置主分类和子分类的选中状态
  activeMainCategory.value = 0;

  // 重置所有子分类的选中状态为0
  Object.keys(activeSubCategory).forEach(key => {
    activeSubCategory[key] = 0;
  });

  // 等待DOM更新后再获取数据并刷新tabs布局
  nextTick(() => {
    fetchRankData();
    // 获取所有tabs实例并调用resize方法
    const tabsElements = document.querySelectorAll(".van-tabs");
    tabsElements.forEach(el => {
      // 使用类型断言解决TypeScript错误
      const vantTabsInstance = (el as any).__vue__;
      if (vantTabsInstance && typeof vantTabsInstance.resize === "function") {
        vantTabsInstance.resize();
      }
    });
  });
};

// 主分类切换
const handleMainCategoryChange = () => {
  // 重置当前主分类下的子分类选中状态
  const currentCategory = mainCategories.value[activeMainCategory.value].key;
  activeSubCategory[currentCategory] = 0;
  nextTick(() => {
    fetchRankData();
  });
};

// 子分类切换
const handleSubCategoryChange = () => {
  nextTick(() => {
    fetchRankData();
  });
};

// 页面加载时获取数据
onMounted(() => {
  fetchRankData();
});
</script>

<style scoped lang="less">
.rank-page {
  height: 100vh;
  background-image: url("@/assets/rank/rankBg.png");
  background-size: 100% 100%;
  background-position: top center;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.rank-header {
  // width: 200px;
  padding: 18px 24px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  position: relative;

  .back-icon {
    position: absolute;
    left: 16px;
    top: 16px;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }

  .rank-title {
    margin-bottom: 16px;
    gap: 8px;
    font-size: 24px;
    font-weight: bold;
    line-height: normal;
    text-align: center;
    display: flex;
    align-items: center;
    letter-spacing: normal;
    color: rgba(0, 0, 0, 0.85);

    .rank-title-img {
      width: 120px;
      height: 29px;
      object-fit: contain;
    }
  }
  .rank-title-icon {
    font-size: 120px;
  }

  .rank-type-switch {
    width: 130px;
    height: 30px;
    border-radius: 6px;
    opacity: 1;
    box-sizing: border-box;
    border: 1px solid rgba(52, 104, 224, 0.45);
    font-size: 12px;
    font-weight: normal;
    line-height: 18px;
    font-variation-settings: "opsz" auto;
    color: #3468e0;
    display: flex;
    align-items: center;
    justify-content: center;
    .exchange-icon {
      width: 14px;
      height: 14px;
      object-fit: contain;
      margin-left: 4px;
    }
  }
}
.rank-box {
  position: relative;
  overflow: hidden;
  :deep(.van-tabs__wrap) {
    overflow: inherit;
  }
}

.rank-categories {
  margin: 12px auto;
  background-color: #fff;
  width: 95%;
  border-top-left-radius: var(--rounded-xl);
  border-top-right-radius: var(--rounded-xl);
  :deep(.van-tabs__nav--line) {
    border-radius: var(--rounded-xl);
    padding-bottom: 0;
  }
  :deep(.van-tabs--line .van-tabs__wrap.van-tabs__wrap) {
    height: 44px;
  }
  :deep(.van-tabs__wrap .van-tabs__line) {
    bottom: 0;
    width: 24px;
    height: 4px;
  }
  :deep(.van-tabs__content .van-tabs__wrap .van-tabs__line) {
    bottom: 0;
    width: 62px;
    height: 2px;
  }
  :deep(.van-tab) {
    height: 44px;
    font-size: var(--font-size-h3);
    &--active {
      background-size: 110% 150%;
      background-repeat: no-repeat;
      background-position: center;
      margin-top: -6px;
      height: 48px;
      color: var(--text-secondary);
      // 根据索引显示不同背景
      &:nth-child(1) {
        background-image: url("@/assets/rank/rankTitle1.png");
      }

      &:nth-child(2) {
        background-image: url("@/assets/rank/rankTitle2.png");
      }

      &:nth-child(3) {
        background-image: url("@/assets/rank/rankTitle3.png");
      }
    }
  }
  :deep(.tab-l2 .van-tab) {
    &--active {
      margin-top: 0px;
      // 根据索引显示不同背景
      // &:nth-child(1) {
      //   margin: 0 8px;
      // }

      // &:nth-child(2) {
      //   margin: 0 8px;
      // }
    }
  }
  :deep(.tab-l2 .van-tab) {
    &:nth-child(1) {
      margin-right: 10px;
    }
  }
}

.rank-list {
  height: v-bind('currentRankType === "team" ? "350px" : "300px"');
  overflow-y: scroll;
  // padding: 10px 5px;
}
:deep(.van-tabs__content) {
  padding-left: 5px;
}
:deep(.van-tab--shrink) {
  padding: 0;
}
:deep(.van-tab--active) {
  font-weight: 500 !important;
  height: 48px;
  color: rgba(0, 0, 0, 0.85) !important;
}
.rank-header-row {
  width: 95%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  font-weight: 600;
  font-size: var(--font-size-h2);
  color: var(--text-secondary);
}

.thin-border {
  position: relative;
}
.thin-border::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 200%;
  height: 200%;
  border-bottom: 1px solid #f0f0f0;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
}

.rank-number-header {
  width: 70px;
  font-size: 12px;
  font-weight: 500;
  line-height: 22px;
  text-align: center;
  /* Text/正文 */
  color: rgba(0, 0, 0, 0.85);
  white-space: nowrap;
}

.rank-info-header {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  padding-right: 16px;
  white-space: nowrap;
}

.rank-item {
  width: 95%;
  min-height: 46px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  // margin-bottom: 12px;
  background-color: #fff;
  transition: transform 0.2s;
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}
.rank-number {
  width: 70px;
  min-height: 22px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  color: rgba(0, 0, 0, 0.85);
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 限制为两行 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  word-break: break-word; /* 防止长单词撑开容器 */

  .rank-img {
    width: 24px;
    height: 24px;
    object-fit: contain;
    margin: 0 auto;
  }

  &.rank-1,
  &.rank-2,
  &.rank-3 {
    background: none;
    height: 24px;
  }
}

.rank-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 20px;
  padding-right: 16px;

  .rank-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .rank-values {
    display: flex;
    align-items: center;
    gap: 24px;
    font-size: 14px;

    .monthly-value {
      color: #333;
      font-weight: 500;
    }

    .total-value {
      color: #666;
    }
  }
}

.rank-logo {
  position: absolute;
  top: 10px;
  right: 16px;

  .logo-image {
    width: 140px;
    height: auto;
    object-fit: contain;
  }
}

.personal-rank-info {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 999;
  padding: 16px 11px 16px 5px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom)); // 适配底部安全区域

  .personal-rank-item {
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    letter-spacing: normal;
    color: rgba(0, 0, 0, 0.85);
    .rank-img {
      // flex: 1;
      width: 24px;
      height: 24px;
      object-fit: contain;
      margin: 0 auto;
    }
    span {
      // flex: 1;
      text-align: center;
      font-weight: 500;
    }
  }
}
:deep(
  .van-tabs__nav--line.van-tabs__nav--shrink,
  .van-tabs__nav--line.van-tabs__nav--complete
) {
  margin: 10px 0;
}
.rank-placeholder {
  height: 54px; // 与 personal-rank-info 的高度保持一致
  width: 100%;
}

/* 添加空数据提示样式 */
.empty-data {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-describe);
  font-size: var(--font-size-h3);
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}
</style>
