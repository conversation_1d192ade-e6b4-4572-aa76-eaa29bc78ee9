<template>
  <div class="poster-page">
    <div
      class="poster-imageBg"
      :style="{
        backgroundImage: `url(${selectedTemplate.iconUrl})`
      }"
    />
    <div class="create-poster">
      <div ref="posterRef" class="poster-preview">
        <!-- 海报主图 -->
        <img
          :src="selectedTemplate.iconUrl"
          class="main-image"
          :data-oss-id="selectedTemplate.ossId"
        />
        <!-- 用户信息区域 -->
        <div v-if="showUserInfo" class="user-info">
          <img v-if="userAvatar" :src="userAvatar" alt="" class="user-avatar" />
          <span class="user-name">{{ userName }}</span>
        </div>

        <!-- 固定文案区域 -->
        <div class="fixed-text">
          <p class="text-line1">{{ fixedText.line1 }}</p>
          <p class="text-line2">{{ fixedText.line2 }}</p>
        </div>

        <!-- 二维码区域 -->
        <div class="qr-code">
          <vue-qrcode
            :value="shareUrl"
            :options="{
              width: vminToPx(pxToVmin(70)),
              margin: 2,
              color: {
                dark: '#000000',
                light: '#ffffff'
              },
              correctLevel: 'H'
            }"
          />
        </div>
      </div>

      <!-- 控制区域 -->
      <div class="control-panel">
        <van-checkbox v-model="showUserInfo">携带头像昵称</van-checkbox>
      </div>

      <!-- 底部按钮 -->
      <div class="bottom-buttons">
        <van-button type="primary" block @click="handleGeneratePoster"
          >确认生成海报</van-button
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/store/modules/auth";
import VueQrcode from "@chenfengyuan/vue-qrcode";
import { usePosterStore } from "@/store/modules/poster";
import { useFastPoster } from "@/composables/useFastPoster";
import { vminToPx, pxToVmin } from "@/utils/index";

defineOptions({
  name: "GeneratePoster2",
  components: {
    VueQrcode
  }
});

const { posterImage, generatePoster } = useFastPoster();
const router = useRouter();
const authStore = useAuthStore();
const posterRef = ref(null);
const showUserInfo = ref(true);

const selectedTemplate = computed(() => authStore.posterInfo || {});
const userAvatar = computed(() => authStore.userInfo?.avatar || "");
const userName = computed(() => authStore.userInfo?.nickName || "");

const shareUrl = ref(
  `${location.origin}${import.meta.env.VITE_PUBLIC_PATH}driveAppointment/message?userId=${authStore.userInfo.id}&source=11`
);

const fixedText = {
  line1: "诚邀您共同享受驾驶乐趣！长按下方二维码，接受邀请预约试驾吧！",
  line2: "长按二维码接受邀请"
};

const posterStore = usePosterStore();

const handleGeneratePoster = async () => {
  showToast({
    type: "loading",
    message: "海报生成中...",
    forbidClick: true,
    duration: 0
  });
  try {
    await generatePoster(posterRef.value);
    closeToast();

    const newItem = {
      iconUrl: posterImage.value,
      display: 1,
      name: "已生成海报",
      id: Date.now()
    };

    // 添加调试日志
    console.log("准备保存到 store:", newItem);
    posterStore.setGeneratedPoster(newItem);
    console.log("保存后的 store 数据:", posterStore.generatedPoster);

    router.back();
  } catch (error) {
    closeToast();
    console.error("海报生成失败:", error);
  }
};
</script>

<style scoped>
.poster-page {
  display: flex;
}
.poster-page div {
  box-sizing: border-box;
}
.poster-container {
  position: relative;
  min-height: 100vh;
  padding-top: 64px;
}
.poster-imageBg {
  width: 100vw;
  height: calc(100vh - 64px);
  background-size: cover;
  background-position: center;
  filter: blur(24px);
  position: absolute;
}
.swiper-container {
  /* margin-top: 64px; */
}

.poster-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
}

.poster-image {
  width: 314px;
  height: 418px;
}

.poster-name {
  width: 314px;
  height: 46px;
  text-align: center;
  line-height: 46px;
  font-size: var(--font-size-h2);
  color: var(--text-primary);
  background-color: #fff;
}

.create-poster {
  margin: 0 auto;
}

.poster-preview {
  /* display: flex;
  flex-direction: column; */
  position: relative;
  height: 554px;
  width: 314px;
  margin: 24px auto 0;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.main-image {
  width: 100%;
  height: auto;
  display: block;
}

.user-info {
  height: 32px;
  width: 284px;
  position: absolute;
  top: 430px;
  left: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 10;
}

.user-avatar {
  width: 32px;
  height: 32px;
  flex: none;
  border-radius: 16px;
  object-fit: cover;
  background-size: cover;
  background-position: center;
}

.user-name {
  display: inline-block;
  width: 100%;
  font-size: var(--font-size-h3);
  line-height: 12px;
  letter-spacing: 0em;
  color: rgba(0, 0, 0, 0.85);
  text-size-adjust: 100% !important;
}

.fixed-text {
  position: absolute;
  bottom: 10px;
  left: 12px;
  width: 218px;
  text-align: left;
  z-index: 10;
}

.text-line1 {
  width: 222px;
  height: 44px;
  font-size: var(--font-size-h3);
  font-weight: normal;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  text-size-adjust: 100% !important;
}
.text-line2 {
  width: 200px;
  height: 18px;
  margin-top: 8px;
  font-size: var(--font-size-caption);
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0em;
  color: rgba(0, 0, 0, 0.25);
  text-size-adjust: 100% !important;
}

.qr-code {
  position: absolute;
  bottom: 22px;
  right: 10px;
  width: 70px;
  height: 70px;
  z-index: 10;
  background: #fff;
}

.qr-code img {
  width: 100%;
  height: 100%;
  image-rendering: -webkit-optimize-contrast;
}

.control-panel {
  margin: 8px auto 17px;
  width: 314px;
}
:deep(.van-checkbox__label) {
  color: var(--text-highlight) !important;
  z-index: 1;
  font-size: var(--font-size-h3);
  background-color: var(--color-block-background);
  opacity: 0.7;
}

.bottom-buttons button {
  width: 351px;
  display: flex;
  font-size: var(--font-size-h2);
  height: var(--van-button-large-height);
  justify-content: space-around;
}

.btn-left,
.btn-right {
  width: 169.5px;
  height: var(--van-button-large-height);
  font-size: var(--font-size-h2);
}
:deep(.van-swipe__indicators) {
  bottom: 0 !important;
}

.img-poster {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: rgba(0, 0, 0, 0.8); */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.preview-image {
  max-width: 90%;
  max-height: 80vh;
  object-fit: contain;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.backMain {
  position: absolute;
  top: 5px;
  left: 5px;
}
</style>
