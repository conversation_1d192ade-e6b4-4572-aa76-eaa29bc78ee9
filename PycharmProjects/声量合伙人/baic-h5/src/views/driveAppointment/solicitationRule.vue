<template>
  <div class="solicitation-rule">
    <div class="solicitation-rule-content">
      <div style="font-size: var(--font-size-h2)">邀约规则</div>
      <div style="font-size: var(--font-size-h3)">活动规则:</div>
      <div style="font-size: var(--font-size-h3)" v-html="ruleStr" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { rulesText } from "@/api/driveAppointment";
defineOptions({
  name: "SolicitationRule"
});

const ruleStr = ref("");

const getRule = async () => {
  const res = await rulesText({ source: 0 });
  ruleStr.value = res.data.content;
};
getRule();
</script>
<style scoped>
.solicitation-rule {
  padding: 12px;
  box-sizing: border-box;
}
.solicitation-rule div {
  box-sizing: border-box;
}
.solicitation-rule-content {
  width: 351px;
  background: var(--color-block-background);
  border-radius: var(--rounded-md);
  padding: 12px;
  text-align: justify;
  line-height: 1.5;
}
</style>
