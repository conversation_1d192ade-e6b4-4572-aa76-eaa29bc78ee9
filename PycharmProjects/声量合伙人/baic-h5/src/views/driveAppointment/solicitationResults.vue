<template>
  <div class="solicitation-results">
    <Search
      placeholder="请输入手机尾号后四位"
      :clearable="true"
      @input="onInput"
      @onSearch="onSearch"
    />
    <Empty
      v-if="tableList.length < 1"
      icon="search-empty"
      marginTop="80"
      text="暂无数据"
    />
    <!-- 标题 -->
    <van-cell v-if="tableList.length > 0" class="list-title">
      <template #title>
        <div style="display: flex">
          <div v-for="item in column" :key="item.key" class="result-item">
            {{ item.title }}
          </div>
        </div>
      </template>
    </van-cell>
    <van-list
      v-if="tableList.length > 0"
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      class="result-list"
      @load="onLoad"
    >
      <!-- 内容 -->
      <van-cell
        v-for="(item, index) in tableList"
        :key="index"
        :class="index == tableList.length - 1 ? 'result-cell' : ''"
      >
        <template #title>
          <div style="display: flex">
            <div class="result-item">
              {{ index + 1 }}
            </div>
            <div class="result-item">
              {{ item.name }}
            </div>
            <div class="result-item">
              {{ item.phonenumber }}
            </div>
            <div class="result-item">
              <img v-if="item.iconUrl" :src="item.iconUrl" alt="" />
            </div>
          </div>
        </template>
      </van-cell>
    </van-list>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/store/modules/auth";
import { resultList } from "@/api/driveAppointment";

defineOptions({
  name: "SolicitationResults"
});

const authStore = useAuthStore();
const total = ref(0);
const loading = ref(false);
const finished = ref(false);
const listParams = ref({
  pageNum: 1,
  pageSize: 10,
  phonenumber: null,
  userId: authStore.userInfo.id
});
const tableList = ref([]);
const column = ref([
  {
    title: "序号",
    key: "id",
    tooltip: false,
    align: "left"
  },
  {
    title: "姓名",
    key: "name",
    tooltip: false,
    align: "left"
  },
  {
    title: "手机号",
    key: "phonenumber",
    tooltip: false,
    align: "left"
  },
  {
    title: "来源",
    key: "iconUrl",
    tooltip: false,
    align: "left"
  }
]);
const onInput = (value: any) => {
  listParams.value.phonenumber = value;
};
const onSearch = (value: any) => {
  listParams.value.phonenumber = value;
  listParams.value.pageNum = 1;
  getList();
};
const onLoad = () => {
  if (tableList.value.length >= total.value || loading.value) return;
  listParams.value.pageNum++;
  getList();
};

const getList = async () => {
  showLoadingToast({
    message: "加载中...",
    duration: 0,
    forbidClick: true
  });
  loading.value = true;
  try {
    const res = await resultList(listParams.value);
    if (listParams.value.pageNum === 1) {
      tableList.value = res.data.rows;
    } else {
      let list = tableList.value;
      res.data.rows.forEach((item: any) => {
        list.push(item);
      });
      tableList.value = list;
    }
    total.value = res.data.total;

    // 加载状态结束
    loading.value = false;
    closeToast();
    if (tableList.value.length >= total.value) {
      finished.value = true;
    }
  } catch (error) {
    // 加载状态结束
    loading.value = false;
    closeToast();
    if (tableList.value.length >= total.value) {
      finished.value = true;
    }
  }
};
onMounted(() => {
  getList();
});
</script>
<style scoped>
.solicitation-results {
  padding: 8px 12px;
  box-sizing: border-box;
}
.solicitation-results div {
  box-sizing: border-box;
}
.result-item {
  width: 25%;
  word-break: break-word;
  display: flex;
  justify-content: center;
  align-items: center;
}
.result-item img {
  width: 20px;
}
.result-list {
  width: 351px;
  max-height: 80vh;
  overflow-y: auto;
  border-radius: 0 0 var(--rounded-md) var(--rounded-md);
  font-size: var(--font-size-h3);
}
.result-cell {
  border-radius: 0 0 var(--rounded-md) var(--rounded-md) !important;
}
.list-title {
  margin-top: 20px;
  border-radius: var(--rounded-md) var(--rounded-md) 0 0;
  font-size: var(--font-size-h2);
  width: 351px;
}
:deep(.van-icon-search) {
  color: rgba(0, 0, 0, 0.25);
}
:deep(.search-input) {
  padding: 6px !important;
}
:deep(.van-cell) {
  padding: 12px 0;
}
</style>
