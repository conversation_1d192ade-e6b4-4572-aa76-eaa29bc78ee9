<template>
  <div class="link-content">
    <div class="link-header">
      <div class="link-title">专属链接</div>
      <div class="link-check-result" @click="toResult">
        查看邀约结果
        <img :src="arrowRightIcon" alt="volume" class="notice-icon" />
      </div>
    </div>
    <div class="link-body">
      {{ linkUrl }}
    </div>
    <van-button
      class="link-copy"
      plain
      type="primary"
      :icon="copyBtn"
      @click="show = true"
      >一键复制</van-button
    >
    <van-popup
      v-model:show="show"
      position="bottom"
      :style="{ height: '50%' }"
      round
    >
      <div class="link-sourceTitle">
        <span class="choose-btn" @click="closePop">取消</span>
        <span>请选择分享渠道</span>
        <span class="choose-btn" @click="submitLink">确认</span>
      </div>
      <div class="link-sourceContent">
        <van-radio-group v-model="radio">
          <van-cell-group style="padding: 0 12px">
            <van-cell
              v-for="item in sourceList"
              :key="item.id"
              clickable
              :style="{ background: radio == item.id ? '#F0F7FF' : '' }"
              @click="radio = item.id"
            >
              <template #title>
                <img class="link-sourceIcon" :src="item.iconUrl" />
                <span>{{ item.platformName }}</span>
              </template>
              <template v-slot:right-icon>
                <van-radio :name="item.id" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import arrowRightIcon from "@/assets/home/<USER>";
import copyBtn from "@/assets/driveAppointment/copy.png";
import { useAuthStore } from "@/store/modules/auth";
import { selectList } from "@/api/driveAppointment";
import { copyToClipboard } from "@/utils/validate";

defineOptions({
  name: "GenerateLink"
});

const router = useRouter();
const authStore = useAuthStore();
const show = ref(false);
const source = ref("");
const radio = ref(11);
const baseUrl = ref(
  `${location.origin}${import.meta.env.VITE_PUBLIC_PATH}driveAppointment/message?userId=${authStore.userInfo.id}&source=`
);
const linkUrl = ref(baseUrl.value + radio.value);

const submitLink = () => {
  if (!radio.value) {
    showToast("请先选择分享渠道");
    return;
  }
  source.value = sourceList.value.find(
    (item: any) => item.id == radio.value
  ).id;
  // 生成新的链接
  linkUrl.value = baseUrl.value + source.value;
  let link = linkUrl.value;
  copyToClipboard(link);
  show.value = false;
};
const sourceList = ref([]);

const getselectList = () => {
  // 会多返回微信渠道
  selectList({ isChannel: 1 })
    .then((res: any) => {
      if (res.code == 200) {
        sourceList.value = res.data;
      } else {
        sourceList.value = [];
      }
    })
    .catch((err: any) => {
      sourceList.value = [];

      console.log(err);
    });
};

const closePop = () => {
  show.value = false;
  radio.value = null;
};

const toResult = () => {
  router.push({
    name: "SolicitationResults"
  });
};

onMounted(() => {
  getselectList();
});
</script>
<style scoped>
.link-content {
  width: 351px;
  /* height: 304px; */
  margin: 12px auto;
  padding: 12px;
  background: var(--color-block-background);
  border-radius: var(--rounded-md);
  box-sizing: border-box;
}
.link-content div {
  box-sizing: border-box;
}
.link-header {
  display: flex;
  justify-content: space-between;
}
.link-title {
  font-size: var(--font-size-h2);
  color: var(--text-primary);
  font-weight: 500;
}
.link-check-result {
  color: var(--text-describe);
  font-size: var(--font-size-h3);
  display: flex;
  align-items: center;
}
.link-check-result img {
  width: 14px;
  margin-left: 4px;
}
.link-body {
  margin: 16px auto;
  width: 327px;
  height: 178px;
  padding: 12px;
  border-radius: var(--rounded-md);
  border: 1px solid var(--color-border);
  color: var(--text-secondary);
  font-size: var(--font-size-h3);
  word-break: break-word;
}
.link-copy {
  width: var(--van-button-large-width);
  height: 48px;
  border-radius: 6px;
  box-sizing: border-box;
  /* 主色 */
  border: 1px solid #3366cc;
  font-size: 16px;
  line-height: 22px;
  /* 主色 */
  color: #3366cc;
}
.link-sourceIcon {
  width: 24px;
  border-radius: 50%;
  margin-right: 4px;
}
.link-sourceTitle {
  text-align: center;
  font-size: var(--font-size-h2);
  height: 50px;
  background: var(--color-block-background);
  line-height: 50px;
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
}
.link-sourceContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  height: calc(100% - 50px);
}
:deep(.van-radio-group) {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}
:deep(.van-icon__image) {
  width: 18px;
  height: 18px;
  margin-right: 8px;
}
:deep(.van-cell) {
  display: flex;
  align-items: center;
  border-radius: var(--rounded-md);
}
:deep(.van-cell__title) {
  display: flex;
  align-items: center;
}
:deep(.van-cell__value) {
  display: flex;
  justify-content: flex-end;
}
.choose-btn {
  font-size: var(--font-size-h3);
  color: var(--color-primary);
}
:deep(.van-cell:after) {
  border: none;
}
:deep(.van-hairline--top-bottom:after) {
  border: none;
}
</style>
