<template>
  <!--预约试驾留资-->
  <div>
    <div class="head-img">
      <img class="head-img" src="@/assets/driveAppointment/meg-bg.png" alt="" />
    </div>
    <div class="meg-conm van-safe-area-bottom">
      <div class="meg-title">
        <img src="@/assets/driveAppointment/meg-title.png" alt="" />
        <!-- <div class="text-center">与家出发 向心所悦</div> -->
        <div class="text-center" />
      </div>
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field
            v-model="vehicleModel"
            is-link
            readonly
            placeholder="车型"
            @click="showMode = true"
          />
          <van-popup v-model:show="showMode" round position="bottom">
            <van-picker
              :columns="orderVehicleModelColumns"
              :columns-field-names="{
                text: 'label',
                value: 'id',
                children: 'children'
              }"
              @confirm="onConfirmVehicleModel"
              @cancel="showMode = false"
            />
          </van-popup>
          <van-field
            v-model="form.name"
            placeholder="姓名"
            maxlength="50"
            @input="validateNameLength"
          />

          <van-field
            v-model="form.honorific"
            is-link
            readonly
            placeholder="尊称"
            @click="showPicker = true"
          />
          <van-popup v-model:show="showPicker" round position="bottom">
            <van-picker
              :columns="columns"
              @confirm="onConfirm"
              @cancel="showPicker = false"
            />
          </van-popup>
          <van-field
            v-model="form.phonenumber"
            type="tel"
            placeholder="手机号"
          />
          <van-field
            v-model="form.smsCode"
            label-align="top"
            placeholder="请输入验证码"
          >
            <template #button>
              <button
                class="sms-btn"
                :disabled="countdown > 0"
                @click.prevent="sendSmsCode"
              >
                {{ countdown > 0 ? `${countdown}s后重试` : "获取验证码" }}
              </button>
            </template>
          </van-field>
          <!-- 地区选择 -->
          <van-field
            v-model="form.city"
            readonly
            clickable
            rows="1"
            autosize
            type="textarea"
            placeholder="点击选择地区"
            @click="showAreaPicker = true"
          />
          <van-popup v-model:show="showAreaPicker" round position="bottom">
            <van-area
              v-model="area"
              :loading="areaLoading"
              columns-num="2"
              title="地区"
              :area-list="areaData"
              @confirm="onConfirmRegion"
              @cancel="showAreaPicker = false"
            />
          </van-popup>
          <van-field>
            <template #input>
              <van-radio-group direction="horizontal" @click="onChangePrivacy">
                <van-radio :name="true" class="radio-text" :checked="checked"
                  >我已仔细阅读并接受<span
                    class="privacy-text"
                    @click.stop="privacy"
                    >《隐私条款》</span
                  ></van-radio
                >
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>
        <div class="po-f-bottom">
          <van-button
            size="large"
            block
            type="primary"
            native-type="submit"
            :disabled="!isSubmit || !checked"
            >提交</van-button
          >
        </div>
      </van-form>
    </div>
    <!-- 添加隐私条款弹窗 -->
    <van-popup
      v-model:show="showPrivacyPopup"
      round
      position="center"
      class="privacy-popup"
    >
      <div class="privacy-content">
        <h3 class="privacy-title">隐私政策</h3>
        <div class="privacy-text-content">
          我特此授权北京汽车销售有限公司，须按照数据保护规定，保留我的信息数据，以便解答我的问题。我同意北京汽车销售有限公司把我的数据用于其他营销调查和统计目的，并允许北京汽车销售有限公司根据我所提供的信息与我联系。
        </div>
        <div class="privacy-btn-container">
          <van-button type="primary" block @click="showPrivacyPopup = false"
            >我知道了</van-button
          >
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import { validPhone, validCustomName } from "@/utils/validate";
import { add, carTree, getSmsCode } from "@/api/driveAppointment";
import { getAreaList } from "@/utils/index";

defineOptions({
  name: "DriveAppointmentMessage"
});
const router = useRouter();
const areaData = ref({
  province_list: {},
  city_list: {},
  county_list: {}
});
const showPicker = ref(false);
const showAreaPicker = ref(false);
const shareUserId = ref(router.currentRoute.value.query.userId ?? "");
const source = ref(router.currentRoute.value.query.source ?? "");
const form = ref({
  name: "",
  phonenumber: "",
  honorific: "",
  smsCode: "",
  city: "",
  carId: ""
});
const countdown = ref(0);
const checked = ref(false);
const isSubmit = ref(false);
const showMode = ref(false);
const vehicleModel = ref();
const columns = [
  { text: "女士", value: "女士" },
  { text: "先生", value: "先生" }
];
const area = ref("");
const city = ref("");
let timer = null;
const orderVehicleModelColumns = ref();
const onConfirmRegion = (value: any) => {
  form.value.city = value.selectedOptions.map(item => item.text).join("/");
  city.value = value.selectedOptions.map(item => item.value).join("/");
  showAreaPicker.value = false;
};
const onChangePrivacy = (event: any) => {
  // 切换选中状态
  checked.value = !checked.value;
};
/**
 * 处理车型选择确认事件
 * @param eve - 车型选择器的确认事件对象，包含selectedOptions和selectedValues
 */
const onConfirmVehicleModel = (eve: any) => {
  // 检查事件对象及其属性是否存在
  if (!eve || !eve.selectedOptions || !eve.selectedValues) {
    console.error("车型选择数据无效");
    showMode.value = false;
    return;
  }

  try {
    // 确保selectedOptions数组至少有两个元素
    if (
      eve.selectedOptions.length >= 2 &&
      eve.selectedOptions[0]?.label &&
      eve.selectedOptions[1]?.label
    ) {
      // 组合车型名称（品牌-车型）
      vehicleModel.value =
        eve.selectedOptions[0].label + " " + eve.selectedOptions[1].label;
    } else {
      // 处理数据不完整的情况
      vehicleModel.value = eve.selectedOptions[0]?.label || "未知车型";
    }

    // 设置车型ID，确保有效值
    form.value.carId = eve.selectedValues[1] || "";
  } catch (error) {
    console.error("处理车型选择时出错:", error);
    // 出错时设置默认值或保持原值
    vehicleModel.value = vehicleModel.value || "未知车型";
  } finally {
    // 无论成功失败都关闭选择器
    showMode.value = false;
  }
};
const onConfirm = (value: any) => {
  showPicker.value = false;
  form.value.honorific = value.selectedOptions[0].text;
};
const startCountdown = () => {
  countdown.value = 60;
  timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};
const sendSmsCode = async () => {
  if (!form.value.phonenumber) {
    showToast("手机号不能为空");
    return;
  }
  if (!validPhone(form.value.phonenumber)) {
    showToast("手机号格式不正确");
    return;
  }
  // 调用API发送验证码
  try {
    // 调用短信接口
    await getSmsCode({
      phonenumber: form.value.phonenumber
    });

    showToast("验证码已发送");
    isSubmit.value = true; // 验证码已发送
    startCountdown();
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};
// 添加隐私条款弹窗状态
const showPrivacyPopup = ref(false);

// 修改隐私条款点击函数
const privacy = () => {
  showPrivacyPopup.value = true;
};
const onSubmit = async () => {
  if (!form.value.carId) {
    showToast("请选择车型");
    return;
  }
  if (!validCustomName(form.value.name, true)) {
    showToast("请输入正确的姓名");
    return;
  }
  if (!form.value.honorific) {
    showToast("请选择尊称");
    return;
  }
  if (!validPhone(form.value.phonenumber)) {
    showToast("请输入正确的手机号");
    return;
  }
  if (!form.value.smsCode) {
    showToast("请输入验证码");
    return;
  }
  if (!form.value.city) {
    showToast("请输入试驾地区");
    return;
  }
  console.log("222", form.value);

  try {
    const { code } = await add({
      ...form.value,
      source: source.value,
      userId: shareUserId.value,
      city: city.value
    });
    if (code == 200) {
      showSuccessToast({
        message: "提交成功",
        duration: 0,
        forbidClick: true
      });
      router.back();
    }
  } catch (error) {
    showFailToast(error?.msg || "提交失败");
  }
};
const geyCarList = async () => {
  try {
    const { code, data } = await carTree();
    if (code === 200) {
      orderVehicleModelColumns.value = data || [];
    }
  } catch (error) {
    showFailToast(error?.msg || "保存失败");
  }
};

const areaLoading = ref(false);
const getAreaTreeList = () => {
  areaLoading.value = true;
  getAreaList()
    .then((data: any) => {
      areaData.value = data;
    })
    .finally(() => {
      areaLoading.value = false;
    });
};

// 在 script setup 中添加验证函数
// 修改验证函数
const validateNameLength = (event: Event) => {
  const value = (event.target as HTMLInputElement).value;
  if (!value) return;

  const length = [...value].reduce((acc, char) => {
    return acc + (/[\u4e00-\u9fa5]/.test(char) ? 2 : 1);
  }, 0);

  if (length > 50) {
    form.value.name = value.slice(0, -1);
    showToast("已达到最大输入长度");
  }
};

geyCarList();
getAreaTreeList();
</script>
<style scoped lang="less">
.head-img {
  width: 100vmin;
}
.meg-conm {
  border-radius: var(--rounded-xl) var(--rounded-xl) 0px 0px;
  background-color: #fff;
  margin-top: -10px;
  position: relative;
  z-index: 1;
  min-height: calc(100vh - 195px);
}

.sms-btn {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  display: flex;
  align-items: flex-end;
  letter-spacing: 0em;
  color: var(--color-primary);
}
.meg-title {
  padding-left: 24px;
  padding: 24px;
  .text-center {
    text-align: left;
    color: var(--text-describe);
    font-size: 10px;
    margin-top: 4px;
    line-height: 13px;
  }
}
:deep(.van-cel) {
  border-width: 0px 0px 1px 0px;
  border-style: solid;
  border-color: #f0f0f0;
}
.radio-text {
  font-size: var(--font-size-h3);
}
.po-f-bottom {
  width: 100vmin;
  padding: 12px;
  box-sizing: border-box;
  // border-top: 1px solid #f0f0f0;
}
.privacy-text {
  color: var(--text-highlight);
}
/* 添加隐私弹窗样式 */
.privacy-popup {
  width: 80%;
  max-width: 320px;
  padding: 0;
  border-radius: 12px;
  overflow: hidden;
}

.privacy-content {
  padding: 20px;
}

.privacy-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
}

.privacy-text-content {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 20px;
}

.privacy-btn-container {
  margin-top: 16px;
}
</style>
