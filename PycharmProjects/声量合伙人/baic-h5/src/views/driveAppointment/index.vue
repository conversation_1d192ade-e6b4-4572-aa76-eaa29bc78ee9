<template>
  <div class="content">
    <!-- <div>
      <div class="content-title">邀约试驾</div>
      <div class="content-tip">Sincerely Invite A Test Drive</div>
    </div> -->
    <img class="banner-img" src="@/assets/driveAppointment/banner.png" />
    <div class="fun-list">
      <div v-for="item in funList" :key="item.type" class="fun-item">
        <div class="fun-title">
          {{ item.title }}
        </div>
        <div class="fun-tips">
          {{ item.tips }}
        </div>
        <van-button
          class="fun-btn"
          type="primary"
          size="small"
          @click="goToPage(item.path)"
          >{{ item.btnText }}</van-button
        >
        <img class="fun-icon" alt="" :src="item.icon" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import placard from "@/assets/driveAppointment/placard.png";
import link from "@/assets/driveAppointment/link.png";
import rule from "@/assets/driveAppointment/rule.png";
import achievement from "@/assets/driveAppointment/achievement.png";

defineOptions({
  name: "DriveAppointment"
});
const router = useRouter();
const bgImg = ref("");
const funList = ref([
  {
    title: "生成海报",
    tips: "个人专属海报值得拥有",
    btnText: "去生成",
    icon: placard,
    path: "driveAppointment/generatePoster",
    type: 1
  },
  {
    title: "生成链接",
    tips: "在互联网的海洋里遨游",
    btnText: "去生成",
    icon: link,
    type: 2,
    path: "driveAppointment/generateLink"
  },
  {
    title: "活动规则",
    tips: "无规矩不成方圆",
    btnText: "去查看",
    icon: rule,
    path: "driveAppointment/solicitationRule",
    type: 3
  },
  {
    title: "邀约成果",
    tips: "成功的背后离不开邀约",
    btnText: "去查看",
    icon: achievement,
    type: 4,
    path: "driveAppointment/solicitationResults"
  }
]);
const goToPage = (path: string) => {
  if (!path) return;
  router.push({
    path
    // hash: `#${ids}`
  });
};
</script>
<style scoped>
.content {
  background-image: url("@/assets/driveAppointment/bg2.png");
  background-size: 100%;
  background-repeat: no-repeat;
  min-height: 100vh;
  padding: 0 16px;
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  box-sizing: border-box;
}
.banner-img {
  width: 100%;
  height: 100%;
  margin: 45px 0 4px;
}
.content div {
  box-sizing: border-box;
}
.content-title {
  font-size: var(--font-size-h1);
  font-weight: bold;
  line-height: normal;
  display: flex;
  align-items: center;
  letter-spacing: 0em;
  color: var(--text-primary);
  text-shadow: 0px 4px 20px #ffffff;
  margin: 61px 0 4px;
}
.content-tip {
  font-size: var(--font-size-caption);
  font-weight: normal;
  line-height: 18px;
  text-transform: capitalize;
  letter-spacing: 0em;
  color: var(--text-describe);
  text-shadow: 0px 4px 20px #ffffff;
}
/* .fun-list {
  width: 343px;
  height: 272px;
  border-radius: var(--rounded-xl);
  display: flex;
  flex-direction: row;
  padding: 20px;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;
  background: var(--color-car-background);
  backdrop-filter: blur(40px);
  margin: 0 auto 0;
} */

.fun-list {
  width: 343px;
  /* height: 272px; */
  border-radius: var(--rounded-xl);
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 12px;
  padding: 20px;
  background: var(--color-car-background);
  backdrop-filter: blur(40px);
  margin: 0 auto 0;
}
.fun-item {
  width: 145px;
  min-height: 110px;
  padding: 12px 10.5px;
  border-radius: var(--rounded-xl);
  background: var(--color-block-background);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.04);
  position: relative;
}
.fun-title {
  font-size: var(--font-size-h3);
  line-height: 22px;
  font-weight: 500;
  margin-bottom: 4px;
}
.fun-tips {
  font-size: var(--font-size-small);
  font-weight: normal;
  color: var(--text-describe);
}
.fun-btn {
  /* width: 60px;
  height: 28px; */
  text-align: center;
  color: #ffffff;
  line-height: 28px;
  font-size: var(--font-size-caption);
  font-weight: normal;
  margin: 16px 0 0;
}
.fun-icon {
  height: 46px;
  width: auto;
  position: absolute;
  bottom: 0;
  right: 0;
}
</style>
