<template>
  <div class="poster-page">
    <div class="poster-container">
      <div
        class="poster-imageBg"
        :style="{
          backgroundImage: `url(${templateList[currentIndex]?.iconUrl})`
        }"
      />
      <!-- 当模板列表长度大于1时使用轮播，否则直接展示 -->
      <div v-if="templateList.length > 1" class="swiper-container">
        <van-swipe
          class="poster-swipe"
          :show-indicators="true"
          indicator-color="var(--color-primary)"
          @change="updateCurrentIndex"
        >
          <van-swipe-item
            v-for="item in templateList"
            :key="item.id"
            class="swipe-item"
          >
            <div class="poster-item">
              <img
                v-if="templateList[currentIndex]?.name === '已生成海报'"
                :src="item.iconUrl"
                :alt="item.name"
                class="poster-image2"
              />
              <img
                v-else
                :src="item.iconUrl"
                :alt="item.name"
                class="poster-image"
              />
              <div
                v-if="templateList[currentIndex]?.name !== '已生成海报'"
                class="poster-name"
              >
                {{ item.name }}
              </div>
              <!-- <div v-else class="poster-name2">请长按图片保存</div> -->
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
      <!-- 单个模板展示 -->
      <div
        v-else-if="templateList.length === 1"
        class="single-container"
        style="position: relative"
      >
        <div class="poster-item">
          <img
            v-if="templateList[0].name === '已生成海报'"
            :src="templateList[0].iconUrl"
            :alt="templateList[0].name"
            class="poster-image2"
          />
          <img
            v-else
            :src="templateList[0].iconUrl"
            :alt="templateList[0].name"
            class="poster-image"
          />
          <div
            v-if="templateList[currentIndex]?.name !== '已生成海报'"
            class="poster-name"
          >
            {{ templateList[0].name }}
          </div>
        </div>
      </div>
      <div v-else style="height: 76vmax" />
      <div
        v-if="templateList[currentIndex]?.name === '已生成海报'"
        class="poster-name2"
      >
        请长按图片保存
      </div>
      <!-- 底部按钮 -->
      <div class="bottom-buttons" style="margin-top: 34px">
        <template v-if="templateList[currentIndex]?.name === '已生成海报'">
          <!-- <van-button class="btn-right" type="primary">长按图片保存</van-button> -->
        </template>
        <template v-else>
          <!-- <van-button
            class="btn-left"
            type="primary"
            plain
            @click="toGenerateLink"
            >生成链接</van-button
          > -->
          <van-button class="btn-right" type="primary" @click="toSetpNext"
            >生成专属海报</van-button
          >
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { selectTemplateList } from "@/api/driveAppointment";
import { useAuthStore } from "@/store/modules/auth";
import VueQrcode from "@chenfengyuan/vue-qrcode";
import { usePosterStore } from "@/store/modules/poster";

defineOptions({
  name: "GeneratePoster",
  components: {
    VueQrcode
  }
});

const router = useRouter();
const authStore = useAuthStore();
const templateList = ref([]);
const currentIndex = ref(0);

const toSetpNext = () => {
  if (templateList.value.length < 1) return;
  let param = {
    ...templateList.value[currentIndex.value]
  };
  authStore.setPoster(param);
  router.push("/driveAppointment/generatePoster2");
};
const selectedTemplate = computed(() => {
  return templateList.value[currentIndex.value] || {};
});

const shareUrl = ref(
  `${location.origin}${import.meta.env.VITE_PUBLIC_PATH}driveAppointment/message?userId=${authStore.userInfo.id}&source=`
);
console.log("shareUrl", shareUrl.value);

const fixedText = {
  line1: "诚邀您共同享受驾驶乐趣!长按下方二维码,接受邀请预约试驾吧!",
  line2: "长按二维码接受邀请"
};

// 获取海报模板列表
const getTemplateList = async () => {
  try {
    const res = await selectTemplateList({ display: 1 });
    if (res.code === 200) {
      templateList.value = res.data;
    }
  } catch (error) {
    console.error("获取模板列表失败:", error);
  }
};

// 跳转到生成链接页面
const toGenerateLink = () => {
  router.push("/driveAppointment/generateLink");
};

// 更新轮播索引
const updateCurrentIndex = index => {
  currentIndex.value = index;
};

const posterStore = usePosterStore();

// 检查是否有新生成的海报
onMounted(async () => {
  console.log("开始获取模板列表");
  await getTemplateList();
  console.log("模板列表获取完成:", templateList.value);

  if (posterStore.generatedPoster) {
    console.log("发现新生成的海报:", posterStore.generatedPoster);
    templateList.value = [posterStore.generatedPoster, ...templateList.value];
    console.log("更新后的模板列表:", templateList.value);
    posterStore.clearGeneratedPoster();
  }

  closeToast();
});

const handleDelete = () => {
  // 从列表中删除当前项
  templateList.value = templateList.value.filter(
    (_, index) => index !== currentIndex.value
  );
  showToast("删除成功");
};
</script>

<style scoped>
.poster-page div {
  box-sizing: border-box;
}
.poster-container {
  position: relative;
  min-height: 100vh;
  padding-top: 10px;
}
.poster-imageBg {
  width: 100vw;
  height: calc(100vh - 64px);
  background-size: cover;
  background-position: center;
  filter: blur(24px);
  position: absolute;
}
.swiper-container {
  /* margin-top: 64px; */
}
.poster-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
}

.poster-image {
  width: 314px;
  height: 418px;
}
.poster-image2 {
  width: auto;
  height: 464px;
}
.poster-name {
  width: 314px;
  height: 46px;
  text-align: center;
  line-height: 46px;
  font-size: var(--font-size-h2);
  color: var(--text-primary);
  background-color: #fff;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding: 0 10px;
}
.poster-name2 {
  height: 30px;
  width: 100%;
  text-align: center;
  line-height: 30px;
  font-size: var(--font-size-h3);
  color: var(--text-placeholder);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding: 0 10px;
  position: relative;
  margin-top: 20px;
  margin-bottom: -40px;
  /* position: absolute;
  bottom: 80px; */
}

.create-poster {
  padding: 5px 16px 0 16px;
  min-height: 100vh;
  background-color: #c6e6f7;
}

.poster-preview {
  position: relative;
  height: 570px;
  width: 100%;
  max-width: 375px;
  margin: 0 auto;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.main-image {
  width: 100%;
  height: auto;
  display: block;
}

.user-info {
  position: absolute;
  top: 465px;
  left: 10px;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 10;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.user-name {
  color: #000;
  font-size: 15px;
  font-weight: 500;
}

.fixed-text {
  position: absolute;
  bottom: 10px;
  left: 10px;
  width: 220px;
  text-align: left;
  z-index: 10;
}

.text-line1 {
  color: #000;
  font-size: 14px;
  letter-spacing: 1px;
}
.text-line2 {
  margin-top: 4px;
  opacity: 0.5;
  color: #000;
}

.qr-code {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 100px;
  height: 100px;
  z-index: 10;
}

.qr-code img {
  width: 100%;
  height: 100%;
}

.control-panel {
  margin: 10px 0;
}

.bottom-buttons {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.btn-left,
.btn-right {
  width: 169.5px;
  height: var(--van-button-large-height);
  font-size: var(--font-size-h2);
}
:deep(.van-swipe__indicators) {
  bottom: 0 !important;
}

.img-poster {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.preview-image {
  max-width: 90%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.backMain {
  position: absolute;
  top: 5px;
  left: 5px;
}
</style>
