<template>
  <div class="point-page">
    <div class="point-box flex justify-between items-center">
      <div class="flex-item">
        <div class="font-bold one-line">
          {{ getIntegerPart(pointBox.leftNum) }}
        </div>
        <div class="desc">
          {{ tabType === 2 ? "总额" : "支出" }}
        </div>
      </div>
      <div class="flex-item">
        <div class="font-bold one-line">
          {{ getIntegerPart(pointBox.rightNum) }}
        </div>
        <div class="desc">
          {{ tabType === 2 ? "当期收入" : "收入" }}
        </div>
      </div>
    </div>
    <div class="date-box">
      <!-- 日期展示 -->
      <div
        class="date-selector font-medium flex items-center"
        @click="pickerStatus = true"
      >
        {{ year }}-{{ month }}
        <svg-icon name="arrow-right-s-line" />
      </div>
      <!-- 列表 -->
      <div v-if="list.length > 0">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="getList"
        >
          <van-cell
            v-for="(item, index) in list"
            :key="item.type"
            style="padding: 0"
          >
            <div
              class="detail-item flex justify-between items-center"
              :class="{ 'border-b-1': index < list.length - 1 }"
            >
              <div class="icon-box flex justify-center items-center">
                <svg-icon name="icon-my-point-exchange" class="icon" />
              </div>
              <div
                class="flex-1 flex-wrap mr-[12px]"
                style="justify-content: start"
              >
                <div
                  class="one-line mb-[6px] font-medium"
                  style="
                    -webkit-line-clamp: 2;
                    font-size: var(--font-size-h2);
                    color: var(--text-primary);
                  "
                >
                  {{ item.businessDesc }}
                </div>
                <div class="date">
                  {{ item.recordTime }}
                </div>
              </div>
              <div class="font-bold money w-[80px]">
                {{ item.symbol }}{{ getIntegerPart(item.changeValue) }}
              </div>
            </div>
          </van-cell>
        </van-list>
      </div>
      <Empty v-else />
    </div>
    <!-- 年月选框 -->
    <van-overlay :show="pickerStatus" class="flex items-end">
      <van-date-picker
        v-if="pickerStatus"
        v-model="currentDate"
        style="width: 100vw"
        title="选择年月"
        :max-date="new Date()"
        :columns-type="['year', 'month']"
        @confirm="
          val => {
            handleCheckDate(val);
          }
        "
        @cancel="pickerStatus = false"
      />
    </van-overlay>
  </div>
</template>
<script setup lang="ts">
import {
  pointList,
  inComeAndExpense,
  thermalValue,
  thermalIncome
} from "@/api/home/<USER>";
const props = defineProps<{
  tabType: number;
}>();
const tabType = ref(props.tabType);
const pointBox = ref<any>({
  leftNum: "0",
  rightNum: "0"
});
const year = ref<string>(""); //年
const month = ref<string>(""); //月
const currentDate = ref<any[]>([]); //默认选当月
const pickerStatus = ref<boolean>(false); //年月选框的展示
const loading = ref(false); //加载状态
const finished = ref(false); //是否加载完毕
const list = ref<any[]>([]); //列表数据
const defaultPage = {
  pageNum: 1,
  pageSize: 20
};
const pageData = ref<any>(); //分页数据
// 热力值总额
const getThermalValue = async () => {
  try {
    const { data } = await thermalValue();
    pointBox.value.leftNum = getIntegerPart(data);
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};
// 热力值当期收入
const getThermalIncome = async () => {
  try {
    const { data } = await thermalIncome({
      yearMonth: `${year.value}-${month.value}`
    });
    pointBox.value.rightNum = getIntegerPart(data);
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};
// 任务奖励/积分的支出和收入
const getInComeAndExpense = async () => {
  try {
    // 调用短信接口
    const { data } = await inComeAndExpense({
      pointType: tabType.value,
      yearMonth: `${year.value}-${month.value}`
    });
    pointBox.value.leftNum = getIntegerPart(data.expensePoint);
    pointBox.value.rightNum = getIntegerPart(data.incomePoint);
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};

// 获取列表
const getList = async () => {
  try {
    const { rows, total } = await pointList({
      category: tabType.value,
      yearMonth: `${year.value}-${month.value}`,
      pageNum: pageData.value.pageNum,
      pageSize: pageData.value.pageSize
    });
    list.value = [...list.value, ...rows];
    loading.value = false;
    if (total <= list.value.length) {
      finished.value = true;
    } else {
      finished.value = false;
      pageData.value.pageNum += 1;
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};

// 选择年月
const handleCheckDate = (val: any) => {
  year.value = String(val.selectedValues[0]);
  month.value = String(val.selectedValues[1]);
  pickerStatus.value = false;
  getPageData();
};
// 获取当天年月
const getNowDate = () => {
  const now = new Date();
  year.value = String(now.getFullYear());
  month.value = String(now.getMonth() + 1).padStart(2, "0");
  currentDate.value = [year.value, month.value];
};
// 去除小数点
const getIntegerPart = (value: string) => {
  const parts = value.split(".");
  return parts[0];
};
// 调接口汇总
const getPageData = () => {
  pageData.value = { ...defaultPage };
  list.value = [];
  if (tabType.value === 0 || tabType.value === 1 || tabType.value === 2) {
    getList();
  }
  if (tabType.value === 0 || tabType.value === 1) {
    getInComeAndExpense();
  }
  if (tabType.value === 2) {
    getThermalValue();
    getThermalIncome();
  }
};
onMounted(() => {
  getNowDate();
  getPageData();
});
watch(
  () => props.tabType,
  newType => {
    tabType.value = newType;
    getNowDate();
    getPageData();
  }
);
</script>
<style lang="less" scoped>
.point-page {
  color: var(--text-primary);
  padding: 2px 12px;
  .point-box {
    width: 351px;
    height: 76px;
    background: linear-gradient(104deg, #dce1eb 29%, #f2f3f7 100%);
    box-sizing: border-box;
    border: 1px solid #dce1eb;
    border-radius: var(--rounded-xl);
    padding: 16px 12px 12px;
    margin-top: 10px;
    .flex-item {
      flex: 1; // 使每个子元素平分可用空间
      display: flex;
      flex-direction: column;
      align-items: center; // 使子元素内容垂直居中
      .desc {
        margin-top: 4px;
        font-size: var(--font-size-caption);
        color: var(--text-describe);
      }
    }
  }

  .icon-box {
    border-radius: var(--rounded-md);
    width: 50px;
    height: 50px;
    background-color: var(--color-background);
    margin-right: 12px;
    .icon {
      width: 32px;
      height: 32px;
    }
  }
  .one-line {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1; /* 限制为两行 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    word-break: break-word; /* 防止长单词撑开容器 */
    font-size: var(--font-size-h1);
  }
  .date-box {
    border-radius: var(--rounded-xl);
    padding: 12px 15px 0;
    background-color: var(--color-block-background);
    margin: 12px 0;
    .date-selector {
      font-size: var(--font-size-h2);
      margin-bottom: 16px;
    }
    :deep(.van-cell__value) {
      text-align: left;
    }
    :deep(.van-cell:after) {
      display: none;
    }
    .detail-item {
      padding-bottom: 14px;
      margin-bottom: 12px;
      .date {
        font-size: var(--font-size-caption);
        color: var(--text-describe);
      }
      .money {
        font-size: var(--font-size-h1);
        color: #3366cc;
        text-align: right;
      }
    }
    .border-b-1 {
      border-bottom: 1px solid var(--color-divider);
    }
  }
}
</style>
