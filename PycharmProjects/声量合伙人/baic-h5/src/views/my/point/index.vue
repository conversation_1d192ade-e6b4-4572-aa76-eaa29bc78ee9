<template>
  <div class="my-point van-safe-area-bottom">
    <van-tabs v-model:active="active" sticky>
      <van-tab v-for="item in tabList" :key="item.key" :title="item.title" />
      <TabDetail :tabType="active" />
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
import TabDetail from "./components/point-detail.vue";

defineOptions({
  name: "MyPoint"
});

// 定义 Tab 类型
interface Tab {
  title: string;
  key: number;
}

// 获取路由实例
const route = useRoute();
const router = useRouter();

// 当前激活的 tab
const active = ref<number>(-1);

// Tab 列表
const tabList: Tab[] = [
  { title: "任务奖励积分", key: 0 },
  { title: "销售转化积分", key: 1 },
  { title: "热力值", key: 2 }
];

// 从路由参数中获取 active
const getActiveFromRoute = () => {
  const activeFromRoute = Number(route.query.active);
  if (
    !isNaN(activeFromRoute) &&
    activeFromRoute >= 0 &&
    activeFromRoute < tabList.length
  ) {
    active.value = activeFromRoute;
  } else {
    active.value = 0;
  }
};

// 监听 active 变化，更新路由
watch(active, newActive => {
  router.replace({ query: { active: newActive.toString() } });
});

// 页面挂载时从路由参数初始化 active
onMounted(() => {
  getActiveFromRoute();
});

// 页面重新展示时从路由参数初始化 active
onActivated(() => {
  getActiveFromRoute();
});
</script>

<style lang="less" scoped>
.my-point {
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  font-size: var(--font-size-h2);
}
</style>
