<script setup lang="ts">
import { getMyFuncBtnList, balance } from "@/api/my";
import { distinctionUrl, phoneDesensitization } from "@/utils/validate";
import { useAuthStore } from "@/store/modules/auth";
import { getMyUserInfo } from "@/api/my";
import { to as toPromise } from "await-to-js";
import setPageTitle from "@/utils/set-page-title";

defineOptions({
  name: "My"
});

const router = useRouter();
const rowsList = ref([]);
const authStore = useAuthStore();
const myData = computed(() => authStore.userInfo);
const taskRewardPoint = ref(0);
const saleConversionPoint = ref(0);
const thermalValue = ref(0);
const { setUser } = useAuthStore();

// 加载状态
const isUserInfoLoading = ref(true);
const isFunctionsLoading = ref(true);

// 去除小数点
const getIntegerPart = (value: string) => {
  const parts = value.split(".");
  return parts[0];
};

// 处理大数值显示
const formatLargeNumber = (value: string | number) => {
  const numStr = String(value);
  const integerPart = getIntegerPart(numStr);
  return integerPart.length > 7 ? "999999+" : integerPart;
};

// 编辑用户信息
const edit = () => {
  console.log("编辑");
  router.push({ path: "my/edit" });
};

// 跳转功能
const jump = (item: any) => {
  if (distinctionUrl(item.jumpUrl)) {
    setPageTitle("\u200E");
    window.location.href = item.jumpUrl;
  } else {
    router.push({ path: item.jumpUrl });
  }
};

// 获取数据
const getMy = () => {
  getMyFuncBtnList({ showType: 1 })
    .then((res: any) => {
      if (res.code == 200) {
        rowsList.value = res.data;
        isFunctionsLoading.value = false;
      }
    })
    .catch(error => {
      showFailToast(error?.msg || "请求失败");
    });

  balance()
    .then((res: any) => {
      if (res.code == 200) {
        saleConversionPoint.value = res.data.saleConversionPoint;
        taskRewardPoint.value = res.data.taskRewardPoint;
        thermalValue.value = res.data.thermalValue;
      }
    })
    .catch(error => {
      showFailToast(error?.msg || "请求失败");
    })
    .finally(() => {
      isUserInfoLoading.value = false;
    });
};

onMounted(async () => {
  const [error, userInfoResponse] = await toPromise(getMyUserInfo());
  if (!error) {
    setUser(userInfoResponse.data); // 保存用户信息
  }
  getMy();
});
</script>

<template>
  <div class="my-box">
    <!-- 用户信息骨架屏 -->
    <!-- 用户信息骨架屏 -->
    <div v-if="isUserInfoLoading" class="skeleton-user-info">
      <div class="skeleton-header">
        <!-- 头像骨架 -->
        <div class="skeleton-avatar shimmer" />
        <!-- 用户信息骨架 -->
        <div class="skeleton-info">
          <div class="skeleton-name shimmer" />
          <div class="skeleton-department shimmer" />
        </div>
        <!-- 编辑按钮骨架 -->
        <div class="skeleton-edit shimmer" />
      </div>
      <!-- 积分卡片骨架 -->
      <div class="skeleton-points">
        <div v-for="i in 3" :key="i" class="skeleton-point-item shimmer">
          <div class="skeleton-point-value" />
          <div class="skeleton-point-label" />
        </div>
      </div>
    </div>

    <!-- 真实用户信息 -->
    <div v-else class="my-content my-head-bg">
      <div class="flex">
        <div v-if="myData.avatar" class="my-head-img">
          <img :src="myData.avatar" alt="" />
        </div>
        <div class="my-head-info">
          <div class="my-head-info-word">
            <div class="my-head-info-name">{{ myData.name }}</div>
            <div v-if="myData.departmentName" class="my-head-info-text">
              {{ myData.departmentName }}
            </div>
          </div>
          <div class="my-head-info-department">
            ID号：{{ phoneDesensitization(myData.phonenumber) }}
          </div>
        </div>
        <div class="my-head-info-edit" @click="edit">
          <svg-icon name="edit" class="edit-icon" />
        </div>
      </div>
      <div class="flex my-car-top">
        <div
          class="my-car-box-left"
          @click="router.push({ name: 'MyPoint', query: { active: 0 } })"
        >
          <div class="my-car-box-num">
            {{ formatLargeNumber(taskRewardPoint) }}
          </div>
          <div class="flex my-car-box-desc">
            任务奖励积分<svg-icon name="right" class="my-car-box-icon" />
          </div>
        </div>
        <div
          class="my-car-box-left"
          @click="router.push({ name: 'MyPoint', query: { active: 1 } })"
        >
          <div class="my-car-box-num">
            {{ formatLargeNumber(saleConversionPoint) }}
          </div>
          <div class="flex my-car-box-desc">
            销售转化积分<svg-icon name="right" class="my-car-box-icon" />
          </div>
        </div>
        <div
          class="my-car-box-left"
          @click="router.push({ name: 'MyPoint', query: { active: 2 } })"
        >
          <div class="my-car-box-num">
            {{ formatLargeNumber(thermalValue) }}
          </div>
          <div class="flex my-car-box-desc">
            热力值<svg-icon name="right" class="my-car-box-icon" />
          </div>
        </div>
      </div>
    </div>

    <!-- 功能列表骨架屏 -->
    <div class="my-content">
      <div class="my-title">常用功能</div>
      <div v-if="isFunctionsLoading" class="skeleton-functions">
        <div v-for="i in 8" :key="i" class="skeleton-function-item">
          <div class="skeleton-icon shimmer" />
          <div class="skeleton-text shimmer" />
        </div>
      </div>
      <div v-else class="my-tab">
        <div
          v-for="item in rowsList"
          :key="item.id"
          class="my-tab-item"
          @click="jump(item)"
        >
          <img :src="item.icon" alt="" class="my-tab-icon" />
          <div class="my-tab-text">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.shimmer {
  position: relative;
  overflow: hidden;
  background: #f6f7f8;
  border-radius: 4px;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -150%;
    width: 150%;
    height: 100%;
    background: linear-gradient(
      to right,
      transparent 0%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 100%
    );
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  100% {
    left: 200%;
  }
}

/* 用户信息骨架屏 */
.skeleton-user-info {
  margin: 0 auto;
  width: calc(100% - 28px);
  background: #fff;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.skeleton-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 头像骨架 */
.skeleton-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

/* 用户信息骨架 */
.skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-name {
  width: 120px;
  height: 24px; /* 匹配真实姓名高度 */
  border-radius: 4px;
}

.skeleton-department {
  width: 80px;
  height: 20px; /* 匹配部门名称高度 */
}

.skeleton-id {
  width: 100px;
  height: 16px; /* 匹配ID号高度 */
}

/* 编辑按钮骨架 */
.skeleton-edit {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

/* 积分卡片骨架 */
.skeleton-points {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.skeleton-point-item {
  width: 30%;
  text-align: center;
}

.skeleton-point-value {
  width: 60px;
  height: 23px; /* 匹配积分数值高度 */
  margin: 0 auto 8px;
}

.skeleton-point-label {
  width: 80px;
  height: 16px; /* 匹配积分标签高度 */
  margin: 0 auto;
}

.skeleton-functions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding-top: 12px;
}

.skeleton-function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.skeleton-icon {
  width: 30px;
  height: 30px;
  border-radius: 8px;
}

.skeleton-text {
  width: 60%;
  height: 14px;
}

/* 原有样式保持不变 */
.my-box {
  width: 100%;
  background-color: var(--color-background);
  padding-top: 12px;
  background-image: url("@/assets/my/my-bg.png");
  background-position: top;
  background-size: 100%;
  background-repeat: no-repeat;
}

.my-content {
  margin: 0 auto;
  width: calc(100% - 28px);
  background-color: #fff;
  border-radius: 6px;
  margin-bottom: 12px;
  padding: 12px;
}

.my-head-bg {
  border: 1px solid;
  border-image: radial-gradient(
      1% 9% at 89% 0%,
      #ffffff 0%,
      rgba(255, 255, 255, 0) 100%
    )
    2;
  background: linear-gradient(117deg, #dce1eb 28%, #f2f3f7 99%);
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.my-head-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid rgba(255, 255, 255, 0.5);
}

.my-head-img img {
  min-height: 100%;
  object-fit: cover;
  display: block;
}

.my-head-info {
  margin-left: 10px;
  width: calc(100% - 90px);
}

.my-head-info-name {
  font-size: 20px;
  color: var(--text-highlight);
  line-height: 1.5;
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  font-weight: 500;
}

.my-head-info-department {
  margin-top: 4px;
  font-size: var(--font-size-caption);
  color: var(--text-describe);
}

.my-head-icon {
  font-size: 16px;
}

.my-car-box {
  margin: 0 auto;
  width: calc(100% - 28px);
  background-color: #fff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #f4f4f4;
  background: radial-gradient(
      91% 62% at 100% 1%,
      #c9d5fd 29%,
      rgba(201, 213, 253, 0) 97%
    ),
    radial-gradient(92% 65% at 0% 0%, #e5e3e6 16%, rgba(229, 227, 230, 0) 98%),
    linear-gradient(180deg, #d2e3ff 0%, #92b1e8 21%);
}

.my-car-top {
  margin-top: 16px;
  padding: 4px 10px;
}

.my-car-box-left {
  width: 90px;
  color: var(--text-describe);
  font-size: 14px;
  text-align: center;
}

.my-car-box-num {
  font-weight: bold;
  color: var(--text-primary);
  font-size: 20px;
  text-align: center;
  margin-bottom: 4px;
}

.my-car-box-desc {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-caption);
  font-weight: normal;
  height: 18px;
  text-align: center;
  color: var(--text-describe);
}

.my-car-box-right {
  color: #fff;
  border: 1px solid #fff;
  font-size: var(--font-size-caption);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 6px;
  border-radius: 30px;
}

.my-title {
  font-size: 16px;
  line-height: 22px;
  color: var(--text-highlight);
  font-weight: bold;
}

.my-tab {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: auto 10px;
  justify-content: space-evenly;
  margin: 0 -12px;
}

.my-tab-item {
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: column;
  color: var(--text-highlight);
  font-size: var(--font-size-caption);
  margin-top: 16px;
}

.my-tab-icon {
  width: 30px;
  height: 30px;
  display: block;
  margin-bottom: 4px;
}

.my-tab-text {
  font-size: var(--font-size-caption);
  font-weight: normal;
  line-height: 18px;
  color: var(-----text-primary);
}

.my-head-info-text {
  background: #f0f7ff;
  border: 1px solid #b1cdf2;
  color: var(--color-primary);
  font-size: var(--font-size-caption);
  text-align: center;
  padding: 0 4px;
  line-height: 20px;
  margin-left: 8px;
  max-width: 90px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  border-radius: 3px;
}

.my-head-info-word {
  display: flex;
  align-items: center;
}

.edit-icon {
  font-size: 16px;
}

.my-car-box-icon {
  color: var(--text-describe);
}

:deep(.van-button--plain) {
  background-color: transparent;
}

.my-footer {
  width: 100%;
  padding: 0 12px;
  position: fixed;
  left: 0;
  bottom: calc(var(--van-tabbar-height) + env(safe-area-inset-bottom) + 12px);
}
</style>
