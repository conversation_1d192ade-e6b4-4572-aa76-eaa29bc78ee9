<script setup lang="ts">
import setPageTitle from "@/utils/set-page-title";

defineOptions({
  name: "CustomerService"
});
const handleCall = () => {
  window.location.href = "tel:4000606711";
};
const handleOpenService = () => {
  setPageTitle("\u200E");
  window.location.href = "https://work.weixin.qq.com/kfid/kfc89e9f71a097e85b4";
};
</script>
<template>
  <div class="p-[12px]">
    <div class="customer-service font-medium">
      <!-- <div class="h-[50px] mb-[15px]">
        <div class="mb-[6px]">客服电话</div>
        <div class="tel flex items-center" @click="handleCall">
          <svg-icon name="phone-line" class="mr-[4px]" />
          <div>400-0606-711</div>
        </div>
      </div> -->
      <div class="h-[50px]">
        <div class="mb-[6px]">客服时间</div>
        <div class="time font-normal">周一至周五 10:00-19:00</div>
      </div>
    </div>
  </div>
  <div class="footer">
    <van-button
      type="primary"
      size="large"
      plain
      color="var(--color-primary)"
      class="var(--van-button-large-height)"
      @click="handleOpenService"
    >
      <div class="flex items-center">
        <svg-icon name="customer-service-2-line" class="mr-[8px]" />
        在线客服
      </div>
    </van-button>
    <div class="van-safe-area-bottom" style="background: #fff" />
  </div>
</template>
<style lang="less" scoped>
.customer-service {
  border-radius: var(--rounded-md);
  background-color: var(--color-block-background);
  color: var(--text-primary);
  font-size: var(--font-size-h2);
  padding: 12px;
  .tel {
    font-size: var(--font-size-h3);
    color: var(--color-primary);
  }
  .time {
    font-size: var(--font-size-h3);
    color: var(--text-secondary);
  }
}
.footer {
  width: 100%;
  padding: 8px 12px;
  position: fixed;
  left: 0;
  bottom: 0;
  background-color: var(--color-block-background);
}
</style>
