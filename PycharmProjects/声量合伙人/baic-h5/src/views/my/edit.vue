<template>
  <div class="info-box">
    <van-form required="auto" @submit="onSubmit">
      <van-cell-group inset>
        <van-field
          v-model="myData.name"
          label="姓名"
          :rules="[{ required: true, message: '请输入姓名' }]"
        />
        <van-field
          v-model="myData.phonenumber"
          type="tel"
          label="电话"
          disabled
          :rules="[{ required: true, message: '请输入电话' }]"
        />
        <van-field
          v-model="myData.weixinId"
          label="微信号"
          placeholder="请输入微信号"
          :rules="[{ required: true, message: '请输入微信号' }]"
        />
        <!-- <van-field @click="moer">
          <template #label>
            <div class="text-flex">更多<i-icon :icon="Fa6Right" /></div>
          </template>
        </van-field> -->
      </van-cell-group>
      <van-cell-group inset class="info-top">
        <van-field
          v-model="myData.email"
          label="邮箱"
          placeholder="请输入邮箱"
        />
        <!-- 生日选择 -->
        <van-field
          v-model="myData.birthday"
          readonly
          clickable
          label="生日"
          placeholder="点击选择生日"
          @click="showDatePicker = true"
        />
        <van-popup v-model:show="showDatePicker" round position="bottom">
          <van-date-picker
            v-model="currentDate"
            title="选择生日"
            :min-date="minDate"
            :max-date="new Date()"
            @confirm="onConfirmBirthday"
            @cancel="showDatePicker = false"
          />
        </van-popup>
        <!-- 地区选择 -->
        <van-field
          v-model="myData.address"
          readonly
          clickable
          label="地区"
          rows="1"
          autosize
          type="textarea"
          placeholder="点击选择地区"
          @click="showAreaPicker = true"
        />
        <van-popup v-model:show="showAreaPicker" round position="bottom">
          <van-area
            v-model="area"
            title="地区"
            :area-list="areaList"
            @confirm="onConfirmRegion"
            @cancel="showAreaPicker = false"
          />
        </van-popup>
        <van-field
          v-model="myData.fullAddress"
          rows="1"
          type="textarea"
          autosize
          label="详细地址"
          placeholder="请输入详细地址"
        />
        <van-field label="头像">
          <template #input>
            <van-uploader
              v-model="fileList"
              :max-count="1"
              :before-read="beforeRead"
              :after-read="afterRead"
              cross-origin
              @delete="onDelete"
            />
          </template>
        </van-field>
      </van-cell-group>

      <div class="btn-box">
        <van-button
          class="btn-right"
          plain
          type="danger"
          size="large"
          block
          @click="dropOut"
        >
          退出登录
        </van-button>
        <van-button
          class="btn-right"
          plain
          type="primary"
          size="large"
          block
          @click="back"
        >
          返回
        </van-button>
        <van-button size="large" block native-type="submit" type="primary">
          保存
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup lang="ts">
import { areaList } from "@vant/area-data";
import { saveMyUserInfo, uploadFile } from "@/api/my";
import {
  validPhone,
  validEmail,
  validCustomName,
  calculateLength
} from "@/utils/validate";
import { useAuthStore } from "@/store/modules/auth";
import { getMyUserInfo } from "@/api/my";
import { to as toPromise } from "await-to-js";

defineOptions({
  name: "MyEdit"
});
const router = useRouter();
const authStore = useAuthStore(); // 获取 authStore 实例
const fileList = ref([]);
const currentDate = ref([]);
const minDate = ref(new Date(2000, 0, 1));
const showDatePicker = ref(false);
const showAreaPicker = ref(false);
const area = ref("");
const type = ref(1);
const myData = ref({
  birthday: "",
  address: "",
  avatar: "",
  phonenumber: "",
  email: "",
  name: "",
  nickName: "",
  fullAddress: "",
  weixinId: ""
});
const { setUser } = useAuthStore();
onMounted(async () => {
  try {
    const userInfo = authStore.userInfo;

    if (userInfo) {
      myData.value = { ...userInfo }; // 将用户信息赋值给 myData
      if (myData.value.birthday) {
        currentDate.value = myData.value.birthday.split("-");
      } else {
        const today = new Date();
        myData.value.birthday = `${today.getFullYear()}-${String(
          today.getMonth() + 1
        ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;
        currentDate.value = myData.value.birthday.split("-");
      }
      if (myData.value.address) {
        area.value = myData.value.address.split("/").join(",");
      }
      if (myData.value.avatar) {
        fileList.value = [
          {
            url: myData.value.avatar,
            status: "done",
            message: "上传成功",
            isImage: true, // 指示资源为图片
            name: "avatar" // 可选，但建议提供
          }
        ];
      }
    }
  } catch (error) {
    showFailToast(error?.msg);
  }
});

const moer = () => {
  type.value = 2;
};

// 退出登录
const dropOut = () => {
  showDialog({
    message: "确认退出登录？",
    showCancelButton: true
  }).then(() => {
    useAuthStore()
      .logout()
      .then(() => {
        showToast("退出成功");
        setTimeout(() => {
          router.replace({ path: "/login" });
        });
      });
  });
};

const back = () => {
  type.value = 1;
  router.back();
};
const checkWordCount = (value: string) => {
  const length = calculateLength(value);
  if (length > 200) {
    showToast("详细地址长度应在200个字符以内");
    return false;
  } else {
    return true;
  }
};
const checkWordCount2 = (value: string) => {
  const length = calculateLength(value);
  if (length > 50) {
    showToast("微信号应在50个字符以内");
    return false;
  } else {
    return true;
  }
};
const onSubmit = async values => {
  console.log(
    "submit",
    values,
    myData.value,
    validCustomName(myData.value.name)
  );
  if (!validPhone(myData.value.phonenumber)) {
    showToast("请输入正确的手机号");
    return;
  }
  if (myData.value.email != "" && !validEmail(myData.value.email)) {
    showToast("请输入正确的邮箱");
    return;
  }
  if (!validCustomName(myData.value.name)) {
    showToast("请输入正确的姓名");
    return;
  }
  if (!checkWordCount2(myData.value.weixinId)) {
    return;
  }
  if (!checkWordCount(myData.value.fullAddress)) {
    return;
  }
  try {
    const { code } = await saveMyUserInfo({ ...myData.value });
    if (code == 200) {
      showToast("保存成功");
      const [error, userInfoResponse] = await toPromise(getMyUserInfo());
      setUser(userInfoResponse.data); // 保存用户信息
      back();
    }
  } catch (error) {
    showFailToast(error?.msg || "保存失败");
  }
};

const beforeRead = file => {
  const allowedTypes = ["image/jpeg", "image/png"];
  if (!allowedTypes.includes(file.type)) {
    showToast("请上传 jpg 或 png 格式的图片");
    return false;
  }
  return true;
};
const onConfirmBirthday = (value: any) => {
  showDatePicker.value = false;
  myData.value.birthday = value?.selectedValues.join("-");
};

const onConfirmRegion = (value: any) => {
  console.log(value, "value", area.value);
  myData.value.address = value.selectedOptions.map(item => item.text).join("");
  showAreaPicker.value = false;
};
const afterRead = (file: any) => {
  console.log(file, fileList.value, "file");
  const formData = new FormData();
  formData.append("file", file.file);
  uploadFile(formData)
    .then((res: any) => {
      console.log(res, "------------");
      if (res.code === 200) {
        myData.value.avatar = res.data.url;
        fileList.value = [
          {
            url: res.data.url,
            status: "done",
            message: "上传成功"
          }
        ];
      } else {
        showToast("上传失败，请重试");
      }
    })
    .catch(error => {
      showFailToast(error?.msg || "上传失败");
    });
};

const onDelete = (file: any) => {
  console.log("删除头像", file);
  myData.value.avatar = "";
  fileList.value = [];
};
</script>

<style lang="less" scoped>
.info-box {
  padding-top: 2rem;
}
.btn-box {
  display: flex;
  align-items: center;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  padding: 8px 12px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 8px);
}
.btn-right {
  margin-right: 12px;
}
.text-flex {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.info-top {
  margin-top: 12px;
}
:deep(.van-field__control) {
  text-align: right;
}
:deep(.van-field__control--custom) {
  justify-content: right;
}
:deep(.van-uploader) {
  border-radius: 6px;
  overflow: hidden;
  width: 80px;
  height: 80px;
}
:deep(.van-uploader__preview) {
  margin: 0;
}
:deep(.van-uploader__preview-image) {
  border-radius: 6px;
  overflow: hidden;
  width: 80px;
  height: 80px;
}
:deep(.van-cell) {
  align-items: center;
}
:deep(.van-field__error-message) {
  text-align: right;
}
</style>
