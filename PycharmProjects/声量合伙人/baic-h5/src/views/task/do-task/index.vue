<script setup lang="ts">
import {
  taskDetail,
  taskPreview,
  getTask,
  forwardLog,
  transpondVisit,
  getMemberTaskStatus,
  taskVisitLog
} from "@/api/task/index";
import { btnMapping, iconMapping } from "@/views/task/mapping";
import router from "@/router";
import OperationProcess from "@/views/task/components/operation-process.vue";
import { useWechatShare } from "@/composables/useWechatShare";
import { useWechatMenu } from "@/composables/useWechatMenu";
import { useWechatArticle } from "@/composables/useWechatArticle";
import { useLaunchWeapp } from "@/composables/useLaunchWeapp";
import { useAuthStore } from "@/store/modules/auth";
import { pxToVmin, vminToPx } from "@/utils";
import useWechat from "@/composables/useWechat";

const imgWidth = vminToPx(pxToVmin(375 - 24 - 24)); // 调用计算宽度的函数

defineOptions({
  name: "DoTask"
});

const authStore = useAuthStore();
const route = useRoute();
const noAuth = route.meta.noAuth;
const appid = import.meta.env.VITE_APP_TRANSFER_WX_APP_APPID; // 小程序的 appid
const envVersion = import.meta.env.VITE_APP_TRANSFER_WX_APP_ENVVERSION; // 小程序版本
const scriptContent = ref("");
const path = ref(""); // 用于存储接口返回的 path
const { wxTag } = useLaunchWeapp(appid, path, envVersion, scriptContent);
const showPage = ref(false);
const taskId = ref<string>(route.query.id as string); //任务id
const showHistory = ref<boolean>(false); //浏览记录
const showInstructions = ref<boolean>(false); //转发的操作指南
const showStepStatus = ref<boolean>(false); //其它的操作指南
const historyList = ref<any[]>([]); //浏览记录列表
const btnStatus = ref<boolean>(true); //底部按钮是否展示
const detailShow = ref<boolean>(false); //任务详情是否展示
const { setupWechatShare } = useWechatShare();
const { hideMenuItems, showMenuItems } = useWechatMenu();
const tabType = ref<string>(route.query.type as string); //任务入口
const memberTaskId = ref<string>(route.query.memberTaskId as string);
// 轮播图图片列表
const carouselImages = ref({
  // 朋友圈
  0: [
    `${import.meta.env.VITE_APP_OSS_URL}/assets/task/pyq_step_1.png`,
    `${import.meta.env.VITE_APP_OSS_URL}/assets/task/pyq_step_2.png`,
    `${import.meta.env.VITE_APP_OSS_URL}/assets/task/pyq_step_3.png`
  ],
  // 原创
  1: [
    `${import.meta.env.VITE_APP_OSS_URL}/assets/task/yc_step_1.png`,
    `${import.meta.env.VITE_APP_OSS_URL}/assets/task/yc_step_2.png`,
    `${import.meta.env.VITE_APP_OSS_URL}/assets/task/yc_step_3.png`
  ],
  // 视频号
  3: [
    `${import.meta.env.VITE_APP_OSS_URL}/assets/task/sph_step_1.png`,
    `${import.meta.env.VITE_APP_OSS_URL}/assets/task/sph_step_2.png`,
    `${import.meta.env.VITE_APP_OSS_URL}/assets/task/sph_step_3.png`
  ]
});
const titleImage = ref({
  // 朋友圈
  0: [`${import.meta.env.VITE_APP_OSS_URL}/assets/task/pyq_head.png`],
  // 原创
  1: [`${import.meta.env.VITE_APP_OSS_URL}/assets/task/yc_head.png`],
  // 视频号
  3: [`${import.meta.env.VITE_APP_OSS_URL}/assets/task/sph_head.png`]
});

const showPreview = ref(false);
const previewImages = ref<string[]>([]);
const startPosition = ref(0);
const isClick = ref(false); //防止多次点击

// 显示哪几个按钮
const shouldShowButton = (key: string, type: any) => {
  const status = taskBasic.value.memberTaskStatus;
  // console.log(taskBasic.value.type, taskBasic.value.memberTaskStatus);
  // 在任务不为微信时
  if (
    (tabType.value == "0" ||
      tabType.value == "1" ||
      tabType.value == "wxpub") &&
    type === 1 &&
    taskBasic.value.saleFlag != 0
  ) {
    return key === "5";
  }
  if (
    taskBasic.value.type === 0 ||
    taskBasic.value.type === 1 ||
    taskBasic.value.type === 3
  ) {
    switch (status) {
      // 判断完成状态
      case 0:
        // 展示对应的按钮
        if (taskBasic.value.saleFlag != 0) {
          return key === "5";
        } else {
          return key === "0";
        }
      case 1:
        return key === "6";
      case 2:
      case 3:
        btnStatus.value = false;
        return false;
      case 4:
        return key === "6";
      case 5:
        btnStatus.value = false;
        return false;
      default:
        return false;
    }
  }
  // 在任务是微信时
  if (taskBasic.value.type === 2) {
    switch (status) {
      // 判断完成状态
      case 0:
        return key === "0";
      case 1:
        return key === "0";
      case 2:
      case 3:
      case 4:
      case 5:
        return key === "0" || key === "1" || key === "2";
      default:
        btnStatus.value = false;
        return false;
    }
  }

  btnStatus.value = false;
  return false;
};
const handleButtonClick = (key: string) => {
  showHistory.value = false;

  if (noAuth) {
    showToast("预览状态无法操作");
    return;
  }
  if (isClick.value) {
    return;
  }
  isClick.value = true;
  switch (key) {
    case "0":
      // 活动详情
      detailShow.value = true;
      break;
    case "1":
      // 浏览记录
      getForwardLog();
      // 调接口
      break;
    case "2":
      // 再次转发
      // 呼起分享弹窗
      // setupWechatShare();
      // 呼不起来,先用操作指南替一下
      // showInstructions.value = true;
      break;
    case "3":
      // 微信操作指南
      // showInstructions.value = true;
      break;
    case "4":
      // 其它的操作指南
      // showStepStatus.value = true;
      break;
    case "5":
      //领取活动
      handleGetTask();
      break;
    case "6":
      // 提交审核
      getNextTaskDetail();
      break;
    default:
      break;
  }
};

// 任务基础信息
const taskBasic = ref<any>({});
const typeDetail = ref<any>({});
// 定义响应式变量
const { container, loading, sanitizedHtml, loadArticleContent } =
  useWechatArticle({
    usePreview: !!noAuth
  });
// 获取任务详情
const getTaskDetail = async () => {
  try {
    // 动态选择API方法
    const apiMethod = noAuth ? taskPreview : taskDetail;
    const { data } = await apiMethod({
      id: taskId.value,
      memberTaskId: memberTaskId.value
    });
    // console.log(data);
    if (data.saleFlag !== 1 && route.query.type === "wxpub") {
      showToast("任务已失效");
      // 跳转到首页
      return router.replace("/");
    }
    taskBasic.value = data;

    // switch (data.type) {
    switch (taskBasic.value.type) {
      case 0:
        // 转发朋友圈
        typeDetail.value = data.forwardCircleVo;
        // if (data.memberTaskStatus === 1) {
        //   detailShow.value = true;
        // }
        break;

      case 1:
        // 原创
        typeDetail.value = data.originalVo;
        break;

      case 2:
        typeDetail.value = data.forwardVo;

        // 转发微信
        // 获取公众号文章
        if (authStore.userInfo?.openId) {
          // 本地取不到openId,调试的时候注掉此判断,在下面接口入参里写死,发版的时候别忘了改回来
          getTaskVisitLog(); //浏览任务
        }
        if (
          !noAuth &&
          taskBasic.value.memberTaskStatus === 0 &&
          taskBasic.value.saleFlag != 0
        ) {
          handleGetTask();
        }
        if (data.forwardVo.articleType === 1) {
          await loadArticleContent(data.forwardVo.articleLink);
        }
        // 使用 useWechatShare 设置微信分享
        // console.log("title", data.name, data.forwardVo.articleTitle);
        // console.log("desc", data.forwardVo.forwardText);
        // console.log("imgUrl", data.forwardVo.ossIdsUrl);
        // if (!noAuth) {
        //   setupWechatShare(
        //     {
        //       title: data.forwardVo.articleTitle,
        //       desc: data.forwardVo.forwardText,
        //       link: `${window.location.origin}${import.meta.env.VITE_PUBLIC_PATH}share?userId=${authStore.userInfo.id}&state=${taskId.value}`,
        //       imgUrl: data.forwardVo.ossIdsUrl
        //     },
        //     getTranspondVisit
        //   );
        // }
        if (data.memberTaskStatus === 1) {
          detailShow.value = true;
        }
        break;
      case 3:
        // 视频号
        typeDetail.value = data.videoNumberVo;
        // 视频号ID:data.videoNumberVo.videoId, worksId:data.videoNumberVo.worksId
        // const imgWidth = vminToPx(pxToVmin(375 - 32 - 24)); // 调用计算宽度的函数
        // path.value = `/pages/channels/channels?finderUserName=${typeDetail.value.videoId}&feedId=${typeDetail.value.worksId}`;
        // scriptContent.value = `<style>.img { width: ${imgWidth}px; }</style>
        //     <img class="img" src="${data.videoNumberVo.ossIdsUrl}" />`;

        break;
      default:
        break;
    }
    btnStatus.value = true;
    showPage.value = true;
  } catch (error) {
    router.go(-1);
    showFailToast(error?.msg || "请求失败");
  }
};
// 关闭任务详情弹窗
const closePop = () => {
  detailShow.value = false;
  if (taskBasic.value.memberTaskStatus === 1) {
    showInstructions.value = true;
  }
};
// 做分享任务
const getTranspondVisit = async () => {
  try {
    // 转发任务
    await transpondVisit({
      userId: authStore.userInfo.id,
      type: 1,
      taskId: taskId.value
    });
    taskBasic.value.memberTaskStatus = 2;
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};

// 浏览记录
const getForwardLog = async () => {
  try {
    const { data } = await forwardLog({
      userId: authStore.userInfo.id,
      type: 7,
      taskId: taskId.value
    });
    // console.log(data);
    historyList.value = data;
    showHistory.value = true;
    document.title = "浏览记录";
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
  isClick.value = false;
};
// 浏览任务
const getTaskVisitLog = async () => {
  try {
    await taskVisitLog({
      userId: authStore.userInfo.id,
      type: 7,
      taskId: taskId.value,
      wechatOpenId: authStore.userInfo?.openId
    });
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};
// 再次调用任务详情接口
const getNextTaskDetail = async () => {
  try {
    const { data } = await taskDetail({
      id: taskId.value,
      memberTaskId: memberTaskId.value
    });
    // 只更改成功状态的值
    // taskBasic.value.memberTaskStatus = data.memberTaskStatus;
    if (data.memberTaskStatus === 1 || data.memberTaskStatus === 4) {
      router.push({
        name: "SubmitTask",
        query: {
          type: taskBasic.value.type,
          id: taskBasic.value.id,
          name: taskBasic.value.name,
          memberTaskId: memberTaskId.value
        }
      });
    }
    if (data.memberTaskStatus === 0) {
      showToast("任务领取中，请稍后重试");
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
  isClick.value = false;
};
const memberTaskStatus = async () => {
  try {
    // 查询状态
    const { data } = await getMemberTaskStatus({
      id: taskId.value
    });
    // console.log(data);
    //0：任务领取中
    // 1：任务已领取
    // 2：任务领取失败，请重新领取
    if (data === 0) {
      showToast("任务领取中");
    }
    if (data === 1) {
      showToast("任务已领取");
      taskBasic.value.memberTaskStatus = data;
    }
    if (data === 2) {
      showToast("任务领取失败，请重新领取");
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};
// 领取活动
const handleGetTask = async () => {
  try {
    await getTask({
      userId: authStore.userInfo.id,
      type: taskBasic.value.type,
      taskId: taskId.value
    })
      .then(res => {
        if (res.data) {
          showToast("任务领取成功！");
        } else {
          showToast("任务领取失败，请重新领取");
        }
      })
      .catch(() => {
        showToast("任务领取失败，请重新领取");
      });
    taskBasic.value.memberTaskStatus = 1;
    // memberTaskStatus();
  } catch (error) {
    showToast("任务领取中");
    return;
  }
  isClick.value = false;
};
// const handleGetTask = async () => {
//   try {
//     const { data } = await getTask({
//       userId: authStore.userInfo.id,
//       type: taskBasic.value.type,
//       taskId: taskId.value
//     });
//     // 这个接口是看操作是否成功不是领取是否成功,查看领取是否成功还需要再调用一遍详情接口
//     if (data) {
//       setTimeout(() => {
//         getNextTaskDetail();
//       }, 500);
//     }
//   } catch (error) {
//     showFailToast(error?.msg || "请求失败");
//   }
// };
// 一键复制转发语
const handleCopy = () => {
  const text = typeDetail.value.forwardText;
  const tempInput = document.createElement("input");
  tempInput.value = text;
  document.body.appendChild(tempInput);
  // 选中输入框中的文本
  tempInput.select();
  document.execCommand("copy"); // 复制文本
  // 移除临时输入框
  document.body.removeChild(tempInput);
  showToast("复制成功");
};
// 去除小数点
const getIntegerPart = (value: string) => {
  const parts = value.split(".");
  return parts[0];
};

// 处理图片预览
const handlePreviewImage = (url: string | string[], position?: number) => {
  previewImages.value = Array.isArray(url) ? url : [url];
  startPosition.value = position || 0;
  showPreview.value = true;
};

// 预览切换回调
const onPreviewChange = (index: number) => {
  startPosition.value = index;
};

// onDeactivated(() => {
//   showPage.value = false;
//   detailShow.value = false;
// });
// onActivated(() => {
//   btnStatus.value = true;
//   showInstructions.value = false;
//   showStepStatus.value = false;
//   getTaskDetail();
// });

onMounted(() => {
  showPage.value = false;
  detailShow.value = false;
  btnStatus.value = true;
  showInstructions.value = false;
  showStepStatus.value = false;
  document.title = "任务详情";
  getTaskDetail();
});
watch(
  () => ({
    taskId: route.query.id
  }),
  ({ taskId: newTaskId }) => {
    taskId.value = newTaskId as string;
  }
);

// 按需初始化微信SDK
const { isReady } = useWechat(
  [
    "hideMenuItems",
    "showAllNonBaseMenuItem",
    "updateAppMessageShareData",
    "updateTimelineShareData",
    "onMenuShareTimeline",
    "onMenuShareAppMessage"
  ],
  ["wx-open-launch-weapp"]
);

// 当SDK就绪后配置分享
watch([isReady, () => taskBasic.value], ([ready, newVal]) => {
  console.log("ready, newVal", ready, newVal);
  // 仅视频号任务需要初始化
  if (ready && newVal.type === 3) {
    // 视频号ID:data.videoNumberVo.videoId, worksId:data.videoNumberVo.worksId
    path.value = `/pages/channels/channels?finderUserName=${newVal.videoNumberVo.videoId}&feedId=${newVal.videoNumberVo.worksId}`;

    scriptContent.value = `<style>.img { width: ${imgWidth}px; }</style>
        <img class="img" src="${newVal.videoNumberVo.ossIdsUrl}" />`;
  }
  // 仅转发任务需要初始化
  if (ready && newVal.type === 2 && !noAuth) {
    setupWechatShare(
      {
        title: newVal.forwardVo.articleTitle,
        desc: newVal.forwardVo.forwardText,
        link: `${window.location.origin}${import.meta.env.VITE_PUBLIC_PATH}share?userId=${authStore.userInfo.id}&state=${taskId.value}`,
        imgUrl: newVal.forwardVo.ossIdsUrl
      },
      getTranspondVisit
    );
  }
  // 转发任务不需要初始化 转发任务的预览功能
  if (ready && (newVal.type !== 2 || (newVal.type === 2 && noAuth))) {
    hideMenuItems([
      "menuItem:share:appMessage",
      "menuItem:share:timeline",
      "menuItem:share:qq",
      "menuItem:share:weiboApp",
      "menuItem:favorite",
      "menuItem:share:facebook",
      "menuItem:share:QZone",
      // "menuItem:editTag",
      // "menuItem:delete",
      "menuItem:copyUrl",
      // "menuItem:originPage",
      // "menuItem:readMode",
      "menuItem:openWithQQBrowser",
      "menuItem:openWithSafari",
      "menuItem:share:email",
      "menuItem:share:brand"
    ]);
  }
  if (ready && newVal.type === 2 && !noAuth) {
    showMenuItems();
  }
});
</script>

<template>
  <div class="do-task-page">
    <div class="main">
      <div
        v-if="taskBasic.type === 2"
        :style="{
          margin:
            !showHistory && typeDetail.articleType === 1 ? '-12px -12px 0' : ''
        }"
      >
        <!-- 公众号内容 -->
        <div v-show="!showHistory && typeDetail.articleType === 1">
          <BaseLoadingIndicator
            v-if="loading"
            style="height: 100vh"
            text="文章加载中..."
          />
          <div ref="container" class="wechat-iframe" v-html="sanitizedHtml" />
        </div>
        <div v-show="!showHistory && typeDetail.articleType === 2">
          <img :src="typeDetail.articleOssIdsUrl" style="width: 100%" />
        </div>
        <!-- 浏览记录 -->
        <div v-if="showHistory">
          <Empty
            v-if="!historyList || !historyList.length"
            icon="empty"
            text="暂无数据"
            marginTop="0"
            marginBottom="12"
            background-color="var(--color-block-background)"
          />
          <div v-else>
            <div v-for="item in historyList" :key="item.id">
              <div class="history-item">
                <svg-icon class="avatar" name="avatar-default" />
                <div>
                  <div class="name">{{ item.wechatName || "无名高手" }}</div>
                  <div class="date">
                    {{ item.createTime }}进入浏览
                    <!-- score>0是有效触达 -->
                    <text v-if="item.reachStatus" class="desc">有效触达</text>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <Empty v-else marginTop="20vh" /> -->
        </div>
      </div>
      <div
        v-if="
          taskBasic.type === 0 || taskBasic.type === 1 || taskBasic.type === 3
        "
      >
        <div class="public-box flex-row items-center">
          <div class="icon-box flex items-center justify-center">
            <svg-icon :name="iconMapping[taskBasic.type]" class="icon" />
          </div>
          <div class="flex flex-col">
            <div class="tips-title">{{ taskBasic.name }}</div>
            <div class="desc flex">
              <div class="mr-[10px]">
                {{
                  taskBasic.type === 0
                    ? "转发朋友圈"
                    : taskBasic.type === 1
                      ? "原创任务"
                      : taskBasic.type === 3
                        ? "视频号互动"
                        : ""
                }}
              </div>
              <div class="w-1/2">id:{{ taskId }}</div>
            </div>
          </div>
        </div>
        <div class="public-box flex-col">
          <div class="mb16 tips-title">活动信息</div>
          <div
            v-if="typeDetail.rewards"
            class="flex flex-row items-center mb8 detail-box"
          >
            <svg-icon name="gift-2-line" class="mr4 small-icon" />
            <div class="name">活动奖励：</div>
            <div>{{ getIntegerPart(typeDetail.rewards) }} 积分</div>
          </div>
          <div class="flex flex-row items-center detail-box">
            <svg-icon name="hourglass-line" class="mr4 small-icon" />
            <div class="name">截止时间：</div>
            <div>{{ taskBasic.deadlineTime }}</div>
          </div>
        </div>
        <div class="public-box flex-col">
          <div class="mb16 tips-title">活动介绍</div>
          <div class="activity-info" v-html="typeDetail.detail" />
        </div>
        <div v-if="taskBasic.type === 0" class="public-box flex-col">
          <div class="mb16 flex flex-row">
            <div class="mr12 tips-title">海报图片</div>
            <div class="name detail-box">点击可查看大图</div>
          </div>
          <!-- 单张图片时的显示 -->
          <template v-if="!typeDetail.ossIdsUrl?.includes(',')">
            <img
              :src="typeDetail.ossIdsUrl"
              class="ossIdUrl"
              style="border-radius: var(--rounded-md)"
              @click="handlePreviewImage(typeDetail.ossIdsUrl)"
            />
          </template>
          <!-- 多张图片时的显示 -->
          <template v-else>
            <div class="image-grid">
              <img
                v-for="(url, index) in typeDetail.ossIdsUrl.split(',')"
                :key="index"
                :src="url"
                class="grid-image"
                style="border-radius: var(--rounded-md)"
                @click="
                  handlePreviewImage(typeDetail.ossIdsUrl.split(','), index)
                "
              />
            </div>
          </template>
        </div>
        <div
          v-if="typeDetail.forwardText && typeDetail.forwardText !== ''"
          class="public-box flex-col"
        >
          <div class="mb16 flex flex-row justify-between">
            <div class="mr12 tips-title">转发语</div>
            <div class="point detail-box" @click="handleCopy">
              一键复制转发语
            </div>
          </div>
          <div class="activity-info">
            {{ typeDetail.forwardText }}
          </div>
        </div>
        <div v-if="taskBasic.type === 3" class="public-box flex-col">
          <div class="mb16 flex flex-row">
            <div class="mr12 tips-title">目标内容</div>
            <div class="name detail-box">
              {{
                typeDetail.videoId
                  ? "点击下方视频跳转目标内容"
                  : "长按识别下方二维码跳转目标视频号"
              }}
            </div>
          </div>
          <div v-show="typeDetail.videoId" ref="wxTag" />
          <div v-show="!typeDetail.videoId">
            <img class="img" :src="taskBasic.videoNumberVo.ossIdsUrl" />
          </div>
        </div>

        <div
          v-if="
            taskBasic.memberTaskStatus === 2 ||
            taskBasic.memberTaskStatus === 3 ||
            taskBasic.memberTaskStatus === 4
          "
        >
          <!-- <van-button
            v-if="!btnStatus"
            type="primary"
            size="large"
            color="var(--color-primary)"
            class="var(--van-button-large-height) btn"
            @click="showStepStatus = true"
          >
            操作指南
          </van-button> -->
          <!-- 原创任务的 -->
          <div v-if="taskBasic.type === 1" class="public-box flex-col mt12">
            <div class="mb16">数据效果</div>
            <!-- 文案写死 -->
            <div class="activity-info">数据更新中，请耐心等待。</div>
          </div>

          <div
            v-if="
              taskBasic.approveStatusName ||
              (taskBasic.type === 1 && taskBasic.originalApproveStatusName)
            "
            class="public-box flex-col"
          >
            <div class="mb16">审核结果</div>
            <div class="activity-info">
              <span
                v-if="
                  taskBasic.type === 1 && taskBasic.originalApproveStatusName
                "
              >
                {{ taskBasic.originalApproveStatusName }}
              </span>
              <span v-else>{{ taskBasic.approveStatusName }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 底部按钮 -->
    <div v-if="btnStatus" class="footer flex justify-between items-center">
      <van-button
        v-for="item in btnMapping"
        v-show="shouldShowButton(item.key, taskBasic.type)"
        :key="item.key"
        type="primary"
        :plain="item.status"
        color="var(--color-primary)"
        class="var(--van-button-large-height) btn"
        @click="handleButtonClick(item.key)"
      >
        <div class="flex items-center">
          <svg-icon
            v-if="item.icon"
            :name="item.icon"
            class="mr4"
            style="width: 20px; height: 20px"
          />
          {{ item.name }}
        </div>
      </van-button>
    </div>
    <div class="van-safe-area-bottom" style="background: #fff" />
  </div>
  <!-- 微信的操作指南 -->
  <!-- <van-overlay :show="showInstructions">
    <div
      class="flex items-center justify-end"
      @click="showInstructions = false"
    >
      <img
        src="@/assets/task/popup_instructions.png"
        class="w-[344px] h-[392px]"
      />
    </div>
  </van-overlay> -->
  <van-overlay :show="showStepStatus">
    <!-- 操作指南组件 -->
    <operation-process
      v-model:show="showStepStatus"
      :title-image="titleImage[taskBasic.type]"
      :image-list="carouselImages[taskBasic.type]"
    />
  </van-overlay>
  <!-- 任务详情弹窗 -->
  <van-popup
    v-model:show="detailShow"
    round
    position="bottom"
    class="detail-popup"
    @close="closePop"
  >
    <div class="flex items-center justify-between mb30">
      <svg-icon name="icon-close" @click="closePop" />
      <div>任务详情</div>
      <!-- 一个占位的div,勿删 -->
      <div />
    </div>
    <div class="van-safe-area-bottom">
      <div class="detail-box">
        <div class="title">任务标题</div>
        <div class="mb20">{{ taskBasic.name }}</div>
        <div class="title">任务奖励</div>
        <div v-if="typeDetail.rewards">
          转发奖励：{{ getIntegerPart(typeDetail.rewards) }} 积分
        </div>
        <div v-if="typeDetail.taskScore">
          浏览奖励：{{ getIntegerPart(typeDetail.taskScore) }} 积分
        </div>
        <div v-if="typeDetail.rewardMax">
          奖励上限：{{ getIntegerPart(typeDetail.rewardMax) }} 积分
        </div>
        <div class="title mt20">截止时间</div>
        <div>{{ taskBasic.deadlineTime }}</div>
      </div>
    </div>
  </van-popup>
  <van-image-preview
    v-model:show="showPreview"
    :images="previewImages"
    :start-position="startPosition"
    :min-zoom="1"
    :max-zoom="3"
    @change="onPreviewChange"
  />
</template>
<style lang="less" scoped>
.mt12 {
  margin-top: 12px;
}
.mr4 {
  margin-right: 4px;
}
.mr12 {
  margin-right: 12px;
}
.mb8 {
  margin-bottom: 8px;
}
.mb16 {
  margin-bottom: 16px;
}
.mb12 {
  margin-bottom: 12px;
}
.mb20 {
  margin-bottom: 20px;
}
.mb30 {
  margin-bottom: 30px;
}
.mt2 {
  margin-top: 4px;
}
.mt20 {
  margin-top: 20px;
}
.fs-h2 {
  font-size: var(--font-size-h2);
}
.small-icon {
  width: 16px;
  height: 16px;
}
.do-task-page {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .main {
    padding: 12px 12px 0;
    flex-grow: 1;
    overflow-y: scroll;
    overflow-x: hidden;
    .wechat-iframe {
      background: var(--color-block-background);
      width: 100%;
      border: none;
      font-size: inherit; /* 继承父元素的字体大小 */
      line-height: inherit; /* 继承父元素的行高 */
    }
    .history-item {
      padding: 12px;
      border-radius: var(--rounded-md);
      background-color: var(--color-block-background);
      margin-bottom: 12px;
      display: flex;
      flex-direction: row;
      align-items: center;
      .avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-right: 12px;
        background-color: var(--color-background);
      }
      .name {
        font-size: var(--font-size-h2);
        color: var(--text-primary);
        font-weight: 500;
      }
      .date {
        margin-top: 6px;
        font-size: var(--font-size-caption);
        color: var(--text-describe);
        .desc {
          color: var(--color-primary);
        }
      }
    }
    .public-box {
      padding: 12px;
      display: flex;
      font-size: var(--font-size-h2);
      color: var(--text-primary);
      background-color: var(--color-block-background);
      border-radius: var(--rounded-md);
      margin-bottom: 12px;
      line-height: 22px;
      .tips-title {
        font-weight: 500;
      }
      .icon-box {
        flex: none;
        border-radius: var(--rounded-md);
        width: 50px;
        height: 50px;
        background-color: var(--color-background);
        margin-right: 12px;
        .icon {
          width: 32px;
          height: 32px;
          color: var(--color-primary);
        }
      }
      .one-line {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1; /* 限制为两行 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
        word-break: break-word; /* 防止长单词撑开容器 */
      }
      .desc {
        margin-top: 6px;
        font-size: var(--font-size-caption);
        color: var(--text-describe);
      }
      .activity-info {
        word-break: break-word; /* 防止长单词撑开容器 */
        color: var(--text-secondary);
        font-size: var(--font-size-h3);
        :deep(ul),
        :deep(ol) {
          padding-left: 20px;
        }

        :deep(ol) {
          list-style-type: decimal;
        }

        :deep(ul) {
          list-style-type: disc;
        }

        :deep(li) {
          margin-bottom: 8px;
        }
      }
    }
    .detail-box {
      font-size: var(--font-size-h3);
    }

    .name {
      color: var(--text-describe);
    }
    .point {
      color: var(--color-primary);
    }
  }
  .footer {
    padding: 8px 10px;
    height: 64px;
    width: 100vw;
    background-color: var(--color-block-background);
    .btn {
      padding: 0;
      margin: 0 6px;
      flex: 1;
      height: var(--van-button-large-height);
      font-size: 16px;
    }
  }
}
.ossIdUrl {
  width: 110px;
  height: 110px;
}
.detail-popup {
  padding: 18px 12px;
  font-size: var(--font-size-h2);
  line-height: 22px;
  color: var(--text-primary);
  .detail-box {
    border-radius: var(--rounded-md);
    border: 0.5px solid #d9d9d9;
    padding: 20px;
    background-color: #f8f8f8;
    font-size: var(--font-size-h3);
    max-height: 300px;
    overflow-y: auto;
    .title {
      color: var(--text-describe);
      margin-bottom: 4px;
      // margin-top: 20px;
    }
  }
}

.image-preview-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80%;
  height: 80%;
  background-color: transparent;
  z-index: 2000;
}

.preview-image {
  width: auto;
  height: 80%;
  object-fit: contain;
  border-radius: var(--rounded-md);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.preview-close-icon {
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  font-size: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 8px;
  cursor: pointer;
  z-index: 2001;
}
.task-type {
  display: inline-block;
  font-size: 12px;
  color: #fff;
  background-color: var(--primary-color);
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}
.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  width: 100%;

  .grid-image {
    width: 100%;
    aspect-ratio: 1;
    object-fit: cover;
  }
}
:deep(.van-image-preview) {
  .van-image-preview__index {
    color: #fff;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 4px 12px;
    border-radius: 12px;
  }

  .van-image-preview__loading {
    color: #fff;
    font-size: 14px;
  }

  .van-image-preview__close-icon {
    color: #fff;
    font-size: 24px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 8px;
    border-radius: 50%;
  }
}
.img {
  width: 327px;
}
</style>
