<script setup lang="ts">
import TabDetail from "./components/task-detail.vue";
// import { getMyUserInfo } from "@/api/my";
import { useWechatShare } from "@/composables/useWechatShare";
defineOptions({
  name: "Task"
});

const { setupWechatShare } = useWechatShare();
const active = ref<number>(0); //tab-key
const iconStatus = ref<boolean>(false); //按钮状态
const tabList = [
  { title: "任务广场", key: 0 },
  { title: "待完成", key: 1 },
  { title: "进行中", key: 2 },
  { title: "已完成", key: 3 }
];
const tagNumber = ref<number | null>(null); //tag-key
const tagList = ref([
  { title: "已失效", key: 0 },
  { title: "生效", key: 1 }
]);

// 弹窗里的tag
const handleTagSelect = (index: number) => {
  tagNumber.value = index;
};
// const getUserInfo = () => {
//   getMyUserInfo({})
//     .then((res: any) => {
//       console.log(res);
//       if (res.code == 200) {
//         useAuthStore().setUser(res.data);
//       }
//     })
//     .catch(error => {
//       showFailToast(error?.msg || "请求失败");
//     });
// };
onActivated(() => {
  setupWechatShare({
    title: document.title,
    desc: "",
    link: `${window.location.origin}${import.meta.env.VITE_PUBLIC_PATH}share`,
    imgUrl: ""
  });
});
onMounted(() => {
  // getUserInfo();
});
</script>

<template>
  <div class="custom-tabs">
    <!-- tab栏 -->
    <div class="tabs">
      <van-sticky>
        <van-tabs
          v-model:active="active"
          sticky
          class="flex z-10"
          @click="tagNumber = null"
        >
          <van-tab
            v-for="item in tabList"
            :key="item.key"
            :title="item.title"
          />
          <div v-if="active === 0" class="icon-box">
            <svg-icon
              :name="iconStatus ? 'filter-line-blue' : 'filter-line'"
              class="icon"
              @click="
                iconStatus = true;
                tagNumber = null;
              "
            />
          </div>
        </van-tabs>
      </van-sticky>
      <TabDetail :tabType="active" :tagNumber="tagNumber" />
      <!-- 点筛选出的弹窗 -->
      <van-popup
        v-model:show="iconStatus"
        position="top"
        class="popup"
        :z-index="2"
      >
        <div class="tag-list">
          <div v-for="(item, index) in tagList" :key="item.key">
            <div
              class="tag-item"
              :class="{ special: tagNumber === index }"
              @click="handleTagSelect(index)"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
      </van-popup>
    </div>
  </div>
</template>
<style lang="less" scoped>
.custom-tabs {
  display: flex;
  font-size: var(--font-size-h3);
  color: var(--text-primary);
  .tabs {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    line-height: 22px;
    :deep(.van-tab--active) {
      font-weight: 500;
      // font-size: var(--font-size-h2);
    }
    // :deep(.van-tab) {
    //   font-size: var(--font-size-h2);
    // }
    :deep(.van-tab__text.van-tab__text--ellipsis) {
      font-size: var(--font-size-h2);
    }
    :deep(.van-tabs__wrap) {
      width: 90vw;

      height: 42px;
    }
    .icon-box {
      width: 10vw;
      height: 42px;
      background-color: var(--color-block-background);
      display: flex;
      justify-content: center;
      align-items: center;
      .icon {
        width: var(--font-size-h1);
        height: var(--font-size-h1);
      }
    }
  }

  .popup {
    padding: 16px 12px;
    top: 40px;
    border-bottom-left-radius: var(--rounded-xl);
    border-bottom-right-radius: var(--rounded-xl);
    .tag-list {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;

      .tag-item {
        padding: 8px 12px;
        background-color: #f8f8f8;
        border-radius: var(--rounded-md);
        margin: 0 10px 10px 0;
        border: 1px solid #f8f8f8;
        line-height: normal;
      }
      .special {
        background-color: #f0f7ff;
        border: 1px solid var(--color-primary);
        color: var(--color-primary);
      }
    }
  }
}
/deep/ .van-tabs {
  background-color: #fff;
}
</style>
