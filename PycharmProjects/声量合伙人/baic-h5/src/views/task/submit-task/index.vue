<script setup lang="ts">
import { iconMapping } from "@/views/task/mapping";
import { uploadFile } from "@/api/my";
import { submitTask } from "@/api/task/index";
import {
  reportBusinessLogAsync,
  reportStartLogAsync,
  reportSuccessLogAsync,
  reportErrorLogAsync
} from "@/utils/reportLog";
import router from "@/router";
import { useAuthStore } from "@/store/modules/auth";
const authStore = useAuthStore();
const route = useRoute();
const type = ref<string>(route.query.type as string);
const taskId = ref<string>(route.query.id as string);
const name = ref<string>(route.query.name as string);
const information = ref<string>("");
const fileList = ref<any[]>([]);
const loading = ref(false);
const isHarmonyNext = ref(/OpenHarmony/i.test(navigator.userAgent));
const memberTaskId = ref<string>(route.query.memberTaskId as string);

defineOptions({
  name: "SubmitTask"
});
// 提交任务
const handleSumbit = async () => {
  try {
    await reportBusinessLogAsync(
      "提交任务",
      "submitTask",
      "/portal/task/basic/submitTask",
      async () => {
        return await submitTask({
          userId: authStore.userInfo.id,
          ossIds: ossIds.value,
          type: type.value,
          taskId: taskId.value,
          information: information.value,
          memberTaskId: memberTaskId.value
        });
      },
      JSON.stringify({
        userId: authStore.userInfo.id,
        taskId: taskId.value,
        type: type.value,
        ossIdsCount: ossIds.value.length
      })
    );
    showToast("提交成功");
    router.go(-1);
  } catch (error) {
    ossIds.value = [];
    showFailToast(error?.msg || "请求失败");
  }
};
// 上传前校验
const beforeRead = file => {
  // 判断是不是多选传图
  const status = Array.isArray(file);
  const allowedTypes = ["image/jpeg", "image/png"];

  // 添加数量校验
  if (status) {
    const totalFiles = fileList.value.length + file.length;
    if (totalFiles > 15) {
      showToast("最多只能上传15张图片");
      return false;
    }
    // 校验文件类型
    for (const item of file) {
      if (!allowedTypes.includes(item.type)) {
        showToast("请上传 jpg 或 png 格式的图片");
        return false;
      }
    }
  } else {
    if (fileList.value.length >= 15) {
      showToast("最多只能上传15张图片");
      return false;
    }
    if (!allowedTypes.includes(file.type)) {
      showToast("请上传 jpg 或 png 格式的图片");
      return false;
    }
  }
  return true;
};

const ossIds = ref<string[]>([]);
const placeholder = ref(
  type.value !== "1"
    ? "请在此粘贴审核资料"
    : "请在此粘贴审核资料，如发布成功后的链接等"
);
// 提交前先上传图片获取ossIds
const afterRead = async () => {
  if (fileList.value.length <= 0 && information.value === "") {
    return showFailToast("请填写审核资料或上传相关截图");
  }
  const uploadPromises = fileList.value.map((item: any) => {
    const formData = new FormData();
    formData.append("file", item.file);

    const uploadWithRetry = async (retries = 3) => {
      const file = formData.get("file") as File;
      const operParam = JSON.stringify({
        name: file.name,
        size: file.size,
        type: file.type
      });

      try {
        // 记录开始上传日志
        reportStartLogAsync(
          "上传图片",
          "uploadFile",
          "/portal/resource/oss/upload",
          operParam
        );

        const startTime = Date.now();
        const res = await uploadFile(formData);
        const costTime = Date.now() - startTime;

        // 记录上传成功日志
        reportSuccessLogAsync(
          "上传图片",
          "uploadFile",
          "/portal/resource/oss/upload",
          operParam,
          JSON.stringify({ ossId: res.data.ossId }),
          costTime
        );

        return res.data.ossId;
      } catch (error: any) {
        if (error?.code === "ERR_NETWORK" && retries > 0) {
          // 记录重试日志
          reportErrorLogAsync(
            `上传图片失败-准备第${4 - retries}次重试`,
            "uploadFile",
            "/portal/resource/oss/upload",
            error,
            JSON.stringify({
              ...JSON.parse(operParam),
              remainingRetries: retries
            })
          );
          await new Promise(resolve => setTimeout(resolve, 1000));
          return uploadWithRetry(retries - 1);
        }

        // 记录最终失败日志
        reportErrorLogAsync(
          "上传图片失败-最终失败",
          "uploadFile",
          "/portal/resource/oss/upload",
          error,
          JSON.stringify({
            ...JSON.parse(operParam),
            totalRetries: 3 - retries
          })
        );

        console.error("上传失败", error);
        fileList.value = [];
        ossIds.value = [];
        if (error?.code === "ERR_NETWORK") {
          showFailToast("网络波动，请稍后重试");
        } else {
          showFailToast("网络异常，请稍后再试");
        }
        throw error;
      }
    };

    return uploadWithRetry();
  });
  try {
    loading.value = true;
    const ids: string[] = await Promise.all(uploadPromises);
    ossIds.value = ids;
    await handleSumbit();
  } catch (error) {
    console.error("error:", error);
  } finally {
    loading.value = false;
  }
  return;
};
onDeactivated(() => {
  fileList.value = [];
  ossIds.value = [];
  information.value = "";
});
watch(
  () => ({
    type: route.query.type,
    taskId: route.query.id,
    name: route.query.name
  }),
  ({ type: newType, taskId: newTaskId, name: newName }) => {
    type.value = newType as string;
    taskId.value = newTaskId as string;
    name.value = newName as string;
  }
);
</script>

<template>
  <div class="submit-task-page">
    <div class="main">
      <div class="public-box flex-row items-center">
        <div class="icon-box flex items-center justify-center">
          <svg-icon :name="iconMapping[type]" class="icon" />
        </div>
        <div class="flex flex-col">
          <div class="one-line">{{ name }}</div>
          <div class="desc">id:{{ taskId }}</div>
        </div>
      </div>
      <div v-if="type === '1'" class="public-box flex-col">
        <div class="mb16">审核资料</div>
        <van-field
          v-model="information"
          class="audit-input"
          label=""
          :autosize="{ maxHeight: 100 }"
          type="textarea"
          maxlength="1000"
          show-word-limit
          placeholder="请在此粘贴审核资料，如发布成功后的链接等"
        />
      </div>
      <div class="public-box flex-col">
        <div class="mb16">
          <text class="mr12">上传相关截图</text>
          <text class="name detail-box"
            >最多上传15张<text v-if="isHarmonyNext"
              >; 鸿蒙系统每次只能上传一张图片</text
            ></text
          >
        </div>
        <van-uploader
          v-model="fileList"
          multiple
          :max-count="15"
          :before-read="beforeRead"
        />
      </div>
    </div>
    <!-- 底部按钮 -->
    <van-notice-bar
      v-if="type === '1'"
      wrapable
      :scrollable="false"
      text="如您原创作品是视频号，只需上传作品二维码，点击提交按钮即可。"
    />
    <div class="footer flex justify-between items-center">
      <van-button
        type="primary"
        color="var(--color-primary)"
        class="var(--van-button-large-height) btn"
        :loading="loading"
        loading-text="提 交 中..."
        @click="afterRead"
      >
        提 交
      </van-button>
    </div>
    <div class="van-safe-area-bottom" style="background: #fff" />
  </div>
</template>
<style lang="less" scoped>
.mr12 {
  margin-right: 12px;
}
.mb16 {
  margin-bottom: 16px;
}
.submit-task-page {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .main {
    padding: 16px 16px 0;
    flex-grow: 1;
    overflow-y: scroll;

    .public-box {
      padding: 12px;
      display: flex;
      font-size: var(--font-size-h2);
      color: var(--text-primary);
      background-color: var(--color-block-background);
      border-radius: var(--rounded-md);
      margin-bottom: 12px;
      line-height: 22px;
      .icon-box {
        border-radius: var(--rounded-md);
        width: 50px;
        height: 50px;
        background-color: var(--color-background);
        margin-right: 12px;
        .icon {
          width: 32px;
          height: 32px;
          color: var(--color-primary);
        }
      }
      .one-line {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1; /* 限制为两行 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
        word-break: break-word; /* 防止长单词撑开容器 */
      }
      .desc {
        margin-top: 6px;
        font-size: var(--font-size-caption);
        color: var(--text-describe);
      }
      .audit-input {
        box-sizing: border-box;
        border: 1px solid #f0f0f0;
        border-radius: var(--rounded-md);
        padding: 12px;
      }
    }
    .detail-box {
      font-size: var(--font-size-h3);
    }
    .name {
      color: var(--text-describe);
    }
    .point {
      color: var(--color-primary);
    }
  }
  .footer {
    padding: 8px 10px;
    height: 64px;
    width: 100vw;
    background-color: var(--color-block-background);
    .btn {
      margin: 0 6px;
      flex: 1;
    }
  }
}
</style>
