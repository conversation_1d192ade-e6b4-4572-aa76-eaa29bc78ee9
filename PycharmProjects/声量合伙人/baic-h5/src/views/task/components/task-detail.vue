<template>
  <div ref="scrollContainer" class="task-page" @scroll="handleScroll">
    <!-- 数据加载中 -->
    <van-overlay :show="loading" class-name="loading-overlay" teleport="body">
      <BaseLoadingIndicator
        class="loading-container"
        background="transparent"
        text="任务加载中..."
      />
    </van-overlay>
    <!-- <BaseLoadingIndicator
      v-if="loading"
      class="loading-container"
      text="任务加载中..."
    /> -->
    <!-- 数据加载完成 -->
    <div v-if="!loading">
      <!-- 有数据时显示任务列表 -->
      <div v-if="taskList.length > 0">
        <div
          v-for="item in taskList"
          :key="item.id"
          class="task-box w-[100%]"
          @click="handleTaskBtnClick(item)"
        >
          <div class="detail-item flex w-[100%]">
            <div class="flex flex-col w-[100%]">
              <!-- 任务名称和标签 -->
              <div class="flex items-center mb-12">
                <div class="icon-box flex items-center justify-center">
                  <svg-icon :name="iconMapping[item.type]" class="icon" />
                </div>
                <div class="flex flex-col flex-1 text-left">
                  <div class="one-line">{{ item.name }}</div>
                  <div class="flex">
                    <div class="tag">{{ tagMapping[item.type] }}</div>
                    <div v-if="item.isExamine === 1" class="tag-1">
                      {{ item.isExamine === 1 ? "考核任务" : "" }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 发布媒体 -->
              <div class="flex flex-row items-center mb-4">
                <svg-icon name="send-plane-2-line" class="mr-4 small-icon" />
                <div>发布媒体：</div>
                <div>{{ mediaMapping[item.type] }}</div>
              </div>

              <!-- 转发奖励 -->
              <div
                v-if="item.taskReward"
                class="flex flex-row items-center mb-4"
              >
                <svg-icon
                  v-if="item.type !== 1"
                  name="gift-2-line"
                  class="mr-4 small-icon"
                />
                <div v-if="item.type !== 1">
                  <text v-if="item.type !== 3">转发奖励：</text>
                  <text v-else>互动奖励：</text>
                  <text class="point"
                    >{{ getIntegerPart(item.taskReward) }}积分</text
                  >
                </div>
              </div>

              <!-- 浏览奖励 -->
              <div
                v-if="item.taskScore"
                class="flex flex-row items-center mb-4"
              >
                <svg-icon name="gift-2-line" class="mr-4 small-icon" />
                <div>浏览奖励：</div>
                <div class="point">
                  {{ getIntegerPart(item.taskScore) }}积分
                </div>
              </div>

              <!-- 转发微信奖励上限 -->
              <!-- <div
                v-if="item.type === 2 && !item.rewardMax"
                class="flex flex-row items-center mb-4"
              >
                <svg-icon name="gift-2-line" class="mr-4" />
                <div>转发微信奖励上限：</div>
                <div class="point">不限制</div>
              </div> -->
            </div>

            <!-- 分割线 -->
            <van-divider class="w-[100%]" />

            <!-- 任务底部信息 -->
            <div class="footer w-[100%]">
              <div class="date">
                {{ item.createTime }}至{{ item.deadlineTime }}
              </div>
              <!--item.saleFlag === 0和item.memberTaskStatus == 5是已失效，item.memberTaskStatus：0是没有领取，item.memberTaskStatus == 3和approveStatus：0是审核中-->
              <van-button
                size="normal"
                type="primary"
                class="btn"
                :class="getButtonClass(item)"
              >
                {{ getButtonText(item) }}
              </van-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据为空时显示提示 -->
      <Empty
        v-else
        marginTop="12"
        marginBottom=""
        background-color="var(--color-block-background)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { taskSquare, myTaskList } from "@/api/task/index";
import {
  iconMapping,
  tagMapping,
  mediaMapping,
  memberTaskStatusMapping,
  btnStatusMapping
} from "@/views/task/mapping";
import { useAuthStore } from "@/store/modules/auth";
import debounce from "lodash.debounce";
import {
  reportSuccessLogAsync,
  reportErrorLogAsync,
  reportStartLogAsync
} from "@/utils/reportLog";

// 定义任务项的类型
interface TaskItem {
  id: number;
  type: number;
  name: string;
  taskReward?: string;
  taskScore?: string;
  rewardMax?: string;
  createTime: string;
  deadlineTime: string;
  memberTaskStatus: number;
  saleFlag: number;
  isExamine: number;
  approveStatus: number;
}

// 获取路由实例
const router = useRouter();

// 获取用户信息
const authStore = useAuthStore();

// Props 定义
const props = defineProps<{
  tabType: number; // 当前 tab 类型
  tagNumber: number; // 当前标签编号
}>();

// Refs
const tabType = ref(props.tabType); // 当前 tab 类型
const tagNumber = ref(props.tagNumber); // 当前标签编号
const taskStatusList = ref<number[]>([]); // 任务状态列表
const taskList = ref<TaskItem[]>([]); // 任务列表
const loading = ref(true); // 加载状态
const page = ref({
  pageSize: 10,
  pageNum: 1
}); // 分页
const isEffective = ref(null); // 1:生效0：失效
const scrollContainer = ref<HTMLElement | null>(null);
const isLoading = ref(false);
const total = ref(0);
const isFirstLoad = ref(true); // 标记是否首次加载
const menu = ref(2);
/**
 * 获取任务广场数据
 */
// 修改getTaskSquare方法，确保每次请求都有唯一参数
// 优化getTaskSquare方法的条件判断
const getTaskSquare = async () => {
  // 检查是否已加载全部数据
  const hasMoreData =
    total.value === 0 || total.value > page.value.pageNum * page.value.pageSize;
  if (!hasMoreData) {
    loading.value = false;
    closeToast();
    showFailToast("没有更多了");
    isLoading.value = false;
    return;
  }

  let param = {};
  if (tabType.value === 1) {
    param = {
      pageSize: page.value.pageSize,
      pageNum: page.value.pageNum,
      menu: 1,
      _t: Date.now() // 显式添加时间戳，确保iOS不使用缓存
    };
  } else {
    param = {
      pageSize: page.value.pageSize,
      pageNum: page.value.pageNum,
      menu: 0,
      isEffective: tagNumber.value,
      _t: Date.now() // 显式添加时间戳，确保iOS不使用缓存
    };
  }

  // 记录请求开始日志
  reportStartLogAsync(
    "获取任务广场数据-开始",
    "getTaskSquare",
    "/portal/task/basic/list",
    JSON.stringify(param)
  );

  const startTime = Date.now(); // 记录开始时间
  try {
    const scrollTop = scrollContainer.value?.scrollTop || 0;
    const { data } = await taskSquare({ ...param });

    // 计算请求耗时
    const costTime = Date.now() - startTime;

    // 上传成功日志
    reportSuccessLogAsync(
      "获取任务广场数据-成功",
      "getTaskSquare",
      "/portal/task/basic/list",
      JSON.stringify(param),
      JSON.stringify(data),
      costTime
    );

    total.value = data.total;
    if (data.rows.length > 0) {
      if (page.value.pageNum === 1) {
        taskList.value = data.rows;
      } else {
        taskList.value = taskList.value.concat(data.rows);
      }
      page.value.pageNum++;
      restoreScrollPosition(scrollTop);
    }
    console.log(taskList.value, "taskList.value11");
  } catch (error: any) {
    // 计算请求耗时
    const costTime = Date.now() - startTime;

    // 上传错误日志
    reportErrorLogAsync(
      "获取任务广场数据-失败",
      "getTaskSquare",
      "/portal/task/basic/list",
      JSON.stringify(param),
      JSON.stringify({
        error: error?.message || error?.msg || "未知错误",
        code: error?.code || error?.status,
        response: error?.response?.data || error
      }),
      costTime
    );

    // 只在首次加载时清空列表，分页加载失败时保留已有数据
    if (page.value.pageNum === 1) {
      taskList.value = [];
    }
    showFailToast(error?.msg || "请求失败");
  } finally {
    loading.value = false; // 无论成功或失败，都关闭加载状态
    closeToast();
    isLoading.value = false;
  }
};

/**
 * 获取我的任务列表
 */
const getMyTaskList = async () => {
  if (
    total.value > (page.value.pageNum - 1) * page.value.pageSize ||
    total.value == 0
  ) {
  } else {
    loading.value = false;
    closeToast();
    showFailToast("没有更多了");
    isLoading.value = false;
    return;
  }

  const requestParam = {
    userId: authStore.userInfo.id,
    menu: menu.value,
    isEffective: isEffective.value,
    pageSize: page.value.pageSize,
    pageNum: page.value.pageNum
  };

  // 记录请求开始日志
  reportStartLogAsync(
    "获取我的任务列表-开始",
    "getMyTaskList",
    "/portal/task/basic/myTaskList",
    JSON.stringify(requestParam)
  );

  const startTime = Date.now(); // 记录开始时间
  try {
    const scrollTop = scrollContainer.value?.scrollTop || 0;
    const { data } = await myTaskList(requestParam);

    // 计算请求耗时
    const costTime = Date.now() - startTime;

    // 上传成功日志
    reportSuccessLogAsync(
      "获取我的任务列表-成功",
      "getMyTaskList",
      "/portal/task/basic/myTaskList",
      JSON.stringify(requestParam),
      JSON.stringify(data),
      costTime
    );

    total.value = data.total;
    if (data.rows.length > 0) {
      if (page.value.pageNum === 1) {
        taskList.value = data.rows;
      } else {
        taskList.value = taskList.value.concat(data.rows);
      }
      page.value.pageNum++;
      restoreScrollPosition(scrollTop);
    }
    console.log(taskList.value, "taskList.value");
  } catch (error: any) {
    // 计算请求耗时
    const costTime = Date.now() - startTime;

    // 上传错误日志
    reportErrorLogAsync(
      "获取我的任务列表-失败",
      "getMyTaskList",
      "/portal/task/basic/myTaskList",
      JSON.stringify(requestParam),
      JSON.stringify({
        error: error?.message || error?.msg || "未知错误",
        code: error?.code || error?.status,
        response: error?.response?.data || error
      }),
      costTime
    );

    taskList.value = [];
    showFailToast(error?.msg || "请求失败");
  } finally {
    loading.value = false; // 无论成功或失败，都关闭加载状态
    closeToast();
    isLoading.value = false;
  }
};

/**
 * 处理任务按钮点击事件
 * @param id - 任务 ID
 */
const handleTaskBtnClick = (item: any) => {
  console.log("handleTaskBtnClick", item);

  router.push({
    name: "DoTask",
    query: { id: item.id, type: tabType.value, memberTaskId: item.memberTaskId }
  });
};

/**
 * 获取页面数据
 */
const getPageData = debounce((type: number) => {
  if (type !== 1) {
    loading.value = true; // 开始加载
  } else {
    showLoadingToast({
      message: "加载中...",
      duration: 0,
      forbidClick: true
    });
  }
  if (tabType.value === 0 || tabType.value === 1) {
    getTaskSquare(); // 获取任务广场数据
  } else {
    menu.value = tabType.value === 2 ? 2 : 3;
    getMyTaskList(); // 获取我的任务列表
  }
}, 500);

/**
 * 去除小数点，返回整数部分
 * @param value - 带小数的字符串
 * @returns 整数部分
 */
const getIntegerPart = (value: string) => {
  const parts = value.split(".");
  return parts[0];
};

// 初始化加载数据
onMounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener("scroll", handleScroll);
  }
});

onBeforeUnmount(() => {
  // 移除事件监听器
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener("scroll", handleScroll);
  }
});
const resetAndLoadData = () => {
  total.value = 0;
  tabType.value = props.tabType;
  tagNumber.value = props.tagNumber;
  page.value.pageNum = 1;
  taskList.value = [];
  loading.value = true;
  getPageData(0);
  scrollContainer.value?.scrollTo({ top: 0 });
  isLoading.value = true;
};
// 创建防抖函数
const debouncedResetAndLoadData = debounce(() => {
  resetAndLoadData();
}, 300);

// 监听 tabType 和 tagNumber 的变化
watch(
  () => ({
    tabType: props.tabType,
    tagNumber: props.tagNumber
  }),
  () => {
    if (isFirstLoad.value) {
      // 首次加载立即执行
      resetAndLoadData();
      isFirstLoad.value = false;
    } else {
      // 后续变化使用防抖
      debouncedResetAndLoadData();
    }
  },
  { immediate: true }
);

// 处理滚动事件
const handleScroll = (event: Event) => {
  if (!scrollContainer.value) return;
  const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value;
  const distanceToBottom = scrollHeight - (scrollTop + clientHeight);
  // ✅ 距离底部50px时触发加载
  if (distanceToBottom < 10) {
    if (!isLoading.value) {
      isLoading.value = true;
      getPageData(1);
    }
  }
};

/**
 * 获取按钮样式类名
 * @param item - 任务项
 * @returns 按钮样式类名
 */
const getButtonClass = (item: TaskItem) => {
  // 已失效状态
  if (
    item.memberTaskStatus == 5 ||
    (!item.memberTaskStatus && item.saleFlag === 0)
  ) {
    return "grey-btn";
  }

  // 已完成状态
  if (
    item.memberTaskStatus == 2 ||
    (item.memberTaskStatus == 3 && item.approveStatus !== 0)
  ) {
    return "green-btn";
  }

  // 审核中状态
  if (item.memberTaskStatus == 3 && item.approveStatus === 0) {
    return "yellow-btn";
  }

  return "";
};

/**
 * 获取按钮文本
 * @param item - 任务项
 * @returns 按钮显示文本
 */
const getButtonText = (item: TaskItem) => {
  // 已失效状态
  if (
    item.memberTaskStatus == 5 ||
    (!item.memberTaskStatus && item.saleFlag === 0)
  ) {
    return "已失效";
  }

  // 审核中状态
  if (item.memberTaskStatus == 3 && item.approveStatus === 0) {
    return "审核中";
  }

  // 其他状态使用映射表
  return memberTaskStatusMapping[item.memberTaskStatus];
};
const restoreScrollPosition = (scrollTop: number) => {
  if (!scrollContainer.value) return;
  requestAnimationFrame(() => {
    if (scrollContainer.value) {
      scrollContainer.value.scrollTop = scrollTop;
    }
  });
};
</script>

<style lang="less" scoped>
.mb-4 {
  margin-bottom: 4px;
}
.mb-12 {
  margin-bottom: 12px;
}
.mr-4 {
  margin-right: 4px;
}

.task-page {
  padding-left: 0;
  padding: 2px 12px;
  color: var(--text-describe);
  overflow-y: auto;
  height: calc(100vh - 100px);
  scroll-behavior: auto;
  .task-box {
    border-radius: var(--rounded-md);
    padding: 12px;
    background-color: var(--color-block-background);
    margin: 12px 0;
    .detail-item {
      display: flex;
      flex-direction: column;
      .icon-box {
        border-radius: var(--rounded-md);
        width: 50px;
        height: 50px;
        background-color: var(--color-background);
        margin-right: 12px;
        .icon {
          width: 32px;
          height: 32px;
          color: var(--color-primary);
        }
      }
      .one-line {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        word-break: break-word;
        font-size: var(--font-size-h2);
        color: var(--text-primary);
        line-height: normal;
        margin-bottom: 6px;
        font-weight: 500;
      }
      .small-icon {
        width: 16px;
        height: 16px;
      }
      .tag {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        height: 20px;
        padding: 0 4px;
        background-color: #f0f7ff;
        border: 1px solid #b1cdf2;
        color: var(--color-primary);
        border-radius: 3px;
        font-size: var(--font-size-caption);
        box-sizing: border-box;
      }
      .point {
        color: var(--color-primary);
      }
      :deep(.van-divider) {
        margin: 12px;
        border-color: var(--color-divider);
      }
      .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .date {
          font-size: var(--font-size-caption);
        }
        .btn {
          height: var(--van-button-default-height);
        }
      }
    }
  }
}
.loading-container {
  height: calc(100vh - 42px - 56px - 4px); /* 占据整个视口高度 */
}
</style>

<style>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 99;
}
.grey-btn {
  background-color: #cecece;
  color: #6c6c6c;
  border-color: #cecece;
}
.green-btn {
  background-color: #dcebc5;
  color: #567722;
  border-color: #dcebc5;
}
.yellow-btn {
  background-color: #fdf7f0;
  color: #e99d42;
  border-color: #fdf7f0;
}
.tag-1 {
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  height: 5.3333vmin;
  padding: 0 1.0667vmin;
  background-color: #fdf7f0;
  border: 1px solid #eba856;
  color: #e99d42;
  border-radius: 0.8vmin;
  font-size: var(--font-size-caption);
  box-sizing: border-box;
  margin-left: 4px;
}
</style>
