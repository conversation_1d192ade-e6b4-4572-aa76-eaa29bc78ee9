<script setup lang="ts">
defineOptions({
  name: "OperationProcess"
});

interface Props {
  titleImage?: string;
  imageList: string[];
  show: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  titleImage: "",
  imageList: () => [],
  show: false
});

const emit = defineEmits(["update:show"]);

// 轮播图当前索引
const currentIndex = ref(0);

// 处理轮播图切换
const handleSwipeChange = (index: number) => {
  currentIndex.value = index;
};

const swipeRef = ref();

// 上一张图片
const handlePrev = () => {
  swipeRef.value?.prev();
};

// 下一张图片
const handleNext = () => {
  swipeRef.value?.next();
};

// 关闭弹窗
const handleClose = () => {
  swipeRef.value?.swipeTo(0);
  emit("update:show", false);
};
</script>

<template>
  <van-overlay :show="show">
    <div class="operation-guide">
      <div v-if="titleImage" class="guide-header">
        <img :src="titleImage" alt="指南标题" />
      </div>
      <div class="guide-carousel">
        <div class="carousel-container">
          <!-- 轮播图 -->
          <van-swipe
            ref="swipeRef"
            v-model:current-index="currentIndex"
            :show-indicators="true"
            :loop="true"
            :touchable="true"
            :stop-propagation="false"
            :duration="500"
            :initial-swipe="0"
            :height="450"
            indicator-color="var(--color-primary)"
            @change="handleSwipeChange"
          >
            <van-swipe-item v-for="(image, index) in imageList" :key="index">
              <img :src="image" :alt="`指南步骤${index + 1}`" />
            </van-swipe-item>
          </van-swipe>
          <!-- 左右切换按钮 -->
          <div
            v-show="currentIndex > 0"
            class="carousel-btn carousel-btn-left"
            @click="handlePrev"
          >
            <img src="@/assets/task/bt_left.png" alt="上一步" />
          </div>
          <div
            v-show="currentIndex < imageList.length - 1"
            class="carousel-btn carousel-btn-right"
            @click="handleNext"
          >
            <img src="@/assets/task/bt_right.png" alt="下一步" />
          </div>
        </div>
      </div>
      <van-icon name="cross" class="close-icon" @click="handleClose" />
    </div>
  </van-overlay>
</template>

<style lang="less" scoped>
.operation-guide {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  position: relative;

  .guide-header {
    position: relative;
    img {
      width: 370px;
      height: auto;
    }
  }

  .guide-carousel {
    width: 450px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .carousel-container {
      position: relative;
      width: 100%;
      height: 500px;
      margin: 0 auto;

      :deep(.van-swipe) {
        height: 100%;
        width: 100%;
        touch-action: pan-y;

        &-item {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 450px;

          img {
            width: auto;
            height: 450px;
            object-fit: contain;
          }
        }

        &__indicators {
          bottom: 10px;

          .van-swipe__indicator {
            width: 8px;
            height: 8px;
            background-color: rgba(255, 255, 255, 0.3);
            opacity: 1;

            &--active {
              background-color: var(--color-primary);
            }
          }
        }
      }

      .carousel-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        z-index: 10;
        padding: 10px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 24px;
          height: 24px;
        }

        &-left {
          left: 40px;
        }

        &-right {
          right: 40px;
        }
      }
    }
  }

  .close-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
  }
}
</style>
