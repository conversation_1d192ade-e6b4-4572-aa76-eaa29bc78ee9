<template>
  <div class="image-preview-container">
    <!-- 图片展示 -->
    <BaseLoadingIndicator
      v-if="isLoading"
      text="图片加载中..."
      style="height: 100vh"
    />
    <van-image
      height="auto"
      width="100vw"
      fit="fill"
      :src="url"
      alt=""
      @load="isLoading = false"
      @error="isLoading = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
// 初始化 url 和加载状态
const url = ref<string>(
  typeof router.currentRoute.value.query.url === "string"
    ? router.currentRoute.value.query.url
    : ""
);
const isLoading = ref<boolean>(true);
</script>

<style scoped>
.image-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
