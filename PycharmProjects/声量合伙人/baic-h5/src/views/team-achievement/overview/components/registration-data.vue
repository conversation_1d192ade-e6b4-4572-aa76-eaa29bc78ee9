<script setup lang="ts">
import PieCharts from "@/views/team-achievement/overview/components/pie-charts.vue";
import { getDeptByUser } from "@/api/teamAchievement";
import {
  getRegisterData,
  getMediumManageSelectList
} from "@/api/teamAchievement";
import { useAuthStore } from "@/store/modules/auth";

defineOptions({
  name: "registrationData"
});

const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const departmentName = ref("");
const chatData = ref([
  // { value: 100, name: "已注册" },
  // { value: 50, name: "未注册" }
]);
const departmentNameColumns = [
  { text: "部门名称1", value: "Hangzhou" },
  { text: "部门名称2", value: "Ningbo" },
  { text: "部门名称3", value: "Wenzhou" },
  { text: "部门名称4", value: "Shaoxing" },
  { text: "部门名称5", value: "Huzhou" }
];

const loading = ref(false);
const finished = ref(false);

const registerData = ref({
  registerNum: 0,
  noRegisterNum: 0,
  workNum: 0,
  registerRate: 0
});
const mediumList = ref([]);

const getRegisterDataFunction = async () => {
  showLoadingToast({
    message: "加载中...",
    duration: 0,
    forbidClick: true
  });
  try {
    const { code, data } = await getRegisterData({
      // 将数值转换为字符串以避免精度丢失
      deptId: selectedDepartmentId.value
    });
    if (code === 200) {
      // registerData.value = data;
      registerData.value = {
        registerNum: data.registerNum !== null ? data.registerNum : 0,
        noRegisterNum: data.noRegisterNum !== null ? data.noRegisterNum : 0,
        workNum: data.workNum !== null ? data.workNum : 0,
        registerRate: data.registerRate !== null ? data.registerRate : 0
      };
      chatData.value = data;
      showDepartmentNamePicker.value = false;
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  } finally {
    closeToast();
  }
};

const getMediumManageSelectListFunction = async () => {
  try {
    const { code, data } = await getMediumManageSelectList({});
    if (code === 200) {
      mediumList.value = data;
    }
    loading.value = false;
    finished.value = true;
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
    loading.value = false;
    finished.value = true;
  }
};

// const onDepartmentNameConfirm = ({ selectedOptions }) => {
//   showDepartmentNamePicker.value = false;
//   departmentName.value = selectedOptions[0].text;
// };

const toRegisterDetail = () => {
  const queryData = {
    departmentName: departmentName.value,
    departmentId: selectedDepartmentId.value,
    ...registerData.value
  };
  router.push({
    path: "/team-achievement/overview/registration/detail",
    // 把部门名称，部门id，部门注册数据都传过去
    query: {
      ...route.query,
      departmentData: JSON.stringify(queryData)
    }
  });
};

const toMediaDetail = (item: any) => {
  const queryData = {
    departmentName: departmentName.value,
    departmentId: selectedDepartmentId.value,
    choiceMedium: {
      id: item.id,
      platformName: item.platformName,
      mediumId: item.mediumId
    },
    mediumList: mediumList.value.map(item => ({
      id: item.id,
      platformName: item.platformName,
      mediumId: item.mediumId
    }))
  };
  router.push({
    path: "/team-achievement/overview/media/detail",
    // 把部门名称，部门id，当前选择的媒体，媒体列表数据都传过去
    query: {
      mediumData: JSON.stringify(queryData)
    }
  });
};

const showDepartmentNamePicker = ref(false);
const selectedDepartmentId = ref("");
const departmentList = ref<any>([]);

// 用于记录当前显示的部门列表
const currentDepartmentList = ref(departmentList.value);
// 用于记录上级部门列表，以便回退
const departmentListStack = ref<Array<Array<any>>>([]);

// 切换到子部门列表
const showChildren = (dept: any) => {
  if (dept.children) {
    departmentListStack.value.push(currentDepartmentList.value);
    currentDepartmentList.value = dept.children;
  }
};
// 回退到上一级部门列表
const goBack = () => {
  if (departmentListStack.value.length > 0) {
    currentDepartmentList.value = departmentListStack.value.pop()!;
  }
};
// 递归查找部门
const findDepartment = (deptList, deptId) => {
  for (const dept of deptList) {
    if (dept.id === deptId) {
      return dept;
    }
    if (dept.children) {
      const found = findDepartment(dept.children, deptId);
      if (found) {
        return found;
      }
    }
  }
  return null;
};
// 打印选择的部门信息
const printSelectedDepartment = () => {
  console.log("当前 selectedDepartmentId:", selectedDepartmentId.value); // 添加调试信息
  const selectedDept = findDepartment(
    departmentList.value,
    selectedDepartmentId.value
  );
  if (selectedDept) {
    console.log("选择的部门信息:", selectedDept);
    departmentName.value = selectedDept.label; // 更新部门名称显示
    getRegisterDataFunction();
    // showDepartmentNamePicker.value = false;
  } else {
    console.log(
      "未找到对应的部门信息，selectedDepartmentId:",
      selectedDepartmentId.value
    );
  }
};
// 获取部门信息
const getDeptByUserFun = async () => {
  try {
    const { code, data } = await getDeptByUser({});
    if (code === 200) {
      departmentList.value = data;
      currentDepartmentList.value = departmentList.value;
      console.log("departmentList", departmentList.value);
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};

onMounted(() => {
  console.log("registration-data");
  console.log("authStore", authStore.userInfo);
  selectedDepartmentId.value = authStore.userInfo.deptId;
  departmentName.value = authStore.userInfo.deptName;
  getDeptByUserFun();
  getRegisterDataFunction();
});
</script>

<template>
  <div class="registration-card">
    <van-cell :border="false" title="注册数据" class="title" is-link>
      <template #value>
        <span @click="toRegisterDetail">查看详情</span>
      </template>
    </van-cell>
    <div class="pick-box">
      <div class="border">
        <van-field
          v-model="departmentName"
          is-link
          center
          :border="false"
          readonly
          label=""
          arrow-direction="down"
          placeholder="部门名称"
          @click="showDepartmentNamePicker = true"
        />
      </div>
    </div>
    <pie-charts
      style="margin: 16px 0"
      :data="chatData"
      inner-radius="85%"
      outer-radius="100%"
    />
    <div class="chat-list">
      <div class="chat-list-item">
        <div class="chat-list-item-top">
          <div class="chat-list-item-top-icon" style="background: #ec6666" />
          <div class="chat-list-item-top-title">已注册</div>
        </div>
        <div class="chat-list-item-bottom">
          {{ registerData.registerNum }}
          <span class="people">人</span>
        </div>
      </div>
      <div class="chat-list-item">
        <div class="chat-list-item-top">
          <div class="chat-list-item-top-icon" style="background: #3366cc" />
          <div class="chat-list-item-top-title">部门在职人数</div>
        </div>
        <div class="chat-list-item-bottom">
          {{ registerData.workNum }}
          <span class="people">人</span>
        </div>
      </div>
    </div>
    <div class="chat-list">
      <div class="chat-list-item">
        <div class="chat-list-item-top">
          <div class="chat-list-item-top-icon" style="background: #85e1ed" />
          <div class="chat-list-item-top-title">未注册</div>
        </div>
        <div class="chat-list-item-bottom">
          {{ registerData.noRegisterNum }}
          <span class="people">人</span>
        </div>
      </div>
      <div class="chat-list-item">
        <div class="chat-list-item-top">
          <div class="chat-list-item-top-icon" style="background: #3366cc" />
          <div class="chat-list-item-top-title">总注册率</div>
        </div>
        <div class="chat-list-item-bottom">
          {{ registerData.registerRate }}%
        </div>
      </div>
    </div>
  </div>
  <div class="media">
    <div class="media-title">媒体绑定</div>
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="getMediumManageSelectListFunction"
    >
      <van-cell
        v-for="item in mediumList"
        :key="item.id"
        is-link
        center
        :title="item.platformName"
        :title-style="{
          color: 'var(--text-primary)',
          fontSize: 'var(--font-size-h3)'
        }"
        @click="toMediaDetail(item)"
      >
        <template #icon>
          <van-image
            class="midia-icon"
            width="28"
            radius="8"
            height="28"
            :src="item.iconUrl"
          />
        </template>
      </van-cell>
    </van-list>
  </div>
  <!-- 部门弹窗 -->
  <van-popup
    v-model:show="showDepartmentNamePicker"
    round
    position="bottom"
    class="department-popup-container"
  >
    <div class="department-popup">
      <!-- <div class="department-header">
          <div @click="goBack">
            <van-icon name="arrow-left" color="#3366CC" />
          </div>
          <span class="department-header-title">选择部门</span>
          <div @click="showDepartmentNamePicker = false">
            <van-icon name="cross" color="#3366CC" />
          </div>
        </div> -->
      <div class="department-header">
        <div v-if="departmentListStack.length > 0" @click="goBack">返回</div>
        <div v-else />
        <span class="department-header-title">选择部门</span>
        <div @click="printSelectedDepartment">确认</div>
      </div>
      <div class="department-list">
        <van-radio-group v-model="selectedDepartmentId">
          <div
            v-for="dept in currentDepartmentList"
            :key="dept.id"
            :class="[
              'department-item',
              { active: selectedDepartmentId === dept.id }
            ]"
          >
            <div class="department-item-content">
              <van-radio
                :name="dept.id"
                label-position="right"
                icon-size="20"
                checked-color="#3366CC"
                label-disabled
              >
                {{ dept.label }}
              </van-radio>
              <!-- 当有子部门时显示箭头图标，点击切换到子部门列表 -->
              <van-icon
                v-if="dept.children"
                name="arrow"
                color="#3366CC"
                class="department-arrow"
                @click="showChildren(dept)"
              />
            </div>
          </div>
        </van-radio-group>
      </div>
    </div>
    <!-- 添加底部确定按钮 -->
    <!-- <div class="department-footer">
        <van-button size="large" type="primary" @click="printSelectedDepartment"
          >确定</van-button
        >
        <div class="van-safe-area-bottom" style="background: #fff" />
      </div> -->
  </van-popup>
</template>

<style scoped lang="less">
.registration-card {
  background-color: var(--color-block-background);
  border-radius: var(--rounded-md);
  padding: 12px;

  .border {
    border-radius: var(--rounded-md);
  }

  :deep(.van-cell) {
    padding: 0;
  }

  :deep(.van-field__control) {
    padding: 7px;
    font-size: var(--font-size-h3);
  }

  :deep(.van-picker__cancel) {
    color: #3366cc;
  }

  .chat-list {
    margin-top: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;

    .chat-list-item {
      padding: 12px 8px;
      background: #f3f6f9;
      width: 100%;
      height: 75px;
      border-radius: var(--rounded-md);

      .chat-list-item-top {
        height: 18px;
        display: flex;
        gap: 8px;
        align-items: center;

        .chat-list-item-top-icon {
          width: 10px;
          height: 10px;
          border-radius: var(--rounded-sm);
        }

        .chat-list-item-top-title {
          font-size: var(--font-size-caption);
          color: var(--text-secondary);
          line-height: 18px;
        }
      }

      .chat-list-item-bottom {
        height: 25px;
        line-height: 25px;
        padding-left: 18px;
        margin-top: 8px;
        color: #000000;
        font-size: var(--font-size-h1);
        font-weight: 500;

        .people {
          color: var(--text-placeholder);
          font-size: var(--font-size-caption);
          font-weight: normal;
          margin-left: 4px;
          margin-right: 4px;
        }
      }
    }
  }

  .title {
    :deep(.van-cell__title) {
      color: var(--text-primary);
      font-size: var(--font-size-h2);
      font-weight: 500;
    }

    padding-bottom: 16px;
  }

  .pick-box {
    width: 160px;
    // margin-bottom: 16px;

    :deep(.van-icon) {
      padding-right: 8px;
    }
    :deep(.van-cell) {
      line-height: 22px;
      border-radius: var(--rounded-md);
      border: 1px solid var(--color-border);
    }
  }
}

.media {
  margin-top: 12px;
  background-color: var(--color-block-background);
  border-radius: var(--rounded-md);

  .media-title {
    font-weight: 500;
    font-size: var(--font-size-h2);
    color: var(--text-primary);
    margin-bottom: 8px;
    padding-left: 12px;
    padding-top: 12px;
  }
  :deep(.van-cell) {
    height: 52px;
    padding: 12px;
  }
}
.department-popup-container {
  height: 334px;
  padding: 0 12px 12px;
}
.department-popup {
  height: 322px;
  padding: 0px 0 12px;
  .department-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #3366cc;
    font-size: 14px;
    height: 52px;
    font-size: var(--font-size-h3);

    .department-header-title {
      font-size: var(--font-size-h2);
      font-weight: 500;
      color: var(--text-primary);
    }
  }

  .department-list {
    flex: 1;
    // margin-top: 16px;
    overflow-y: auto;
    max-height: 260px;

    .department-item {
      padding: 12px;
      color: var(--text-primary);
      font-size: 14px;
      font-weight: 500;
      border-radius: var(--rounded-md);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    :deep(.van-radio__label) {
      color: var(--text-primary);
    }

    .department-item-content {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between; /* 使子元素两端对齐 */
    }

    .department-arrow {
      margin-left: auto; /* 使箭头靠右 */
    }

    .active {
      background: #f0f7ff;
    }

    .department-children {
      padding-left: 20px;
    }
  }
  .department-footer {
    padding: 12px;
    text-align: center;
  }
}
.midia-icon {
  margin-right: 8px;
}
</style>
