<script setup>
import * as echarts from "echarts";
// const { labelThreshold, data } = defineProps({
//   // 外显阈值比例 (默认0.5)
//   labelThreshold: {
//     type: Number,
//     default: 0.5
//   },
//   data: {
//     type: Array,
//     default: () => []
//   }
// });
const labelThreshold = 0.5;
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  }
});
const result = ref([]);
const handleData = () => {
  result.value = [
    {
      value: props.data.deptUserNum !== null ? props.data.deptUserNum : 0,
      name: "部门在职人数"
    },
    {
      value: props.data.taskUserNum !== null ? props.data.taskUserNum : 0,
      name: "任务覆盖人数"
    },
    {
      value: props.data.receiveUserNum !== null ? props.data.receiveUserNum : 0,
      name: "领取人数"
    },
    {
      value: props.data.finishUserNum !== null ? props.data.finishUserNum : 0,
      name: "完成人数"
    }
    // { value: 100, name: "部门在职人数" },
    // { value: 60, name: "任务覆盖人数" },
    // { value: 90, name: "领取人数" },
    // { value: 30, name: "完成人数" }
  ];
  return result.value;
};
const container = ref(null);
let chartInstance = null;

// 生成带标签配置的数据
const processData = originData => {
  const max = originData.length ? Math.max(...originData.map(d => d.value)) : 0;
  return originData.map((item, index) => ({
    ...item,
    // 标签小于最大值 labelThreshold 时外显
    label: {
      position: item.value / max < labelThreshold ? "outside" : "inside",
      formatter: ({ dataIndex }) => {
        // const styleName = dataIndex < 2 ? "darkText" : "lightText"; // 根据索引选择样式名称
        // let styleName = "darkText";
        // if (item.value / max < labelThreshold) {
        //   // 外显
        //   styleName = "darkText";
        // } else {
        //   if (dataIndex < 2) {
        //     styleName = "darkText";
        //   } else {
        //     styleName = "lightText";
        //   }
        // }
        const styleName =
          item.value / max < labelThreshold ? "darkText" : "lightText";
        return `{${styleName}|${result.value[dataIndex].name}}`;
      }
    }
  }));
};

const initChart = () => {
  const options = {
    tooltip: false,
    series: [
      {
        emphasis: {
          disabled: true // 关键配置：禁用高亮
        },
        silent: false,
        type: "funnel",
        // 漏斗图数据改为死值，不动态改变
        // data: processData(handleData()),
        data: [
          { value: 6, name: "部门在职人数" },
          { value: 5, name: "任务覆盖人数" },
          { value: 4, name: "领取人数" },
          { value: 3, name: "完成人数" }
        ],
        width: "100%",
        height: "100%",
        left: "center",
        top: "0",
        gap: 0,
        label: {
          show: true,
          fontSize: 12,
          position: "inside",
          color: "#fff"
          // rich: {
          //   text: {
          //     fontSize: 12
          //   },
          //   darkText: {
          //     fontSize: 12,
          //     color: "rgba(0, 0, 0, 0.85)" // 黑色
          //   },
          //   lightText: {
          //     fontSize: 12,
          //     color: "#fff" // 白色
          //   }
          // }
        },
        labelLine: {
          show: false
        }
      }
    ],
    color: ["#5087ec", "#68bbc4", "#58a55c", "#f2bd42"]
  };

  chartInstance.setOption(options);
};

onMounted(() => {
  chartInstance = echarts.init(container.value);
  initChart();

  // 响应式处理
  const resizeObserver = new ResizeObserver(() => {
    chartInstance.resize();
  });
  resizeObserver.observe(container.value);

  onBeforeUnmount(() => {
    resizeObserver.disconnect();
  });
});

onBeforeUnmount(() => {
  if (chartInstance && !chartInstance.isDisposed()) {
    chartInstance.dispose();
  }
});
// watchEffect(() => {
//   // 在这里更新图表
//   console.log("Data changed:", props.data);
//   // 更新图表的代码
//   if (chartInstance) {
//     initChart();
//   }
// });
</script>

<template>
  <div
    style="
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
    "
  >
    <div ref="container" class="funnel-chart" />
  </div>
</template>

<style>
.funnel-chart {
  width: 250px;
  height: 203.2px;
}
</style>
