<script setup>
import * as echarts from "echarts";

// const {
//   data = [],
//   colors = ["#3366CC", "#85E1ED", "#EC6666"],
//   innerRadius = "55%",
//   outerRadius = "75%",
//   title = "部门注册人数"
// }
// const colors = ["#3366CC", "#85E1ED", "#EC6666"];
const colors = ["#EC6666", "#79D2DE", "#3366CC"];
// 下面两个参数用于控制饼图内圈和外圈的大小
const innerRadius = "88%";
const outerRadius = "100%";
const title = "部门在职人数";
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  }
  // colors: Array,
  // innerRadius: String,
  // outerRadius: String,
  // title: String
});

const container = ref(null);
let chartInstance = null;

// 计算数据总和
const total = computed(() =>
  props.data.reduce((sum, item) => sum + item.value, 0)
);
const result = ref([]);
const handleData = () => {
  result.value = [
    {
      value: props.data.registerNum || 0,
      name: "已注册"
    },
    {
      value: props.data.noRegisterNum || 0,
      name: "未注册"
    }
  ];
  return result.value;
};

const getOptions = () => ({
  tooltip: false,
  color: colors,
  series: [
    {
      type: "pie",
      radius: [innerRadius, outerRadius],
      itemStyle: {
        borderColor: "#fff",
        borderWidth: 0, // 间隔宽度
        borderRadius: 10
      },
      label: {
        show: true,
        position: "center",
        formatter: () => `{value|${props.data.workNum || 0}}\n{title|${title}}`,
        rich: {
          title: {
            fontSize: 14,
            color: "rgba(0, 0, 0, 0.65)",
            lineHeight: 24,
            padding: [5, 0, 0, 0]
          },
          value: {
            fontSize: 36,
            fontWeight: "500",
            color: "rgba(0, 0, 0, 0.85)",
            lineHeight: 48
          }
        }
      },
      labelLine: {
        show: false
      },
      emphasis: false,
      data: handleData()
    }
  ]
});

onMounted(() => {
  chartInstance = echarts.init(container.value);
  chartInstance.setOption(getOptions());

  // 响应式处理
  const resizeObserver = new ResizeObserver(() => {
    chartInstance.resize();
  });
  resizeObserver.observe(container.value);

  onBeforeUnmount(() => {
    resizeObserver.disconnect();
  });
});

onBeforeUnmount(() => {
  chartInstance?.dispose();
});
watchEffect(() => {
  // 在这里更新图表
  console.log("Data changed:", props.data);
  // 更新图表的代码
  if (chartInstance) {
    chartInstance.setOption(getOptions());
  }
});
</script>

<template>
  <div
    ref="container"
    class="center-label-ring"
    :style="{ width: '100%', height: '100%', minHeight: '181px' }"
  />
</template>

<style>
/* 优化中间文字对齐 */
.center-label-ring canvas {
  vertical-align: top !important;
}
</style>
