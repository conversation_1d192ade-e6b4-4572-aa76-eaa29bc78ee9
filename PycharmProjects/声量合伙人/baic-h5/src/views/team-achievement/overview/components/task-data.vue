<script setup lang="ts">
import FunnelCharts from "@/views/team-achievement/overview/components/funnel-charts.vue";
import { getTaskData, getTaskName, getDeptByUser } from "@/api/teamAchievement";
import type { DatePickerColumnType } from "vant";
import { useAuthStore } from "@/store/modules/auth";
import { getMyUserInfo } from "@/api/my";
import { to as toPromise } from "await-to-js";

defineOptions({
  name: "taskData"
});
const authStore = useAuthStore();
const { setUser } = useAuthStore();
const route = useRoute();
const router = useRouter();
const chatData = ref({
  // { value: 100, name: "部门在职人数" },
  // { value: 60, name: "任务覆盖人数" },
  // { value: 90, name: "领取人数" },
  // { value: 30, name: "完成人数" }
  deptUserNum: null,
  taskUserNum: null,
  receiveUserNum: null,
  finishUserNum: null,
  taskUserNumRatio: null,
  receiveUserNumRatio: null,
  finishUserNumRatio: null
});

const taskLoading = ref(false);
const taskFinished = ref(true);
const taskList = ref([]);
const checkedTask = ref({
  id: 0,
  name: "",
  dateStr: ""
});
const taskQuery = ref({
  searchKey: "",
  startTime: "", // 2025-03-11
  endTime: ""
});

const defaultDepartmentName = ref("");
const departmentName = ref("");
const taskName = ref("");
const taskDateSelect = ref("2025年01月");
const taskDate = ref(["2025", "01"]);
const columnsType: DatePickerColumnType[] = ["year", "month"];
const minDate = new Date(2025, 0, 1);
// 获取当前日期
const now = new Date();
// 获取当前年份
const currentYear = now.getFullYear();
// 获取当前月份（从 0 开始计数）
const currentMonth = now.getMonth();
// 获取当前日期
const currentDay = now.getDate();
// 创建与 minDate 格式一致的 maxDate
const maxDate = new Date(currentYear, currentMonth, currentDay);
// 补零函数
const padZero = (num: number) => num.toString().padStart(2, "0");
taskDate.value = [currentYear.toString(), padZero(currentMonth + 1)];
taskDateSelect.value = `${taskDate.value[0]}年${taskDate.value[1]}月`;

const timePopup = reactive({
  currentDate: [currentYear + "", padZero(currentMonth + 1), "01"],
  startTime: false,
  endTime: false
});

const showTaskDatePopup = ref(false);
const showDepartmentNamePicker = ref(false);
const showTaskNamePopup = ref(false);

// 部门列表
// const departmentList = ref([
//   {
//     id: "1898918525321408513",
//     parentId: "1898918357951901697",
//     label: "小苹果部门",
//     weight: 1,
//     disabled: false
//   },
//   {
//     id: "1898919046421737474",
//     parentId: "1898918357951901697",
//     label: "大西瓜部门",
//     weight: 2,
//     disabled: false,
//     children: [
//       {
//         id: "1898919531954368513",
//         parentId: "1898919046421737474",
//         label: "有籽西瓜",
//         weight: 1,
//         disabled: false,
//         children: [
//           {
//             id: "1898919531954368511",
//             parentId: "1898919531954368513",
//             label: "有籽西瓜111",
//             weight: 1,
//             disabled: false
//           },
//           {
//             id: "1898919614464716822",
//             parentId: "1898919531954368513",
//             label: "有籽西瓜222",
//             weight: 2,
//             disabled: false
//           }
//         ]
//       },
//       {
//         id: "1898919614464716802",
//         parentId: "1898919046421737474",
//         label: "无籽西瓜",
//         weight: 2,
//         disabled: false
//       }
//     ]
//   },
//   {
//     id: "1898919219201896450",
//     parentId: "1898918357951901697",
//     label: "大香蕉部门",
//     weight: 3,
//     disabled: false
//   },
//   {
//     id: "1898919219201896411",
//     parentId: "1898918357951901697",
//     label: "大香蕉部门2",
//     weight: 3,
//     disabled: false
//   },
//   {
//     id: "1898919219201896422",
//     parentId: "1898918357951901697",
//     label: "大香蕉部门3",
//     weight: 3,
//     disabled: false
//   },
//   {
//     id: "1898919219201896433",
//     parentId: "1898918357951901697",
//     label: "大香蕉部门4",
//     weight: 3,
//     disabled: false
//   },
//   {
//     id: "1898919219201896444",
//     parentId: "1898918357951901697",
//     label: "大香蕉部门5",
//     weight: 3,
//     disabled: false
//   },
//   {
//     id: "1898919219201896455",
//     parentId: "1898918357951901697",
//     label: "大香蕉部门6",
//     weight: 3,
//     disabled: false
//   }
// ]);
const departmentList = ref<any>([]);

// 任务基础数据
const basicData = ref({
  deptUserNum: 0, //部门在职人数
  taskUserNum: 0, //任务覆盖人数
  receiveUserNum: 0, //领取人数
  finishUserNum: 0, //完成人数
  taskUserNumRatio: 0, //任务覆盖人数占比
  receiveUserNumRatio: 0, //领取人数占比
  finishUserNumRatio: 0 //完成人数占比
});

// const onDepartmentNameConfirm = ({ selectedOptions }) => {
//   showDepartmentNamePicker.value = false;
//   departmentName.value = selectedOptions[0].text;
// };

const closeTaskNamePopup = () => {
  showTaskNamePopup.value = false;
};

const onStartConfirm = ({ selectedValues }) => {
  if (
    new Date(selectedValues.join("-")).getTime() >
    new Date(taskQuery.value.endTime).getTime()
  ) {
    showToast("开始日期不能大于结束日期");
    return;
  }
  taskQuery.value.startTime = selectedValues.join("-");
  timePopup.startTime = false;
};
const onEndConfirm = ({ selectedValues }) => {
  if (
    new Date(selectedValues.join("-")).getTime() <
    new Date(taskQuery.value.startTime).getTime()
  ) {
    showToast("结束日期不能小于开始日期");
    return;
  }
  taskQuery.value.endTime = selectedValues.join("-");
  timePopup.endTime = false;
};

// 获取任务列表
const getTaskNameFun = async () => {
  checkedTask.value = {
    id: 0,
    name: "",
    dateStr: ""
  };
  taskList.value = [];
  taskLoading.value = true;
  taskFinished.value = false;
  try {
    const { code, data } = await getTaskName({
      taskName: taskQuery.value.searchKey,
      startTime: taskQuery.value.startTime,
      endTime: taskQuery.value.endTime
    });
    if (code === 200) {
      taskList.value = data;
      if (taskList.value.length > 0) {
        checkedTask.value = {
          name: taskList.value[0].name,
          id: taskList.value[0].id,
          dateStr: taskList.value[0].deadlineTime
        };
      }
    }
    taskLoading.value = false;
    taskFinished.value = true;
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
    taskLoading.value = false;
    taskFinished.value = true;
  }
};

const clickRadio = (item: any) => {
  checkedTask.value = {
    name: item.name,
    id: item.id,
    dateStr: item.deadlineTime
  };
};

const onDateConfirm = () => {
  // 这里可以添加日期确认后的逻辑，例如更新任务查询的日期
  console.log(taskDate.value);
  taskDateSelect.value = `${taskDate.value[0]}年${taskDate.value[1]}月`;
  getTaskDataFun();
};

// 获取任务基础数据
const getTaskDataFun = async () => {
  showLoadingToast({
    message: "加载中...",
    duration: 0,
    forbidClick: true
  });
  try {
    const params = {
      deptId: selectedDepartmentId.value.toString(),
      dateStr: taskDate.value.join("-")
    };
    const { code, data } = await getTaskData(params);
    if (code === 200) {
      // basicData.value = data;
      basicData.value = {
        deptUserNum: data.deptUserNum !== null ? data.deptUserNum : 0,
        taskUserNum: data.taskUserNum !== null ? data.taskUserNum : 0,
        receiveUserNum: data.receiveUserNum !== null ? data.receiveUserNum : 0,
        finishUserNum: data.finishUserNum !== null ? data.finishUserNum : 0,
        taskUserNumRatio:
          data.taskUserNumRatio !== null ? data.taskUserNumRatio : "0.00",
        receiveUserNumRatio:
          data.receiveUserNumRatio !== null ? data.receiveUserNumRatio : "0.00",
        finishUserNumRatio:
          data.finishUserNumRatio !== null ? data.finishUserNumRatio : "0.00"
      };
      // chatData.value = data;
      showDepartmentNamePicker.value = false;
      showTaskDatePopup.value = false;
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  } finally {
    closeToast();
  }
};

// 获取部门信息
const getDeptByUserFun = async () => {
  try {
    const { code, data } = await getDeptByUser({});
    if (code === 200) {
      departmentList.value = data;
      currentDepartmentList.value = departmentList.value;
      console.log("departmentList", departmentList.value);
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};

const toTaskDetailPage = async () => {
  if (!checkedTask.value.name) {
    showToast("请选择任务");
    return;
  }
  const params = {
    taskId: checkedTask.value.id,
    taskName: checkedTask.value.name,
    deptId: selectedDepartmentId.value.toString(),
    taskUserNum: basicData.value.taskUserNum,
    departmentName: departmentName.value,
    dateStr: checkedTask.value.dateStr
  };
  router.push({
    path: "/team-achievement/overview/task/detail",
    query: {
      ...route.query,
      taskDetail: JSON.stringify(params)
    }
  });
  showTaskNamePopup.value = false;
};

const selectedDepartmentId = ref("");

// 用于记录当前显示的部门列表
const currentDepartmentList = ref(departmentList.value);
// 用于记录上级部门列表，以便回退
const departmentListStack = ref<Array<Array<any>>>([]);

// 切换到子部门列表
const showChildren = (dept: any) => {
  if (dept.children) {
    departmentListStack.value.push(currentDepartmentList.value);
    currentDepartmentList.value = dept.children;
  }
};
// 回退到上一级部门列表
const goBack = () => {
  if (departmentListStack.value.length > 0) {
    currentDepartmentList.value = departmentListStack.value.pop()!;
  }
};
// 递归查找部门
const findDepartment = (deptList, deptId) => {
  for (const dept of deptList) {
    if (dept.id === deptId) {
      return dept;
    }
    if (dept.children) {
      const found = findDepartment(dept.children, deptId);
      if (found) {
        return found;
      }
    }
  }
  return null;
};
// 打印选择的部门信息
const printSelectedDepartment = () => {
  console.log("当前 selectedDepartmentId:", selectedDepartmentId.value); // 添加调试信息
  const selectedDept = findDepartment(
    departmentList.value,
    selectedDepartmentId.value
  );
  if (selectedDept) {
    console.log("选择的部门信息:", selectedDept);
    departmentName.value = selectedDept.label; // 更新部门名称显示
    getTaskDataFun();
    // showDepartmentNamePicker.value = false;
  } else {
    console.log(
      "未找到对应的部门信息，selectedDepartmentId:",
      selectedDepartmentId.value
    );
  }
};

const showTaskPopup = async () => {
  taskQuery.value = {
    searchKey: "",
    startTime: `${currentYear}-${padZero(currentMonth + 1)}-01`,
    endTime: `${currentYear}-${padZero(currentMonth + 1)}-${padZero(currentDay)}`
  };
  await getTaskNameFun();
  showTaskNamePopup.value = true;
};

const updatedUserInfo = async () => {
  const [error, userInfoResponse] = await toPromise(getMyUserInfo());
  if (!error) {
    setUser(userInfoResponse.data);
  }
  console.log("authStore", authStore.userInfo);
  selectedDepartmentId.value = authStore.userInfo.deptId;
  departmentName.value = authStore.userInfo.deptName;
  getTaskDataFun();
  getDeptByUserFun();
};

onMounted(() => {
  console.log("task-data");
  updatedUserInfo();
});
</script>

<template>
  <div class="card">
    <van-cell
      :border="false"
      class="title"
      title="基础数据"
      is-link
      value="查看详情"
      @click="showTaskPopup"
    />
    <div class="pick-box">
      <div class="border">
        <van-field
          v-model="departmentName"
          is-link
          center
          :border="false"
          readonly
          label=""
          arrow-direction="down"
          placeholder="部门名称"
          @click="showDepartmentNamePicker = true"
        />
      </div>
      <div class="border">
        <van-field
          v-model="taskDateSelect"
          is-link
          center
          :border="false"
          readonly
          label=""
          arrow-direction="down"
          placeholder="选择时间"
          @click="showTaskDatePopup = true"
        />
      </div>
    </div>
    <funnel-charts style="margin-bottom: 8px" :data="chatData" />

    <div class="chat-list">
      <div class="chat-list-item">
        <div class="chat-list-item-top">
          <div class="chat-list-item-top-icon" style="background: #5087ec" />
          <div class="chat-list-item-top-title">部门在职人数</div>
        </div>
        <div class="chat-list-item-bottom">
          {{ basicData.deptUserNum }}
          <span class="people">人</span>
        </div>
      </div>
      <div class="chat-list-item">
        <div class="chat-list-item-top">
          <div class="chat-list-item-top-icon" style="background: #68bbc4" />
          <div class="chat-list-item-top-title">任务覆盖人数（占比）</div>
        </div>
        <div class="chat-list-item-bottom">
          {{ basicData.taskUserNum }}
          <span class="people">人</span>
          <span class="ratio">{{ `(${basicData.taskUserNumRatio}%)` }} </span>
        </div>
      </div>
    </div>
    <div class="chat-list">
      <div class="chat-list-item">
        <div class="chat-list-item-top">
          <div class="chat-list-item-top-icon" style="background: #58a55c" />
          <div class="chat-list-item-top-title">领取人数（占比）</div>
        </div>
        <div class="chat-list-item-bottom">
          {{ basicData.receiveUserNum }}
          <span class="people">人</span>
          <span class="ratio">
            {{ `(${basicData.receiveUserNumRatio}%)` }}</span
          >
        </div>
      </div>
      <div class="chat-list-item">
        <div class="chat-list-item-top">
          <div class="chat-list-item-top-icon" style="background: #f2bd42" />
          <div class="chat-list-item-top-title">完成人数（占比）</div>
        </div>
        <div class="chat-list-item-bottom">
          {{ basicData.finishUserNum }}
          <span class="people">人</span>
          <span class="ratio"> {{ `(${basicData.finishUserNumRatio}%)` }}</span>
        </div>
      </div>
    </div>

    <!-- 部门弹窗 -->
    <van-popup
      v-model:show="showDepartmentNamePicker"
      round
      position="bottom"
      class="department-popup-container"
    >
      <div class="department-popup">
        <!-- <div class="department-header">
          <div @click="goBack">
            <van-icon name="arrow-left" color="#3366CC" />
          </div>
          <span class="department-header-title">选择部门</span>
          <div @click="showDepartmentNamePicker = false">
            <van-icon name="cross" color="#3366CC" />
          </div>
        </div> -->
        <div class="department-header">
          <div v-if="departmentListStack.length > 0" @click="goBack">返回</div>
          <div v-else />
          <span class="department-header-title">选择部门</span>
          <div @click="printSelectedDepartment">确认</div>
        </div>
        <div class="department-list">
          <van-radio-group v-model="selectedDepartmentId">
            <div
              v-for="dept in currentDepartmentList"
              :key="dept.id"
              :class="[
                'department-item',
                { active: selectedDepartmentId === dept.id }
              ]"
            >
              <div class="department-item-content">
                <van-radio
                  :name="dept.id"
                  label-position="right"
                  icon-size="20"
                  checked-color="#3366CC"
                  label-disabled
                >
                  {{ dept.label }}
                </van-radio>
                <!-- 当有子部门时显示箭头图标，点击切换到子部门列表 -->
                <van-icon
                  v-if="dept.children"
                  name="arrow"
                  color="#3366CC"
                  class="department-arrow"
                  @click="showChildren(dept)"
                />
              </div>
            </div>
          </van-radio-group>
        </div>
      </div>
      <!-- 添加底部确定按钮 -->
      <!-- <div class="department-footer">
        <van-button size="large" type="primary" @click="printSelectedDepartment"
          >确定</van-button
        >
        <div class="van-safe-area-bottom" style="background: #fff" />
      </div> -->
    </van-popup>
    <!-- 筛选弹窗 -->
    <van-popup
      v-model:show="showTaskNamePopup"
      round
      position="bottom"
      class="task-popup-container"
    >
      <div class="task-popup">
        <div class="task-header">
          <div @click="closeTaskNamePopup">取消</div>
          <span class="task-header-title">选择任务</span>
          <div @click="toTaskDetailPage">确认</div>
        </div>
        <div class="search">
          <van-field
            v-model="taskQuery.searchKey"
            class="border search-input"
            label=""
            center
            left-icon="search"
            placeholder="请输入任务名称"
          />
          <van-button type="primary" class="search-btn" @click="getTaskNameFun"
            >搜索</van-button
          >
        </div>
        <div class="search-time">
          <van-field
            v-model="taskQuery.startTime"
            class="border"
            style="text-align: center"
            center
            readonly
            name="datePicker"
            left-icon="clock-o"
            label=""
            placeholder="开始日期"
            @click="
              timePopup.startTime = true;
              timePopup.currentDate = taskQuery.startTime.split('-');
            "
          >
            <template #left-icon>
              <img
                src="@/assets/team-achievement/calendar-line.png"
                alt=""
                class="calender"
              />
            </template>
          </van-field>
          <van-icon style="padding-left: 0" name="minus" color="#bfbfbf" />
          <van-field
            v-model="taskQuery.endTime"
            center
            class="border"
            left-icon="clock-o"
            readonly
            name="datePicker"
            label=""
            placeholder="结束日期"
            @click="
              timePopup.endTime = true;
              timePopup.currentDate = taskQuery.endTime.split('-');
            "
          >
            <template #left-icon>
              <img
                src="@/assets/team-achievement/calendar-line.png"
                alt=""
                class="calender"
              />
            </template>
          </van-field>
        </div>
        <div class="task">
          <div class="task-title">任务名称</div>
          <div class="task-list">
            <van-list
              v-model:loading="taskLoading"
              :finished="taskFinished"
              finished-text="没有更多了"
            >
              <van-radio-group v-model="checkedTask.id">
                <div
                  v-for="item in taskList"
                  :key="item.id"
                  :class="['task-item', { active: checkedTask.id === item.id }]"
                >
                  <van-radio
                    :name="item.id"
                    label-position="left"
                    icon-size="20"
                    checked-color="#3366CC"
                    @click="clickRadio(item)"
                    >{{ item.name }}
                  </van-radio>
                </div>
              </van-radio-group>
            </van-list>
          </div>
        </div>
      </div>
    </van-popup>
    <!-- 时间弹窗 -->
    <van-popup v-model:show="showTaskDatePopup" round position="bottom">
      <van-date-picker
        v-model="taskDate"
        title="选择日期"
        :min-date="minDate"
        :max-date="maxDate"
        :columns-type="columnsType"
        @confirm="onDateConfirm"
        @cancel="showTaskDatePopup = false"
      >
        <!-- 自定义顶部栏 -->
        <!-- <template #toolbar>
          <div class="custom-date-picker-header">
            <span></span>
            <span>选择时间</span>
            <div class="right" @click="showTaskDatePopup = false">取消</div>
          </div>
        </template> -->
      </van-date-picker>
      <!-- 自定义确认按钮放在弹窗最下面 -->
      <!-- <div class="custom-date-picker-footer">
        <van-button type="primary" @click="onDateConfirm">确认</van-button>
      </div> -->
    </van-popup>
    <van-popup v-model:show="timePopup.startTime" round position="bottom">
      <van-date-picker
        v-model="timePopup.currentDate"
        title="开始日期"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onStartConfirm"
        @cancel="timePopup.startTime = false"
      />
    </van-popup>
    <van-popup v-model:show="timePopup.endTime" round position="bottom">
      <van-date-picker
        v-model="timePopup.currentDate"
        title="结束日期"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onEndConfirm"
        @cancel="timePopup.endTime = false"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="less">
.card {
  background-color: var(--color-block-background);
  border-radius: var(--rounded-md);
  padding: 12px;

  .border {
    border-radius: var(--rounded-md);
  }

  :deep(.van-cell) {
    padding: 0;
    height: 36px;
  }

  :deep(.van-field__control) {
    padding: 7px;
    font-size: 14px;
  }

  :deep(.van-picker__cancel) {
    color: #3366cc;
  }

  :deep(.van-cell__value) {
    font-size: 14px;
  }

  .chat-list {
    margin-top: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;

    .chat-list-item {
      padding: 12px 8px;
      background: #f3f6f9;
      width: 100%;
      height: 75px;
      border-radius: var(--rounded-md);

      .chat-list-item-top {
        height: 18px;
        display: flex;
        gap: 8px;
        align-items: center;

        .chat-list-item-top-icon {
          width: 10px;
          height: 10px;
          border-radius: var(--rounded-sm);
        }

        .chat-list-item-top-title {
          font-size: var(--font-size-caption);
          color: var(--text-secondary);
          line-height: 18px;
        }
      }

      .chat-list-item-bottom {
        height: 25px;
        line-height: 25px;
        padding-left: 18px;
        margin-top: 8px;
        color: #000000;
        font-size: var(--font-size-h1);
        font-weight: 500;

        .people {
          color: var(--text-placeholder);
          font-size: var(--font-size-caption);
          font-weight: normal;
          margin-left: 4px;
          margin-right: 4px;
        }
        .ratio {
          font-size: var(--font-size-h3);
        }
      }
    }
  }

  .title {
    :deep(.van-cell__title) {
      color: var(--text-primary);
      font-size: 16px;
      font-weight: 500;
    }

    padding-bottom: 16px;
  }

  .pick-box {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: space-around;
    margin-bottom: 16px;

    :deep(.van-icon) {
      padding-right: 8px;
    }
    :deep(.van-cell) {
      height: 36px;
      line-height: 22px;
      border-radius: var(--rounded-md);
      border: 1px solid var(--color-border);
    }
  }

  .task-popup {
    padding: 0 12px 12px;
    height: 100%;

    .task-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #3366cc;
      font-size: 14px;
      height: 52px;
      font-size: var(--font-size-h3);

      .task-header-title {
        font-size: var(--font-size-h2);
        font-weight: 500;
        color: var(--text-primary);
      }
    }

    .search {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;

      .search-btn {
        white-space: nowrap;
      }

      .search-input {
        width: 283px;
        line-height: 18px;
        :deep(.van-field__left-icon) {
          margin-left: 8px;
          color: #bfbfbf;
        }
      }
    }

    .search-time {
      margin-top: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      :deep(.van-cell) {
        padding: 0 8px;
      }

      // :deep(.van-icon) {
      //   padding-left: 8px;
      //   color: #bfbfbf;
      // }
      .calender {
        width: 16px;
        height: 16px;
      }
    }

    .task {
      margin-top: 16px;
      flex: 1;

      .task-title {
        font-weight: 500;
        font-size: var(--font-size-h2);
        color: var(--text-primary);
      }

      .task-list {
        margin-top: 8px;
        overflow-y: auto;
        max-height: 220px;

        :deep(.van-radio) {
          justify-content: space-between;
        }

        :deep(.van-radio__icon) {
          text-align: right;
        }

        .task-item {
          min-height: 46px;
          padding: 12px;
          font-size: var(--font-size-h3);
          font-weight: 500;
          border-radius: var(--rounded-md);
        }
        :deep(.van-radio__label) {
          color: var(--text-primary);
        }

        .active {
          background: #f0f7ff;
        }
      }
    }
  }
}
.custom-date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 12px;
}

.custom-date-picker-header .right {
  order: 2;
}

.custom-date-picker-footer {
  padding: 12px;
  text-align: center;
}
.department-popup-container {
  height: 334px;
  padding: 0 12px 12px;
}
.department-popup {
  height: 322px;
  padding: 0px 0 12px;
  .department-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #3366cc;
    font-size: 14px;
    height: 52px;
    font-size: var(--font-size-h3);

    .department-header-title {
      font-size: var(--font-size-h2);
      font-weight: 500;
      color: var(--text-primary);
    }
  }

  .department-list {
    flex: 1;
    // margin-top: 16px;
    overflow-y: auto;
    max-height: 260px;

    .department-item {
      padding: 12px;
      color: var(--text-primary);
      font-size: 14px;
      font-weight: 500;
      border-radius: var(--rounded-md);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    :deep(.van-radio__label) {
      color: var(--text-primary);
    }

    .department-item-content {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between; /* 使子元素两端对齐 */
    }

    .department-arrow {
      margin-left: auto; /* 使箭头靠右 */
    }

    .active {
      background: #f0f7ff;
    }

    .department-children {
      padding-left: 20px;
    }
  }
  .department-footer {
    padding: 12px;
    text-align: center;
  }
}
.task-popup-container {
  height: 408px;
  padding: 0 12px 12px 12px;
}
:deep(.van-picker__title) {
  font-weight: 500;
}
</style>
