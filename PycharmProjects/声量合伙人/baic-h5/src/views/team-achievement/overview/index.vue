<script setup lang="ts">
import TaskData from "@/views/team-achievement/overview/components/task-data.vue";
import RegistrationData from "@/views/team-achievement/overview/components/registration-data.vue";

defineOptions({
  name: "TeamAchievement"
});
const route = useRoute();
const router = useRouter();

// 默认值为0（第一个Tab），确保类型为 number
const activeTab = ref<number>(
  typeof route.query.tab === "string" ? parseInt(route.query.tab, 10) : 0 // 默认第一个Tab
);

// 监听路由变化
watch(
  () => route.query.tab,
  newTab => {
    activeTab.value = typeof newTab === "string" ? parseInt(newTab, 10) : 0;
  }
);

// Tab切换处理
const handleTabChange = (tabIndex: number) => {
  router.replace({
    query: {
      ...route.query,
      tab: tabIndex.toString() // 将数字转为字符串存储
    }
  });
};
</script>

<template>
  <div>
    <van-tabs v-model:active="activeTab" @change="handleTabChange">
      <van-tab title="任务数据">
        <div class="p-[12px]">
          <task-data />
        </div>
      </van-tab>
      <van-tab title="注册数据">
        <div class="p-[12px]">
          <registration-data />
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<style scoped>
:deep(.van-tab) {
  color: var(--text-describe);
}
:deep(.van-tab--active) {
  color: var(--text-primary);
}
:deep(.van-tab__text--ellipsis) {
  font-size: var(--font-size-h2);
}
</style>
