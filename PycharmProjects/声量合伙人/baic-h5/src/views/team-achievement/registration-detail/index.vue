<!-- 注册详情 -->
<script setup lang="ts">
import { getRegisterDetail, getRegisterDetailUrl } from "@/api/teamAchievement";
import { copyToClipboard } from "@/utils/validate";

defineOptions({
  name: "RegistrationDetail"
});

const router = useRouter();
const route = useRoute();
const list = ref([]);

const loading = ref(false);
const finished = ref(false);
/* 
departmentId: 1111111
departmentName: "部门1"
noRegisterNum: 0
registerNum: 0
registerRate: 0
workNum: 0
 */
const departmentData = ref(
  route.query.departmentData
    ? JSON.parse(route.query.departmentData as string)
    : null
);

const registerData = ref([
  // {
  //   employeeName: "",
  //   status: 0
  // }
]);
const total = ref(0);
const pageNum = ref(1);
const pageSize = ref(20);

const updateData = async () => {
  // 检查 departmentData.value 是否为 null
  if (departmentData.value) {
    try {
      const { data } = await getRegisterDetail({
        deptId: departmentData.value.departmentId,
        pageNum: pageNum.value,
        pageSize: pageSize.value
      });
      registerData.value = [...registerData.value, ...data.rows];
      total.value = data.total;
      if (registerData.value.length >= total.value) {
        finished.value = true;
      } else {
        pageNum.value++;
      }
      loading.value = false;
    } catch (error) {
      showFailToast(error?.msg || "请求失败");
      loading.value = false;
      finished.value = true;
    }
  } else {
    // 如果 departmentData.value 为 null，重置相关数据
    registerData.value = [];
  }
};

const getRegisterDetailUrlFun = async () => {
  showLoadingToast({
    message: "加载中...",
    duration: 0,
    forbidClick: true
  });
  const clipboardItemOut = new ClipboardItem({
    "text/plain": getRegisterDetailUrl({
      deptId: departmentData.value.departmentId,
      deptName: departmentData.value.departmentName
    }).then(data => {
      console.log("data", data);
      let text = `${data.data}`;
      console.log("text", text);
      return new Blob([text], { type: "text/plain" });
    })
  });
  await navigator.clipboard.write([clipboardItemOut]);
  showToast("复制成功");
  // try {
  //   const { data } = await getRegisterDetailUrl({
  //     deptId: departmentData.value.departmentId,
  //     deptName: departmentData.value.departmentName
  //   });
  //   console.log(data);
  //   setTimeout(() => {
  //     console.log(222);
  //     copyToClipboard(data);
  //     console.log(333);
  //   }, 50);
  // } catch (error) {
  //   showFailToast(error?.msg || "请求失败");
  // }
};

onMounted(() => {
  updateData();
});

// 监听路由变化
watch(
  () => route.query.departmentData,
  newDepartmentData => {
    departmentData.value = newDepartmentData
      ? JSON.parse(newDepartmentData as string)
      : null;
    updateData();
  }
);
</script>

<template>
  <div style="padding: 12px">
    <div class="info-box">
      <div class="department">
        <img src="@/assets/team-achievement/team-fill.png" alt="" />
        <div class="title">{{ departmentData?.departmentName }}</div>
      </div>
      <div class="people">
        部门在职人数：<span>{{ departmentData?.workNum }}</span
        >人
      </div>
      <div class="info-container">
        <div class="info-item">
          <div class="item-value">{{ departmentData?.registerNum }}</div>
          <div class="item-label">已注册</div>
        </div>
        <div class="info-item">
          <div class="item-value">{{ departmentData?.noRegisterNum }}</div>
          <div class="item-label">未注册</div>
        </div>
      </div>
    </div>
    <Empty
      v-if="!registerData || !registerData.length"
      icon="empty"
      text="暂无数据"
      marginTop="12"
      marginBottom="12"
      background-color="var(--color-block-background)"
    />
    <div v-else class="list">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="updateData"
      >
        <div
          v-for="(item, index) in registerData"
          :key="index"
          class="list-item"
        >
          <div class="left">{{ item.employeeName }}</div>
          <div class="right">
            <div
              v-if="item.status"
              style="display: flex; align-items: center; gap: 4px"
            >
              <img
                src="@/assets/team-achievement/checkbox-circle-success.png"
                alt=""
              />
              <div>已注册</div>
            </div>
            <div v-else style="display: flex; align-items: center; gap: 4px">
              <img src="@/assets/team-achievement/close-circle.png" alt="" />
              <div>未注册</div>
            </div>
          </div>
        </div>
      </van-list>
    </div>
  </div>
  <div class="bottom">
    <div class="btn" @click="getRegisterDetailUrlFun">
      <img src="@/assets/team-achievement/file-copy.png" alt="" />
      <div>复制链接</div>
    </div>
    <div class="van-safe-area-bottom" style="background: #fff" />
  </div>
</template>

<style scoped lang="less">
.info-box {
  height: 147px;
  padding: 12px;
  border-radius: var(--rounded-md);
  background: linear-gradient(117deg, #dce1eb 28%, #f2f3f7 100%);
  box-sizing: border-box;
  // border: 0.5px solid;
  // border-image: linear-gradient(117deg, #f2f3f7 28%, #dce1eb 100%) 0.5;

  .department {
    height: 22px;
    line-height: 25px;
    font-weight: 500;
    font-size: var(--font-size-h1);
    display: flex;
    align-items: center;
    gap: 4px;

    img {
      width: 20px;
      height: 20px;
    }
    .title {
      width: 100%;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }
  }

  .people {
    padding-left: 24px;
    margin-top: 4px;
    line-height: 18px;
    font-size: var(--font-size-caption);
    color: var(--text-describe);

    span {
      color: var(--color-primary);
    }
  }

  .info-container {
    width: 327px;
    padding-top: 24px;
    // padding-left: 28px;
    // padding-right: 28px;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .info-item {
      width: 135.5px;
      height: 52px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 4px;

      .item-value {
        font-weight: bold;
        color: var(--text-primary);
        font-size: 20px;
      }

      .item-label {
        color: var(--text-describe);
        font-size: var(--font-size-caption);
      }
    }
  }
}

.list {
  margin-top: 12px;

  .list-item {
    height: 46px;
    border-radius: var(--rounded-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    margin-top: 8px;
    background: #ffffff;
    line-height: 18px;

    .left {
      font-size: var(--font-size-h2);
      font-weight: 500;
      width: 200px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: var(--font-size-caption);

      img {
        width: 16px;
        height: 16px;
      }
    }

    &:last-child {
      margin-bottom: 80px;
    }
  }
  :deep(.van-list__finished-text) {
    margin-bottom: 94px;
  }
}

.bottom {
  z-index: 999;
  position: fixed;
  width: 100%;
  bottom: 0;
  background-color: #ffffff;
  padding: 8px 16px;

  .btn {
    height: 48px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    gap: 4px;
    color: var(--color-primary);
    font-size: var(--font-size-h2);
    border: 1px solid #3366cc;
    border-radius: var(--rounded-md);

    img {
      width: 20px;
      height: 20px;
    }
  }
}
</style>
