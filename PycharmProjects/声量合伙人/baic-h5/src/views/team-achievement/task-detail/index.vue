<script setup lang="ts">
import { getTaskDetailUrl, getTaskDetail } from "@/api/teamAchievement";
import { copyToClipboard } from "@/utils/validate";

defineOptions({
  name: "TaskDetail"
});

const route = useRoute();

const taskDetailList = ref({
  unReceiveNum: 0,
  receiveNum: 0,
  submitNum: 0,
  finishNum: 0,
  taskUserNum: 0,
  memberList: [
    // {
    //   employeeName: "",
    //   status: 0
    // }
  ],
  total: 0
});

const loading = ref(false);
const finished = ref(false);

const taskDetail = ref(
  route.query.taskDetail ? JSON.parse(route.query.taskDetail as string) : null
);
console.log("taskDetail", taskDetail.value);
const pageNum = ref(1);
const pageSize = ref(20);

// 获取任务完成详情数据
const getTaskDetailFun = async () => {
  try {
    const { code, data } = await getTaskDetail({
      deptId: taskDetail.value.deptId,
      taskId: taskDetail.value.taskId,
      dateStr: taskDetail.value.dateStr,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    });
    if (code === 200) {
      taskDetailList.value = {
        unReceiveNum: data.unReceiveNum,
        receiveNum: data.receiveNum,
        submitNum: data.submitNum,
        finishNum: data.finishNum,
        taskUserNum: data.taskUserNum,
        memberList: [...taskDetailList.value.memberList, ...data.memberList],
        total: data.total
      };
      if (
        taskDetailList.value.memberList.length >= taskDetailList.value.total
      ) {
        finished.value = true;
      } else {
        pageNum.value++;
      }
      loading.value = false;
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
    loading.value = false;
    finished.value = true;
  }
};

const updateData = async () => {
  if (taskDetail.value) {
    getTaskDetailFun();
  } else {
    taskDetailList.value = {
      unReceiveNum: 0,
      receiveNum: 0,
      submitNum: 0,
      finishNum: 0,
      taskUserNum: 0,
      memberList: [],
      total: 0
    };
  }
};

// 获取复制链接
const getTaskDetailUrlFun = async () => {
  showLoadingToast({
    message: "加载中...",
    duration: 0,
    forbidClick: true
  });
  const clipboardItemOut = new ClipboardItem({
    "text/plain": getTaskDetailUrl({
      deptId: taskDetail.value.deptId,
      taskId: taskDetail.value.taskId,
      deptName: taskDetail.value.departmentName,
      taskName: taskDetail.value.taskName
    }).then(data => {
      console.log("data", data);
      let text = `${data.data}`;
      console.log("text", text);
      return new Blob([text], { type: "text/plain" });
    })
  });
  await navigator.clipboard.write([clipboardItemOut]);
  showToast("复制成功");
  // showLoadingToast({
  //   message: "加载中...",
  //   duration: 0,
  //   forbidClick: true
  // });
  // try {
  //   const { data } = await getTaskDetailUrl({
  //     deptId: taskDetail.value.deptId,
  //     taskId: taskDetail.value.taskId,
  //     deptName: taskDetail.value.departmentName,
  //     taskName: taskDetail.value.taskName
  //   });
  //   console.log(data);
  //   setTimeout(() => {
  //     console.log(111);
  //     copyToClipboard(data);
  //   }, 50);
  // } catch (error) {
  //   showFailToast(error?.msg || "请求失败");
  // }
};

// 监听路由变化
watch(
  () => route.query.taskDetail,
  newTaskDetail => {
    taskDetail.value = newTaskDetail
      ? JSON.parse(newTaskDetail as string)
      : null;
    updateData();
  }
);
onMounted(() => {
  updateData();
});
</script>

<template>
  <div style="padding: 12px">
    <div class="info-box">
      <div class="department">
        <img src="@/assets/team-achievement/team-fill.png" alt="" />
        <div class="title">{{ taskDetail?.departmentName }}</div>
      </div>
      <div class="people">
        任务覆盖人数：<span>{{ taskDetailList.taskUserNum || 0 }}</span
        >人
      </div>
      <div class="info-container">
        <div class="info-item">
          <div class="item-value">{{ taskDetailList.unReceiveNum || 0 }}</div>
          <div class="item-label">已触达</div>
        </div>
        <div class="info-item">
          <div class="item-value">{{ taskDetailList.receiveNum || 0 }}</div>
          <div class="item-label">已领取</div>
        </div>
        <div class="info-item">
          <div class="item-value">{{ taskDetailList.submitNum || 0 }}</div>
          <div class="item-label">已提交</div>
        </div>
        <div class="info-item">
          <div class="item-value">{{ taskDetailList.finishNum || 0 }}</div>
          <div class="item-label">已完成</div>
        </div>
      </div>
    </div>
    <Empty
      v-if="
        !taskDetailList ||
        !taskDetailList.memberList ||
        !taskDetailList.memberList.length
      "
      icon="empty"
      text="暂无数据"
      marginTop="12"
      marginBottom="12"
      background-color="var(--color-block-background)"
    />
    <div v-else class="list">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="updateData"
      >
        <div
          v-for="(item, index) in taskDetailList.memberList"
          :key="index"
          class="list-item"
        >
          <div class="left">{{ item.employeeName }}</div>
          <div class="right">
            <div
              v-if="item.status !== 0"
              style="display: flex; align-items: center; gap: 4px"
            >
              <img
                src="@/assets/team-achievement/checkbox-circle-success.png"
                alt=""
              />
              <div>已领取</div>
            </div>
            <div v-else style="display: flex; align-items: center; gap: 4px">
              <img src="@/assets/team-achievement/close-circle.png" alt="" />
              <div>未领取</div>
            </div>
            <div
              v-if="item.status === 2 || item.status === 3"
              style="display: flex; align-items: center; gap: 4px"
            >
              <img
                src="@/assets/team-achievement/checkbox-circle-success.png"
                alt=""
              />
              <div>已提交</div>
            </div>
            <div v-else style="display: flex; align-items: center; gap: 4px">
              <img src="@/assets/team-achievement/close-circle.png" alt="" />
              <div>未提交</div>
            </div>
            <div
              v-if="item.status === 3"
              style="display: flex; align-items: center; gap: 4px"
            >
              <img
                src="@/assets/team-achievement/checkbox-circle-success.png"
                alt=""
              />
              <div>已完成</div>
            </div>
            <div v-else style="display: flex; align-items: center; gap: 4px">
              <img src="@/assets/team-achievement/close-circle.png" alt="" />
              <div>未完成</div>
            </div>
          </div>
        </div>
      </van-list>
    </div>
  </div>
  <div class="bottom">
    <div class="btn" @click="getTaskDetailUrlFun">
      <img src="@/assets/team-achievement/file-copy.png" alt="" />
      <div>复制链接</div>
    </div>
    <div class="van-safe-area-bottom" style="background: #fff" />
  </div>
</template>

<style scoped lang="less">
.info-box {
  height: 147px;
  padding: 12px;
  border-radius: var(--rounded-md);
  background: linear-gradient(117deg, #dce1eb 28%, #f2f3f7 100%);
  box-sizing: border-box;
  // border: 0.5px solid;
  // border-image: linear-gradient(117deg, #f2f3f7 28%, #dce1eb 100%) 0.4;

  .department {
    font-weight: 500;
    font-size: var(--font-size-h1);
    display: flex;
    align-items: center;
    gap: 4px;

    img {
      width: 20px;
      height: 20px;
    }
    .title {
      width: 100%;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }
  }

  .people {
    padding-left: 24px;
    margin-top: 4px;
    line-height: 18px;
    font-size: var(--font-size-caption);
    color: var(--text-describe);

    span {
      color: #3366cc;
    }
  }

  .info-container {
    padding-top: 24px;
    padding-left: 25px;
    padding-right: 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .info-item {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 4px;
      padding: 4px;

      .item-value {
        font-weight: bold;
        color: var(--text-primary);
        font-size: 20px;
      }

      .item-label {
        color: var(--text-describe);
        font-size: var(--font-size-caption);
      }
    }
  }
}

.list {
  margin-top: 12px;

  .list-item {
    height: 46px;
    border-radius: var(--rounded-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    margin-top: 8px;
    background: #ffffff;
    position: relative;

    .left {
      width: 100px;
      font-size: var(--font-size-h2);
      font-weight: 500;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      word-break: break-all;
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: var(--font-size-caption);

      img {
        width: 16px;
        height: 16px;
      }
    }

    &:last-child {
      margin-bottom: 80px;
    }
  }
  :deep(.van-list__finished-text) {
    margin-bottom: 94px;
  }
}

.bottom {
  z-index: 999;
  position: fixed;
  width: 100%;
  bottom: 0;
  background-color: #ffffff;
  padding: 8px 16px;

  .btn {
    height: 48px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    gap: 4px;
    color: var(--color-primary);
    font-size: var(--font-size-h2);
    border: 1px solid #3366cc;
    border-radius: var(--rounded-md);

    img {
      width: 20px;
      height: 20px;
    }
  }
}
</style>
