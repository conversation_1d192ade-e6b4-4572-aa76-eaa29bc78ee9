<!-- 媒体绑定详情 -->
<script setup lang="ts">
import { getBindDetail } from "@/api/teamAchievement";

defineOptions({
  name: "MediaDetail"
});
const list = ref([]);

const loading = ref(false);
const finished = ref(false);
const route = useRoute();
const showMediumNamePicker = ref(false);
const mediumNameColumns = ref([]);
const choiceMedium = ref("");

const mediumData = ref(
  route.query.mediumData ? JSON.parse(route.query.mediumData as string) : null
);
// console.log(mediumData.value);

const mediumDetailData = ref({
  bindNum: 0,
  unBindNum: 0,
  bindVoList: [
    // {
    //   employeeName: "",
    //   status: 0
    // }
  ],
  total: 0
});

const pageNum = ref(1);
const pageSize = ref(20);

const getBindDetailList = async (deptId: number, mediumId: number) => {
  // console.log(deptId, mediumId);
  try {
    const { data } = await getBindDetail({
      deptId,
      mediumId,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    });
    mediumDetailData.value = {
      bindNum: data.bindNum,
      unBindNum: data.unBindNum,
      bindVoList: [...mediumDetailData.value.bindVoList, ...data.bindVoList],
      total: data.total
    };
    if (
      mediumDetailData.value.bindVoList.length >= mediumDetailData.value.total
    ) {
      finished.value = true;
    } else {
      pageNum.value++;
    }
    loading.value = false;
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
    loading.value = false;
    finished.value = true;
  }
};

const updateData = async () => {
  // console.log(mediumData.value);
  if (mediumData.value) {
    getBindDetailList(
      mediumData.value.departmentId,
      mediumData.value.choiceMedium.id
    );
    // 提取 id 和 platformName 并赋值给 mediumNameColumns.value
    mediumNameColumns.value = mediumData.value.mediumList.map(item => ({
      value: item.id,
      text: item.platformName
    }));
    choiceMedium.value = mediumData.value.choiceMedium.platformName;
  } else {
    mediumDetailData.value = {
      bindNum: 0,
      unBindNum: 0,
      bindVoList: [],
      total: 0
    };
    mediumNameColumns.value = [];
    choiceMedium.value = "";
  }
};

const onMediumNameConfirm = ({ selectedOptions }) => {
  showMediumNamePicker.value = false;
  choiceMedium.value = selectedOptions[0].text;
  console.log(choiceMedium.value);
  mediumDetailData.value = {
    bindNum: 0,
    unBindNum: 0,
    bindVoList: [],
    total: 0
  };
  pageNum.value = 1;
  mediumData.value.choiceMedium.id = selectedOptions[0].value;
  getBindDetailList(mediumData.value.departmentId, selectedOptions[0].value);
};

// 监听路由变化
watch(
  () => route.query.mediumData,
  newMediumData => {
    mediumData.value = newMediumData
      ? JSON.parse(newMediumData as string)
      : null;
    pageNum.value = 1;
    updateData();
  }
);
onMounted(() => {
  updateData();
});
</script>

<template>
  <div style="padding: 12px">
    <div class="info-box">
      <div class="department">
        <img src="@/assets/team-achievement/team-fill.png" alt="" />
        <div class="title">{{ mediumData?.departmentName }}</div>
      </div>
      <div class="info-container">
        <div class="info-item">
          <div class="item-value">{{ mediumDetailData.bindNum || 0 }}</div>
          <div class="item-label">已绑定</div>
        </div>
        <div class="info-item">
          <div class="item-value">{{ mediumDetailData.unBindNum || 0 }}</div>
          <div class="item-label">未绑定</div>
        </div>
      </div>
    </div>
    <div class="pick-box">
      <van-field
        v-model="choiceMedium"
        is-link
        center
        :border="false"
        readonly
        label=""
        arrow-direction="down"
        placeholder="选择媒体"
        @click="showMediumNamePicker = true"
      />
    </div>
    <Empty
      v-if="!mediumDetailData.bindVoList || !mediumDetailData.bindVoList.length"
      icon="empty"
      text="暂无数据"
      marginTop="12"
      marginBottom="12"
      background-color="var(--color-block-background)"
    />
    <div v-else class="list">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="updateData"
      >
        <div
          v-for="(item, index) in mediumDetailData.bindVoList"
          :key="index"
          class="list-item"
        >
          <div class="left">{{ item.employeeName }}</div>
          <div class="right">
            <div
              v-if="item.status"
              style="display: flex; align-items: center; gap: 4px"
            >
              <img
                src="@/assets/team-achievement/checkbox-circle-success.png"
                alt=""
              />
              <div>已绑定</div>
            </div>
            <div v-else style="display: flex; align-items: center; gap: 4px">
              <img src="@/assets/team-achievement/close-circle.png" alt="" />
              <div>未绑定</div>
            </div>
          </div>
        </div>
      </van-list>
    </div>
    <van-popup v-model:show="showMediumNamePicker" round position="bottom">
      <van-picker
        title="选择媒体"
        :columns="mediumNameColumns"
        @cancel="showMediumNamePicker = false"
        @confirm="onMediumNameConfirm"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="less">
.info-box {
  height: 125px;
  padding: 12px;
  border-radius: var(--rounded-md);
  background: linear-gradient(117deg, #dce1eb 28%, #f2f3f7 100%);
  box-sizing: border-box;
  // border: 0.5px solid;
  // border-image: linear-gradient(117deg, #f2f3f7 28%, #dce1eb 100%) 0.5;

  .department {
    height: 22px;
    line-height: 25px;
    font-weight: 500;
    font-size: var(--font-size-h1);
    display: flex;
    align-items: center;
    gap: 4px;

    img {
      width: 20px;
      height: 20px;
    }
    .title {
      width: 100%;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }
  }

  .people {
    padding-left: 24px;
    padding-top: 4px;
    font-size: var(--font-size-caption);
    color: var(--text-describe);

    span {
      color: #3366cc;
    }
  }

  .info-container {
    width: 327px;
    padding-top: 24px;
    // padding-left: 28px;
    // padding-right: 28px;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .info-item {
      width: 135.5px;
      height: 52px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 4px;

      .item-value {
        font-weight: bold;
        color: var(--text-primary);
        font-size: 20px;
      }

      .item-label {
        color: var(--text-describe);
        font-size: var(--font-size-caption);
      }
    }
  }
}
.pick-box {
  width: 160px;
  height: 36px;
  margin: 12px 0 12px 0;
  line-height: 36px;

  :deep(.van-cell) {
    width: 159.5px;
    border-radius: var(--rounded-md);
    line-height: 22px;
    padding: 6px 8px 8px;
    height: 36px;
  }

  :deep(.van-icon) {
    padding-right: 8px;
  }
}

.list {
  margin-top: 12px;

  .list-item {
    border-radius: var(--rounded-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    margin-top: 8px;
    background: #ffffff;
    height: 46px;
    line-height: 18px;

    .left {
      font-size: var(--font-size-h2);
      font-weight: 500;
      width: 200px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: var(--font-size-caption);

      img {
        width: 16px;
        height: 16px;
      }
    }

    &:last-child {
      margin-bottom: 80px;
    }
  }
  :deep(.van-list__finished-text) {
    margin-bottom: 94px;
  }
}
:deep(.van-picker__title) {
  font-weight: 500;
}
</style>
