<script setup lang="ts">
import { getNotice } from "@/api/home";

// 类型定义
interface NoticeItem {
  id: string;
  title: string;
  details: string;
  createTime: string;
}

defineOptions({
  name: "announcement"
});

const route = useRoute();
const loading = ref(true);
const finished = ref(true);
const isRefreshing = ref(false);
const notices = ref<NoticeItem[]>([]); // 使用明确的类型接口

/* 数据获取逻辑 */
const fetchNotices = async (isRefresh = false) => {
  try {
    const res = await getNotice();
    notices.value = res.data; // 统一数据赋值逻辑
    await nextTick(handleHashScroll); // 简化 nextTick 使用
  } catch (error) {
    handleError(error);
  } finally {
    loading.value = false;
    isRefreshing.value = false;
  }
};

/* 错误处理 */
const handleError = (error: unknown) => {
  console.error("接口请求异常:", error);
};

/* 下拉刷新处理 */
const handleRefresh = () => {
  isRefreshing.value = true;
  fetchNotices(true);
};

/* 哈希路由滚动定位 */
const handleHashScroll = () => {
  if (!route.hash) return;

  const targetElement = document.getElementById(route.hash.slice(1));
  targetElement?.scrollIntoView({
    behavior: "smooth",
    block: "start"
  });
};

// 监听路由变化
watch(() => route.hash, handleHashScroll);

// 生命周期
onMounted(fetchNotices);
</script>

<template>
  <div class="content-container">
    <van-pull-refresh
      v-model="isRefreshing"
      success-text="刷新成功"
      @refresh="handleRefresh"
    >
      <!-- 数据加载中 -->
      <BaseLoadingIndicator
        v-if="loading"
        class="loading-container"
        text="公告加载中..."
      />
      <!-- 数据加载完成 -->
      <div v-else>
        <van-list
          v-if="notices.length"
          v-model:loading="loading"
          :finished="finished"
          finished-text="已经到底了~"
        >
          <article
            v-for="item in notices"
            :id="item.id"
            :key="item.id"
            class="notice-card"
          >
            <header class="notice-header">
              <img
                src="@/assets/announcement/volume-down-fill.png"
                alt="公告图标"
              />
              <h2 class="notice-title">{{ item.title }}</h2>
            </header>

            <section
              v-if="item.details"
              class="notice-content"
              v-html="item.details"
            />

            <footer class="notice-time">
              {{ item.createTime }}
            </footer>
          </article>
        </van-list>
        <Empty
          v-else
          icon="empty"
          text="暂无公告"
          marginBottom="12"
          background-color="var(--color-block-background)"
        />
      </div>
    </van-pull-refresh>
  </div>
</template>

<style lang="less">
.content-container {
  min-height: 100vh;
  padding: 12px;
}

.notice-card {
  padding: 12px;
  margin-bottom: 12px;
  border-radius: var(--rounded-md);
  background-color: var(--color-block-background);
}

.notice-header {
  display: flex;
  align-items: center;
  img {
    width: 32px;
    height: 32px;
  }
}

.notice-title {
  font-size: var(--font-size-h2);
  color: var(--text-highlight);
  font-weight: 500;
  margin-left: 8px;
}

.notice-content {
  font-size: var(--font-size-h3);
  color: var(--text-secondary);
  line-height: 22px;
  margin: 8px 0 14px;
  padding-bottom: 14px;
  border-bottom: 1px solid #f0f0f0;
}

.notice-time {
  font-size: var(--font-size-caption);
  color: rgba(0, 0, 0, 0.45);
}
.loading-container {
  height: calc(100vh - 24px); /* 占据整个视口高度 */
}
</style>
