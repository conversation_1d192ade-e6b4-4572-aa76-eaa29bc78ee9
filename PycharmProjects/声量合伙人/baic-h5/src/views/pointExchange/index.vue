<template>
  <div class="point-exchange min-h-screen">
    <div class="tabs">
      <van-sticky>
        <ul class="tab-list">
          <li
            v-for="(item, index) in tabList"
            :key="index"
            :class="{ active: active === index }"
            @click="handleTabSelect(index)"
          >
            {{ item.title }}
          </li>
        </ul>
      </van-sticky>
    </div>
    <div class="exchange-panel">
      <span class="point-surplus"
        >您当前在北汽集团BAIC平台的积分余额为：
        <span class="highlight-point">{{ pointSurplus }}</span>
        积分</span
      >
      <div class="tip-content">
        <div class="flex items-center">
          <img class="tip-icon" :src="tipIcon" alt="" />
          <span class="tip-title">约合</span>
        </div>
        <span class="equal-num">
          <span class="highlight-equal">{{ equalNum }}</span
          >元</span
        >
      </div>
      <div class="tip-content">
        <div class="flex items-center">
          <img class="tip-icon" :src="tipIcon" alt="" />
          <span class="tip-title">本次兑换</span>
        </div>
        <div class="flex items-center">
          <van-field
            v-model="exchangeNum"
            class="point-input"
            type="digit"
            :formatter="formatter"
            :border="false"
          />
          <span class="point-unit">积分</span>
        </div>
      </div>
      <img class="point-icon" :src="pointIcon" alt="" />
    </div>
    <div class="fixed bottom-0 left-0 w-full">
      <van-field>
        <template #input>
          <van-radio-group direction="horizontal" @click="onChangePrivacy">
            <van-radio :name="true" class="radio-text" :checked="checked"
              >已阅读并同意<span class="privacy-text" @click.stop="service"
                >《用户服务协议》</span
              >及<span class="privacy-text" @click.stop="privacy"
                >《隐私条款》</span
              ></van-radio
            >
          </van-radio-group>
        </template>
      </van-field>
      <div class="btn-box">
        <van-button class="btn-left" size="large" block @click="backHome">
          去首页
        </van-button>
        <van-button size="large" block type="primary" @click="submitExchange">
          提交
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import pointIcon from "@/assets/pointExchange/point-icon.png";
import tipIcon from "@/assets/pointExchange/tip-icon.png";

defineOptions({
  name: "PointExchange"
});
const router = useRouter();
const tabList = [
  { title: "任务积分奖励", key: 0 },
  { title: "销售转化积分", key: 1 }
];
const active = ref<number>(0); //tab-key
const pointSurplus = ref<number>(2000); //积分余额
const equalNum = ref<number>(200); //积分价值
const exchangeNum = ref<number>(); //本次兑换积分数
const checked = ref(false); //是否同意隐私条款

// tab栏
const handleTabSelect = (index: number) => {
  active.value = index;
};

const onChangePrivacy = (event: any) => {
  // 切换选中状态
  checked.value = !checked.value;
};

// 格式化输入内容，只允许输入正整数，并且不超过积分余额
const formatter = (value: any) => {
  // 只保留数字部分
  let formatted = value.replace(/\D/g, "");
  // 去除前导零
  formatted = formatted.replace(/^0+/, "");
  // 限制输入的最大值为积分余额
  if (parseInt(formatted) > pointSurplus.value) {
    formatted = pointSurplus.value.toString();
  }
  return formatted;
};

const service = () => {
  console.log("用户服务协议");
  // router.push({ path: "my" });
};

const privacy = () => {
  console.log("隐私协议");
  // router.push({ path: "my" });
};

const backHome = () => {
  router.push({ path: "/" });
};

const submitExchange = () => {
  console.log(exchangeNum.value);
  if (!checked.value) {
    showToast("请先阅读并同意《用户服务协议》及《隐私条款》");
    return;
  }
  console.log("提交兑换");
};
</script>

<style lang="less" scoped>
.point-exchange {
  font-size: var(--font-size-h3);
  color: var(--text-primary);
  height: 42px;
  background-color: var(--color-block-background);
  display: flex;
  flex-direction: column;
  align-items: center;

  .tabs {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    line-height: 30px;
  }

  .tab-list {
    background-color: var(--color-block-background);
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    padding: 12px 12px 0;
    justify-content: space-around;
    // border-bottom: 2px solid #ccc;
    font-size: var(--font-size-h2);
    position: relative;
    z-index: 3;

    li {
      // padding: 0px 10px 4px;
      cursor: pointer;
      color: var(--text-describe);
    }

    li.active {
      font-weight: 500;
      color: var(--text-primary);
      border-bottom: 2px solid var(--color-primary);
    }
  }

  .exchange-panel {
    background-color: #f3f6f9;
    width: 351px;
    height: 264px;
    margin-top: 12px;
    position: relative;
    display: flex;
    align-content: flex-start;
    flex-wrap: wrap;
    border-radius: var(--rounded-xl);

    .point-surplus {
      font-size: var(--font-size-h1);
      color: var(--text-primary);
      width: 303px;
      height: 56px;
      font-weight: 500;
      line-height: 28px;
      margin: 24px 24px 0 24px;

      .highlight-point {
        color: var(--color-primary);
      }
    }

    .tip-content {
      width: 100%;
      margin: 24px 24px 0 24px;

      .tip-icon {
        width: 4px;
        height: 14px;
        margin-right: 12px;
      }

      .tip-title {
        font-size: var(--font-size-h3);
        color: var(--text-primary);
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0em;
      }

      .equal-num {
        font-size: var(--font-size-h1);
        color: var(--text-primary);
        width: auto;
        height: 56px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0em;
        margin: 4px 16px 0 16px;

        .highlight-equal {
          color: var(--color-primary);
        }
      }

      .point-input {
        border-radius: var(--rounded-md);
        width: 160px;
        height: 36px;
        font-size: var(--font-size-h3);
        margin: 4px 8px 0 16px;
        line-height: var(--font-size-h3);
      }

      .point-unit {
        font-size: var(--font-size-h3);
        color: var(--text-primary);
        font-weight: normal;
        line-height: 22px;
        text-transform: uppercase;
        letter-spacing: 0em;
      }
    }

    .point-icon {
      width: 116px;
      height: 150px;
      position: absolute;
      top: 106px;
      left: 235px;
    }
  }

  .radio-text {
    font-size: var(--font-size-h3);
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0em;
  }

  .privacy-text {
    color: var(--text-highlight);
  }

  .btn-box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    // position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding: 8px 12px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 8px);
    border-width: 0.5px 0px 0px 0px;
    border-style: solid;
    border-color: #ececec;
  }

  .btn-left {
    margin-right: 12px;
  }
}
</style>
