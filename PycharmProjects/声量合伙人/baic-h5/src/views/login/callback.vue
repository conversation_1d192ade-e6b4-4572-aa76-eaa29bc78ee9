<script setup lang="ts">
import { getSmsCode, login as loginApi } from "@/api/login";
import { validPhone } from "@/utils/validate";
import { useAuthStore } from "@/store/modules/auth";

defineOptions({
  name: "LoginCallback"
});

const authStore = useAuthStore();
const router = useRouter();

// 表单数据
const phone = ref("");
const smsCode = ref("");
const countdown = ref(0);
const isSmsCodeSent = ref(false); // 用于跟踪验证码是否已发送
let countdownTimer: number | null = null;

/**
 * 发送验证码
 */
const sendSmsCode = async () => {
  if (!phone.value) {
    showToast("手机号不能为空");
    return;
  }
  if (!validPhone(phone.value)) {
    showToast("手机号格式不正确");
    return;
  }

  try {
    // 调用短信接口
    await getSmsCode({ phonenumber: phone.value });
    showToast("验证码已发送");
    isSmsCodeSent.value = true; // 标记验证码已发送
    startCountdown();
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};

/**
 * 开始倒计时
 */
const startCountdown = () => {
  countdown.value = 60;
  countdownTimer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer!);
      countdownTimer = null;
    }
  }, 1000) as unknown as number; // 强制类型转换;
};

/**
 * 处理登录
 */
const handleLogin = async () => {
  if (!phone.value) {
    showToast("手机号不能为空");
    return;
  }
  if (!validPhone(phone.value)) {
    showToast("手机号格式不正确");
    return;
  }
  if (!smsCode.value) {
    showToast("请输入验证码");
    return;
  }

  showToast("登录中...");
  try {
    // 调用登录接口
    const { data } = await loginApi({
      clientId: import.meta.env.VITE_APP_CLIENT_ID,
      grantType: "portal",
      phonenumber: phone.value,
      smsCode: smsCode.value,
      nickName: authStore.wxInfo?.nickname,
      openId: authStore.wxInfo?.openid,
      avatar: authStore.wxInfo?.headImgUrl
    });
    // 保存 token 并重置用户信息
    authStore.setToken({ token: data.access_token });
    authStore.resetWx();

    showToast("登录成功");
    router.replace({ name: "Home" });
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};
</script>

<template>
  <div class="login-wrap van-safe-area-bottom">
    <img
      class="login-logo block flex-none"
      alt="login logo"
      src="@/assets/login/login_logo.png"
    />
    <div class="content-wrap">
      <div class="title">北汽集团员工</div>
      <van-form
        class="form-wrap flex-1 flex flex-col justify-between"
        @submit="handleLogin"
      >
        <div>
          <!-- 手机号输入 -->
          <van-field
            v-model="phone"
            label="手机号"
            label-align="top"
            name="手机号"
            placeholder="请输入手机号"
          >
            <template #button />
          </van-field>

          <!-- 验证码输入 -->
          <van-field
            v-model="smsCode"
            label="验证码"
            label-align="top"
            name="验证码"
            placeholder="请输入验证码"
          >
            <template #button>
              <button
                class="sms-btn"
                :disabled="countdown > 0"
                @click.prevent="sendSmsCode"
              >
                {{ countdown > 0 ? `${countdown}s后重试` : "获取验证码" }}
              </button>
            </template>
          </van-field>
          <van-field />
        </div>
        <!-- 登录按钮 -->
        <div>
          <van-button
            size="large"
            block
            type="primary"
            native-type="submit"
            :disabled="!isSmsCodeSent"
          >
            登录
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<style scoped lang="less">
.login-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-block-background);
}

.login-logo {
  width: 375px;
  height: 276px;
}

.content-wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  z-index: 99;
  background-color: var(--color-block-background);
  padding: 16px;
  margin-top: -12px;
  border-radius: var(--rounded-md) var(--rounded-md) 0 0;

  .form-wrap {
    margin-top: 6px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

.title {
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  text-transform: uppercase;
  letter-spacing: 0em;
  color: var(--text-primary);

  &::after {
    content: "";
    display: block;
    margin: 4px 0 0 16px;
    width: 52px;
    height: 4px;
    border-radius: 1px;
    opacity: 1;
    background: var(--color-primary);
  }
}

.sms-btn {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  display: flex;
  align-items: flex-end;
  letter-spacing: 0em;
  color: var(--color-primary);
}

:deep(.van-field) {
  padding: 24px 0 12px 0;
  line-height: 18px;
}

:deep(.van-cell:after) {
  left: 0;
  right: 0;
}
</style>
