<template>
  <div class="page-404">
    <h3 class="error-code">404</h3>
    <p class="error-message">页面不存在</p>
    <van-button type="primary" @click="goHome">返回首页</van-button>
  </div>
</template>

<script setup>
defineOptions({
  name: "404"
});

const router = useRouter();
const goHome = () => {
  router.replace("/");
};
</script>

<style scoped>
.page-404 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
}

.error-code {
  font-size: 80px;
  color: var(--color-primary);
  margin: 0;
}

.error-message {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 20px 0 40px;
}
</style>
