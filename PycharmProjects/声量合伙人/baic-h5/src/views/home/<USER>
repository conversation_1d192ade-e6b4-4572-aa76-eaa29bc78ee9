<!-- 首页 -->
<script setup lang="ts">
import {
  getNotice,
  saveVisitRecord,
  getAds,
  getArticManagementList,
  saveArticManagement
} from "@/api/home";
import contentCenter from "@/assets/home/<USER>";
import arrowRightIcon from "@/assets/home/<USER>";
import timeIcon from "@/assets/home/<USER>";
import { isExternal } from "@/utils/validate";
import setPageTitle from "@/utils/set-page-title";
// import useWechat from "@/composables/useWechat";
// import { useLaunchWeapp } from "@/composables/useLaunchWeapp";

defineOptions({
  name: "Home"
});
const router = useRouter();

// const appid = import.meta.env.VITE_APP_TRANSFER_WX_APP_APPID; // 小程序的 appid
// const envVersion = import.meta.env.VITE_APP_TRANSFER_WX_APP_ENVVERSION; // 小程序版本
// const path = ref("/pages/home/<USER>"); // 用于存储接口返回的 path
// const scriptContent = ref(`
//   <style>
//     * {
//         -webkit-tap-highlight-color: transparent;
//         -webkit-touch-callout: none;
//       }
//     .btn {
//       width: 100px;
//       height: 100px;
//       background-color: #fff;
//       -webkit-tap-highlight-color: transparent; /* 去除移动端点击高亮 */
//       outline: none;
//       user-select: none;
//       opacity: 0;
//     }
//     .btn:active {
//       background-color: #fff; /* 保持点击时背景色不变 */
//     }
//   </style>
//   <div class="btn"></div>
// `);
// const { wxTag } = useLaunchWeapp(appid, path, envVersion, scriptContent);

// 按需初始化微信SDK
// const { isReady } = useWechat([], ["wx-open-launch-weapp"]);

// // 当SDK就绪后配置分享
// watch([isReady], ([ready]) => {
//   if (ready) {
//     path.value = `/pages/home/<USER>
//   }
// });

// 新闻列表
const newsList = ref([
  {
    title: "",
    id: ""
  }
]);
//广告列表
const bannerImages = ref([
  {
    imgUrl: "",
    toUrl: "",
    id: "",
    createTime: "",
    name: ""
  }
]);
//文章列表
const articleList = ref([]);
// 获取公告列表
const getNoticeList = async () => {
  const res: any = await getNotice();
  newsList.value = res.data;
};
// 获取广告列表
const getAdsList = async () => {
  const res: any = await getAds();
  bannerImages.value = res.rows;
  // console.log(res);
};
// 跳转公告列表页面
const goToPage = (ids: string) => {
  router.push({
    path: "/announcement",
    hash: `#${ids}`
    // hash: "#1896481783600029697"
  });
};
// 保存浏览公告访问记录
const saveRecord = async (noticeId: string) => {
  try {
    const { code, data } = await saveVisitRecord({
      noticeId
    });
    if (code === 200) {
      goToPage(noticeId);
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};
const goHref = (url: string) => {
  if (url) {
    setPageTitle("\u200E");
    window.location.href = url;
  }
};

// 快捷菜单跳转
const goToMenuPage = (item: any) => {
  if (item.state == 0) {
    showToast("敬请期待");
    return;
  }
  router.push({
    path: item.path
  });
};

// 获取文章列表
const getArticList = async (orderType: number) => {
  try {
    const { code, data } = await getArticManagementList({
      orderType
    });
    if (code === 200) {
      articleList.value = data;
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};

// 跳转文章详情
const jumpArticle = async (news: any) => {
  if (!news?.id) {
    console.error("文章 ID 不存在");
    return;
  }

  // 1. 立即执行跳转逻辑（不等待保存记录）
  const handleNavigation = () => {
    if (news.taskStatus === 0) {
      handleExternalLink(news.articleUrl);
    } else {
      handleInternalTask(news.taskId);
    }
  };
  handleNavigation(); // 立即跳转

  // 2. 异步保存浏览记录（不阻塞跳转）
  try {
    await saveArticManagement({ articleId: news.id });
  } catch (error) {
    console.error("保存浏览记录失败:", error);
    // 失败不影响主流程
  }
};

// 处理外部链接跳转（支持微信环境）
const handleExternalLink = (url: string) => {
  // 校验 URL 合法性
  if (!isExternal(url)) {
    console.error("链接地址无效");
    return;
  }

  // 微信环境特殊处理
  // if (isWechatBrowser()) {
  //   showManualRedirectDialog(url);
  // } else {
  handleStandardRedirect(url);
  // }
};

// 处理标准跳转（非微信环境）
const handleStandardRedirect = (url: string) => {
  const newWindow = window.open(url, "_blank");
  console.log("newWindow", newWindow);
  if (
    !newWindow ||
    newWindow.closed ||
    typeof newWindow.closed === "undefined"
  ) {
    // showManualRedirectDialog(url);
    setPageTitle("\u200E");
    window.location.href = url;
  }
};

// 显示手动跳转提示（使用 Vant 组件）
const showManualRedirectDialog = (url: string) => {
  showDialog({
    message: "是否打开文章？",
    showCancelButton: true
  })
    .then(() => {
      // 用户点击“打开”
      console.log("用户跳转");
      setPageTitle("\u200E");
      window.location.href = url;
    })
    .catch(() => {
      // 用户点击“取消”
      console.log("用户取消跳转");
    });
};

// 处理内部任务跳转
const handleInternalTask = (taskId: string) => {
  if (!taskId) {
    console.error("任务 ID 不存在");
    return;
  }
  router.push({
    path: "/task/doTask",
    query: { id: taskId }
  });
};

const tabList = [
  { title: "最新", key: 1 },
  { title: "最热", key: 2 }
];
const active = ref<number>(1); //tab-key
// tab栏
const handleTabSelect = (index: number) => {
  active.value = index;
  getArticList(active.value);
};

// 计算属性，处理时间格式
const formatCreateTime = computed(() => {
  return (time: string | undefined) => {
    if (time) {
      return time.split(" ")[0];
    }
    return ""; // 如果 time 为 undefined 或者空字符串，返回空字符串
  };
});

onMounted(() => {
  getNoticeList();
  getAdsList();
  getArticList(active.value);
});
</script>
<template>
  <div class="home-box">
    <!-- 轮播图 Banner -->
    <div class="home-img-box">
      <van-swipe
        class="mx-auto home-img"
        :autoplay="3000"
        :show-indicators="false"
      >
        <van-swipe-item
          v-for="(item, index) in bannerImages"
          :key="index"
          class="banner-swiper"
          @click="goHref(item.toUrl)"
        >
          <img v-if="item.imgUrl" :src="item.imgUrl" class="full-image" />
        </van-swipe-item>
      </van-swipe>
    </div>

    <!-- 公告栏 -->
    <div class="home-notice-box">
      <van-notice-bar
        class="notice-bar"
        background="#fff"
        color="#000000"
        left-icon="volume-o"
        right-icon="arrow-right-o"
        :scrollable="false"
      >
        <!-- 自定义左侧图标 -->
        <template #left-icon>
          <svg-icon name="home-ad" class="notice-icon" />
          <!-- 公告文本 -->
          <div class="notice-tip">公告</div>
        </template>
        <!-- 滚动公告内容 -->
        <van-swipe
          vertical
          class="h-[42px]"
          :autoplay="3000"
          :touchable="false"
          :show-indicators="false"
        >
          <van-swipe-item
            v-for="(news, index) in newsList"
            :key="index"
            class="notice-text"
            @click="saveRecord(news.id)"
          >
            {{ news.title }}
          </van-swipe-item>
        </van-swipe>
        <template #right-icon>
          <img
            :src="arrowRightIcon"
            alt="volume"
            class="notice-icon-right"
            @click="router.push({ name: 'Announcement' })"
          />
        </template>
      </van-notice-bar>
    </div>

    <!-- 快捷菜单 -->
    <div class="menu-box">
      <div class="menu-list">
        <div
          class="menu-item"
          @click="goToMenuPage({ path: 'rank', state: 1 })"
        >
          <svg-icon name="home-trophy" class="menu-icon" />
          <span class="menu-text">排行榜</span>
        </div>
        <div
          class="menu-item"
          @click="goToMenuPage({ path: 'driveAppointment', state: 1 })"
        >
          <img
            class="menu-icon"
            src="http://prod-baic.oss-cn-beijing.aliyuncs.com/2025/04/22/39c9770f282344a3b93280ada61ef3b2.png"
            alt=""
          />
          <span class="menu-text">邀约试驾</span>
        </div>
        <!-- <div
          class="menu-item"
          @click="goToMenuPage({ path: 'pointExchange', state: 0 })"
        >
          <svg-icon name="home-copper-diamond" class="menu-icon" />
          <span class="menu-text">积分商城</span>
        </div> -->
        <!-- <div
          class="menu-item"
          @click="goToMenuPage({ path: 'pointExchange', state: 0 })"
        >
          <img class="menu-icon" :src="contentCenter" alt="" />
          <span class="menu-text">内容中心</span>
          <div
            ref="wxTag"
            style="
              overflow: hidden;
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
            "
          />
        </div> -->
        <!-- <div
          class="menu-item"
          @click="goToMenuPage({ path: 'media-binding/list', state: 1 })"
        >
          <svg-icon name="home-smartphone" class="menu-icon" />
          <span class="menu-text">绑定媒体</span>
        </div> -->
      </div>
    </div>

    <!-- 原创热文 -->
    <div class="hot-article-box">
      <span class="box-title">原创热文</span>
      <div class="tabs">
        <ul class="tab-list">
          <li
            v-for="item in tabList"
            :key="item.key"
            :class="{ active: active === item.key }"
            @click="handleTabSelect(item.key)"
          >
            {{ item.title }}
            <div v-if="active === item.key" class="active-line" />
          </li>
        </ul>
      </div>
      <!-- 新闻列表 -->
      <div v-if="articleList && articleList.length" class="article-list">
        <div
          v-for="(news, index) in articleList"
          :key="index"
          class="article-item"
          @click="jumpArticle(news)"
        >
          <img :src="news.ossUrl" alt="新闻图片" class="article-img" />
          <div class="article-info">
            <span class="article-title">{{ news.title }}</span>
            <p class="article-date">
              <img :src="timeIcon" alt="" class="time-icon" />
              {{ formatCreateTime(news.createTime) }}
            </p>
          </div>
        </div>
      </div>
      <Empty
        v-else
        icon="empty"
        text="暂无数据"
        marginTop="12"
        marginBottom="12"
        background-color="var(--color-block-background)"
      />
    </div>
  </div>
</template>
<style lang="less" scoped>
.home-box {
  padding-top: 12px;
}
.home-img-box {
  background-color: #fff;
  width: 351px;
  margin: 0 12px;
  margin-bottom: 12px;
  border-radius: var(--rounded-md);
  overflow: hidden;
  .home-img {
    width: 100%;
    height: 176px;
    overflow: hidden;
  }
  .banner-swiper {
    .full-image {
      width: 100%;
      height: 100%;
      object-fit: fill;
    }
  }
}
.home-notice-box {
  margin: 0 12px;
  .notice-bar {
    width: 351px;
    height: 42px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 12px;
    background-color: var(--color-block-background);
    border-radius: var(--rounded-md);
  }
  .notice-icon {
    margin-right: 4px;
    width: 16px;
    height: 16px;
  }
  .notice-tip {
    font-size: var(--font-size-caption);
    font-weight: 500;
    line-height: 18px;
    display: flex;
    align-items: center;
    letter-spacing: 0em;
    color: var(--text-primary);
    margin-right: 8px;
  }
  .notice-text {
    font-size: var(--font-size-caption);
    // font-weight: 500;
    line-height: 18px;
    display: flex;
    align-items: center;
    letter-spacing: 0em;
    color: var(--text-primary);
  }
  .notice-icon-right {
    margin-left: 8px;
    width: 16px;
    height: 16px;
  }
}
.menu-box {
  width: 351px;
  height: 82px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  gap: 16px;
  margin: 12px 12px 0 12px;
  background-color: var(--color-block-background);
  border-radius: var(--rounded-md);
  box-sizing: border-box;
  .menu-list {
    margin-inline: auto;
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    width: 100%;
    gap: 4px;
  }
  .menu-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    // width: 80px;
  }
  .menu-icon {
    width: 32px;
    height: 32px;
  }
  .menu-text {
    font-size: var(--font-size-h3);
    color: var(--text-primary);
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0em;
    margin: 4px 0 0 0;
  }
}
.hot-article-box {
  width: 351px;
  display: flex;
  flex-direction: column;
  margin: 16px 12px 0 12px;
  .box-title {
    font-size: var(--font-size-h2);
    color: var(--text-primary);
    font-weight: 500;
    line-height: 22px;
  }
  .tabs {
    width: 100%;
    max-width: 351px;
    margin: 12px 12px 0 0;
    line-height: 28px;
  }

  .tab-list {
    background: none;
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: flex-start;
    font-size: var(--font-size-h3);
    position: relative;
    z-index: 3;

    li {
      // padding: 0px 10px 4px;
      cursor: pointer;
      color: var(--text-describe);
      margin-right: 24px;
    }

    li.active {
      font-weight: 500;
      color: var(--text-primary);
      // border-bottom: 2px solid var(--color-primary);
    }
    .active-line {
      width: 20px;
      height: 2px;
      background: var(--color-primary);
      margin: auto;
    }
  }
  .article-list {
    width: 100%;
    max-width: 351px;
    margin: 12px 0 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .article-item {
      width: 100%;
      height: 96px;
      display: flex;
      position: relative;
      background-color: var(--color-block-background);
      border-radius: var(--rounded-sm);
      padding: 12px;
      margin-bottom: 12px;
      cursor: pointer;
      .article-img {
        width: 108px;
        height: 72px;
        border-radius: var(--rounded-sm);
        object-fit: cover;
      }
      .article-info {
        flex: 1;
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .article-title {
          width: 100%;
          // height: 44px;
          font-size: var(--font-size-h3);
          font-weight: 500;
          line-height: 22px;
          color: var(--text-highlight);
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          word-break: break-all;
        }
        .article-date {
          width: 100%;
          height: 18px;
          font-size: var(--font-size-caption);
          color: var(--text-describe);
          display: inline-flex;
          line-height: normal;
          align-items: center;
          .time-icon {
            width: 14px;
            height: 14px;
            margin-right: 4px;
          }
        }
      }
    }
  }
}
</style>
