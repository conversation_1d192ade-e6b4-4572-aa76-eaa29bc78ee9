<script setup lang="ts">
import { getWechatOpenId } from "@/api/wechat";
import { taskVisit } from "@/api/task/index";
import to from "await-to-js";

defineOptions({
  name: "ShareCallback"
});
console.log("查看分享链接", window.location.href);
const code = new URLSearchParams(window.location.search).get("code");
const state = new URLSearchParams(window.location.search).get("state");
const taskId = state.split("AAA")[0];
const userId = state.split("AAA")[1];
if (code) {
  const [err, { data } = {}] = await to(getWechatOpenId({ code }));
  if (err) {
    console.log("err");
  }
  if (data?.openId) {
    try {
      // 浏览任务
      const { data: res } = await taskVisit({
        userId: userId,
        type: 7,
        taskId: taskId,
        wechatOpenId: data?.openId
      });
      console.log("res", res);
      window.location.replace(res.articleLink);
    } catch (error) {
      showFailToast(error?.msg || "请求失败");
    }
  }
}
</script>
