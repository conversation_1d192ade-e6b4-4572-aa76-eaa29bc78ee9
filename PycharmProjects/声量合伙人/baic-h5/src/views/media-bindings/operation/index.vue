<script setup lang="ts">
import { bindingMediumManage, updatedMediumManage } from "@/api/media";
import OperationProcess from "./components/operationProcess.vue";
import arrowRightIcon from "@/assets/home/<USER>";
defineOptions({
  name: "MediaBindingOperation"
});

const router = useRouter();
const route = useRoute();
const mediaLinks = ref("");
const id = ref("");
const formRef = ref(null);
const isNewBinding = ref(false);
const showInstructions = ref(false);
const titleName = ref("");
const stepUrl = ref([]);

// 接收媒体信息参数
const item = ref(
  route.query.item ? JSON.parse(route.query.item as string) : null
);

const updateData = () => {
  // 检查 item.value 是否为 null
  if (item.value) {
    mediaLinks.value = item.value?.accountUrl || "";
    id.value = item.value?.accountId || "";
    if (item.value.accountId || item.value.accountUrl) {
      isNewBinding.value = false;
    } else {
      isNewBinding.value = true;
    }
    titleName.value = item.value?.platformName || "";
    stepUrl.value =
      item.value?.stepUrl === null ? [] : item.value?.stepUrl.split(",");
  } else {
    // 如果 item.value 为 null，重置相关数据
    mediaLinks.value = "";
    id.value = "";
    isNewBinding.value = false;
    titleName.value = "";
    stepUrl.value = [];
  }
};

const onSubmit = async () => {
  if (!mediaLinks.value) {
    showToast("请输入媒体链接");
    return;
  }
  if (!id.value) {
    showToast("请输入媒体账户");
    return;
  }
  try {
    let response;
    if (isNewBinding.value) {
      // 如果是新绑定，调用 bindingMediumManage 方法
      response = await bindingMediumManage({
        accountUrl: mediaLinks.value,
        accountId: id.value,
        mediumId: item.value.mediumId
      });
    } else {
      // 如果不是新绑定，调用 updatedMediumManage 方法
      response = await updatedMediumManage({
        id: item.value.id,
        accountUrl: mediaLinks.value,
        accountId: id.value,
        mediumId: item.value.mediumId
      });
    }
    const { code, data } = response;
    if (code === 200) {
      showToast("绑定成功");
      router.replace({
        path: "/media-binding/list"
      });
    }
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};

// 初始化数据
updateData();

// 监听路由变化
watch(
  () => route.query.item,
  newItem => {
    item.value = newItem ? JSON.parse(newItem as string) : null;
    updateData();
  }
);
</script>

<template>
  <div class="content">
    <div class="form-box">
      <div class="cell1">
        <div class="flex items-center">
          <img class="img" :src="item?.iconUrl" />
          <div class="label-color">{{ item?.platformName }}</div>
        </div>
        <div
          class="flex items-center text-[#000000] opacity-45 gap-[4px]"
          @click="showInstructions = true"
        >
          <div>绑定说明</div>
          <svg-icon name="arrow-icon-right" class="arrow-icon-right" />
        </div>
      </div>
      <van-form
        ref="formRef"
        class="form"
        input-align="right"
        error-message-align="right"
        @submit="onSubmit"
      >
        <van-cell-group inset>
          <van-field
            v-model="mediaLinks"
            name="媒体链接"
            label="媒体链接"
            placeholder="请输入媒体链接"
            rows="1"
            type="textarea"
            autosize
            maxlength="255"
            :rules="[{ required: true, message: '请输入媒体链接' }]"
          />
          <van-field
            v-model="id"
            type="textarea"
            rows="1"
            autosize
            name="媒体账户ID"
            label="媒体账户ID"
            placeholder="请输入媒体账户"
            maxlength="63"
            :rules="[{ required: true, message: '请输入媒体账户' }]"
          />
        </van-cell-group>
      </van-form>
    </div>
    <div
      class="bg-white w-full fixed bottom-0 left-0 right-0 van-safe-area-bottom"
    >
      <div class="my-[8px] mx-[12px]">
        <van-button
          size="large"
          block
          type="primary"
          text="提交信息"
          @click="onSubmit"
        />
      </div>
    </div>
    <operation-process
      v-model:show="showInstructions"
      :title-name="titleName"
      :image-list="stepUrl"
    />
  </div>
</template>

<style scoped>
.content {
  padding: 12px;
  .form-box {
    background-color: var(--color-block-background);
    border-radius: var(--rounded-md);
    font-size: var(--font-size-h3);
    padding-bottom: 12px;
    .cell1 {
      padding: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .img {
        width: 28px;
        height: 28px;
        margin-right: 8px;
        border-radius: var(--rounded-md);
      }
      .arrow-icon-right {
        margin-left: 4px;
        width: 16px;
        height: 16px;
      }
    }
  }
}
:deep(.van-cell-group--inset) {
  margin: 0;
}

.text-basic {
  font-size: var(--van-cell-font-size);
}

.label-color {
  color: var(--text-primary);
}

.form {
  margin-top: 8px;
  color: var(--text-primary);
  font-size: var(--font-size-h3);
  :deep(van-van-field) {
    height: 46px;
    line-height: 22px;
  }
  :deep(.van-cell__title) {
    font-size: var(--font-size-h3);
  }
}
</style>
