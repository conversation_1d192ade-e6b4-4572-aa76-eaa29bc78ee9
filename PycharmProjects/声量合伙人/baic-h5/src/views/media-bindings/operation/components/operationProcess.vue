<script setup lang="ts">
defineOptions({
  name: "OperationProcess"
});

interface Props {
  titleName: string;
  imageList: string[];
  show: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  titleImage: "",
  imageList: () => [],
  show: false
});

const emit = defineEmits(["update:show"]);

// 轮播图当前索引
const currentIndex = ref(0);

// 处理轮播图切换
const handleSwipeChange = (index: number) => {
  currentIndex.value = index;
};

const swipeRef = ref();

// 上一张图片
const handlePrev = () => {
  swipeRef.value?.prev();
};

// 下一张图片
const handleNext = () => {
  swipeRef.value?.next();
};

// 关闭弹窗
const handleClose = () => {
  emit("update:show", false);
};
</script>

<template>
  <van-overlay :show="show">
    <div class="operation-guide">
      <div v-if="titleName" class="guide-header">
        <div class="title-content">
          <div class="main-title">
            <span class="sub-text">{{ titleName }}</span
            ><br />
            <span class="sub-text">绑定操作流程</span>
          </div>
          <div class="desc-text">给自己一个机会,创造属于自己的平台</div>
        </div>
        <img src="@/assets/media/bading.png" alt="绑定" class="binding-image" />
      </div>
      <div class="guide-carousel">
        <div class="carousel-container">
          <!-- 轮播图 -->
          <van-swipe
            ref="swipeRef"
            v-model:current-index="currentIndex"
            :show-indicators="true"
            :loop="true"
            :touchable="true"
            :stop-propagation="false"
            :duration="500"
            :initial-swipe="0"
            :height="500"
            indicator-color="var(--color-primary)"
            @change="handleSwipeChange"
          >
            <van-swipe-item v-for="(image, index) in imageList" :key="index">
              <img :src="image" :alt="`指南步骤${index + 1}`" />
            </van-swipe-item>
          </van-swipe>
        </div>
      </div>
      <!-- 左右切换按钮 -->
      <div
        v-show="currentIndex > 0"
        class="carousel-btn carousel-btn-left"
        @click="handlePrev"
      >
        <img src="@/assets/task/bt_left.png" alt="上一步" />
      </div>
      <div
        v-show="currentIndex < imageList.length - 1"
        class="carousel-btn carousel-btn-right"
        @click="handleNext"
      >
        <img src="@/assets/task/bt_right.png" alt="下一步" />
      </div>
      <van-icon name="cross" class="close-icon" @click="handleClose" />
    </div>
  </van-overlay>
</template>

<style lang="less" scoped>
.operation-guide {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  position: relative;

  .guide-header {
    width: 337px;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 6px 12px 0 26px;
    .title-content {
      flex: 1;
      .main-title {
        margin-bottom: 8px;

        .sub-text {
          font-weight: bold;
          color: #ffffff;
          font-size: 24px;
          text-shadow:
            0px 4px 20px rgba(89, 235, 255, 0.5),
            0px 4px 10px rgba(0, 0, 0, 0.3);
        }
      }

      .desc-text {
        color: #ffffff;
        font-size: var(--font-size-caption);
        font-weight: normal;
        line-height: normal;
        text-align: left;
        display: flex;
        align-items: center;
        letter-spacing: normal;
        text-shadow:
          0px 4px 20px rgba(89, 235, 255, 0.5),
          0px 4px 10px rgba(0, 0, 0, 0.3);
      }
    }
    img {
      width: 122px;
      height: 122px;
      object-fit: contain;
      // margin-top: 6px;
    }
  }

  .guide-carousel {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-top: 8px;

    .carousel-container {
      position: relative;
      width: 100%;
      height: 450px;
      margin: 0 auto;

      :deep(.van-swipe) {
        height: 100%;
        width: auto;
        touch-action: pan-y;
        overflow: unset;

        &-item {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 450px;

          img {
            height: 450px;
            width: auto;
            object-fit: contain;
          }
        }

        &__indicators {
          bottom: -10px;

          .van-swipe__indicator {
            width: 8px;
            height: 8px;
            background-color: rgba(255, 255, 255, 0.3);
            opacity: 1;

            &--active {
              background-color: var(--color-primary);
            }
          }
        }
      }
    }
  }

  .close-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
  }
  .carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    z-index: 10;
    // padding: 10px;
    // background: rgba(0, 0, 0, 0.5);
    // border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 32px;
      height: 32px;
    }

    &-left {
      left: 12px;
    }

    &-right {
      right: 12px;
    }
  }
}
</style>
