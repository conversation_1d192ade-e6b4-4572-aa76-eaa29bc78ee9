<script setup lang="ts">
import { getMediumManageList } from "@/api/media";
import arrowRightIcon from "@/assets/home/<USER>";

defineOptions({
  name: "MediaBindingList"
});

const boundList = ref([]);
const boundListLoading = ref(false);
const boundListFinished = ref(false);

const router = useRouter();
const route = useRoute();

const goPage = (item: object) => {
  router.replace({
    path: "/media-binding/operation",
    query: { item: JSON.stringify(item) }
  });
};

const getList = async () => {
  try {
    const { data } = await getMediumManageList();
    boundList.value = data;
    // 加载状态结束
    boundListFinished.value = true;
    boundListLoading.value = false;
  } catch (error) {
    showFailToast(error?.msg || "请求失败");
  }
};

onMounted(() => {
  getList();
});

watch(
  () => ({
    date: route.query.date
  }),
  () => {
    getList();
  }
);
</script>
<template>
  <div class="van-safe-area-bottom">
    <div class="binding-box">
      <van-list
        v-model:loading="boundListLoading"
        :finished="boundListFinished"
        finished-text=""
        class="list"
      >
        <template v-for="item in boundList" :key="item.id">
          <van-cell v-if="!item?.accountId || !item?.accountUrl">
            <div class="flex justify-between items-center">
              <div class="flex items-center label-color">
                <img class="img" :src="item.iconUrl" alt="" />
                <div>{{ item.platformName }}</div>
              </div>
              <div class="flex items-center gap-2" @click="goPage(item)">
                <div>去绑定</div>
                <svg-icon name="arrow-icon-right" class="arrow-icon-right" />
              </div>
            </div>
          </van-cell>
        </template>
      </van-list>
      <van-list
        v-show="boundList.length > 0"
        v-model:loading="boundListLoading"
        class="mt-[12px] list"
        :finished="boundListFinished"
      >
        <template v-for="item in boundList" :key="item.id">
          <van-cell v-if="item?.accountId || item?.accountUrl">
            <div>
              <div class="flex justify-between items-center">
                <div class="flex items-center label-color">
                  <img class="img" :src="item.iconUrl" alt="" />
                  <div>{{ item.platformName }}</div>
                </div>
                <div class="flex items-center gap-2" @click="goPage(item)">
                  <div>查看详情</div>
                  <svg-icon name="arrow-icon-right" class="arrow-icon-right" />
                </div>
              </div>
              <div class="account-url-box">
                <div class="account-url">{{ item.accountUrl }}</div>
              </div>
            </div>
          </van-cell>
        </template>
      </van-list>
    </div>
  </div>
</template>

<style lang="less" scoped>
.binding-box {
  padding: 12px;
}
.list {
  border-radius: var(--rounded-md);
  overflow: hidden;

  :deep(.van-cell__title) {
    font-size: var(--font-size-h3);
    color: var(--text-primary);
  }

  :deep(.van-cell__value) {
    font-size: var(--font-size-h3);
    color: var(--text-describe);
  }

  :deep(.van-cell) {
    padding: 12px;
  }

  .arrow-icon-right {
    margin-left: 4px;
    width: 16px;
    height: 16px;
  }

  .account-url-box {
    height: 46px;
    margin-top: 12px;
    padding: 12px;
    border-radius: var(--rounded-md);
    border: 1px solid #f0f0f0;
    background: var(--color-block-background);
    .account-url {
      font-size: var(--font-size-h3);
      color: var(--text-primary);
      font-weight: normal;
      line-height: 22px;
      letter-spacing: 0em;
      text-align: left;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }
  }
}

.label-color {
  color: var(--van-cell-text-color);
}

.img {
  width: 28px;
  height: 28px;
  margin-right: 8px;
  border-radius: var(--rounded-md);
}
</style>
