<script setup lang="ts">
defineOptions({
  name: "MediaBindingDes"
});

const images = [
  "https://fastly.jsdelivr.net/npm/@vant/assets/apple-1.jpeg",
  "https://fastly.jsdelivr.net/npm/@vant/assets/apple-2.jpeg",
  "https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg"
];
</script>

<template>
  <div class="h-screen w-screen">
    <van-image
      height="130px"
      width="100%"
      fit="fill"
      src="https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg"
    />
    <van-swipe lazy-render class="van-swipe">
      <van-swipe-item v-for="image in images" :key="image">
        <van-image fit="fill" :src="image" class="size-full" />
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<style scoped>
.van-swipe {
  height: calc(100vh - 130px);
}
</style>
