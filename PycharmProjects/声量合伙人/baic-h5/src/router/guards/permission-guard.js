import { to as toPromise } from "await-to-js";
import { generateAuthUrl } from "@/utils/wechat";
import { getWechatUserInfo } from "@/api/wechat";
import { getMyUserInfo } from "@/api/my";
import { useAuthStore } from "@/store/modules/auth";
import setPageTitle from "@/utils/set-page-title";
import { reportErrorLogAsync } from "@/utils/reportLog";

// 白名单路由，无需登录即可访问
const WHITE_LIST = [
  "/loginCallback",
  "/shareCallback",
  "/task/doPreview",
  "/404"
];

/**
 * 创建权限守卫
 * @param {Router} router - Vue Router 实例
 */
export function createPermissionGuard(router) {
  router.beforeEach(async (to, from, next) => {
    const { token, userInfo, setWx, setUser, resetWx, logout } = useAuthStore();

    // 处理微信分享页面的授权跳转
    if (to.path === "/share") {
      try {
        setPageTitle("\u200E");
        window.location.replace(
          generateAuthUrl(
            "snsapi_base",
            "shareCallback",
            `${to.query.state}AAA${to.query.userId}`
          )
        );
      } catch (error) {
        console.error("微信分享授权跳转失败:", error);
      }
      return; // 终止后续逻辑
    }

    // 如果用户未登录
    if (!token) {
      // 开发环境下跳过授权
      if (import.meta.env.VITE_APP_CLOSE_WX_AUTH === "true") {
        if (to.path === "/login") {
          return next({ path: "/loginCallback" }); // 跳转到登录页
        }
        if (WHITE_LIST.includes(to.path) || to.meta.noAuth) {
          return next(); // 允许访问白名单或无需授权的路由
        }
        return next({ path: "/loginCallback" }); // 跳转到登录页
      }

      // 处理微信授权回调
      if (to.path === "/loginCallback") {
        const code = new URLSearchParams(window.location.search).get("code");
        if (!code) {
          resetWx();
          try {
            setPageTitle("\u200E");
            window.location.replace(generateAuthUrl()); // 重新跳转微信授权
          } catch (error) {
            console.error("微信授权跳转失败:", error);
          }
          return; // 终止后续逻辑
        }

        const [error, wxInfoResponse] = await toPromise(
          getWechatUserInfo({ code })
        );
        if (error) {
          reportErrorLogAsync(
            "路由守卫-获取微信用户信息失败",
            "getWechatUserInfo",
            "/wx/auth/getUserInfo",
            error,
            JSON.stringify({ code, path: to.path })
          );
          return next({ path: "/login", replace: true }); // 获取用户信息失败，跳转到登录页
        }

        setWx(wxInfoResponse.data); // 保存微信信息

        // 清理URL中的code参数，避免重复使用
        const url = new URL(window.location.href);
        url.searchParams.delete("code");
        url.searchParams.delete("state");
        window.history.replaceState({}, document.title, url.toString());

        return next(); // 继续导航
      }

      // 允许访问白名单或无需授权的路由
      if (WHITE_LIST.includes(to.path) || to.meta.noAuth) {
        return next();
      }

      // 其他情况跳转微信授权
      try {
        setPageTitle("\u200E");
        window.location.replace(generateAuthUrl());
      } catch (error) {
        console.error("微信授权跳转失败:", error);
      }
      return; // 终止后续逻辑
    }

    // 如果用户已登录，但访问的是登录页或授权回调页，则跳转到首页
    if (to.path === "/login" || to.path === "/loginCallback") {
      return next({ path: "/", replace: true });
    }

    // 允许访问白名单或无需授权的路由
    if (WHITE_LIST.includes(to.path) || to.meta.noAuth) {
      return next();
    }

    if (!userInfo?.id) {
      const [error, userInfoResponse] = await toPromise(getMyUserInfo());
      if (error) {
        reportErrorLogAsync(
          "路由守卫-获取用户信息失败",
          "getMyUserInfo",
          "/portal/member/user/getInfo",
          error,
          JSON.stringify({
            fromPath: from.path,
            toPath: to.path,
            hasToken: !!token
          })
        );
        await logout();
        showFailToast(error?.msg || "请求失败");
        return next({ path: "/login", replace: true }); // 获取用户信息失败，跳转到登录页
      }

      setUser(userInfoResponse.data); // 保存用户信息
      return next(); // 继续导航
    } else {
      next();
    }

    // 检查路由是否存在，如果不存在则跳转到 404 页面
    const isRouteExists = router
      .getRoutes()
      .some(route => route.name === to.name);
    if (!isRouteExists) {
      return next({ name: "404", query: { path: to.fullPath } });
    }

    // 允许访问
    next();
  });
}
