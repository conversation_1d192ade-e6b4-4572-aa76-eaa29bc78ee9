import {
  createRouter,
  createWebHistory,
  type RouteLocationNormalized
} from "vue-router";
import routes from "./routes";
// import { useCachedViewStoreHook } from "@/store/modules/cached-view";
// import NProgress from "@/utils/progress";
import setPageTitle from "@/utils/set-page-title";
import { setupRouterGuards } from "./guards";

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_PUBLIC_PATH),
  routes
});

export interface toRouteType extends RouteLocationNormalized {
  meta: {
    title?: string;
    noCache?: boolean;
  };
}

// 权限
setupRouterGuards(router);

router.beforeEach((to: toRouteType, from, next) => {
  // NProgress.start();
  // 路由缓存
  // 去除缓存
  // useCachedViewStoreHook().addCachedView(to);
  // 页面 title
  setPageTitle(to.meta.title);
  next();
});

router.afterEach(() => {
  // NProgress.done();
});

export default router;
