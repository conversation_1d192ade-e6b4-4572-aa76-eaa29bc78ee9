import Layout from "@/layout/index.vue";
import type { RouteRecordRaw } from "vue-router";

const routes: Array<RouteRecordRaw> = [
  // 登录相关路由
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录"
    }
  },
  {
    path: "/loginCallback",
    name: "LoginCallback",
    component: () => import("@/views/login/callback.vue"),
    meta: {
      title: "登录"
    }
  },

  // 分享相关路由
  {
    path: "/share",
    name: "Share",
    component: () => import("@/views/share/index.vue")
  },
  {
    path: "/shareCallback",
    name: "ShareCallback",
    component: () => import("@/views/share/callback.vue")
  },

  // 任务预览
  {
    path: "/assignment/doPreview",
    name: "DoPreview",
    component: () => import("@/views/task/do-task/index.vue"),
    meta: {
      title: "任务详情",
      noAuth: true
    }
  },

  // 图片预览
  {
    path: "/imagePreview",
    name: "ImagePreview",
    component: () => import("@/views/image-preview/index.vue"),
    meta: {
      title: "内容详情",
      noAuth: true
    }
  },

  // 主布局路由
  {
    path: "/",
    name: "root",
    component: Layout,
    redirect: { name: "Home" },
    children: [
      {
        path: "home",
        name: "Home",
        component: () => import("@/views/home/<USER>"),
        meta: {
          title: "首页",
          isShowTab: true
        }
      },
      {
        path: "rank",
        name: "Rank",
        component: () => import("@/views/rank/index.vue"),
        meta: {
          title: "排行榜"
        }
      },
      {
        path: "announcement",
        name: "Announcement",
        component: () => import("@/views/announcement/index.vue"),
        meta: {
          title: "公告列表"
        }
      },
      {
        path: "driveAppointment",
        name: "DriveAppointment",
        component: () => import("@/views/driveAppointment/index.vue"),
        meta: {
          title: "邀约试驾"
        }
      },
      {
        path: "driveAppointment/generateLink",
        name: "GenerateLink",
        component: () => import("@/views/driveAppointment/generateLink.vue"),
        props: route => ({ key: route.path + new Date().getTime() }),
        meta: {
          title: "生成链接"
        }
      },
      {
        path: "driveAppointment/generatePoster",
        name: "GeneratePoster",
        component: () => import("@/views/driveAppointment/generatePoster.vue"),
        props: route => ({ key: route.path + new Date().getTime() }),
        meta: {
          title: "生成海报"
        }
      },
      {
        path: "driveAppointment/generatePoster2",
        name: "GeneratePoster2",
        component: () => import("@/views/driveAppointment/generatePoster2.vue"),
        props: route => ({ key: route.path + new Date().getTime() }),
        meta: {
          title: "生成海报"
        }
      },
      {
        path: "driveAppointment/solicitationResults",
        name: "SolicitationResults",
        component: () =>
          import("@/views/driveAppointment/solicitationResults.vue"),
        props: route => ({ key: route.path + new Date().getTime() }),
        meta: {
          title: "邀约成果"
        }
      },
      {
        path: "driveAppointment/solicitationRule",
        name: "SolicitationRule",
        component: () =>
          import("@/views/driveAppointment/solicitationRule.vue"),
        props: route => ({ key: route.path + new Date().getTime() }),
        meta: {
          title: "活动规则" //规则
        }
      },
      {
        path: "driveAppointment/message",
        name: "DriveAppointmentMessage",
        component: () => import("@/views/driveAppointment/message.vue"),
        meta: {
          title: "邀约试驾",
          noAuth: true
        }
      },
      {
        path: "assignment",
        name: "Task",
        component: () => import("@/views/task/index.vue"),
        meta: {
          title: "任务广场",
          isShowTab: true
        }
      },
      {
        path: "task/doTask",
        name: "DoTaskOld",
        component: () => import("@/views/task/do-task/index.vue"),
        meta: {
          title: "任务详情"
        }
      },
      {
        path: "assignment/doTask",
        name: "DoTask",
        component: () => import("@/views/task/do-task/index.vue"),
        meta: {
          title: "任务详情"
        }
      },
      {
        path: "assignment/doTask/submitTask",
        name: "SubmitTask",
        component: () => import("@/views/task/submit-task/index.vue"),
        meta: {
          title: "提交详情"
        }
      },
      {
        path: "my",
        name: "My",
        component: () => import("@/views/my/index.vue"),
        meta: {
          title: "我的",
          isShowTab: true
        }
      },
      {
        path: "my/edit",
        name: "Edit",
        component: () => import("@/views/my/edit.vue"),
        meta: {
          title: "编辑个人资料"
        }
      },
      {
        path: "my/point",
        name: "MyPoint",
        component: () => import("@/views/my/point/index.vue"),
        meta: {
          title: "积分明细"
        }
      },
      {
        path: "my/customer-service",
        name: "CustomerService",
        component: () => import("@/views/my/customer-service/index.vue"),
        meta: {
          title: "联系客服"
        }
      },
      {
        path: "media-binding/list",
        name: "MediaBindingList",
        component: () => import("@/views/media-bindings/list/index.vue"),
        meta: {
          title: "媒体绑定"
        }
      },
      {
        path: "media-binding/operation",
        name: "MediaBindingOperation",
        component: () => import("@/views/media-bindings/operation/index.vue"),
        meta: {
          title: "媒体绑定"
        }
      },
      {
        path: "media-binding/des",
        name: "MediaBindingDes",
        component: () => import("@/views/media-bindings/description/index.vue"),
        meta: {
          title: "媒体绑定"
        }
      },
      {
        path: "team-achievement/overview",
        name: "TeamAchievement",
        component: () => import("@/views/team-achievement/overview/index.vue"),
        meta: {
          title: "团队成就概览"
        }
      },
      {
        path: "team-achievement/overview/task/detail",
        name: "TaskDetail",
        component: () =>
          import("@/views/team-achievement/task-detail/index.vue"),
        meta: {
          title: "团队数据"
        }
      },
      {
        path: "team-achievement/overview/registration/detail",
        name: "RegistrationDetail",
        component: () =>
          import("@/views/team-achievement/registration-detail/index.vue"),
        meta: {
          title: "注册详情"
        }
      },
      {
        path: "team-achievement/overview/media/detail",
        name: "MediaDetail",
        component: () =>
          import("@/views/team-achievement/media-detail/index.vue"),
        meta: {
          title: "媒体绑定详情"
        }
      },
      {
        path: "pointExchange",
        name: "PointExchange",
        component: () => import("@/views/pointExchange/index.vue"),
        meta: {
          title: "积分兑换"
        }
      }
    ]
  },
  // 404 页面
  {
    path: "/404",
    name: "Page404",
    component: () => import("@/views/error-page/404.vue"),
    meta: {
      title: "404"
    }
  },

  // 捕获所有未匹配的路由
  {
    path: "/:catchAll(.*)",
    redirect: "/404"
  }
];

export default routes;
