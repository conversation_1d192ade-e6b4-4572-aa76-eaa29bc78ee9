{"name": "deep-thinking-mcp", "version": "1.0.0", "description": "A deep thinking MCP server for Gemini AI assistant", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "watch": "tsx watch src/index.ts"}, "keywords": ["mcp", "deep-thinking", "ai", "gemini", "reasoning"], "author": "Augment Agent", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18"}}