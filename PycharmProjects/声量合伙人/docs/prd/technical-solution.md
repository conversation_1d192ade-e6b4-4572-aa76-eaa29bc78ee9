# 人群功能技术实现方案

## 🎯 技术方案概述

### 设计原则
- **兼容性优先**：保持现有功能完全兼容
- **性能优化**：支持大数据量处理和高并发访问
- **扩展性设计**：为未来功能扩展预留空间
- **安全可靠**：确保数据安全和系统稳定

### 技术栈选择
- **后端**: Spring Boot 3.4.2 + MyBatis Plus + Redis
- **前端**: Vue3 + TypeScript + Element Plus/Vant4
- **数据库**: MySQL 8.0
- **文件处理**: Apache POI + 异步处理
- **缓存**: Redis + 本地缓存

## 🗄️ 数据库设计方案

### 核心表结构设计

#### 1. 人群基础信息表 (crowd_basic)
```sql
CREATE TABLE `crowd_basic` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '人群ID',
  `crowd_name` varchar(100) NOT NULL COMMENT '人群名称',
  `crowd_code` varchar(50) NOT NULL COMMENT '人群编码',
  `crowd_desc` varchar(500) DEFAULT NULL COMMENT '人群描述',
  `crowd_type` tinyint(4) DEFAULT '1' COMMENT '人群类型（1:Excel导入 2:条件筛选）',
  `crowd_status` tinyint(4) DEFAULT '1' COMMENT '人群状态（1:正常 2:停用）',
  `total_count` int(11) DEFAULT '0' COMMENT '总用户数',
  `valid_count` int(11) DEFAULT '0' COMMENT '有效用户数',
  `invalid_count` int(11) DEFAULT '0' COMMENT '无效用户数',
  `import_file_name` varchar(255) DEFAULT NULL COMMENT '导入文件名',
  `import_file_url` varchar(500) DEFAULT NULL COMMENT '导入文件地址',
  `tags` json DEFAULT NULL COMMENT '人群标签',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_crowd_code` (`crowd_code`, `tenant_id`),
  KEY `idx_crowd_status` (`crowd_status`),
  KEY `idx_crowd_type` (`crowd_type`),
  KEY `idx_crowd_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='人群基础信息表';
```

#### 2. 人群用户关联表 (crowd_user_relation)
```sql
CREATE TABLE `crowd_user_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `crowd_id` bigint(20) NOT NULL COMMENT '人群ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '系统用户ID（匹配成功时填充）',
  `import_phone` varchar(11) NOT NULL COMMENT '导入的手机号',
  `import_name` varchar(50) DEFAULT NULL COMMENT '导入的姓名',
  `import_employee_no` varchar(50) DEFAULT NULL COMMENT '导入的工号',
  `match_status` tinyint(4) DEFAULT '0' COMMENT '匹配状态（0:未匹配 1:匹配成功 2:匹配失败）',
  `match_field` varchar(20) DEFAULT NULL COMMENT '匹配字段（phone/name/employee_no）',
  `match_time` datetime DEFAULT NULL COMMENT '匹配时间',
  `is_valid` tinyint(4) DEFAULT '1' COMMENT '是否有效（1:有效 0:无效）',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_crowd_user_crowd` (`crowd_id`),
  KEY `idx_crowd_user_user` (`user_id`),
  KEY `idx_crowd_user_phone` (`import_phone`),
  KEY `idx_crowd_user_match` (`match_status`),
  CONSTRAINT `fk_crowd_user_crowd` FOREIGN KEY (`crowd_id`) REFERENCES `crowd_basic` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='人群用户关联表';
```

#### 3. 任务人群关联表 (task_crowd_relation)
```sql
CREATE TABLE `task_crowd_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `crowd_id` bigint(20) NOT NULL COMMENT '人群ID',
  `push_type` tinyint(4) DEFAULT '1' COMMENT '推送类型（1:仅人群 2:人群+组织）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_crowd` (`task_id`, `crowd_id`),
  KEY `idx_task_crowd_task` (`task_id`),
  KEY `idx_task_crowd_crowd` (`crowd_id`)
) ENGINE=InnoDB COMMENT='任务人群关联表';
```

### 现有表结构改造

#### 1. 任务基础信息表改造
```sql
-- 增加人群推送相关字段
ALTER TABLE `task_basic` 
ADD COLUMN `push_target_type` tinyint(4) DEFAULT '1' COMMENT '推送目标类型（1:组织 2:人群 3:组织+人群）' AFTER `push_range`,
ADD COLUMN `crowd_ids` json DEFAULT NULL COMMENT '关联人群ID列表' AFTER `push_target_type`;

-- 添加索引
ALTER TABLE `task_basic` ADD INDEX `idx_task_push_target_type` (`push_target_type`);
```

#### 2. 用户任务表改造
```sql
-- 增加来源标识字段
ALTER TABLE `member_task`
ADD COLUMN `source_type` tinyint(4) DEFAULT '1' COMMENT '来源类型（1:组织推送 2:人群推送）' AFTER `dept_id`,
ADD COLUMN `source_crowd_id` bigint(20) DEFAULT NULL COMMENT '来源人群ID' AFTER `source_type`;

-- 添加索引
ALTER TABLE `member_task` ADD INDEX `idx_member_task_source` (`source_type`, `source_crowd_id`);
```

## 🔧 后端实现方案

### 核心服务类设计

#### 1. 人群管理服务 (CrowdService)
```java
@Service
@RequiredArgsConstructor
public class CrowdServiceImpl implements ICrowdService {
    
    private final CrowdBasicMapper crowdBasicMapper;
    private final CrowdUserRelationMapper crowdUserRelationMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 创建人群
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createCrowd(CrowdBasicBo bo) {
        // 1. 验证人群编码唯一性
        validateCrowdCode(bo.getCrowdCode());
        
        // 2. 保存人群基础信息
        CrowdBasic crowd = MapstructUtils.convert(bo, CrowdBasic.class);
        crowd.setCrowdStatus(CrowdStatusEnum.NORMAL.getCode());
        boolean result = crowdBasicMapper.insert(crowd) > 0;
        
        if (result) {
            bo.setId(crowd.getId());
            // 3. 异步处理Excel导入
            if (StringUtils.isNotBlank(bo.getImportFileUrl())) {
                asyncProcessExcelImport(crowd.getId(), bo.getImportFileUrl());
            }
        }
        
        return result;
    }
    
    /**
     * 异步处理Excel导入
     */
    @Async("taskExecutor")
    public void asyncProcessExcelImport(Long crowdId, String fileUrl) {
        try {
            // 1. 下载并解析Excel文件
            List<CrowdUserImportDto> importData = parseExcelFile(fileUrl);
            
            // 2. 数据验证和清洗
            List<CrowdUserImportDto> validData = validateImportData(importData);
            
            // 3. 批量用户匹配
            List<CrowdUserRelation> relations = batchMatchUsers(crowdId, validData);
            
            // 4. 批量保存关联关系
            crowdUserRelationMapper.insertBatch(relations);
            
            // 5. 更新人群统计信息
            updateCrowdStatistics(crowdId);
            
            // 6. 发送处理完成通知
            sendImportCompleteNotification(crowdId);
            
        } catch (Exception e) {
            log.error("Excel导入处理失败, crowdId: {}", crowdId, e);
            // 标记导入失败状态
            markImportFailed(crowdId, e.getMessage());
        }
    }
}
```

#### 2. 用户匹配服务 (CrowdUserMatchService)
```java
@Service
@RequiredArgsConstructor
public class CrowdUserMatchServiceImpl implements ICrowdUserMatchService {
    
    private final MemberUserMapper memberUserMapper;
    private final EmployeeUserMapper employeeUserMapper;
    
    /**
     * 批量匹配用户
     */
    @Override
    public List<CrowdUserRelation> batchMatchUsers(Long crowdId, List<CrowdUserImportDto> importData) {
        List<CrowdUserRelation> relations = new ArrayList<>();
        
        for (CrowdUserImportDto dto : importData) {
            CrowdUserRelation relation = new CrowdUserRelation();
            relation.setCrowdId(crowdId);
            relation.setImportPhone(dto.getPhone());
            relation.setImportName(dto.getName());
            relation.setImportEmployeeNo(dto.getEmployeeNo());
            
            // 执行用户匹配
            UserMatchResult matchResult = matchUser(dto);
            if (matchResult.isMatched()) {
                relation.setUserId(matchResult.getUserId());
                relation.setMatchStatus(MatchStatusEnum.SUCCESS.getCode());
                relation.setMatchField(matchResult.getMatchField());
                relation.setMatchTime(LocalDateTime.now());
                relation.setIsValid(1);
            } else {
                relation.setMatchStatus(MatchStatusEnum.FAILED.getCode());
                relation.setIsValid(0);
                relation.setRemark(matchResult.getFailReason());
            }
            
            relations.add(relation);
        }
        
        return relations;
    }
    
    /**
     * 用户匹配算法
     */
    private UserMatchResult matchUser(CrowdUserImportDto dto) {
        // 1. 优先按手机号精确匹配
        if (StringUtils.isNotBlank(dto.getPhone())) {
            MemberUser user = memberUserMapper.selectByPhone(dto.getPhone());
            if (user != null) {
                return UserMatchResult.success(user.getId(), "phone");
            }
        }
        
        // 2. 按工号精确匹配
        if (StringUtils.isNotBlank(dto.getEmployeeNo())) {
            EmployeeUser employee = employeeUserMapper.selectByEmployeeNo(dto.getEmployeeNo());
            if (employee != null) {
                // 查找对应的会员用户
                MemberUser user = memberUserMapper.selectByExternalUserId(employee.getId());
                if (user != null) {
                    return UserMatchResult.success(user.getId(), "employee_no");
                }
            }
        }
        
        // 3. 按姓名模糊匹配（需要额外验证）
        if (StringUtils.isNotBlank(dto.getName())) {
            List<MemberUser> users = memberUserMapper.selectByNameLike(dto.getName());
            if (users.size() == 1) {
                return UserMatchResult.success(users.get(0).getId(), "name");
            } else if (users.size() > 1) {
                return UserMatchResult.failed("姓名匹配到多个用户，无法确定");
            }
        }
        
        return UserMatchResult.failed("未找到匹配的用户");
    }
}
```

#### 3. 任务推送服务改造 (TaskPushService)
```java
@Service
@RequiredArgsConstructor
public class TaskPushServiceImpl implements ITaskPushService {
    
    private final TaskCrowdRelationMapper taskCrowdRelationMapper;
    private final CrowdUserRelationMapper crowdUserRelationMapper;
    private final TaskPushDeptMapper taskPushDeptMapper;
    private final MemberUserMapper memberUserMapper;
    
    /**
     * 获取任务推送目标用户
     */
    @Override
    public Set<Long> getTaskPushTargetUsers(Long taskId) {
        TaskBasic task = taskBasicMapper.selectById(taskId);
        Set<Long> targetUsers = new HashSet<>();
        
        // 根据推送目标类型获取用户
        switch (task.getPushTargetType()) {
            case 1: // 仅组织推送
                targetUsers.addAll(getOrgPushUsers(taskId, task.getPushRange()));
                break;
            case 2: // 仅人群推送
                targetUsers.addAll(getCrowdPushUsers(taskId));
                break;
            case 3: // 组织+人群推送
                targetUsers.addAll(getOrgPushUsers(taskId, task.getPushRange()));
                targetUsers.addAll(getCrowdPushUsers(taskId));
                break;
        }
        
        return targetUsers;
    }
    
    /**
     * 获取人群推送用户
     */
    private Set<Long> getCrowdPushUsers(Long taskId) {
        // 1. 获取任务关联的人群
        List<Long> crowdIds = taskCrowdRelationMapper.selectCrowdIdsByTaskId(taskId);
        
        if (CollectionUtils.isEmpty(crowdIds)) {
            return Collections.emptySet();
        }
        
        // 2. 获取人群中的有效用户
        List<Long> userIds = crowdUserRelationMapper.selectValidUserIdsByCrowdIds(crowdIds);
        
        return new HashSet<>(userIds);
    }
    
    /**
     * 获取组织推送用户（原有逻辑）
     */
    private Set<Long> getOrgPushUsers(Long taskId, Integer pushRange) {
        // 保持原有的组织推送逻辑不变
        // ...
    }
}
```

### Excel处理优化方案

#### 1. 异步处理架构
```java
@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean("excelProcessExecutor")
    public ThreadPoolTaskExecutor excelProcessExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("excel-process-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}

@Component
public class ExcelImportProcessor {
    
    /**
     * 分批处理Excel数据
     */
    @Async("excelProcessExecutor")
    public CompletableFuture<ImportResult> processExcelBatch(
            Long crowdId, List<CrowdUserImportDto> batch, int batchIndex) {
        
        try {
            // 1. 批量用户匹配
            List<CrowdUserRelation> relations = crowdUserMatchService.batchMatchUsers(crowdId, batch);
            
            // 2. 批量保存数据
            crowdUserRelationMapper.insertBatch(relations);
            
            // 3. 更新进度
            updateImportProgress(crowdId, batchIndex, batch.size());
            
            return CompletableFuture.completedFuture(ImportResult.success(relations.size()));
            
        } catch (Exception e) {
            log.error("Excel批次处理失败, crowdId: {}, batchIndex: {}", crowdId, batchIndex, e);
            return CompletableFuture.completedFuture(ImportResult.failed(e.getMessage()));
        }
    }
}
```

#### 2. 进度跟踪机制
```java
@Component
public class ImportProgressTracker {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 更新导入进度
     */
    public void updateProgress(Long crowdId, int processedCount, int totalCount) {
        String key = "crowd:import:progress:" + crowdId;
        ImportProgress progress = new ImportProgress();
        progress.setProcessedCount(processedCount);
        progress.setTotalCount(totalCount);
        progress.setProgress(totalCount > 0 ? (processedCount * 100 / totalCount) : 0);
        progress.setUpdateTime(LocalDateTime.now());
        
        redisTemplate.opsForValue().set(key, progress, Duration.ofHours(24));
    }
    
    /**
     * 获取导入进度
     */
    public ImportProgress getProgress(Long crowdId) {
        String key = "crowd:import:progress:" + crowdId;
        return (ImportProgress) redisTemplate.opsForValue().get(key);
    }
}
```

## 🎨 前端实现方案

### 核心组件设计

#### 1. 人群选择器组件 (CrowdSelector.vue)
```vue
<template>
  <div class="crowd-selector">
    <el-select
      v-model="selectedCrowds"
      multiple
      filterable
      remote
      :remote-method="searchCrowds"
      :loading="loading"
      placeholder="请选择人群"
      @change="handleCrowdChange"
    >
      <el-option
        v-for="crowd in crowdOptions"
        :key="crowd.id"
        :label="crowd.crowdName"
        :value="crowd.id"
      >
        <span>{{ crowd.crowdName }}</span>
        <span class="crowd-count">{{ crowd.validCount }}人</span>
      </el-option>
    </el-select>
    
    <!-- 选中人群预览 -->
    <div v-if="selectedCrowds.length > 0" class="selected-crowds">
      <el-tag
        v-for="crowd in selectedCrowdDetails"
        :key="crowd.id"
        closable
        @close="removeCrowd(crowd.id)"
      >
        {{ crowd.crowdName }} ({{ crowd.validCount }}人)
      </el-tag>
    </div>
    
    <!-- 用户数量统计 -->
    <div class="user-count-summary">
      <span>预计推送用户数：{{ totalUserCount }}</span>
      <span v-if="duplicateCount > 0" class="duplicate-warning">
        （重复用户：{{ duplicateCount }}）
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface CrowdOption {
  id: number;
  crowdName: string;
  validCount: number;
}

interface Props {
  modelValue: number[];
  orgUserCount?: number; // 组织推送用户数，用于计算重复
}

const props = withDefaults(defineProps<Props>(), {
  orgUserCount: 0
});

const emit = defineEmits<{
  'update:modelValue': [value: number[]];
  'change': [crowds: CrowdOption[], totalCount: number];
}>();

const selectedCrowds = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const crowdOptions = ref<CrowdOption[]>([]);
const selectedCrowdDetails = ref<CrowdOption[]>([]);
const loading = ref(false);
const totalUserCount = ref(0);
const duplicateCount = ref(0);

// 搜索人群
const searchCrowds = async (query: string) => {
  if (!query) return;
  
  loading.value = true;
  try {
    const response = await searchCrowdsApi({ name: query, status: 1 });
    crowdOptions.value = response.data;
  } catch (error) {
    console.error('搜索人群失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理人群选择变化
const handleCrowdChange = async () => {
  // 获取选中人群详情
  selectedCrowdDetails.value = crowdOptions.value.filter(
    crowd => selectedCrowds.value.includes(crowd.id)
  );
  
  // 计算用户数量
  await calculateUserCount();
  
  emit('change', selectedCrowdDetails.value, totalUserCount.value);
};

// 计算用户数量（包括去重）
const calculateUserCount = async () => {
  if (selectedCrowds.value.length === 0) {
    totalUserCount.value = 0;
    duplicateCount.value = 0;
    return;
  }
  
  try {
    const response = await calculateCrowdUserCountApi({
      crowdIds: selectedCrowds.value,
      orgUserCount: props.orgUserCount
    });
    
    totalUserCount.value = response.data.totalCount;
    duplicateCount.value = response.data.duplicateCount;
  } catch (error) {
    console.error('计算用户数量失败:', error);
  }
};
</script>
```

#### 2. Excel上传组件 (ExcelUploader.vue)
```vue
<template>
  <div class="excel-uploader">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-progress="handleProgress"
      :show-file-list="false"
      drag
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">
        将Excel文件拖到此处，或<em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip">
          支持.xlsx和.xls格式，文件大小不超过10MB
          <el-button type="text" @click="downloadTemplate">下载模板</el-button>
        </div>
      </template>
    </el-upload>
    
    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress :percentage="uploadProgress" />
      <p>正在上传文件...</p>
    </div>
    
    <!-- 处理进度 -->
    <div v-if="processing" class="process-progress">
      <el-progress :percentage="processProgress" />
      <p>正在处理数据，请稍候...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  crowdId?: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'upload-success': [result: ImportResult];
  'upload-error': [error: string];
}>();

const uploadRef = ref();
const uploading = ref(false);
const processing = ref(false);
const uploadProgress = ref(0);
const processProgress = ref(0);

// 上传前验证
const beforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel';
  const isLt10M = file.size / 1024 / 1024 < 10;
  
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件！');
    return false;
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB！');
    return false;
  }
  
  uploading.value = true;
  return true;
};

// 上传进度
const handleProgress = (event: any) => {
  uploadProgress.value = Math.round(event.percent);
};

// 上传成功
const handleSuccess = (response: any) => {
  uploading.value = false;
  
  if (response.code === 200) {
    // 开始监听处理进度
    startProgressPolling(response.data.importId);
  } else {
    emit('upload-error', response.msg);
  }
};

// 轮询处理进度
const startProgressPolling = (importId: string) => {
  processing.value = true;
  
  const pollProgress = async () => {
    try {
      const response = await getImportProgressApi(importId);
      processProgress.value = response.data.progress;
      
      if (response.data.progress >= 100) {
        processing.value = false;
        emit('upload-success', response.data);
      } else {
        setTimeout(pollProgress, 2000); // 2秒后再次查询
      }
    } catch (error) {
      processing.value = false;
      emit('upload-error', '获取处理进度失败');
    }
  };
  
  pollProgress();
};
</script>
```

### 状态管理方案

#### 1. 人群管理Store (useCrowdStore)
```typescript
// stores/crowd.ts
export const useCrowdStore = defineStore('crowd', {
  state: () => ({
    crowdList: [] as CrowdBasic[],
    currentCrowd: null as CrowdBasic | null,
    loading: false,
    importProgress: new Map<number, ImportProgress>()
  }),
  
  getters: {
    activeCrowds: (state) => state.crowdList.filter(crowd => crowd.crowdStatus === 1),
    getCrowdById: (state) => (id: number) => state.crowdList.find(crowd => crowd.id === id)
  },
  
  actions: {
    async fetchCrowdList(params?: CrowdQueryParams) {
      this.loading = true;
      try {
        const response = await getCrowdListApi(params);
        this.crowdList = response.data.rows;
        return response.data;
      } finally {
        this.loading = false;
      }
    },
    
    async createCrowd(crowdData: CrowdCreateParams) {
      const response = await createCrowdApi(crowdData);
      if (response.code === 200) {
        await this.fetchCrowdList(); // 刷新列表
      }
      return response;
    },
    
    updateImportProgress(crowdId: number, progress: ImportProgress) {
      this.importProgress.set(crowdId, progress);
    }
  },
  
  persist: {
    key: 'crowd-store',
    storage: sessionStorage,
    paths: ['currentCrowd'] // 只持久化当前选中的人群
  }
});
```

## 📊 性能优化方案

### 1. 数据库优化
- **索引优化**: 为高频查询字段创建合适的索引
- **分页查询**: 大数据量列表使用分页查询
- **批量操作**: Excel导入使用批量插入
- **读写分离**: 统计查询使用只读库

### 2. 缓存策略
- **Redis缓存**: 人群基础信息、用户匹配结果
- **本地缓存**: 字典数据、配置信息
- **查询缓存**: 统计数据缓存1小时

### 3. 异步处理
- **Excel导入**: 异步处理，避免阻塞
- **用户匹配**: 分批处理，提高效率
- **消息推送**: 异步推送，提高响应速度

## 🔒 安全方案

### 1. 数据安全
- **文件上传**: 限制文件类型和大小
- **数据验证**: 严格的数据格式验证
- **权限控制**: 基于角色的访问控制
- **数据脱敏**: 敏感信息脱敏显示

### 2. 操作审计
- **操作日志**: 记录所有关键操作
- **数据变更**: 记录数据变更历史
- **异常监控**: 监控异常操作和错误

---
*人群功能技术实现方案 v1.0 - 确保技术实现的可行性和可靠性*
