# 业务流程详细说明

## 🔄 核心业务流程

### 1. 用户注册登录流程

#### H5端微信登录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant H as H5应用
    participant W as 微信服务
    participant B as 后端服务
    
    U->>H: 访问H5页面
    H->>H: 检查是否在微信环境
    alt 在微信环境
        H->>W: 微信授权获取用户信息
        W->>H: 返回用户基础信息
        H->>U: 显示手机号输入页面
        U->>H: 输入手机号
        H->>B: 发送验证码请求
        B->>U: 发送短信验证码
        U->>H: 输入验证码
        H->>B: 登录请求(手机号+验证码+微信信息)
        B->>B: 验证码校验
        B->>B: 用户信息创建/更新
        B->>H: 返回token和用户信息
        H->>H: 保存登录状态
        H->>U: 跳转到首页
    else 非微信环境
        H->>U: 跳转到登录页面
        U->>H: 输入手机号和验证码
        H->>B: 登录请求
        B->>H: 返回登录结果
    end
```

#### 管理后台登录流程
```mermaid
sequenceDiagram
    participant A as 管理员
    participant F as 前端
    participant B as 后端
    participant R as Redis
    
    A->>F: 访问登录页面
    F->>B: 获取验证码
    B->>F: 返回验证码图片
    A->>F: 输入用户名、密码、验证码
    F->>B: 登录请求
    B->>B: 验证码校验
    B->>B: 用户名密码校验
    B->>B: 权限信息加载
    B->>R: 保存登录状态
    B->>F: 返回token和用户信息
    F->>F: 保存登录状态
    F->>A: 跳转到首页
```

### 2. 任务管理流程

#### 任务创建发布流程
```mermaid
flowchart TD
    A[营销人员创建任务] --> B[填写任务基础信息]
    B --> C[设置任务详情]
    C --> D[配置奖励规则]
    D --> E[选择推送范围]
    E --> F[选择审核人员]
    F --> G[提交审核]
    G --> H{审核结果}
    H -->|通过| I[任务发布]
    H -->|拒绝| J[修改任务]
    J --> G
    I --> K[创建定时任务]
    K --> L[任务上线]
    L --> M[员工可见可领取]
    
    style A fill:#e1f5fe
    style I fill:#c8e6c9
    style H fill:#fff3e0
```

#### 任务执行流程
```mermaid
flowchart TD
    A[员工查看任务列表] --> B[选择感兴趣的任务]
    B --> C[查看任务详情]
    C --> D{是否领取}
    D -->|是| E[领取任务]
    D -->|否| A
    E --> F[执行任务内容]
    F --> G[上传执行证明]
    G --> H[提交任务]
    H --> I{审核结果}
    I -->|通过| J[获得奖励]
    I -->|拒绝| K[重新提交或放弃]
    K --> F
    J --> L[任务完成]
    
    style E fill:#e8f5e8
    style J fill:#c8e6c9
    style I fill:#fff3e0
```

### 3. 任务审核流程

#### 多级审核机制
```mermaid
flowchart TD
    A[任务提交] --> B[部门初审]
    B --> C{部门审核结果}
    C -->|通过| D[总部复审]
    C -->|拒绝| E[返回修改]
    D --> F{总部审核结果}
    F -->|通过| G[审核通过]
    F -->|拒绝| H[返回部门重审]
    E --> A
    H --> B
    G --> I[发放奖励]
    I --> J[任务完成]
    
    style G fill:#c8e6c9
    style E fill:#ffcdd2
    style H fill:#ffcdd2
```

### 4. 积分奖励流程

#### 积分计算和发放
```mermaid
sequenceDiagram
    participant U as 用户
    participant T as 任务系统
    participant R as 奖励系统
    participant P as 积分系统
    participant N as 通知系统
    
    U->>T: 提交任务
    T->>T: 任务审核通过
    T->>R: 触发奖励计算
    R->>R: 根据任务类型计算基础积分
    R->>R: 根据完成质量计算奖励倍数
    R->>R: 计算最终奖励积分
    R->>P: 发放积分
    P->>P: 更新用户积分余额
    P->>P: 记录积分变动日志
    P->>N: 发送积分到账通知
    N->>U: 推送通知消息
```

## 📊 状态机设计

### 任务状态机
```mermaid
stateDiagram-v2
    [*] --> 草稿: 创建任务
    草稿 --> 待审核: 提交审核
    草稿 --> [*]: 删除草稿
    
    待审核 --> 已发布: 审核通过
    待审核 --> 草稿: 审核拒绝
    
    已发布 --> 进行中: 到达开始时间
    已发布 --> 已取消: 手动取消
    
    进行中 --> 已结束: 到达结束时间
    进行中 --> 已取消: 手动取消
    
    已结束 --> [*]: 归档
    已取消 --> [*]: 归档
```

### 用户任务状态机
```mermaid
stateDiagram-v2
    [*] --> 未领取: 任务发布
    未领取 --> 已领取: 用户领取
    未领取 --> 已过期: 任务过期
    
    已领取 --> 已提交: 提交任务
    已领取 --> 已放弃: 用户放弃
    已领取 --> 已过期: 任务过期
    
    已提交 --> 审核中: 进入审核
    审核中 --> 已完成: 审核通过
    审核中 --> 已拒绝: 审核拒绝
    
    已拒绝 --> 已提交: 重新提交
    已拒绝 --> 已放弃: 放弃任务
    
    已完成 --> [*]: 结束
    已放弃 --> [*]: 结束
    已过期 --> [*]: 结束
```

### 用户状态机
```mermaid
stateDiagram-v2
    [*] --> 未注册: 初始状态
    未注册 --> 已注册: 完成注册
    已注册 --> 已激活: 首次登录
    已激活 --> 正常: 正常使用
    正常 --> 冻结: 违规操作
    冻结 --> 正常: 解除冻结
    正常 --> 注销: 用户注销
    冻结 --> 注销: 强制注销
    注销 --> [*]: 结束
```

## 🔄 异常处理流程

### 任务异常处理
```mermaid
flowchart TD
    A[任务执行异常] --> B{异常类型判断}
    B -->|网络异常| C[重试机制]
    B -->|业务异常| D[记录错误日志]
    B -->|系统异常| E[紧急处理]
    
    C --> F{重试次数检查}
    F -->|未超限| G[延迟重试]
    F -->|已超限| H[标记失败]
    
    D --> I[通知相关人员]
    E --> J[系统告警]
    
    G --> A
    H --> K[人工处理]
    I --> K
    J --> L[运维介入]
    
    K --> M[问题解决]
    L --> M
    M --> N[恢复正常]
```

### 支付异常处理
```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 系统
    participant P as 支付系统
    participant L as 日志系统
    
    U->>S: 发起积分兑换
    S->>P: 调用支付接口
    P-->>S: 支付异常
    S->>L: 记录异常日志
    S->>S: 回滚事务
    S->>U: 返回错误信息
    
    Note over S: 异步补偿机制
    S->>P: 查询支付状态
    alt 支付成功
        S->>S: 补偿处理
        S->>U: 通知处理结果
    else 支付失败
        S->>L: 记录失败日志
        S->>U: 通知失败原因
    end
```

## 📈 数据流转流程

### 用户数据同步流程
```mermaid
flowchart LR
    A[H5用户注册] --> B[用户信息入库]
    B --> C[同步到会员系统]
    C --> D[同步到积分系统]
    D --> E[同步到通知系统]
    
    F[管理后台用户管理] --> G[用户信息更新]
    G --> H[触发数据同步]
    H --> I[更新相关系统]
    
    J[第三方系统] --> K[数据接口]
    K --> L[数据校验]
    L --> M[数据转换]
    M --> N[入库处理]
```

### 任务数据流转
```mermaid
flowchart TD
    A[任务创建] --> B[基础信息存储]
    B --> C[详情信息存储]
    C --> D[奖励配置存储]
    D --> E[推送范围配置]
    E --> F[审核流程配置]
    
    G[任务发布] --> H[生成推送任务]
    H --> I[用户筛选]
    I --> J[消息推送]
    J --> K[用户接收]
    
    L[任务执行] --> M[执行记录]
    M --> N[审核记录]
    N --> O[奖励记录]
    O --> P[统计数据]
```

## 🔔 通知推送流程

### 消息推送机制
```mermaid
sequenceDiagram
    participant E as 事件源
    participant M as 消息中心
    participant Q as 消息队列
    participant P as 推送服务
    participant U as 用户
    
    E->>M: 触发消息事件
    M->>M: 消息模板渲染
    M->>Q: 消息入队
    Q->>P: 消费消息
    P->>P: 用户筛选
    P->>P: 推送渠道选择
    
    alt 站内消息
        P->>U: WebSocket推送
    else 短信通知
        P->>U: 短信发送
    else 微信推送
        P->>U: 微信模板消息
    end
    
    U->>P: 确认接收
    P->>M: 更新推送状态
```

## 🔗 相关文档

- [任务状态机详解](./task-state-machine.md)
- [用户角色权限矩阵](./user-roles.md)
- [积分奖励规则](./reward-rules.md)
- [异常处理指南](./exception-handling.md)
- [数据同步机制](./data-sync.md)

---
*业务流程详细说明，帮助理解系统的核心业务逻辑*
