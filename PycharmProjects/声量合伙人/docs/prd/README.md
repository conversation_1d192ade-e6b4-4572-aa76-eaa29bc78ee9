# PRD产品需求文档

## 📋 产品概述

**声量合伙人**是一个面向企业内部的营销推广和任务分发管理平台，通过多端应用支持员工参与营销活动，实现品牌声量的有效传播和管理。

### 产品定位
- **目标用户**: 企业员工、营销管理人员、系统管理员
- **核心价值**: 提升品牌传播效率，激励员工参与营销活动
- **应用场景**: 企业内部营销、品牌推广、任务分发、绩效管理

## 👥 用户角色体系

### 1. 系统管理员
- **权限范围**: 系统全局管理
- **主要职责**: 
  - 用户权限管理
  - 系统配置维护
  - 数据监控分析
  - 安全审计管理

### 2. 营销管理员
- **权限范围**: 营销活动管理
- **主要职责**:
  - 任务创建和发布
  - 任务审核和管理
  - 数据统计分析
  - 奖励规则配置

### 3. 部门负责人
- **权限范围**: 部门内任务管理
- **主要职责**:
  - 部门任务分配
  - 员工绩效监控
  - 任务完成情况审核
  - 团队数据分析

### 4. 普通员工
- **权限范围**: 个人任务操作
- **主要职责**:
  - 任务领取和完成
  - 个人信息维护
  - 积分和奖励查看
  - 营销内容分享

## 🎯 核心功能模块

### 1. 用户管理模块

#### 1.1 用户注册登录
- **H5端**: 微信授权 + 手机验证码登录
- **管理后台**: 用户名密码 + 验证码登录
- **小程序**: 微信一键登录

#### 1.2 用户信息管理
- 基础信息维护（姓名、头像、部门等）
- 联系方式管理
- 权限角色分配
- 账户状态控制

### 2. 任务管理模块

#### 2.1 任务生命周期
```
任务创建 → 审核中 → 已发布 → 进行中 → 已完成 → 已结算
    ↓        ↓        ↓        ↓        ↓        ↓
  草稿状态  待审核   可领取   执行中   待审核   已关闭
```

#### 2.2 任务类型
- **转发任务**: 朋友圈/群聊转发
- **原创任务**: 原创内容创作
- **视频号任务**: 视频内容发布
- **其他任务**: 自定义任务类型

#### 2.3 任务状态机
```
员工视角:
未领取 → 已领取 → 已提交 → 审核中 → 已完成/已拒绝

管理员视角:
草稿 → 待审核 → 已发布 → 进行中 → 已结束
```

### 3. 审核管理模块

#### 3.1 审核流程
1. **任务发布审核**: 管理员审核任务内容
2. **任务完成审核**: 审核员工提交的任务成果
3. **多级审核**: 支持部门级和总部级审核

#### 3.2 审核状态
- 待审核、审核通过、审核拒绝
- 审核意见和建议
- 审核历史记录

### 4. 奖励积分模块

#### 4.1 积分体系
- **任务奖励积分**: 完成任务获得
- **销售转化积分**: 销售业绩转化
- **热力值**: 活跃度指标

#### 4.2 奖励规则
- 任务类型对应不同积分
- 完成质量影响奖励倍数
- 特殊活动额外奖励

### 5. 数据统计模块

#### 5.1 个人数据
- 任务完成情况
- 积分收益统计
- 排名和成就
- 历史记录查询

#### 5.2 管理数据
- 部门任务统计
- 员工参与度分析
- 任务效果评估
- 营销ROI分析

## 🔄 核心业务流程

### 任务发布流程
```mermaid
graph TD
    A[营销人员创建任务] --> B[填写任务详情]
    B --> C[设置奖励规则]
    C --> D[选择推送范围]
    D --> E[提交审核]
    E --> F{审核结果}
    F -->|通过| G[任务发布]
    F -->|拒绝| H[修改重新提交]
    G --> I[员工可见可领取]
```

### 任务执行流程
```mermaid
graph TD
    A[员工查看任务] --> B[领取任务]
    B --> C[执行任务内容]
    C --> D[上传执行证明]
    D --> E[提交任务]
    E --> F{审核结果}
    F -->|通过| G[获得奖励]
    F -->|拒绝| H[重新提交或放弃]
```

### 用户登录流程
```mermaid
graph TD
    A[用户访问H5] --> B{是否在微信环境}
    B -->|是| C[微信授权获取信息]
    B -->|否| D[跳转登录页]
    C --> E[输入手机号]
    D --> E
    E --> F[发送验证码]
    F --> G[输入验证码]
    G --> H[登录成功]
```

## 📊 数据模型

### 核心实体关系
- **用户** ←→ **部门** (多对一)
- **用户** ←→ **角色** (多对多)
- **任务** ←→ **用户** (多对多，通过会员任务关联)
- **任务** ←→ **奖励配置** (一对多)
- **用户** ←→ **积分记录** (一对多)

### 关键字段设计
- 用户状态: 正常、停用、删除
- 任务状态: 草稿、待审核、已发布、进行中、已结束
- 审核状态: 待审核、通过、拒绝
- 积分类型: 任务奖励、销售转化、热力值

## 🎨 UI/UX设计原则

### 设计风格
- **简洁明了**: 突出核心功能，减少用户认知负担
- **一致性**: 保持各端视觉和交互的一致性
- **响应式**: 适配不同设备和屏幕尺寸
- **品牌化**: 体现企业品牌特色

### 交互原则
- **易用性**: 操作流程简单直观
- **反馈性**: 及时的操作反馈和状态提示
- **容错性**: 友好的错误处理和引导
- **可访问性**: 支持无障碍访问

## 📈 性能要求

### 响应时间
- 页面加载: < 3秒
- 接口响应: < 1秒
- 文件上传: 支持断点续传

### 并发支持
- 同时在线用户: 1000+
- 峰值QPS: 500+
- 数据库连接池: 合理配置

## 🔒 安全要求

### 数据安全
- 用户数据加密存储
- 敏感信息脱敏显示
- 数据备份和恢复

### 访问安全
- 权限控制和数据权限
- 操作日志审计
- 防止SQL注入和XSS攻击

## 🔗 相关文档

- [业务流程详细说明](./business-flows.md)
- [用户角色权限矩阵](./user-roles.md)
- [任务状态机详解](./task-state-machine.md)
- [积分奖励规则](./reward-rules.md)
- [API接口设计](../api/README.md)

---
*产品需求概览，具体功能详情请查看对应的子文档*
