<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>北汽声量合伙人管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #409EFF;
            --sidebar-width: 200px;
            --header-height: 50px;
            --tags-height: 34px;
        }

        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            color: #303133;
            background-color: #f0f2f5;
            overflow: hidden; /* Prevent body scroll */
        }

        .app-wrapper {
            display: flex;
            height: 100vh;
        }

        .sidebar-container {
            width: var(--sidebar-width);
            background-color: #304156;
            transition: width 0.28s;
            overflow-y: auto;
            flex-shrink: 0;
        }
        
        .sidebar-logo-container {
            height: var(--header-height);
            line-height: var(--header-height);
            text-align: center;
        }

        .sidebar-logo-link {
            color: #fff;
            text-decoration: none;
            font-size: 14px;
            font-weight: bold;
        }

        .sidebar-logo {
            width: 32px;
            height: 32px;
            vertical-align: middle;
            margin-right: 12px;
        }

        .el-menu {
            list-style: none;
            padding-left: 0;
            margin: 0;
            border: none;
        }

        .el-menu-item, .el-submenu__title {
            height: 56px;
            line-height: 56px;
            font-size: 14px;
            color: #bfcbd9;
            padding: 0 20px;
            cursor: pointer;
            transition: border-color .3s,background-color .3s,color .3s;
        }

        .el-menu-item i, .el-submenu__title i {
            margin-right: 5px;
            width: 24px;
            text-align: center;
            font-size: 18px;
        }

        .el-menu-item:hover, .el-submenu__title:hover {
            background-color: #263445 !important;
        }
        
        .el-menu-item.is-active {
            color: var(--primary-color);
        }

        .el-submenu .el-menu {
            background-color: #1f2d3d !important;
            display: none; /* Hidden by default */
        }
        
        .el-submenu.is-opened .el-menu {
            display: block; /* Show when opened */
        }

        .el-submenu .el-menu-item {
            height: 50px;
            line-height: 50px;
            padding-left: 40px !important;
        }
        
        .el-submenu .el-menu-item:hover {
             background-color: #001528 !important;
        }
        
        .el-submenu.is-opened > .el-submenu__title .fa-chevron-right {
            transform: rotate(90deg);
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .navbar {
            height: var(--header-height);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fff;
            box-shadow: 0 1px 4px rgba(0,21,41,.08);
            padding: 0 15px;
            flex-shrink: 0;
        }

        .breadcrumb-container {
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        .hamburger-container {
            font-size: 20px;
            cursor: pointer;
            margin-right: 15px;
        }

        .right-menu {
            display: flex;
            align-items: center;
        }

        .right-menu-item {
            padding: 0 8px;
            font-size: 18px;
            cursor: pointer;
        }
        
        .avatar-container {
            margin-left: 15px;
            display: flex;
            align-items: center;
        }
        
        .avatar-container img {
            width: 40px;
            height: 40px;
            border-radius: 10px;
        }

        .tags-view-container {
            height: var(--tags-height);
            background: #fff;
            border-bottom: 1px solid #d8dce5;
            box-shadow: 0 1px 3px 0 rgba(0,0,0,.12), 0 0 3px 0 rgba(0,0,0,.04);
            display: flex;
            align-items: center;
            padding: 0 10px;
            flex-shrink: 0;
        }

        .tags-view-item {
            display: inline-flex;
            align-items: center;
            position: relative;
            cursor: pointer;
            height: 26px;
            line-height: 26px;
            border: 1px solid #d8dce5;
            color: #495060;
            background: #fff;
            padding: 0 8px;
            font-size: 12px;
            margin-left: 5px;
            border-radius: 3px;
        }
        
        .tags-view-item.active {
            background-color: var(--primary-color);
            color: #fff;
            border-color: var(--primary-color);
        }
        
        .tags-view-item .el-icon-close {
            margin-left: 5px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            text-align: center;
            transition: all .3s cubic-bezier(.645,.045,.355,1);
            transform-origin: 100% 50%;
        }
        
        .tags-view-item.active .el-icon-close,
        .tags-view-item:hover .el-icon-close {
            background-color: hsla(0,0%,100%,.3);
            color: #fff;
        }

        .app-main {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .app-main iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Dialog Styles */
        .el-dialog__wrapper {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            overflow: auto;
            margin: 0;
            z-index: 2001;
            background-color: rgba(0,0,0,.5);
            display: none; /* Hidden by default */
        }
        
        .el-dialog {
            position: relative;
            margin: 5vh auto;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,.3);
            box-sizing: border-box;
            width: 750px;
        }

        .el-dialog__header {
            padding: 20px 20px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #ebeef5;
        }
        
        .el-dialog__title {
            font-size: 18px;
            color: #303133;
        }
        
        .el-dialog__headerbtn {
            font-size: 16px;
            cursor: pointer;
            border: none;
            background: transparent;
        }

        .el-dialog__body {
            padding: 20px 30px;
            color: #606266;
            font-size: 14px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .el-dialog__footer {
            padding: 10px 20px 20px;
            text-align: right;
            border-top: 1px solid #ebeef5;
        }
        
        .el-form-item {
            margin-bottom: 22px;
            display: flex;
            align-items: flex-start;
        }
        
        .el-form-item__label {
            width: 140px;
            text-align: right;
            padding-right: 12px;
            line-height: 32px;
            flex-shrink: 0;
        }
        
        .el-form-item__content {
            flex: 1;
            position: relative;
        }
        .input-counter {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #909399;
            font-size: 12px;
            pointer-events: none;
        }
        .el-input.with-counter input {
            padding-right: 55px;
        }
        .form-section-title {
            font-size: 16px;
            color: #303133;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        .el-radio {
            margin-right: 20px;
            line-height: 32px;
        }
        .rich-text-editor {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }
        .rich-text-editor .toolbar {
            border-bottom: 1px solid #dcdfe6;
            padding: 8px;
            background: #f5f7fa;
        }
        .rich-text-editor .editor-content {
            min-height: 120px;
            padding: 10px;
        }
        .el-upload {
            width: 100px;
            height: 100px;
            border: 1px dashed #dcdfe6;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            border-radius: 4px;
        }
        .el-upload:hover {
            border-color: var(--primary-color);
        }
        .upload-tip {
            color: #909399;
            font-size: 12px;
            line-height: 1.5;
            margin-top: 5px;
        }
        .form-item-tip {
            color: #909399;
            font-size: 12px;
            line-height: 1.5;
            margin-top: 5px;
        }
        .el-input, .el-select, .el-date-editor {
            width: 100%;
        }
        .el-input input, .el-select select, .el-date-editor input {
            width: 100%;
            box-sizing: border-box;
            height: 32px;
            line-height: 32px;
            padding: 0 15px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }
        .el-button {
            padding: 8px 15px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #dcdfe6;
            background-color: #fff;
            margin-left: 10px;
        }
        .el-button--primary {
            color: #fff;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
    </style>
    <script>
        // Listen for messages from the iframe
        window.addEventListener('message', function(event) {
            // For security, you might check event.origin in a real app
            if (event.data === 'show-task-dialog') {
                const dialog = document.getElementById('dialog-new-task');
                if (dialog) {
                    dialog.style.display = 'block';
                }
            }
        });

        // Handle menu toggling and dialog closing
        document.addEventListener('DOMContentLoaded', function() {
            // --- Menu Toggle ---
            const submenus = document.querySelectorAll('.el-submenu > .el-submenu__title');
            submenus.forEach(submenu => {
                submenu.addEventListener('click', function() {
                    this.parentElement.classList.toggle('is-opened');
                });
            });

            // --- Dialog Closing Logic ---
            const dialog = document.getElementById('dialog-new-task');
            const btnCloseDialog = document.getElementById('btn-close-dialog');
            const btnCancelDialog = document.getElementById('btn-cancel-dialog');

            const closeDialog = () => {
                if (dialog) dialog.style.display = 'none';
            };

            if(btnCloseDialog) btnCloseDialog.addEventListener('click', closeDialog);
            if(btnCancelDialog) btnCancelDialog.addEventListener('click', closeDialog);
        });

        // Handle new task form logic
        document.addEventListener('DOMContentLoaded', function() {
            const dialog = document.getElementById('dialog-new-task');
            if (!dialog) return;

            const isExamRadios = dialog.querySelectorAll('input[name="is_exam"]');
            const assessmentScopeContainer = dialog.querySelector('#assessment-scope-container');
            const assessmentScopeRadios = dialog.querySelectorAll('input[name="assessment_scope"]');
            const assessmentScopeSelect = assessmentScopeContainer.querySelector('select');

            const pushScopeNormal = dialog.querySelector('#push-scope-normal');
            const pushScopeAssessment = dialog.querySelector('#push-scope-assessment');
            const pushScopeDeptSelectContainer = dialog.querySelector('#push-scope-dept-select');
            const pushScopeDeptSelect = pushScopeDeptSelectContainer.querySelector('select');
            
            const pushScopeNormalRadios = dialog.querySelectorAll('input[name="push_scope"]');
            const pushScopeAssessmentRadios = dialog.querySelectorAll('input[name="push_scope_assessment"]');


            function updateFormState() {
                const isExam = dialog.querySelector('input[name="is_exam"]:checked').value === '1';
                
                // 1. Handle "考核范围" visibility
                assessmentScopeContainer.style.display = isExam ? 'flex' : 'none';

                // 2. Handle "任务推送范围" options
                pushScopeNormal.style.display = isExam ? 'none' : 'inline';
                pushScopeAssessment.style.display = isExam ? 'inline' : 'none';

                // Reset push scope selection when hiding
                if (isExam) {
                    dialog.querySelector('input[name="push_scope_assessment"][value="all"]').checked = true;
                } else {
                    dialog.querySelector('input[name="push_scope"][value="all"]').checked = true;
                }

                // 3. Handle department selection visibility and state
                const isDeptAssessment = isExam && dialog.querySelector('input[name="assessment_scope"]:checked').value === 'dept';
                assessmentScopeSelect.disabled = !isDeptAssessment;

                const isDeptPush = !isExam && dialog.querySelector('input[name="push_scope"]:checked').value === 'dept';
                pushScopeDeptSelectContainer.style.display = isDeptPush ? 'inline-block' : 'none';
                pushScopeDeptSelect.disabled = !isDeptPush;
            }

            isExamRadios.forEach(radio => radio.addEventListener('change', updateFormState));
            assessmentScopeRadios.forEach(radio => radio.addEventListener('change', updateFormState));
            pushScopeNormalRadios.forEach(radio => radio.addEventListener('change', updateFormState));

            // Set initial state when dialog opens or page loads
            updateFormState();
        });
    </script>
</head>
<body>
    <div class="app-wrapper">
        <div class="sidebar-container">
            <div class="sidebar-logo-container">
                <a href="#" class="sidebar-logo-link">
                    <img src="assets/logo.png" class="sidebar-logo">
                    <span>北汽声量合伙人</span>
                </a>
            </div>
            <ul class="el-menu">
                <li class="el-menu-item"><i class="fas fa-home"></i><span>首页</span></li>
                <li class="el-submenu">
                    <div class="el-submenu__title"><i class="fas fa-user-friends"></i><span>用户管理</span><i class="fas fa-chevron-right" style="float:right; line-height: 56px;"></i></div>
                </li>
                <li class="el-submenu">
                    <div class="el-submenu__title"><i class="fas fa-photo-video"></i><span>媒体管理</span><i class="fas fa-chevron-right" style="float:right; line-height: 56px;"></i></div>
                </li>
                <li class="el-submenu is-opened" id="menu-media-task">
                    <div class="el-submenu__title"><i class="fas fa-tasks"></i><span>媒体任务</span><i class="fas fa-chevron-right" style="float:right; line-height: 56px; transition: transform .3s;"></i></div>
                    <ul class="el-menu">
                        <li class="el-menu-item is-active"><span>任务管理</span></li>
                        <li class="el-menu-item"><span>任务审核</span></li>
                    </ul>
                </li>
                <li class="el-submenu">
                    <div class="el-submenu__title"><i class="fas fa-calendar-alt"></i><span>预约试驾</span><i class="fas fa-chevron-right" style="float:right; line-height: 56px;"></i></div>
                </li>
                <li class="el-submenu">
                    <div class="el-submenu__title"><i class="fas fa-file-alt"></i><span>内容发布</span><i class="fas fa-chevron-right" style="float:right; line-height: 56px;"></i></div>
                </li>
                <li class="el-submenu">
                    <div class="el-submenu__title"><i class="fas fa-cogs"></i><span>平台运营</span><i class="fas fa-chevron-right" style="float:right; line-height: 56px;"></i></div>
                </li>
                <li class="el-submenu">
                    <div class="el-submenu__title"><i class="fas fa-chart-bar"></i><span>数据统计</span><i class="fas fa-chevron-right" style="float:right; line-height: 56px;"></i></div>
                </li>
            </ul>
        </div>
        <div class="main-container">
            <div class="navbar">
                <div class="breadcrumb-container">
                    <span class="hamburger-container"><i class="fas fa-bars"></i></span>
                    <span>首页 / 媒体任务 / 任务管理</span>
                </div>
                <div class="right-menu">
                    <span class="right-menu-item"><i class="fas fa-search"></i></span>
                    <span class="right-menu-item"><i class="fas fa-comment-dots"></i></span>
                    <span class="right-menu-item"><i class="fas fa-expand-arrows-alt"></i></span>
                    <span class="right-menu-item"><i class="fas fa-font"></i></span>
                    <span class="right-menu-item"><i class="fas fa-text-height"></i></span>
                    <div class="avatar-container">
                        <img src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" alt="avatar">
                    </div>
                </div>
            </div>
            <div class="tags-view-container">
                <span class="tags-view-item">首页</span>
                <span class="tags-view-item active">任务管理 <span class="el-icon-close">×</span></span>
                <span class="tags-view-item">文章管理 <span class="el-icon-close">×</span></span>
            </div>
            <div class="app-main">
                <iframe src="任务管理.html" frameborder="0"></iframe>
            </div>
        </div>
    </div>


    <div class="el-dialog__wrapper" id="dialog-new-task">
        <div class="el-dialog">
            <div class="el-dialog__header">
                <span class="el-dialog__title">新建任务</span>
                <button type="button" class="el-dialog__headerbtn" id="btn-close-dialog"><i class="fas fa-times"></i></button>
            </div>
            <div class="el-dialog__body">
                <form onsubmit="return false;">
                    <h4 class="form-section-title">基础信息</h4>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 任务类型</label>
                        <div class="el-form-item__content"><div class="el-select"><select><option>转发-朋友圈</option><option>转发-微信</option></select></div></div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 任务名称</label>
                        <div class="el-form-item__content"><div class="el-input with-counter"><input type="text" placeholder="请输入任务名称" maxlength="40"><span class="input-counter">0 / 40</span></div></div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">备注</label>
                        <div class="el-form-item__content"><div class="el-input with-counter"><input type="text" placeholder="请输入备注" maxlength="500"><span class="input-counter">0 / 500</span></div></div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 是否考核任务</label>
                        <div class="el-form-item__content">
                            <label class="el-radio"><input type="radio" name="is_exam" value="1"> 是</label>
                            <label class="el-radio"><input type="radio" name="is_exam" value="0" checked> 否</label>
                        </div>
                    </div>
                    <div class="el-form-item" id="assessment-scope-container" style="display: none;">
                        <label class="el-form-item__label">* 考核范围</label>
                        <div class="el-form-item__content" style="display: flex; align-items: center;">
                            <label class="el-radio"><input type="radio" name="assessment_scope" value="all" checked> 全员考核</label>
                            <label class="el-radio"><input type="radio" name="assessment_scope" value="dept"> 部门考核</label>
                            <div class="el-select" style="margin-left: 10px;">
                                <select disabled multiple><option>产品部</option><option>研发部</option><option>市场部</option></select>
                            </div>
                        </div>
                    </div>
                    <div class="el-form-item" id="push-scope-container">
                        <label class="el-form-item__label">* 任务推送范围</label>
                        <div class="el-form-item__content" style="display: flex; align-items: center;">
                            <!-- Options for "考核任务: 否" -->
                            <span id="push-scope-normal">
                                <label class="el-radio"><input type="radio" name="push_scope" value="all" checked> 全员推送</label>
                                <label class="el-radio"><input type="radio" name="push_scope" value="dept"> 指定部门推送</label>
                            </span>
                            <!-- Options for "考核任务: 是" -->
                            <span id="push-scope-assessment" style="display: none;">
                                <label class="el-radio"><input type="radio" name="push_scope_assessment" value="all" checked> 全员推送</label>
                                <label class="el-radio"><input type="radio" name="push_scope_assessment" value="assessment_dept"> 仅考核部门</label>
                            </span>
                            <div class="el-select" id="push-scope-dept-select" style="margin-left: 10px; display: none;">
                                <select disabled multiple><option>产品部</option><option>研发部</option><option>市场部</option></select>
                            </div>
                        </div>
                    </div>
                     <div class="el-form-item">
                        <label class="el-form-item__label">发布账号</label>
                        <div class="el-form-item__content"><div class="el-input with-counter"><input type="text" placeholder="请输入发布账号" maxlength="500"><span class="input-counter">0 / 500</span></div></div>
                    </div>

                    <h4 class="form-section-title">任务详情设置</h4>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 任务介绍</label>
                        <div class="el-form-item__content">
                            <div class="rich-text-editor">
                                <div class="toolbar">...</div>
                                <div class="editor-content" contenteditable="true" placeholder="请输入"></div>
                            </div>
                        </div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 海报</label>
                        <div class="el-form-item__content">
                            <div class="el-upload"><i class="fas fa-plus" style="font-size: 28px; color: #8c939d;"></i></div>
                            <div class="upload-tip">请上传大小不超过5MB 格式为png/jpg/jpeg的文件</div>
                        </div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 转发语</label>
                        <div class="el-form-item__content"><div class="el-input with-counter"><input type="text" placeholder="请输入转发语" maxlength="200"><span class="input-counter">0 / 200</span></div></div>
                    </div>

                    <h4 class="form-section-title">积分奖励配置</h4>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 任务奖励</label>
                        <div class="el-form-item__content" style="display: flex; align-items: center;">
                            <div class="el-input" style="width: 150px;"><input type="number" placeholder="请输入积分"></div>
                            <span style="margin-left: 10px;">积分</span>
                            <span style="margin-left: 10px; color: #909399; font-size: 12px;">(注:提交任务并审核通过后发放积分)</span>
                        </div>
                    </div>

                    <h4 class="form-section-title">通知设置</h4>
                     <div class="el-form-item">
                        <label class="el-form-item__label">* 工作内容</label>
                        <div class="el-form-item__content">
                            <label class="el-radio"><input type="radio" name="work_content" value="default" checked> 默认文案</label>
                            <label class="el-radio"><input type="radio" name="work_content" value="custom"> 自定义</label>
                        </div>
                    </div>

                    <h4 class="form-section-title">预算和投放设置</h4>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 任务预算</label>
                        <div class="el-form-item__content">
                            <div style="display: flex; align-items: center;">
                                <div class="el-input" style="width: 150px;"><input type="number" placeholder="请输入任务预算"></div>
                                <span style="margin-left: 10px;">元</span>
                            </div>
                            <div class="form-item-tip">注：1元=100积分，如任务所发放积分达到任务预算，则停止继续发放积分，超过预算后提交的任务都视为“任务未完成”</div>
                        </div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 领取奖励截止时间</label>
                        <div class="el-form-item__content"><div class="el-date-editor"><input type="text" placeholder="请选择日期"></div></div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">是否立即上架</label>
                        <div class="el-form-item__content">
                            <label class="el-radio"><input type="radio" name="launch_now" value="1" checked> 立即上架</label>
                            <label class="el-radio"><input type="radio" name="launch_now" value="0"> 定时上架</label>
                        </div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">新任务消息推送</label>
                        <div class="el-form-item__content">
                            <label class="el-radio"><input type="radio" name="push_new" value="1" checked> 推送</label>
                            <label class="el-radio"><input type="radio" name="push_new" value="0"> 不推送</label>
                        </div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">是否在任务广场展示</label>
                        <div class="el-form-item__content">
                            <label class="el-radio"><input type="radio" name="show_in_square" value="1" checked> 展示</label>
                            <label class="el-radio"><input type="radio" name="show_in_square" value="0"> 不展示</label>
                        </div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">审核人员</label>
                        <div class="el-form-item__content"><div class="el-select"><select><option>请选择审核人员</option></select></div></div>
                    </div>
                </form>
            </div>
            <div class="el-dialog__footer">
                <button class="el-button" id="btn-cancel-dialog">取消</button>
                <button class="el-button el-button--primary">提交</button>
            </div>
        </div>
    </div>

</body>
</html>