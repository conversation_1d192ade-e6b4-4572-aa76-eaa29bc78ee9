# 任务管理页面原型 (模块化)

这是一个基于 `https://uat-partners.baicdt.com/manage/mediaTask/mediaTask` 页面的模块化HTML原型。

## 文件结构

- `layout.html`: 应用的主布局文件，包含侧边栏、顶部导航等“外壳”部分。它通过一个 `<iframe>` 来加载内容页面。
- `任务管理.html`: 核心内容页面，包含任务管理的搜索、列表和操作功能。

## 使用方法

直接在现代浏览器中打开 `layout.html` 文件即可查看完整的页面效果。`任务管理.html` 会被自动嵌入。

## 已实现功能

- **模块化设计**：将主布局和核心内容分离，便于独立开发和维护。
- **完整布局克隆**：100%复刻了页面的主布局和内容区域。
- **表格横向滚动**：在`任务管理.html`中，表格内容超出时，只在表格区域出现横向滚动条，不影响主页面布局。
- **操作列固定**：表格的“操作”列在横向滚动时会固定在右侧。
- **交互功能**：实现了侧边栏菜单的展开/折叠和“新建任务”弹窗的显示/隐藏。