<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>北汽声量合伙人管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #409EFF;
            --sidebar-width: 200px;
            --header-height: 50px;
            --tags-height: 34px;
        }

        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            color: #303133;
            background-color: #f0f2f5;
        }

        .app-wrapper {
            display: flex;
            height: 100vh;
        }

        .sidebar-container {
            width: var(--sidebar-width);
            background-color: #304156;
            transition: width 0.28s;
            overflow: hidden;
            flex-shrink: 0;
        }
        
        .sidebar-logo-container {
            height: var(--header-height);
            line-height: var(--header-height);
            text-align: center;
            background: #2b2f3a;
        }

        .sidebar-logo-link {
            color: #fff;
            text-decoration: none;
            font-size: 14px;
            font-weight: bold;
        }

        .sidebar-logo {
            width: 32px;
            height: 32px;
            vertical-align: middle;
            margin-right: 12px;
        }

        .el-menu {
            list-style: none;
            padding-left: 0;
            margin: 0;
            border: none;
        }

        .el-menu-item, .el-submenu__title {
            height: 56px;
            line-height: 56px;
            font-size: 14px;
            color: #bfcbd9;
            padding: 0 20px;
            cursor: pointer;
            transition: border-color .3s,background-color .3s,color .3s;
        }

        .el-menu-item i, .el-submenu__title i {
            margin-right: 5px;
            width: 24px;
            text-align: center;
            font-size: 18px;
        }

        .el-menu-item:hover, .el-submenu__title:hover {
            background-color: #263445 !important;
        }
        
        .el-menu-item.is-active {
            color: var(--primary-color);
        }

        .el-submenu .el-menu {
            background-color: #1f2d3d !important;
        }

        .el-submenu .el-menu-item {
            height: 50px;
            line-height: 50px;
            padding-left: 40px !important;
        }
        
        .el-submenu .el-menu-item:hover {
             background-color: #001528 !important;
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .navbar {
            height: var(--header-height);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fff;
            box-shadow: 0 1px 4px rgba(0,21,41,.08);
            padding: 0 15px;
        }

        .breadcrumb-container {
            display: flex;
            align-items: center;
        }

        .hamburger-container {
            font-size: 20px;
            cursor: pointer;
            margin-right: 15px;
        }

        .right-menu {
            display: flex;
            align-items: center;
        }

        .right-menu-item {
            padding: 0 8px;
            font-size: 18px;
            cursor: pointer;
        }
        
        .avatar-container {
            margin-left: 30px;
        }

        .tags-view-container {
            height: var(--tags-height);
            background: #fff;
            border-bottom: 1px solid #d8dce5;
            box-shadow: 0 1px 3px 0 rgba(0,0,0,.12), 0 0 3px 0 rgba(0,0,0,.04);
            display: flex;
            align-items: center;
            padding: 0 10px;
        }

        .tags-view-item {
            display: inline-block;
            position: relative;
            cursor: pointer;
            height: 26px;
            line-height: 26px;
            border: 1px solid #d8dce5;
            color: #495060;
            background: #fff;
            padding: 0 8px;
            font-size: 12px;
            margin-left: 5px;
        }
        
        .tags-view-item.active {
            background-color: var(--primary-color);
            color: #fff;
            border-color: var(--primary-color);
        }

        .app-main {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .app-container {
            background: #fff;
            padding: 20px;
        }

        .filter-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .filter-item {
            display: flex;
            align-items: center;
        }

        .filter-item label {
            width: 80px;
            text-align: right;
            margin-right: 10px;
            font-size: 14px;
        }

        .el-input, .el-select, .el-date-editor {
            flex: 1;
        }
        
        .el-input input, .el-select select, .el-date-editor input {
            width: 100%;
            height: 32px;
            line-height: 32px;
            padding: 0 15px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }

        .el-button {
            padding: 9px 15px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #dcdfe6;
            background-color: #fff;
        }
        
        .el-button--primary {
            color: #fff;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .table-container {
            margin-top: 20px;
            overflow-x: auto;
        }

        .el-table {
            width: max-content; /* Let table width be determined by its content */
            border-collapse: collapse;
        }

        .el-table th, .el-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
            text-align: left;
            white-space: nowrap; /* 防止文本换行 */
        }

        .el-table th {
            background-color: #fafafa;
            color: #606266;
            font-weight: 500;
        }
        
        .el-table .action-column {
            position: sticky;
            right: 0;
            background-color: #fff;
            z-index: 1;
        }
        
        .el-table th.action-column {
            background-color: #fafafa;
        }

        .el-button--text {
            border: none;
            background: transparent;
            color: var(--primary-color);
            padding: 0;
        }
        
        .el-dialog__wrapper {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            overflow: auto;
            margin: 0;
            z-index: 2001;
            background-color: rgba(0,0,0,.5);
            display: none;
        }
        
        .el-dialog {
            position: relative;
            margin: 15vh auto 50px;
            background: #fff;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(0,0,0,.3);
            box-sizing: border-box;
            width: 50%;
        }

        .el-dialog__header {
            padding: 20px 20px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .el-dialog__title {
            font-size: 18px;
            color: #303133;
        }
        
        .el-dialog__headerbtn {
            font-size: 16px;
            cursor: pointer;
        }

        .el-dialog__body {
            padding: 30px 20px;
            color: #606266;
            font-size: 14px;
        }
        
        .el-dialog__footer {
            padding: 10px 20px 20px;
            text-align: right;
        }
        
        .el-form-item {
            margin-bottom: 22px;
            display: flex;
            align-items: center;
        }
        
        .el-form-item__label {
            width: 120px;
            text-align: right;
            padding-right: 12px;
        }
        
        .el-form-item__content {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <div class="sidebar-container">
            <div class="sidebar-logo-container">
                <a href="#" class="sidebar-logo-link">
                    <img src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg" class="sidebar-logo">
                    <span>北汽声量合伙人</span>
                </a>
            </div>
            <ul class="el-menu">
                <li class="el-menu-item"><i class="fas fa-home"></i><span>首页</span></li>
                <li class="el-submenu">
                    <div class="el-submenu__title"><i class="fas fa-user-friends"></i><span>用户管理</span><i class="fas fa-chevron-right" style="float:right; line-height: 56px;"></i></div>
                </li>
                <li class="el-submenu">
                    <div class="el-submenu__title"><i class="fas fa-photo-video"></i><span>媒体管理</span><i class="fas fa-chevron-right" style="float:right; line-height: 56px;"></i></div>
                </li>
                <li class="el-submenu" id="menu-media-task">
                    <div class="el-submenu__title"><i class="fas fa-tasks"></i><span>媒体任务</span><i class="fas fa-chevron-down" style="float:right; line-height: 56px;"></i></div>
                    <ul class="el-menu">
                        <li class="el-menu-item is-active"><span>任务管理</span></li>
                        <li class="el-menu-item"><span>任务审核</span></li>
                    </ul>
                </li>
                <!-- more menus -->
            </ul>
        </div>
        <div class="main-container">
            <div class="navbar">
                <div class="breadcrumb-container">
                    <span class="hamburger-container"><i class="fas fa-bars"></i></span>
                    <span>首页 / 媒体任务 / 任务管理</span>
                </div>
                <div class="right-menu">
                    <span class="right-menu-item"><i class="fas fa-search"></i></span>
                    <span class="right-menu-item"><i class="far fa-bell"></i></span>
                    <span class="right-menu-item"><i class="fas fa-user-circle"></i> Admin</span>
                </div>
            </div>
            <div class="tags-view-container">
                <span class="tags-view-item active">任务管理</span>
            </div>
            <div class="app-main">
                <div class="app-container">
                    <div class="filter-container">
                        <div class="filter-item"><label>任务ID</label><div class="el-input"><input type="text" placeholder="请输入任务ID"></div></div>
                        <div class="filter-item"><label>任务名称</label><div class="el-input"><input type="text" placeholder="请输入任务名称"></div></div>
                        <div class="filter-item"><label>任务类型</label><div class="el-select"><select><option>请选择任务类型</option></select></div></div>
                        <div class="filter-item"><label>任务状态</label><div class="el-select"><select><option>请选择任务状态</option></select></div></div>
                        <div class="filter-item"><label>发布时间</label><div class="el-date-editor"><input type="text" placeholder="选择日期范围"></div></div>
                        <div class="filter-item"><label>截止时间</label><div class="el-date-editor"><input type="text" placeholder="选择日期范围"></div></div>
                        <div class="filter-item"><label>发布账号</label><div class="el-input"><input type="text" placeholder="请输入发布账号"></div></div>
                        <div class="filter-item">
                            <button class="el-button el-button--primary">查询</button>
                            <button class="el-button">重置</button>
                        </div>
                    </div>
                    
                    <div>
                        <button class="el-button el-button--primary" id="btn-new-task">新建任务</button>
                        <button class="el-button">导出</button>
                    </div>

                    <div class="table-container">
                        <table class="el-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>任务名称</th>
                                    <th>任务类型</th>
                                    <th>发布账号</th>
                                    <th>任务链接</th>
                                    <th>任务预算(元)</th>
                                    <th>实消预算(元)</th>
                                    <th>领取任务数</th>
                                    <th>提交任务数</th>
                                    <th>完成任务数</th>
                                    <th>PV</th>
                                    <th>UV</th>
                                    <th>有效触达</th>
                                    <th>任务发布时间</th>
                                    <th>任务截止时间</th>
                                    <th>任务状态</th>
                                    <th class="action-column">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1950130139070304258</td>
                                    <td>0729微信任务-2 考核</td>
                                    <td>转发-微信</td>
                                    <td>0729微信任务-2 考核</td>
                                    <td><a href="#">https://mp.weixin.qq.com</a></td>
                                    <td>1.00</td>
                                    <td>0.00</td>
                                    <td>0</td>
                                    <td>0</td>
                                    <td>0</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>2025-07-29 17:23:04</td>
                                    <td>2025-07-31</td>
                                    <td>审核中</td>
                                    <td class="action-column">
                                        <button class="el-button el-button--text">预览</button>
                                        <button class="el-button el-button--text">查看</button>
                                        <button class="el-button el-button--text" disabled>编辑</button>
                                        <button class="el-button el-button--text" disabled>立即下架</button>
                                    </td>
                                </tr>
                                <!-- more rows -->
                                <tr>
                                    <td>1950124148523184129</td>
                                    <td>0729互动视频-1 考核</td>
                                    <td>互动-视频号</td>
                                    <td>0729互动视频-1 考核</td>
                                    <td>-</td>
                                    <td>1.00</td>
                                    <td>0.00</td>
                                    <td>1</td>
                                    <td>1</td>
                                    <td>0</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>2025-07-29 17:20:24</td>
                                    <td>2025-07-31</td>
                                    <td>已上架</td>
                                    <td class="action-column">
                                        <button class="el-button el-button--text">预览</button>
                                        <button class="el-button el-button--text">查看</button>
                                        <button class="el-button el-button--text" disabled>编辑</button>
                                        <button class="el-button el-button--text">立即下架</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>1950122323086585857</td>
                                    <td>0729原创任务-1</td>
                                    <td>原创-其他</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>1.00</td>
                                    <td>0.00</td>
                                    <td>0</td>
                                    <td>0</td>
                                    <td>0</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>2025-07-29 17:12:48</td>
                                    <td>2025-07-31</td>
                                    <td>已上架</td>
                                    <td class="action-column">
                                        <button class="el-button el-button--text">预览</button>
                                        <button class="el-button el-button--text">查看</button>
                                        <button class="el-button el-button--text" disabled>编辑</button>
                                        <button class="el-button el-button--text">立即下架</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>1950121137847578626</td>
                                    <td>0729微信任务-1 非考核</td>
                                    <td>转发-微信</td>
                                    <td>0729微信任务-1 非考核</td>
                                    <td><a href="#">https://mp.weixin.qq.com/</a></td>
                                    <td>1.00</td>
                                    <td>0.00</td>
                                    <td>0</td>
                                    <td>0</td>
                                    <td>0</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>2025-07-29 17:11:06</td>
                                    <td>2025-07-31</td>
                                    <td>已上架</td>
                                    <td class="action-column">
                                        <button class="el-button el-button--text">预览</button>
                                        <button class="el-button el-button--text">查看</button>
                                        <button class="el-button el-button--text" disabled>编辑</button>
                                        <button class="el-button el-button--text">立即下架</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>1950119471341547522</td>
                                    <td>0729朋友全任务-1 考核</td>
                                    <td>转发-朋友圈</td>
                                    <td>0729朋友全任务-1</td>
                                    <td>-</td>
                                    <td>1.00</td>
                                    <td>0.01</td>
                                    <td>1</td>
                                    <td>1</td>
                                    <td>1</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>2025-07-29 17:01:28</td>
                                    <td>2025-07-31</td>
                                    <td>已上架</td>
                                    <td class="action-column">
                                        <button class="el-button el-button--text">预览</button>
                                        <button class="el-button el-button--text">查看</button>
                                        <button class="el-button el-button--text" disabled>编辑</button>
                                        <button class="el-button el-button--text">立即下架</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="el-dialog__wrapper" id="dialog-new-task">
        <div class="el-dialog">
            <div class="el-dialog__header">
                <span class="el-dialog__title">新建任务</span>
                <button type="button" class="el-dialog__headerbtn" id="btn-close-dialog"><i class="fas fa-times"></i></button>
            </div>
            <div class="el-dialog__body">
                <form>
                    <h4>基础信息</h4>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 任务类型</label>
                        <div class="el-form-item__content"><div class="el-select"><select><option>转发-朋友圈</option></select></div></div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 任务名称</label>
                        <div class="el-form-item__content"><div class="el-input"><input type="text" placeholder="请输入"></div></div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label">* 是否考核任务</label>
                        <div class="el-form-item__content">
                            <label><input type="radio" name="is_exam" value="1"> 是</label>
                            <label><input type="radio" name="is_exam" value="0" checked> 否</label>
                        </div>
                    </div>
                    <!-- more form items -->
                </form>
            </div>
            <div class="el-dialog__footer">
                <button class="el-button" id="btn-cancel-dialog">取消</button>
                <button class="el-button el-button--primary">提交</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const btnNewTask = document.getElementById('btn-new-task');
            const btnCloseDialog = document.getElementById('btn-close-dialog');
            const btnCancelDialog = document.getElementById('btn-cancel-dialog');
            const dialog = document.getElementById('dialog-new-task');

            btnNewTask.addEventListener('click', () => {
                dialog.style.display = 'block';
            });

            const closeDialog = () => {
                dialog.style.display = 'none';
            };

            btnCloseDialog.addEventListener('click', closeDialog);
            btnCancelDialog.addEventListener('click', closeDialog);
            
            // Toggle submenu
            const menuMediaTask = document.getElementById('menu-media-task');
            const submenu = menuMediaTask.querySelector('.el-menu');
            menuMediaTask.querySelector('.el-submenu__title').addEventListener('click', () => {
                if (submenu.style.display === 'none' || submenu.style.display === '') {
                    submenu.style.display = 'block';
                } else {
                    submenu.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>