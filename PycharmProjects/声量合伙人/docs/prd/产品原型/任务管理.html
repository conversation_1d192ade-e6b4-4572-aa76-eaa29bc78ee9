<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #409EFF;
        }

        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            color: #303133;
            background-color: #fff;
        }

        .app-container {
            padding: 20px;
        }

        .filter-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .filter-item {
            display: flex;
            align-items: center;
        }

        .filter-item label {
            width: 80px;
            text-align: right;
            margin-right: 10px;
            font-size: 14px;
            color: #606266;
        }

        .el-input, .el-select, .el-date-editor {
            flex: 1;
        }
        
        .el-input input, .el-select select, .el-date-editor input {
            width: 100%;
            box-sizing: border-box;
            height: 32px;
            line-height: 32px;
            padding: 0 15px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }

        .el-button {
            padding: 8px 15px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #dcdfe6;
            background-color: #fff;
            margin-left: 10px;
        }
        
        .el-button--primary {
            color: #fff;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .el-button--warning {
            color: #fff;
            background-color: #E6A23C;
            border-color: #E6A23C;
        }

        .table-actions {
            margin-bottom: 20px;
        }

        .table-container {
            margin-top: 20px;
            overflow-x: auto;
        }

        .el-table {
            border-collapse: collapse;
            width: 100%;
            min-width: 1600px;
        }

        .el-table th, .el-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
            text-align: left;
            white-space: nowrap;
        }

        .el-table th {
            background-color: #fafafa;
            color: #606266;
            font-weight: 500;
        }
        
        .el-table .action-column {
            position: sticky;
            right: 0;
            background-color: #fff;
            z-index: 1;
            box-shadow: -2px 0 5px -2px rgba(0,0,0,0.1);
        }
        
        .el-table th.action-column {
            background-color: #fafafa;
        }

        .el-button--text {
            border: none;
            background: transparent;
            color: var(--primary-color);
            padding: 0 5px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="filter-container">
            <div class="filter-item"><label>任务ID</label><div class="el-input"><input type="text" placeholder="请输入任务ID"></div></div>
            <div class="filter-item"><label>任务名称</label><div class="el-input"><input type="text" placeholder="请输入任务名称"></div></div>
            <div class="filter-item"><label>任务类型</label><div class="el-select"><select><option>请选择任务类型</option></select></div></div>
            <div class="filter-item"><label>任务状态</label><div class="el-select"><select><option>请选择任务状态</option></select></div></div>
            <div class="filter-item"><label>发布时间</label><div class="el-date-editor"><input type="text" placeholder="选择日期范围"></div></div>
            <div class="filter-item"><label>截止时间</label><div class="el-date-editor"><input type="text" placeholder="选择日期范围"></div></div>
            <div class="filter-item"><label>发布账号</label><div class="el-input"><input type="text" placeholder="请输入发布账号"></div></div>
            <div class="filter-item">
                <button class="el-button el-button--primary"><i class="fas fa-search"></i> 查询</button>
                <button class="el-button"><i class="fas fa-undo"></i> 重置</button>
            </div>
        </div>
        
        <div class="table-actions">
            <button class="el-button el-button--primary" id="btn-new-task"><i class="fas fa-plus"></i> 新建任务</button>
            <button class="el-button el-button--warning"><i class="fas fa-download"></i> 导出</button>
        </div>

        <div class="table-container">
            <table class="el-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>任务名称</th>
                        <th>任务类型</th>
                        <th>发布账号</th>
                        <th>任务链接</th>
                        <th>任务预算(元)</th>
                        <th>实消预算(元)</th>
                        <th>领取任务数</th>
                        <th>提交任务数</th>
                        <th>完成任务数</th>
                        <th>PV</th>
                        <th>UV</th>
                        <th>有效触达</th>
                        <th>任务发布时间</th>
                        <th>任务截止时间</th>
                        <th>任务状态</th>
                        <th class="action-column">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1950130139070304258</td>
                        <td>0729微信任务-2 考核</td>
                        <td>转发-微信</td>
                        <td>0729微信任务-2 考核</td>
                        <td><a href="#">https://mp.weixin.qq.com</a></td>
                        <td>1.00</td>
                        <td>0.00</td>
                        <td>0</td>
                        <td>0</td>
                        <td>0</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>2025-07-29 17:23:04</td>
                        <td>2025-07-31</td>
                        <td>审核中</td>
                        <td class="action-column">
                            <button class="el-button el-button--text">预览</button>
                            <button class="el-button el-button--text">查看</button>
                            <button class="el-button el-button--text" disabled>编辑</button>
                            <button class="el-button el-button--text" disabled>立即下架</button>
                        </td>
                    </tr>
                    <tr>
                        <td>1950124148523184129</td>
                        <td>0729互动视频-1 考核</td>
                        <td>互动-视频号</td>
                        <td>0729互动视频-1 考核</td>
                        <td>-</td>
                        <td>1.00</td>
                        <td>0.00</td>
                        <td>1</td>
                        <td>1</td>
                        <td>0</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>2025-07-29 17:20:24</td>
                        <td>2025-07-31</td>
                        <td>已上架</td>
                        <td class="action-column">
                            <button class="el-button el-button--text">预览</button>
                            <button class="el-button el-button--text">查看</button>
                            <button class="el-button el-button--text" disabled>编辑</button>
                            <button class="el-button el-button--text">立即下架</button>
                        </td>
                    </tr>
                    <tr>
                        <td>1950122323086585857</td>
                        <td>0729原创任务-1</td>
                        <td>原创-其他</td>
                        <td>-</td>
                        <td>-</td>
                        <td>1.00</td>
                        <td>0.00</td>
                        <td>0</td>
                        <td>0</td>
                        <td>0</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>2025-07-29 17:12:48</td>
                        <td>2025-07-31</td>
                        <td>已上架</td>
                        <td class="action-column">
                            <button class="el-button el-button--text">预览</button>
                            <button class="el-button el-button--text">查看</button>
                            <button class="el-button el-button--text" disabled>编辑</button>
                            <button class="el-button el-button--text">立即下架</button>
                        </td>
                    </tr>
                    <tr>
                        <td>1950121137847578626</td>
                        <td>0729微信任务-1 非考核</td>
                        <td>转发-微信</td>
                        <td>0729微信任务-1 非考核</td>
                        <td><a href="#">https://mp.weixin.qq.com/</a></td>
                        <td>1.00</td>
                        <td>0.00</td>
                        <td>0</td>
                        <td>0</td>
                        <td>0</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>2025-07-29 17:11:06</td>
                        <td>2025-07-31</td>
                        <td>已上架</td>
                        <td class="action-column">
                            <button class="el-button el-button--text">预览</button>
                            <button class="el-button el-button--text">查看</button>
                            <button class="el-button el-button--text" disabled>编辑</button>
                            <button class="el-button el-button--text">立即下架</button>
                        </td>
                    </tr>
                    <tr>
                        <td>1950119471341547522</td>
                        <td>0729朋友全任务-1 考核</td>
                        <td>转发-朋友圈</td>
                        <td>0729朋友全任务-1</td>
                        <td>-</td>
                        <td>1.00</td>
                        <td>0.01</td>
                        <td>1</td>
                        <td>1</td>
                        <td>1</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>2025-07-29 17:01:28</td>
                        <td>2025-07-31</td>
                        <td>已上架</td>
                        <td class="action-column">
                            <button class="el-button el-button--text">预览</button>
                            <button class="el-button el-button--text">查看</button>
                            <button class="el-button el-button--text" disabled>编辑</button>
                            <button class="el-button el-button--text">立即下架</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const btnNewTask = document.getElementById('btn-new-task');
            if (btnNewTask) {
                btnNewTask.addEventListener('click', () => {
                    // Send a message to the parent window
                    window.parent.postMessage('show-task-dialog', '*');
                });
            }
        });
    </script>
</body>
</html>