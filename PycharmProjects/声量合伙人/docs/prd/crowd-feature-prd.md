# 人群功能产品需求文档 (PRD)

## 📋 文档信息

| 项目名称 | 声量合伙人 - 人群功能 |
|---------|---------------------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-30 |
| 产品经理 | AI助手 |
| 开发周期 | 4-6周 |
| 优先级 | P0 (高优先级) |

## 🎯 需求概述

### 业务背景
当前任务发布系统仅支持基于组织架构的推送方式，无法满足精准营销和跨部门协作的需求。为提升营销活动的针对性和效果，需要增加"人群"概念，支持通过Excel导入用户清单进行定向任务推送。

### 核心价值
- **提升推送精准度**：从组织维度扩展到人群维度，实现更精准的用户定向
- **支持跨部门协作**：打破组织边界，支持跨部门的营销活动
- **提高营销效果**：通过精准推送提升用户参与度和任务完成率
- **增强数据分析**：提供人群维度的数据统计和效果分析

## 👥 目标用户

### 主要用户
1. **营销管理员**：创建和管理人群，发布定向任务
2. **部门负责人**：管理部门相关的人群和任务
3. **系统管理员**：维护人群数据，监控系统运行

### 次要用户
1. **普通员工**：接收和完成来自人群推送的任务
2. **数据分析师**：分析人群效果和营销ROI

## 🔍 需求分析

### 核心需求
1. **人群管理**：支持创建、编辑、删除人群
2. **Excel导入**：支持通过Excel文件批量导入用户
3. **用户匹配**：自动匹配导入用户与系统现有用户
4. **任务推送**：支持基于人群的任务发布和推送
5. **数据统计**：提供人群维度的数据分析

### 功能边界
- ✅ 支持Excel导入用户清单
- ✅ 支持手机号、姓名、工号匹配
- ✅ 支持人群与组织混合推送
- ✅ 支持人群维度数据统计
- ❌ 不支持实时用户同步
- ❌ 不支持复杂的用户筛选条件
- ❌ 不支持人群自动更新

## 📊 功能需求

### 1. 人群管理模块

#### 1.1 人群列表页
**页面路径**: `/crowd/list`

**功能描述**: 展示所有人群的基础信息和管理操作

**核心功能**:
- 人群基础信息展示（名称、描述、用户数量、创建时间等）
- 人群状态管理（启用/停用）
- 人群搜索和筛选
- 人群操作（新建、编辑、删除、查看详情）

**数据字段**:
```
- 人群名称 (crowd_name)
- 人群编码 (crowd_code) 
- 人群描述 (crowd_desc)
- 总用户数 (total_count)
- 有效用户数 (valid_count)
- 人群状态 (crowd_status)
- 创建时间 (create_time)
- 操作按钮 (编辑/删除/查看详情)
```

#### 1.2 人群创建页
**页面路径**: `/crowd/create`

**功能描述**: 创建新的人群并导入用户数据

**操作流程**:
1. 填写人群基础信息（名称、描述、标签）
2. 下载Excel模板
3. 上传填写好的Excel文件
4. 系统解析并验证数据
5. 展示匹配结果统计
6. 确认创建人群

**Excel模板规范**:
```
必填字段：
- 手机号 (phone) - 11位手机号格式
- 姓名 (name) - 用户真实姓名

可选字段：
- 工号 (employee_no) - 员工工号
- 部门 (dept_name) - 所属部门名称
- 备注 (remark) - 其他备注信息

数据限制：
- 单次导入不超过10,000条记录
- 支持.xlsx和.xls格式
- 文件大小不超过10MB
```

#### 1.3 人群详情页
**页面路径**: `/crowd/detail/:id`

**功能描述**: 查看人群的详细信息和用户列表

**核心功能**:
- 人群基础信息展示
- 用户列表（支持分页、搜索、筛选）
- 匹配结果统计
- 使用历史记录
- 导出功能

### 2. 任务发布模块改造

#### 2.1 推送目标选择
**改造页面**: `/task/create` 和 `/task/edit/:id`

**新增功能**:
```
推送目标类型选择：
□ 按组织推送 (原有功能)
□ 按人群推送 (新增功能)  
□ 组织+人群推送 (混合模式)
```

**人群选择器组件**:
- 支持多人群选择
- 人群搜索和筛选
- 人群详情预览
- 目标用户数量统计
- 重复用户去重提示

#### 2.2 推送范围预览
**功能增强**:
- 显示组织推送用户数
- 显示人群推送用户数  
- 显示重复用户数量
- 显示最终推送用户数
- 支持用户列表预览

### 3. 数据统计模块扩展

#### 3.1 统计维度扩展
**新增统计维度**:
- 人群维度统计
- 混合维度统计（组织+人群）
- 推送方式效果对比

**统计指标**:
- 人群完成率
- 人群参与度
- 人群转化效果
- 人群价值分析

#### 3.2 统计页面改造
**改造页面**: `/statistics/task`

**新增功能**:
- 统计维度选择器
- 人群效果分析图表
- 人群对比分析
- 推送方式效果对比

## 🔧 技术需求

### 数据库设计
**新增表结构**:
1. `crowd_basic` - 人群基础信息表
2. `crowd_user_relation` - 人群用户关联表  
3. `task_crowd_relation` - 任务人群关联表
4. `task_statistics_summary` - 任务统计汇总表

**现有表改造**:
1. `task_basic` - 增加人群推送相关字段
2. `member_task` - 增加来源标识字段

### 接口设计
**新增接口** (约15-20个):
- 人群CRUD接口
- Excel导入处理接口
- 用户匹配接口
- 人群统计接口
- 任务推送接口改造

### 性能要求
- Excel导入支持异步处理
- 大数据量查询优化
- 统计数据缓存机制
- 分页查询优化

## 🎨 交互设计

### 用户体验原则
1. **简单易用**：操作流程简单直观
2. **信息透明**：及时反馈操作结果
3. **错误友好**：提供清晰的错误提示
4. **性能感知**：异步操作有进度提示

### 关键交互点
1. **Excel上传**：支持拖拽上传，实时进度显示
2. **用户匹配**：匹配结果可视化展示
3. **人群选择**：支持搜索、多选、预览
4. **数据统计**：交互式图表，支持钻取分析

## 📈 验收标准

### 功能验收
- [ ] 人群创建、编辑、删除功能正常
- [ ] Excel导入功能正常，支持数据验证
- [ ] 用户匹配准确率达到95%以上
- [ ] 任务推送支持人群维度
- [ ] 数据统计准确，图表展示正常

### 性能验收
- [ ] Excel导入10,000条数据在5分钟内完成
- [ ] 人群列表页面加载时间<3秒
- [ ] 统计页面查询响应时间<2秒
- [ ] 系统支持1000+并发用户

### 兼容性验收
- [ ] 支持Chrome、Firefox、Safari主流浏览器
- [ ] 支持Excel 2016+版本文件格式
- [ ] 移动端H5页面适配正常

## 🚨 风险评估

### 技术风险
- **数据一致性**：人群数据与用户数据同步问题
- **性能影响**：大数据量导入对系统性能的影响
- **并发处理**：多用户同时操作人群的并发问题

### 业务风险  
- **数据安全**：用户信息泄露风险
- **操作复杂**：功能复杂度增加导致用户学习成本提高
- **数据质量**：导入数据质量不高影响匹配效果

### 缓解措施
- 实施数据备份和恢复机制
- 增加操作日志和审计功能
- 提供详细的用户操作指南
- 建立数据质量检查机制

## 📅 开发计划

### 第一阶段 (2-3周)
- [ ] 数据库设计和创建
- [ ] 人群管理基础功能
- [ ] Excel导入和用户匹配
- [ ] 基础API接口开发

### 第二阶段 (1-2周)  
- [ ] 任务发布页面改造
- [ ] 人群推送逻辑实现
- [ ] 前端页面和组件开发
- [ ] 基础测试和调试

### 第三阶段 (1周)
- [ ] 数据统计功能扩展
- [ ] 性能优化和测试
- [ ] 用户体验优化
- [ ] 文档完善和培训

## 📚 相关文档

- [子任务分解清单](./task-breakdown.md)
- [技术实现方案](./technical-solution.md)  
- [数据库设计方案](./database-design.md)
- [API接口设计](./api-design.md)
- [测试用例设计](./test-cases.md)

---
*人群功能PRD v1.0 - 为精准营销提供强有力的技术支撑*
