<!doctype html><html translate="no"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="mobile-web-app-capable" content="yes"><meta name="renderer" content="webkit"><meta http-equiv="X-UA-Compatible" content="ie=edge"><title>墨刀</title><link rel="icon" id="icon" href="mb-proto2/vis/modao/favicon_home_screen_new.ico"><link rel="apple-touch-icon-precomposed" id="apple-touch-icon" href="mb-proto2/vis/modao/favicon_home_screen_new.ico"><link rel="stylesheet" href="mb-proto2/icons/fa5/css/fa5.css"><script>if (
  function() { try { eval([ 
    'for (const a of []) {}', 
    'let b = { fetch, Proxy }', 
    'let c = (async () => {})()', 
    'let { d0, ...d1 } = { ...b }' 
  ].join(';')) } catch (e) { console.log('!!', e); return true } }()
) location.href = "https://modao.cc/browser-update"</script><script src="mb-static/2410/echarts-map/loadMap.js"></script><script src="env/2203.js"></script><script>window.ENV.NO_SENTRY = true; window.ENV.NO_TRACK = true</script><script>if (ENV.IS_MO) {
    document.title = 'Mockitt';
    document.documentElement.classList.add('wonder-share');
    document.getElementById('icon').href = '/mb-static/2308/favicon-mo.ico';
    document.getElementById('apple-touch-icon').href = '/mb-static/2308/favicon-mo.ico';
  }</script><script>window.dataLayer = window.dataLayer || [];
  function gtag() { dataLayer.push(arguments) };
   ENV.IS_MO && gtag('js', new Date());
   ENV.IS_MO && gtag('config', 'UA-4839360-64');
   ENV.IS_MO && document.write('<script async src="https://www.googletagmanager.com/gtag/js?id=UA-4839360-64"><'+'/script>')</script><script>window.dataLayer = window.dataLayer || [];
  function gtag() { dataLayer.push(arguments) };
   ENV.IS_MO && gtag('js', new Date());
   ENV.IS_MO && gtag('config', 'G-24WTSJBD5B');
   ENV.IS_MO && document.write('<script async src="https://www.googletagmanager.com/gtag/js?id=G-24WTSJBD5B"><'+'/script>')</script><script src="mb-static/2308/core-js-3.32.1/modern.js"></script><script>ENV.NO_TRACK || document.write('<script src="mb-static/2502/sa-1.26.18/_.js"><'+'/script>')</script><script defer="defer" src="mb-proto2/5.3nfpa-vendor-12cccfa39f974631c27d.js"></script><script defer="defer" src="mb-proto2/4.6cqwy-vendor-a33df92963f1424e9130.js"></script><script defer="defer" src="mb-proto2/4.n9fxu-vendor-030207b253056b24344b.js"></script><script defer="defer" src="mb-proto2/3.h4vam-vendor-fb041dce1b91a028e821.js"></script><script defer="defer" src="mb-proto2/preview-html-zip-343d9981d3417f6af735.js"></script><link href="mb-proto2/5.3nfpa-vendor-77fb484d3db729c9d7f6.css" rel="stylesheet"><link href="mb-proto2/preview-html-zip-f0d9de76208db26b1137.css" rel="stylesheet"><script>(function (w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js',
      });
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l !== 'dataLayer' ? '&l=' + l : '';
      j.async = true;
      j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
      f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-TZJK297T');</script></head><body><div id="workspace"></div><noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TZJK297T" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript><script>HZv5_PREVIEW_MODE="device"</script>
<script src="extra/data.0.js"></script>
<script src="extra/data.2.js"></script>
<script src="extra/data.1.js"></script>
</body></html>