try{let Ct=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},kt=new Ct.Error().stack;kt&&(Ct._sentryDebugIds=Ct._sentryDebugIds||{},Ct._sentryDebugIds[kt]="46118b88-de23-435e-8976-489dbad9171e",Ct._sentryDebugIdIdentifier="sentry-dbid-46118b88-de23-435e-8976-489dbad9171e")}catch(Ct){}{let Ct=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};Ct.SENTRY_RELEASE={id:"22.0.4"}}(self.webpackChunk_mb2024_mb_proto=self.webpackChunk_mb2024_mb_proto||[]).push([[214],{2965:()=>{},66999:()=>{},74556:function(Ct,kt,Et){var Ht,Ut;/*!
* Paper.js v0.12.18 - The Swiss Army Knife of Vector Graphics Scripting.
* http://paperjs.org/
*
* Copyright (c) 2011 - 2020, Jürg Lehni & Jonathan Puckey
* http://juerglehni.com/ & https://puckey.studio/
*
* Distributed under the MIT license. See LICENSE file for details.
*
* All rights reserved.
*
* Date: Wed Jul 17 14:57:24 2024 +0200
*
***
*
* Straps.js - Class inheritance library with support for bean-style accessors
*
* Copyright (c) 2006 - 2020 Jürg Lehni
* http://juerglehni.com/
*
* Distributed under the MIT license.
*
***
*
* Acorn.js
* https://marijnhaverbeke.nl/acorn/
*
* Acorn is a tiny, fast JavaScript parser written in JavaScript,
* created by Marijn Haverbeke and released under an MIT license.
*
*/var Ce=(function(zt,D){zt=zt||Et(2965);var lt=zt.window,vt=zt.document,T=new function(){var t=/^(statics|enumerable|beans|preserve)$/,e=[],i=e.slice,n=Object.create,r=Object.getOwnPropertyDescriptor,s=Object.defineProperty,h=e.forEach||function(c,l){for(var p=0,y=this.length;p<y;p++)c.call(l,this[p],p,this)},a=function(c,l){for(var p in this)this.hasOwnProperty(p)&&c.call(l,this[p],p,this)},o=Object.assign||function(c){for(var l=1,p=arguments.length;l<p;l++){var y=arguments[l];for(var C in y)y.hasOwnProperty(C)&&(c[C]=y[C])}return c},u=function(c,l,p){if(c){var y=r(c,"length");(y&&typeof y.value=="number"?h:a).call(c,l,p=p||c)}return p};function _(c,l,p,y,C){var v={};function m(x,b){b=b||(b=r(l,x))&&(b.get?b:b.value),typeof b=="string"&&b[0]==="#"&&(b=c[b.substring(1)]||b);var P=typeof b=="function",I=b,O=C||P&&!b.base?b&&b.get?x in c:c[x]:null,A;(!C||!O)&&(P&&O&&(b.base=O),P&&y!==!1&&(A=x.match(/^([gs]et|is)(([A-Z])(.*))$/))&&(v[A[3].toLowerCase()+A[4]]=A[2]),(!I||P||!I.get||typeof I.get!="function"||!d.isPlainObject(I))&&(I={value:I,writable:!0}),(r(c,x)||{configurable:!0}).configurable&&(I.configurable=!0,I.enumerable=p!=null?p:!A),s(c,x,I))}if(l){for(var f in l)l.hasOwnProperty(f)&&!t.test(f)&&m(f);for(var f in v){var g=v[f],w=c["set"+g],S=c["get"+g]||w&&c["is"+g];S&&(y===!0||S.length===0)&&m(f,{get:S,set:w})}}return c}function d(){for(var c=0,l=arguments.length;c<l;c++){var p=arguments[c];p&&o(this,p)}return this}return _(d,{inject:function(c){if(c){var l=c.statics===!0?c:c.statics,p=c.beans,y=c.preserve;l!==c&&_(this.prototype,c,c.enumerable,p,y),_(this,l,null,p,y)}for(var C=1,v=arguments.length;C<v;C++)this.inject(arguments[C]);return this},extend:function(){for(var c=this,l,p,y=0,C,v=arguments.length;y<v&&!(l&&p);y++)C=arguments[y],l=l||C.initialize,p=p||C.prototype;return l=l||function(){c.apply(this,arguments)},p=l.prototype=p||n(this.prototype),s(p,"constructor",{value:l,writable:!0,configurable:!0}),_(l,this),arguments.length&&this.inject.apply(l,arguments),l.base=c,l}}).inject({enumerable:!1,initialize:d,set:d,inject:function(){for(var c=0,l=arguments.length;c<l;c++){var p=arguments[c];p&&_(this,p,p.enumerable,p.beans,p.preserve)}return this},extend:function(){var c=n(this);return c.inject.apply(c,arguments)},each:function(c,l){return u(this,c,l)},clone:function(){return new this.constructor(this)},statics:{set:o,each:u,create:n,define:s,describe:r,clone:function(c){return o(new c.constructor,c)},isPlainObject:function(c){var l=c!=null&&c.constructor;return l&&(l===Object||l===d||l.name==="Object")},pick:function(c,l){return c!==D?c:l},slice:function(c,l,p){return i.call(c,l,p)}}})};Ct.exports=T,T.inject({enumerable:!1,toString:function(){return this._id!=null?(this._class||"Object")+(this._name?" '"+this._name+"'":" @"+this._id):"{ "+T.each(this,function(t,e){if(!/^_/.test(e)){var i=typeof t;this.push(e+": "+(i==="number"?bt.instance.number(t):i==="string"?"'"+t+"'":t))}},[]).join(", ")+" }"},getClassName:function(){return this._class||""},importJSON:function(t){return T.importJSON(t,this)},exportJSON:function(t){return T.exportJSON(this,t)},toJSON:function(){return T.serialize(this)},set:function(t,e){return t&&T.filter(this,t,e,this._prioritize),this}},{beans:!1,statics:{exports:{},extend:function t(){var e=t.base.apply(this,arguments),i=e.prototype._class;return i&&!T.exports[i]&&(T.exports[i]=e),e},equals:function(t,e){if(t===e)return!0;if(t&&t.equals)return t.equals(e);if(e&&e.equals)return e.equals(t);if(t&&e&&typeof t=="object"&&typeof e=="object"){if(Array.isArray(t)&&Array.isArray(e)){var i=t.length;if(i!==e.length)return!1;for(;i--;)if(!T.equals(t[i],e[i]))return!1}else{var n=Object.keys(t),i=n.length;if(i!==Object.keys(e).length)return!1;for(;i--;){var r=n[i];if(!(e.hasOwnProperty(r)&&T.equals(t[r],e[r])))return!1}}return!0}return!1},read:function(t,e,i,n){if(this===T){var r=this.peek(t,e);return t.__index++,r}var s=this.prototype,h=s._readIndex,a=e||h&&t.__index||0,o=t.length,u=t[a];if(n=n||o-a,u instanceof this||i&&i.readNull&&u==null&&n<=1)return h&&(t.__index=a+1),u&&i&&i.clone?u.clone():u;if(u=T.create(s),h&&(u.__read=!0),u=u.initialize.apply(u,a>0||a+n<o?T.slice(t,a,a+n):t)||u,h){t.__index=a+u.__read;var _=u.__filtered;_&&(t.__filtered=_,u.__filtered=D),u.__read=D}return u},peek:function(t,e){return t[t.__index=e||t.__index||0]},remain:function(t){return t.length-(t.__index||0)},readList:function(t,e,i,n){for(var r=[],s,h=e||0,a=n?h+n:t.length,o=h;o<a;o++)r.push(Array.isArray(s=t[o])?this.read(s,0,i):this.read(t,o,i,1));return r},readNamed:function(t,e,i,n,r){var s=this.getNamed(t,e),h=s!==D;if(h){var a=t.__filtered;if(!a){var o=this.getSource(t);a=t.__filtered=T.create(o),a.__unfiltered=o}a[e]=D}return this.read(h?[s]:t,i,n,r)},readSupported:function(t,e){var i=this.getSource(t),n=this,r=!1;return i&&Object.keys(i).forEach(function(s){if(s in e){var h=n.readNamed(t,s);h!==D&&(e[s]=h),r=!0}}),r},getSource:function(t){var e=t.__source;if(e===D){var i=t.length===1&&t[0];e=t.__source=i&&T.isPlainObject(i)?i:null}return e},getNamed:function(t,e){var i=this.getSource(t);if(i)return e?i[e]:t.__filtered||i},hasNamed:function(t,e){return!!this.getNamed(t,e)},filter:function(t,e,i,n){var r;function s(_){if(!(i&&_ in i)&&!(r&&_ in r)){var d=e[_];d!==D&&(t[_]=d)}}if(n){for(var h={},a=0,o,u=n.length;a<u;a++)(o=n[a])in e&&(s(o),h[o]=!0);r=h}return Object.keys(e.__unfiltered||e).forEach(s),t},isPlainValue:function(t,e){return T.isPlainObject(t)||Array.isArray(t)||e&&typeof t=="string"},serialize:function(t,e,i,n){e=e||{};var r=!n,s;if(r&&(e.formatter=new bt(e.precision),n={length:0,definitions:{},references:{},add:function(d,c){var l="#"+d._id,p=this.references[l];if(!p){this.length++;var y=c.call(d),C=d._class;C&&y[0]!==C&&y.unshift(C),this.definitions[l]=y,p=this.references[l]=[l]}return p}}),t&&t._serialize){s=t._serialize(e,n);var h=t._class;h&&!t._compactSerialize&&(r||!i)&&s[0]!==h&&s.unshift(h)}else if(Array.isArray(t)){s=[];for(var a=0,o=t.length;a<o;a++)s[a]=T.serialize(t[a],e,i,n)}else if(T.isPlainObject(t)){s={};for(var u=Object.keys(t),a=0,o=u.length;a<o;a++){var _=u[a];s[_]=T.serialize(t[_],e,i,n)}}else typeof t=="number"?s=e.formatter.number(t,e.precision):s=t;return r&&n.length>0?[["dictionary",n.definitions],s]:s},deserialize:function(t,e,i,n,r){var s=t,h=!i,a=h&&t&&t.length&&t[0][0]==="dictionary";if(i=i||{},Array.isArray(t)){var o=t[0],u=o==="dictionary";if(t.length==1&&/^#/.test(o))return i.dictionary[o];o=T.exports[o],s=[];for(var _=o?1:0,d=t.length;_<d;_++)s.push(T.deserialize(t[_],e,i,u,a));if(o){var c=s;e?s=e(o,c,h||r):s=new o(c)}}else if(T.isPlainObject(t)){s={},n&&(i.dictionary=s);for(var l in t)s[l]=T.deserialize(t[l],e,i)}return a?s[1]:s},exportJSON:function(t,e){var i=T.serialize(t,e);return e&&e.asString==!1?i:JSON.stringify(i)},importJSON:function(t,e){return T.deserialize(typeof t=="string"?JSON.parse(t):t,function(i,n,r){var s=r&&e&&e.constructor===i,h=s?e:T.create(i.prototype);if(n.length===1&&h instanceof X&&(s||!(h instanceof Gt))){var a=n[0];T.isPlainObject(a)&&(a.insert=!1,s&&(n=n.concat([X.INSERT])))}return(s?h.set:i).apply(h,n),s&&(e=null),h})},push:function(t,e){var i=e.length;if(i<4096)t.push.apply(t,e);else{var n=t.length;t.length+=i;for(var r=0;r<i;r++)t[n+r]=e[r]}return t},splice:function(t,e,i,n){var r=e&&e.length,s=i===D;i=s?t.length:i,i>t.length&&(i=t.length);for(var h=0;h<r;h++)e[h]._index=i+h;if(s)return T.push(t,e),[];var a=[i,n];e&&T.push(a,e);for(var o=t.splice.apply(t,a),h=0,u=o.length;h<u;h++)o[h]._index=D;for(var h=i+r,u=t.length;h<u;h++)t[h]._index=h;return o},capitalize:function(t){return t.replace(/\b[a-z]/g,function(e){return e.toUpperCase()})},camelize:function(t){return t.replace(/-(.)/g,function(e,i){return i.toUpperCase()})},hyphenate:function(t){return t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}}});var Xt={on:function(t,e){if(typeof t!="string")T.each(t,function(s,h){this.on(h,s)},this);else{var i=this._eventTypes,n=i&&i[t],r=this._callbacks=this._callbacks||{};r=r[t]=r[t]||[],r.indexOf(e)===-1&&(r.push(e),n&&n.install&&r.length===1&&n.install.call(this,t))}return this},off:function(t,e){if(typeof t!="string"){T.each(t,function(h,a){this.off(a,h)},this);return}var i=this._eventTypes,n=i&&i[t],r=this._callbacks&&this._callbacks[t],s;return r&&(!e||(s=r.indexOf(e))!==-1&&r.length===1?(n&&n.uninstall&&n.uninstall.call(this,t),delete this._callbacks[t]):s!==-1&&r.splice(s,1)),this},once:function(t,e){return this.on(t,function i(){e.apply(this,arguments),this.off(t,i)})},emit:function(t,e){var i=this._callbacks&&this._callbacks[t];if(!i)return!1;var n=T.slice(arguments,1),r=e&&e.target&&!e.currentTarget;i=i.slice(),r&&(e.currentTarget=this);for(var s=0,h=i.length;s<h;s++)if(i[s].apply(this,n)==!1){e&&e.stop&&e.stop();break}return r&&delete e.currentTarget,!0},responds:function(t){return!!(this._callbacks&&this._callbacks[t])},attach:"#on",detach:"#off",fire:"#emit",_installEvents:function(t){var e=this._eventTypes,i=this._callbacks,n=t?"install":"uninstall";if(e){for(var r in i)if(i[r].length>0){var s=e[r],h=s&&s[n];h&&h.call(this,r)}}},statics:{inject:function t(e){var i=e._events;if(i){var n={};T.each(i,function(r,s){var h=typeof r=="string",a=h?r:s,o=T.capitalize(a),u=a.substring(2).toLowerCase();n[u]=h?{}:r,a="_"+a,e["get"+o]=function(){return this[a]},e["set"+o]=function(_){var d=this[a];d&&this.off(u,d),_&&this.on(u,_),this[a]=_}}),e._eventTypes=n}return t.base.apply(this,arguments)}}},Rt=T.extend({_class:"PaperScope",initialize:function t(){Q=this,this.settings=new T({applyMatrix:!0,insertItems:!0,handleSize:4,hitTolerance:0}),this.project=null,this.projects=[],this.tools=[],this._id=t._id++,t._scopes[this._id]=this;var e=t.prototype;if(!this.support){var i=ft.getContext(1,1)||{};e.support={nativeDash:"setLineDash"in i||"mozDash"in i,nativeBlendModes:oe.nativeModes},ft.release(i)}if(!this.agent){var n=zt.navigator.userAgent.toLowerCase(),r=(/(darwin|win|mac|linux|freebsd|sunos)/.exec(n)||[])[0],s=r==="darwin"?"mac":r,h=e.agent=e.browser={platform:s};s&&(h[s]=!0),n.replace(/(opera|chrome|safari|webkit|firefox|msie|trident|atom|node|jsdom)\/?\s*([.\d]+)(?:.*version\/([.\d]+))?(?:.*rv\:v?([.\d]+))?/g,function(a,o,u,_,d){if(!h.chrome){var c=o==="opera"?_:/^(node|trident)$/.test(o)?d:u;h.version=c,h.versionNumber=parseFloat(c),o={trident:"msie",jsdom:"node"}[o]||o,h.name=o,h[o]=!0}}),h.chrome&&delete h.webkit,h.atom&&delete h.chrome}},version:"0.12.18",getView:function(){var t=this.project;return t&&t._view},getPaper:function(){return this},execute:function(t,e){},install:function(t){var e=this;T.each(["project","view","tool"],function(n){T.define(t,n,{configurable:!0,get:function(){return e[n]}})});for(var i in this)!/^_/.test(i)&&this[i]&&(t[i]=this[i])},setup:function(t){return Q=this,this.project=new Qt(t),this},createCanvas:function(t,e){return ft.getCanvas(t,e)},activate:function(){Q=this},clear:function(){for(var t=this.projects,e=this.tools,i=t.length-1;i>=0;i--)t[i].remove();for(var i=e.length-1;i>=0;i--)e[i].remove()},remove:function(){this.clear(),delete Rt._scopes[this._id]},statics:new function(){function t(e){return e+="Attribute",function(i,n){return i[e](n)||i[e]("data-paper-"+n)}}return{_scopes:{},_id:0,get:function(e){return this._scopes[e]||null},getAttribute:t("get"),hasAttribute:t("has")}}}),$t=T.extend(Xt,{initialize:function(t){this._scope=Q,this._index=this._scope[this._list].push(this)-1,(t||!this._scope[this._reference])&&this.activate()},activate:function(){if(!this._scope)return!1;var t=this._scope[this._reference];return t&&t!==this&&t.emit("deactivate"),this._scope[this._reference]=this,this.emit("activate",t),!0},isActive:function(){return this._scope[this._reference]===this},remove:function(){return this._index==null?!1:(T.splice(this._scope[this._list],null,this._index,1),this._scope[this._reference]==this&&(this._scope[this._reference]=null),this._scope=null,!0)},getView:function(){return this._scope.getView()}}),Yt={findItemBoundsCollisions:function(t,e,i){function n(h){for(var a=new Array(h.length),o=0;o<h.length;o++){var u=h[o].getBounds();a[o]=[u.left,u.top,u.right,u.bottom]}return a}var r=n(t),s=!e||e===t?r:n(e);return this.findBoundsCollisions(r,s,i||0)},findCurveBoundsCollisions:function(t,e,i,n){function r(c){for(var l=Math.min,p=Math.max,y=new Array(c.length),C=0;C<c.length;C++){var v=c[C];y[C]=[l(v[0],v[2],v[4],v[6]),l(v[1],v[3],v[5],v[7]),p(v[0],v[2],v[4],v[6]),p(v[1],v[3],v[5],v[7])]}return y}var s=r(t),h=!e||e===t?s:r(e);if(n){for(var a=this.findBoundsCollisions(s,h,i||0,!1,!0),o=this.findBoundsCollisions(s,h,i||0,!0,!0),u=[],_=0,d=a.length;_<d;_++)u[_]={hor:a[_],ver:o[_]};return u}return this.findBoundsCollisions(s,h,i||0)},findBoundsCollisions:function(t,e,i,n,r){var s=!e||t===e,h=s?t:t.concat(e),a=t.length,o=h.length;function u(F,V,W){for(var H=0,J=F.length;H<J;){var q=J+H>>>1;h[F[q]][V]<W?H=q+1:J=q}return H-1}for(var _=n?1:0,d=_+2,c=n?0:1,l=c+2,p=new Array(o),y=0;y<o;y++)p[y]=y;p.sort(function(F,V){return h[F][_]-h[V][_]});for(var C=[],v=new Array(a),y=0;y<o;y++){var m=p[y],f=h[m],g=s?m:m-a,w=m<a,S=s||!w,x=w?[]:null;if(C.length){var b=u(C,d,f[_]-i)+1;if(C.splice(0,b),s&&r){x=x.concat(C);for(var P=0;P<C.length;P++){var I=C[P];v[I].push(g)}}else for(var O=f[l],A=f[c],P=0;P<C.length;P++){var I=C[P],z=h[I],M=I<a,k=s||I>=a;(r||(w&&k||S&&M)&&O>=z[c]-i&&A<=z[l]+i)&&(w&&k&&x.push(s?I:I-a),S&&M&&v[I].push(g))}}if(w&&(t===e&&x.push(m),v[m]=x),C.length){var E=f[d],N=u(C,d,E);C.splice(N+1,0,m)}else C.push(m)}for(var y=0;y<v.length;y++){var B=v[y];B&&B.sort(function(V,W){return V-W})}return v}},bt=T.extend({initialize:function(t){this.precision=T.pick(t,5),this.multiplier=Math.pow(10,this.precision)},number:function(t){return this.precision<16?Math.round(t*this.multiplier)/this.multiplier:t},pair:function(t,e,i){return this.number(t)+(i||",")+this.number(e)},point:function(t,e){return this.number(t.x)+(e||",")+this.number(t.y)},size:function(t,e){return this.number(t.width)+(e||",")+this.number(t.height)},rectangle:function(t,e){return this.point(t,e)+(e||",")+this.size(t,e)}});bt.instance=new bt;var et=new function(){var t=[[.5773502691896257],[0,.7745966692414834],[.33998104358485626,.8611363115940526],[0,.5384693101056831,.906179845938664],[.2386191860831969,.6612093864662645,.932469514203152],[0,.4058451513773972,.7415311855993945,.9491079123427585],[.1834346424956498,.525532409916329,.7966664774136267,.9602898564975363],[0,.3242534234038089,.6133714327005904,.8360311073266358,.9681602395076261],[.14887433898163122,.4333953941292472,.6794095682990244,.8650633666889845,.9739065285171717],[0,.26954315595234496,.5190961292068118,.7301520055740494,.8870625997680953,.978228658146057],[.1252334085114689,.3678314989981802,.5873179542866175,.7699026741943047,.9041172563704749,.9815606342467192],[0,.2304583159551348,.44849275103644687,.6423493394403402,.8015780907333099,.9175983992229779,.9841830547185881],[.10805494870734367,.31911236892788974,.5152486363581541,.6872929048116855,.827201315069765,.9284348836635735,.9862838086968123],[0,.20119409399743451,.3941513470775634,.5709721726085388,.7244177313601701,.8482065834104272,.937273392400706,.9879925180204854],[.09501250983763744,.2816035507792589,.45801677765722737,.6178762444026438,.755404408355003,.8656312023878318,.9445750230732326,.9894009349916499]],e=[[1],[.8888888888888888,.5555555555555556],[.6521451548625461,.34785484513745385],[.5688888888888889,.47862867049936647,.23692688505618908],[.46791393457269104,.3607615730481386,.17132449237917036],[.4179591836734694,.3818300505051189,.27970539148927664,.1294849661688697],[.362683783378362,.31370664587788727,.22238103445337448,.10122853629037626],[.3302393550012598,.31234707704000286,.26061069640293544,.1806481606948574,.08127438836157441],[.29552422471475287,.26926671930999635,.21908636251598204,.1494513491505806,.06667134430868814],[.2729250867779006,.26280454451024665,.23319376459199048,.18629021092773426,.1255803694649046,.05566856711617366],[.24914704581340277,.2334925365383548,.20316742672306592,.16007832854334622,.10693932599531843,.04717533638651183],[.2325515532308739,.22628318026289723,.2078160475368885,.17814598076194574,.13887351021978725,.09212149983772845,.04048400476531588],[.2152638534631578,.2051984637212956,.18553839747793782,.15720316715819355,.12151857068790319,.08015808715976021,.03511946033175186],[.2025782419255613,.19843148532711158,.1861610000155622,.16626920581699392,.13957067792615432,.10715922046717194,.07036604748810812,.03075324199611727],[.1894506104550685,.18260341504492358,.16915651939500254,.14959598881657674,.12462897125553388,.09515851168249279,.062253523938647894,.027152459411754096]],i=Math.abs,n=Math.sqrt,r=Math.pow,s=Math.log2||function(d){return Math.log(d)*Math.LOG2E},h=1e-12,a=112e-18;function o(d,c,l){return d<c?c:d>l?l:d}function u(d,c,l){function p(b){var P=b*134217729,I=b-P,O=I+P,A=b-O;return[O,A]}var y=c*c-d*l,C=c*c+d*l;if(i(y)*3<C){var v=p(d),m=p(c),f=p(l),g=c*c,w=m[0]*m[0]-g+2*m[0]*m[1]+m[1]*m[1],S=d*l,x=v[0]*f[0]-S+v[0]*f[1]+v[1]*f[0]+v[1]*f[1];y=g-S+(w-x)}return y}function _(){var d=Math.max.apply(Math,arguments);return d&&(d<1e-8||d>1e8)?r(2,-Math.round(s(d))):0}return{EPSILON:h,MACHINE_EPSILON:a,CURVETIME_EPSILON:1e-8,GEOMETRIC_EPSILON:1e-7,TRIGONOMETRIC_EPSILON:1e-8,ANGULAR_EPSILON:1e-5,KAPPA:4*(n(2)-1)/3,isZero:function(d){return d>=-h&&d<=h},isMachineZero:function(d){return d>=-a&&d<=a},clamp:o,integrate:function(d,c,l,p){for(var y=t[p-2],C=e[p-2],v=(l-c)*.5,m=v+c,f=0,g=p+1>>1,w=p&1?C[f++]*d(m):0;f<g;){var S=v*y[f];w+=C[f++]*(d(m+S)+d(m-S))}return v*w},findRoot:function(d,c,l,p,y,C,v){for(var m=0;m<C;m++){var f=d(l),g=f/c(l),w=l-g;if(i(g)<v){l=w;break}f>0?(y=l,l=w<=p?(p+y)*.5:w):(p=l,l=w>=y?(p+y)*.5:w)}return o(l,p,y)},solveQuadratic:function(d,c,l,p,y,C){var v,m=1/0;if(i(d)<h){if(i(c)<h)return i(l)<h?-1:0;v=-l/c}else{c*=-.5;var f=u(d,c,l);if(f&&i(f)<a){var g=_(i(d),i(c),i(l));g&&(d*=g,c*=g,l*=g,f=u(d,c,l))}if(f>=-a){var w=f<0?0:n(f),S=c+(c<0?-w:w);S===0?(v=l/d,m=-v):(v=S/d,m=l/S)}}var x=0,b=y==null,P=y-h,I=C+h;return isFinite(v)&&(b||v>P&&v<I)&&(p[x++]=b?v:o(v,y,C)),m!==v&&isFinite(m)&&(b||m>P&&m<I)&&(p[x++]=b?m:o(m,y,C)),x},solveCubic:function(d,c,l,p,y,C,v){var m=_(i(d),i(c),i(l),i(p)),f,g,w,S,x;m&&(d*=m,c*=m,l*=m,p*=m);function b(N){f=N;var B=d*f;g=B+c,w=g*f+l,S=(B+g)*f+w,x=w*f+p}if(i(d)<h)d=c,g=l,w=p,f=1/0;else if(i(p)<h)g=c,w=l,f=0;else{b(-(c/d)/3);var P=x/d,I=r(i(P),1/3),O=P<0?-1:1,A=-S/d,z=A>0?1.324717957244746*Math.max(I,n(A)):I,M=f-O*z;if(M!==f){do b(M),M=S===0?f:f-x/S/(1+a);while(O*M>O*f);i(d)*f*f>i(p/f)&&(w=-p/f,g=(w-l)/f)}}var k=et.solveQuadratic(d,g,w,y,C,v),E=C==null;return isFinite(f)&&(k===0||k>0&&f!==y[0]&&f!==y[1])&&(E||f>C-h&&f<v+h)&&(y[k++]=E?f:o(f,C,v)),k}}},jt={_id:1,_pools:{},get:function(t){if(t){var e=this._pools[t];return e||(e=this._pools[t]={_id:1}),e._id++}else return this._id++}},L=T.extend({_class:"Point",_readIndex:!0,initialize:function(e,i){var n=typeof e,r=this.__read,s=0;if(n==="number"){var h=typeof i=="number";this._set(e,h?i:e),r&&(s=h?2:1)}else if(n==="undefined"||e===null)this._set(0,0),r&&(s=e===null?1:0);else{var a=n==="string"?e.split(/[\s,]+/)||[]:e;s=1,Array.isArray(a)?this._set(+a[0],+(a.length>1?a[1]:a[0])):"x"in a?this._set(a.x||0,a.y||0):"width"in a?this._set(a.width||0,a.height||0):"angle"in a?(this._set(a.length||0,0),this.setAngle(a.angle||0)):(this._set(0,0),s=0)}return r&&(this.__read=s),this},set:"#initialize",_set:function(t,e){return this.x=t,this.y=e,this},equals:function(t){return this===t||t&&(this.x===t.x&&this.y===t.y||Array.isArray(t)&&this.x===t[0]&&this.y===t[1])||!1},clone:function(){return new L(this.x,this.y)},toString:function(){var t=bt.instance;return"{ x: "+t.number(this.x)+", y: "+t.number(this.y)+" }"},_serialize:function(t){var e=t.formatter;return[e.number(this.x),e.number(this.y)]},getLength:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},setLength:function(t){if(this.isZero()){var e=this._angle||0;this._set(Math.cos(e)*t,Math.sin(e)*t)}else{var i=t/this.getLength();et.isZero(i)&&this.getAngle(),this._set(this.x*i,this.y*i)}},getAngle:function(){return this.getAngleInRadians.apply(this,arguments)*180/Math.PI},setAngle:function(t){this.setAngleInRadians.call(this,t*Math.PI/180)},getAngleInDegrees:"#getAngle",setAngleInDegrees:"#setAngle",getAngleInRadians:function(){if(arguments.length){var t=L.read(arguments),e=this.getLength()*t.getLength();if(et.isZero(e))return NaN;var i=this.dot(t)/e;return Math.acos(i<-1?-1:i>1?1:i)}else return this.isZero()?this._angle||0:this._angle=Math.atan2(this.y,this.x)},setAngleInRadians:function(t){if(this._angle=t,!this.isZero()){var e=this.getLength();this._set(Math.cos(t)*e,Math.sin(t)*e)}},getQuadrant:function(){return this.x>=0?this.y>=0?1:4:this.y>=0?2:3}},{beans:!1,getDirectedAngle:function(){var t=L.read(arguments);return Math.atan2(this.cross(t),this.dot(t))*180/Math.PI},getDistance:function(){var t=arguments,e=L.read(t),i=e.x-this.x,n=e.y-this.y,r=i*i+n*n,s=T.read(t);return s?r:Math.sqrt(r)},normalize:function(t){t===D&&(t=1);var e=this.getLength(),i=e!==0?t/e:0,n=new L(this.x*i,this.y*i);return i>=0&&(n._angle=this._angle),n},rotate:function(t,e){if(t===0)return this.clone();t=t*Math.PI/180;var i=e?this.subtract(e):this,n=Math.sin(t),r=Math.cos(t);return i=new L(i.x*r-i.y*n,i.x*n+i.y*r),e?i.add(e):i},transform:function(t){return t?t._transformPoint(this):this},add:function(){var t=L.read(arguments);return new L(this.x+t.x,this.y+t.y)},subtract:function(){var t=L.read(arguments);return new L(this.x-t.x,this.y-t.y)},multiply:function(){var t=L.read(arguments);return new L(this.x*t.x,this.y*t.y)},divide:function(){var t=L.read(arguments);return new L(this.x/t.x,this.y/t.y)},modulo:function(){var t=L.read(arguments);return new L(this.x%t.x,this.y%t.y)},negate:function(){return new L(-this.x,-this.y)},isInside:function(){return K.read(arguments).contains(this)},isClose:function(){var t=arguments,e=L.read(t),i=T.read(t);return this.getDistance(e)<=i},isCollinear:function(){var t=L.read(arguments);return L.isCollinear(this.x,this.y,t.x,t.y)},isColinear:"#isCollinear",isOrthogonal:function(){var t=L.read(arguments);return L.isOrthogonal(this.x,this.y,t.x,t.y)},isZero:function(){var t=et.isZero;return t(this.x)&&t(this.y)},isNaN:function(){return isNaN(this.x)||isNaN(this.y)},isInQuadrant:function(t){return this.x*(t>1&&t<4?-1:1)>=0&&this.y*(t>2?-1:1)>=0},dot:function(){var t=L.read(arguments);return this.x*t.x+this.y*t.y},cross:function(){var t=L.read(arguments);return this.x*t.y-this.y*t.x},project:function(){var t=L.read(arguments),e=t.isZero()?0:this.dot(t)/t.dot(t);return new L(t.x*e,t.y*e)},statics:{min:function(){var t=arguments,e=L.read(t),i=L.read(t);return new L(Math.min(e.x,i.x),Math.min(e.y,i.y))},max:function(){var t=arguments,e=L.read(t),i=L.read(t);return new L(Math.max(e.x,i.x),Math.max(e.y,i.y))},random:function(){return new L(Math.random(),Math.random())},isCollinear:function(t,e,i,n){return Math.abs(t*n-e*i)<=Math.sqrt((t*t+e*e)*(i*i+n*n))*1e-8},isOrthogonal:function(t,e,i,n){return Math.abs(t*i+e*n)<=Math.sqrt((t*t+e*e)*(i*i+n*n))*1e-8}}},T.each(["round","ceil","floor","abs"],function(t){var e=Math[t];this[t]=function(){return new L(e(this.x),e(this.y))}},{})),Nt=L.extend({initialize:function(e,i,n,r){this._x=e,this._y=i,this._owner=n,this._setter=r},_set:function(t,e,i){return this._x=t,this._y=e,i||this._owner[this._setter](this),this},getX:function(){return this._x},setX:function(t){this._x=t,this._owner[this._setter](this)},getY:function(){return this._y},setY:function(t){this._y=t,this._owner[this._setter](this)},isSelected:function(){return!!(this._owner._selection&this._getSelection())},setSelected:function(t){this._owner._changeSelection(this._getSelection(),t)},_getSelection:function(){return this._setter==="setPosition"?4:0}}),Z=T.extend({_class:"Size",_readIndex:!0,initialize:function(e,i){var n=typeof e,r=this.__read,s=0;if(n==="number"){var h=typeof i=="number";this._set(e,h?i:e),r&&(s=h?2:1)}else if(n==="undefined"||e===null)this._set(0,0),r&&(s=e===null?1:0);else{var a=n==="string"?e.split(/[\s,]+/)||[]:e;s=1,Array.isArray(a)?this._set(+a[0],+(a.length>1?a[1]:a[0])):"width"in a?this._set(a.width||0,a.height||0):"x"in a?this._set(a.x||0,a.y||0):(this._set(0,0),s=0)}return r&&(this.__read=s),this},set:"#initialize",_set:function(t,e){return this.width=t,this.height=e,this},equals:function(t){return t===this||t&&(this.width===t.width&&this.height===t.height||Array.isArray(t)&&this.width===t[0]&&this.height===t[1])||!1},clone:function(){return new Z(this.width,this.height)},toString:function(){var t=bt.instance;return"{ width: "+t.number(this.width)+", height: "+t.number(this.height)+" }"},_serialize:function(t){var e=t.formatter;return[e.number(this.width),e.number(this.height)]},add:function(){var t=Z.read(arguments);return new Z(this.width+t.width,this.height+t.height)},subtract:function(){var t=Z.read(arguments);return new Z(this.width-t.width,this.height-t.height)},multiply:function(){var t=Z.read(arguments);return new Z(this.width*t.width,this.height*t.height)},divide:function(){var t=Z.read(arguments);return new Z(this.width/t.width,this.height/t.height)},modulo:function(){var t=Z.read(arguments);return new Z(this.width%t.width,this.height%t.height)},negate:function(){return new Z(-this.width,-this.height)},isZero:function(){var t=et.isZero;return t(this.width)&&t(this.height)},isNaN:function(){return isNaN(this.width)||isNaN(this.height)},statics:{min:function(t,e){return new Z(Math.min(t.width,e.width),Math.min(t.height,e.height))},max:function(t,e){return new Z(Math.max(t.width,e.width),Math.max(t.height,e.height))},random:function(){return new Z(Math.random(),Math.random())}}},T.each(["round","ceil","floor","abs"],function(t){var e=Math[t];this[t]=function(){return new Z(e(this.width),e(this.height))}},{})),Wt=Z.extend({initialize:function(e,i,n,r){this._width=e,this._height=i,this._owner=n,this._setter=r},_set:function(t,e,i){return this._width=t,this._height=e,i||this._owner[this._setter](this),this},getWidth:function(){return this._width},setWidth:function(t){this._width=t,this._owner[this._setter](this)},getHeight:function(){return this._height},setHeight:function(t){this._height=t,this._owner[this._setter](this)}}),K=T.extend({_class:"Rectangle",_readIndex:!0,beans:!0,initialize:function(e,i,n,r){var s=arguments,h=typeof e,a;if(h==="number"?(this._set(e,i,n,r),a=4):h==="undefined"||e===null?(this._set(0,0,0,0),a=e===null?1:0):s.length===1&&(Array.isArray(e)?(this._set.apply(this,e),a=1):e.x!==D||e.width!==D?(this._set(e.x||0,e.y||0,e.width||0,e.height||0),a=1):e.from===D&&e.to===D&&(this._set(0,0,0,0),T.readSupported(s,this)&&(a=1))),a===D){var o=L.readNamed(s,"from"),u=T.peek(s),_=o.x,d=o.y,c,l;if(u&&u.x!==D||T.hasNamed(s,"to")){var p=L.readNamed(s,"to");c=p.x-_,l=p.y-d,c<0&&(_=p.x,c=-c),l<0&&(d=p.y,l=-l)}else{var y=Z.read(s);c=y.width,l=y.height}this._set(_,d,c,l),a=s.__index}var C=s.__filtered;return C&&(this.__filtered=C),this.__read&&(this.__read=a),this},set:"#initialize",_set:function(t,e,i,n){return this.x=t,this.y=e,this.width=i,this.height=n,this},clone:function(){return new K(this.x,this.y,this.width,this.height)},equals:function(t){var e=T.isPlainValue(t)?K.read(arguments):t;return e===this||e&&this.x===e.x&&this.y===e.y&&this.width===e.width&&this.height===e.height||!1},toString:function(){var t=bt.instance;return"{ x: "+t.number(this.x)+", y: "+t.number(this.y)+", width: "+t.number(this.width)+", height: "+t.number(this.height)+" }"},_serialize:function(t){var e=t.formatter;return[e.number(this.x),e.number(this.y),e.number(this.width),e.number(this.height)]},getPoint:function(t){var e=t?L:Nt;return new e(this.x,this.y,this,"setPoint")},setPoint:function(){var t=L.read(arguments);this.x=t.x,this.y=t.y},getSize:function(t){var e=t?Z:Wt;return new e(this.width,this.height,this,"setSize")},_fw:1,_fh:1,setSize:function(){var t=Z.read(arguments),e=this._sx,i=this._sy,n=t.width,r=t.height;e&&(this.x+=(this.width-n)*e),i&&(this.y+=(this.height-r)*i),this.width=n,this.height=r,this._fw=this._fh=1},getLeft:function(){return this.x},setLeft:function(t){if(!this._fw){var e=t-this.x;this.width-=this._sx===.5?e*2:e}this.x=t,this._sx=this._fw=0},getTop:function(){return this.y},setTop:function(t){if(!this._fh){var e=t-this.y;this.height-=this._sy===.5?e*2:e}this.y=t,this._sy=this._fh=0},getRight:function(){return this.x+this.width},setRight:function(t){if(!this._fw){var e=t-this.x;this.width=this._sx===.5?e*2:e}this.x=t-this.width,this._sx=1,this._fw=0},getBottom:function(){return this.y+this.height},setBottom:function(t){if(!this._fh){var e=t-this.y;this.height=this._sy===.5?e*2:e}this.y=t-this.height,this._sy=1,this._fh=0},getCenterX:function(){return this.x+this.width/2},setCenterX:function(t){this._fw||this._sx===.5?this.x=t-this.width/2:(this._sx&&(this.x+=(t-this.x)*2*this._sx),this.width=(t-this.x)*2),this._sx=.5,this._fw=0},getCenterY:function(){return this.y+this.height/2},setCenterY:function(t){this._fh||this._sy===.5?this.y=t-this.height/2:(this._sy&&(this.y+=(t-this.y)*2*this._sy),this.height=(t-this.y)*2),this._sy=.5,this._fh=0},getCenter:function(t){var e=t?L:Nt;return new e(this.getCenterX(),this.getCenterY(),this,"setCenter")},setCenter:function(){var t=L.read(arguments);return this.setCenterX(t.x),this.setCenterY(t.y),this},getArea:function(){return this.width*this.height},isEmpty:function(){return this.width===0||this.height===0},contains:function(t){return t&&t.width!==D||(Array.isArray(t)?t:arguments).length===4?this._containsRectangle(K.read(arguments)):this._containsPoint(L.read(arguments))},_containsPoint:function(t){var e=t.x,i=t.y;return e>=this.x&&i>=this.y&&e<=this.x+this.width&&i<=this.y+this.height},_containsRectangle:function(t){var e=t.x,i=t.y;return e>=this.x&&i>=this.y&&e+t.width<=this.x+this.width&&i+t.height<=this.y+this.height},intersects:function(){var t=K.read(arguments),e=T.read(arguments)||0;return t.x+t.width>this.x-e&&t.y+t.height>this.y-e&&t.x<this.x+this.width+e&&t.y<this.y+this.height+e},intersect:function(){var t=K.read(arguments),e=Math.max(this.x,t.x),i=Math.max(this.y,t.y),n=Math.min(this.x+this.width,t.x+t.width),r=Math.min(this.y+this.height,t.y+t.height);return new K(e,i,n-e,r-i)},unite:function(){var t=K.read(arguments),e=Math.min(this.x,t.x),i=Math.min(this.y,t.y),n=Math.max(this.x+this.width,t.x+t.width),r=Math.max(this.y+this.height,t.y+t.height);return new K(e,i,n-e,r-i)},include:function(){var t=L.read(arguments),e=Math.min(this.x,t.x),i=Math.min(this.y,t.y),n=Math.max(this.x+this.width,t.x),r=Math.max(this.y+this.height,t.y);return new K(e,i,n-e,r-i)},expand:function(){var t=Z.read(arguments),e=t.width,i=t.height;return new K(this.x-e/2,this.y-i/2,this.width+e,this.height+i)},scale:function(t,e){return this.expand(this.width*t-this.width,this.height*(e===D?t:e)-this.height)}},T.each([["Top","Left"],["Top","Right"],["Bottom","Left"],["Bottom","Right"],["Left","Center"],["Top","Center"],["Right","Center"],["Bottom","Center"]],function(t,e){var i=t.join(""),n=/^[RL]/.test(i);e>=4&&(t[1]+=n?"Y":"X");var r=t[n?0:1],s=t[n?1:0],h="get"+r,a="get"+s,o="set"+r,u="set"+s,_="get"+i,d="set"+i;this[_]=function(c){var l=c?L:Nt;return new l(this[h](),this[a](),this,d)},this[d]=function(){var c=L.read(arguments);this[o](c.x),this[u](c.y)}},{beans:!0})),ce=K.extend({initialize:function(e,i,n,r,s,h){this._set(e,i,n,r,!0),this._owner=s,this._setter=h},_set:function(t,e,i,n,r){return this._x=t,this._y=e,this._width=i,this._height=n,r||this._owner[this._setter](this),this}},new function(){var t=K.prototype;return T.each(["x","y","width","height"],function(e){var i=T.capitalize(e),n="_"+e;this["get"+i]=function(){return this[n]},this["set"+i]=function(r){this[n]=r,this._dontNotify||this._owner[this._setter](this)}},T.each(["Point","Size","Center","Left","Top","Right","Bottom","CenterX","CenterY","TopLeft","TopRight","BottomLeft","BottomRight","LeftCenter","TopCenter","RightCenter","BottomCenter"],function(e){var i="set"+e;this[i]=function(){this._dontNotify=!0,t[i].apply(this,arguments),this._dontNotify=!1,this._owner[this._setter](this)}},{isSelected:function(){return!!(this._owner._selection&2)},setSelected:function(e){var i=this._owner;i._changeSelection&&i._changeSelection(2,e)}}))}),st=T.extend({_class:"Matrix",initialize:function t(e,i){var n=arguments,r=n.length,s=!0;if(r>=6?this._set.apply(this,n):r===1||r===2?e instanceof t?this._set(e._a,e._b,e._c,e._d,e._tx,e._ty,i):Array.isArray(e)?this._set.apply(this,i?e.concat([i]):e):s=!1:r?s=!1:this.reset(),!s)throw new Error("Unsupported matrix parameters");return this},set:"#initialize",_set:function(t,e,i,n,r,s,h){return this._a=t,this._b=e,this._c=i,this._d=n,this._tx=r,this._ty=s,h||this._changed(),this},_serialize:function(t,e){return T.serialize(this.getValues(),t,!0,e)},_changed:function(){var t=this._owner;t&&(t._applyMatrix?t.transform(null,!0):t._changed(25))},clone:function(){return new st(this._a,this._b,this._c,this._d,this._tx,this._ty)},equals:function(t){return t===this||t&&this._a===t._a&&this._b===t._b&&this._c===t._c&&this._d===t._d&&this._tx===t._tx&&this._ty===t._ty},toString:function(){var t=bt.instance;return"[["+[t.number(this._a),t.number(this._c),t.number(this._tx)].join(", ")+"], ["+[t.number(this._b),t.number(this._d),t.number(this._ty)].join(", ")+"]]"},reset:function(t){return this._a=this._d=1,this._b=this._c=this._tx=this._ty=0,t||this._changed(),this},apply:function(t,e){var i=this._owner;return i?(i.transform(null,T.pick(t,!0),e),this.isIdentity()):!1},translate:function(){var t=L.read(arguments),e=t.x,i=t.y;return this._tx+=e*this._a+i*this._c,this._ty+=e*this._b+i*this._d,this._changed(),this},scale:function(){var t=arguments,e=L.read(t),i=L.read(t,0,{readNull:!0});return i&&this.translate(i),this._a*=e.x,this._b*=e.x,this._c*=e.y,this._d*=e.y,i&&this.translate(i.negate()),this._changed(),this},rotate:function(t){t*=Math.PI/180;var e=L.read(arguments,1),i=e.x,n=e.y,r=Math.cos(t),s=Math.sin(t),h=i-i*r+n*s,a=n-i*s-n*r,o=this._a,u=this._b,_=this._c,d=this._d;return this._a=r*o+s*_,this._b=r*u+s*d,this._c=-s*o+r*_,this._d=-s*u+r*d,this._tx+=h*o+a*_,this._ty+=h*u+a*d,this._changed(),this},shear:function(){var t=arguments,e=L.read(t),i=L.read(t,0,{readNull:!0});i&&this.translate(i);var n=this._a,r=this._b;return this._a+=e.y*this._c,this._b+=e.y*this._d,this._c+=e.x*n,this._d+=e.x*r,i&&this.translate(i.negate()),this._changed(),this},skew:function(){var t=arguments,e=L.read(t),i=L.read(t,0,{readNull:!0}),n=Math.PI/180,r=new L(Math.tan(e.x*n),Math.tan(e.y*n));return this.shear(r,i)},append:function(t,e){if(t){var i=this._a,n=this._b,r=this._c,s=this._d,h=t._a,a=t._c,o=t._b,u=t._d,_=t._tx,d=t._ty;this._a=h*i+o*r,this._c=a*i+u*r,this._b=h*n+o*s,this._d=a*n+u*s,this._tx+=_*i+d*r,this._ty+=_*n+d*s,e||this._changed()}return this},prepend:function(t,e){if(t){var i=this._a,n=this._b,r=this._c,s=this._d,h=this._tx,a=this._ty,o=t._a,u=t._c,_=t._b,d=t._d,c=t._tx,l=t._ty;this._a=o*i+u*n,this._c=o*r+u*s,this._b=_*i+d*n,this._d=_*r+d*s,this._tx=o*h+u*a+c,this._ty=_*h+d*a+l,e||this._changed()}return this},appended:function(t){return this.clone().append(t)},prepended:function(t){return this.clone().prepend(t)},invert:function(){var t=this._a,e=this._b,i=this._c,n=this._d,r=this._tx,s=this._ty,h=t*n-e*i,a=null;return h&&!isNaN(h)&&isFinite(r)&&isFinite(s)&&(this._a=n/h,this._b=-e/h,this._c=-i/h,this._d=t/h,this._tx=(i*s-n*r)/h,this._ty=(e*r-t*s)/h,a=this),a},inverted:function(){return this.clone().invert()},concatenate:"#append",preConcatenate:"#prepend",chain:"#appended",_shiftless:function(){return new st(this._a,this._b,this._c,this._d,0,0)},_orNullIfIdentity:function(){return this.isIdentity()?null:this},isIdentity:function(){return this._a===1&&this._b===0&&this._c===0&&this._d===1&&this._tx===0&&this._ty===0},isInvertible:function(){var t=this._a*this._d-this._c*this._b;return t&&!isNaN(t)&&isFinite(this._tx)&&isFinite(this._ty)},isSingular:function(){return!this.isInvertible()},transform:function(t,e,i){return arguments.length<3?this._transformPoint(L.read(arguments)):this._transformCoordinates(t,e,i)},_transformPoint:function(t,e,i){var n=t.x,r=t.y;return e||(e=new L),e._set(n*this._a+r*this._c+this._tx,n*this._b+r*this._d+this._ty,i)},_transformCoordinates:function(t,e,i){for(var n=0,r=2*i;n<r;n+=2){var s=t[n],h=t[n+1];e[n]=s*this._a+h*this._c+this._tx,e[n+1]=s*this._b+h*this._d+this._ty}return e},_transformCorners:function(t){var e=t.x,i=t.y,n=e+t.width,r=i+t.height,s=[e,i,n,i,n,r,e,r];return this._transformCoordinates(s,s,4)},_transformBounds:function(t,e,i){for(var n=this._transformCorners(t),r=n.slice(0,2),s=r.slice(),h=2;h<8;h++){var a=n[h],o=h&1;a<r[o]?r[o]=a:a>s[o]&&(s[o]=a)}return e||(e=new K),e._set(r[0],r[1],s[0]-r[0],s[1]-r[1],i)},inverseTransform:function(){return this._inverseTransform(L.read(arguments))},_inverseTransform:function(t,e,i){var n=this._a,r=this._b,s=this._c,h=this._d,a=this._tx,o=this._ty,u=n*h-r*s,_=null;if(u&&!isNaN(u)&&isFinite(a)&&isFinite(o)){var d=t.x-this._tx,c=t.y-this._ty;e||(e=new L),_=e._set((d*h-c*s)/u,(c*n-d*r)/u,i)}return _},decompose:function(){var t=this._a,e=this._b,i=this._c,n=this._d,r=t*n-e*i,s=Math.sqrt,h=Math.atan2,a=180/Math.PI,o,u,_;if(t!==0||e!==0){var d=s(t*t+e*e);o=Math.acos(t/d)*(e>0?1:-1),u=[d,r/d],_=[h(t*i+e*n,d*d),0]}else if(i!==0||n!==0){var c=s(i*i+n*n);o=Math.asin(i/c)*(n>0?1:-1),u=[r/c,c],_=[0,h(t*i+e*n,c*c)]}else o=0,_=u=[0,0];return{translation:this.getTranslation(),rotation:o*a,scaling:new L(u),skewing:new L(_[0]*a,_[1]*a)}},getValues:function(){return[this._a,this._b,this._c,this._d,this._tx,this._ty]},getTranslation:function(){return new L(this._tx,this._ty)},getScaling:function(){return this.decompose().scaling},getRotation:function(){return this.decompose().rotation},applyToContext:function(t){this.isIdentity()||t.transform(this._a,this._b,this._c,this._d,this._tx,this._ty)}},T.each(["a","b","c","d","tx","ty"],function(t){var e=T.capitalize(t),i="_"+t;this["get"+e]=function(){return this[i]},this["set"+e]=function(n){this[i]=n,this._changed()}},{})),pt=T.extend({_class:"Line",initialize:function(e,i,n,r,s){var h=!1;arguments.length>=4?(this._px=e,this._py=i,this._vx=n,this._vy=r,h=s):(this._px=e.x,this._py=e.y,this._vx=i.x,this._vy=i.y,h=n),h||(this._vx-=this._px,this._vy-=this._py)},getPoint:function(){return new L(this._px,this._py)},getVector:function(){return new L(this._vx,this._vy)},getLength:function(){return this.getVector().getLength()},intersect:function(t,e){return pt.intersect(this._px,this._py,this._vx,this._vy,t._px,t._py,t._vx,t._vy,!0,e)},getSide:function(t,e){return pt.getSide(this._px,this._py,this._vx,this._vy,t.x,t.y,!0,e)},getDistance:function(t){return Math.abs(this.getSignedDistance(t))},getSignedDistance:function(t){return pt.getSignedDistance(this._px,this._py,this._vx,this._vy,t.x,t.y,!0)},isCollinear:function(t){return L.isCollinear(this._vx,this._vy,t._vx,t._vy)},isOrthogonal:function(t){return L.isOrthogonal(this._vx,this._vy,t._vx,t._vy)},statics:{intersect:function(t,e,i,n,r,s,h,a,o,u){o||(i-=t,n-=e,h-=r,a-=s);var _=i*a-n*h;if(!et.isMachineZero(_)){var d=t-r,c=e-s,l=(h*c-a*d)/_,p=(i*c-n*d)/_,y=1e-12,C=-y,v=1+y;if(u||C<l&&l<v&&C<p&&p<v)return u||(l=l<=0?0:l>=1?1:l),new L(t+l*i,e+l*n)}},getSide:function(t,e,i,n,r,s,h,a){h||(i-=t,n-=e);var o=r-t,u=s-e,_=o*n-u*i;return!a&&et.isMachineZero(_)&&(_=(o*i+o*i)/(i*i+n*n),_>=0&&_<=1&&(_=0)),_<0?-1:_>0?1:0},getSignedDistance:function(t,e,i,n,r,s,h){return h||(i-=t,n-=e),i===0?n>0?r-t:t-r:n===0?i<0?s-e:e-s:((r-t)*n-(s-e)*i)/(n>i?n*Math.sqrt(1+i*i/(n*n)):i*Math.sqrt(1+n*n/(i*i)))},getDistance:function(t,e,i,n,r,s,h){return Math.abs(pt.getSignedDistance(t,e,i,n,r,s,h))}}}),Qt=$t.extend({_class:"Project",_list:"projects",_reference:"project",_compactSerialize:!0,initialize:function(e){$t.call(this,!0),this._children=[],this._namedChildren={},this._activeLayer=null,this._currentStyle=new he(null,null,this),this._view=ot.create(this,e||ft.getCanvas(1,1)),this._selectionItems={},this._selectionCount=0,this._updateVersion=0},_serialize:function(t,e){return T.serialize(this._children,t,!0,e)},_changed:function(t,e){if(t&1){var i=this._view;i&&(i._needsUpdate=!0,!i._requested&&i._autoUpdate&&i.requestUpdate())}var n=this._changes;if(n&&e){var r=this._changesById,s=e._id,h=r[s];h?h.flags|=t:n.push(r[s]={item:e,flags:t})}},clear:function(){for(var t=this._children,e=t.length-1;e>=0;e--)t[e].remove()},isEmpty:function(){return!this._children.length},remove:function t(){return t.base.call(this)?(this._view&&this._view.remove(),!0):!1},getView:function(){return this._view},getCurrentStyle:function(){return this._currentStyle},setCurrentStyle:function(t){this._currentStyle.set(t)},getIndex:function(){return this._index},getOptions:function(){return this._scope.settings},getLayers:function(){return this._children},getActiveLayer:function(){return this._activeLayer||new Gt({project:this,insert:!0})},getSymbolDefinitions:function(){var t=[],e={};return this.getItems({class:re,match:function(i){var n=i._definition,r=n._id;return e[r]||(e[r]=!0,t.push(n)),!1}}),t},getSymbols:"getSymbolDefinitions",getSelectedItems:function(){var t=this._selectionItems,e=[];for(var i in t){var n=t[i],r=n._selection;r&1&&n.isInserted()?e.push(n):r||this._updateSelection(n)}return e},_updateSelection:function(t){var e=t._id,i=this._selectionItems;t._selection?i[e]!==t&&(this._selectionCount++,i[e]=t):i[e]===t&&(this._selectionCount--,delete i[e])},selectAll:function(){for(var t=this._children,e=0,i=t.length;e<i;e++)t[e].setFullySelected(!0)},deselectAll:function(){var t=this._selectionItems;for(var e in t)t[e].setFullySelected(!1)},addLayer:function(t){return this.insertLayer(D,t)},insertLayer:function(t,e){if(e instanceof Gt){e._remove(!1,!0),T.splice(this._children,[e],t,0),e._setProject(this,!0);var i=e._name;i&&e.setName(i),this._changes&&e._changed(5),this._activeLayer||(this._activeLayer=e)}else e=null;return e},_insertItem:function(t,e,i){return e=this.insertLayer(t,e)||(this._activeLayer||this._insertItem(D,new Gt(X.NO_INSERT),!0)).insertChild(t,e),i&&e.activate&&e.activate(),e},getItems:function(t){return X._getItems(this,t)},getItem:function(t){return X._getItems(this,t,null,null,!0)[0]||null},importJSON:function(t){this.activate();var e=this._activeLayer;return T.importJSON(t,e&&e.isEmpty()&&e)},removeOn:function(t){var e=this._removeSets;if(e){t==="mouseup"&&(e.mousedrag=null);var i=e[t];if(i){for(var n in i){var r=i[n];for(var s in e){var h=e[s];h&&h!=i&&delete h[r._id]}r.remove()}e[t]=null}}},draw:function(t,e,i){this._updateVersion++,t.save(),e.applyToContext(t);for(var n=this._children,r=new T({offset:new L(0,0),pixelRatio:i,viewMatrix:e.isIdentity()?null:e,matrices:[new st],updateMatrix:!0}),s=0,h=n.length;s<h;s++)n[s].draw(t,r);if(t.restore(),this._selectionCount>0){t.save(),t.strokeWidth=1;var a=this._selectionItems,o=this._scope.settings.handleSize,u=this._updateVersion;for(var _ in a)a[_]._drawSelection(t,e,o,a,u);t.restore()}}}),X=T.extend(Xt,{statics:{extend:function t(e){return e._serializeFields&&(e._serializeFields=T.set({},this.prototype._serializeFields,e._serializeFields)),t.base.apply(this,arguments)},INSERT:{insert:!0},NO_INSERT:{insert:!1}},_class:"Item",_name:null,_applyMatrix:!0,_canApplyMatrix:!0,_canScaleStroke:!1,_pivot:null,_visible:!0,_blendMode:"normal",_opacity:1,_locked:!1,_guide:!1,_clipMask:!1,_selection:0,_selectBounds:!0,_selectChildren:!1,_serializeFields:{name:null,applyMatrix:null,matrix:new st,pivot:null,visible:!0,blendMode:"normal",opacity:1,locked:!1,guide:!1,clipMask:!1,selected:!1,data:{}},_prioritize:["applyMatrix"]},new function(){var t=["onMouseDown","onMouseUp","onMouseDrag","onClick","onDoubleClick","onMouseMove","onMouseEnter","onMouseLeave"];return T.each(t,function(e){this._events[e]={install:function(i){this.getView()._countItemEvent(i,1)},uninstall:function(i){this.getView()._countItemEvent(i,-1)}}},{_events:{onFrame:{install:function(){this.getView()._animateItem(this,!0)},uninstall:function(){this.getView()._animateItem(this,!1)}},onLoad:{},onError:{}},statics:{_itemHandlers:t}})},{initialize:function(){},_initialize:function(t,e){var i=t&&T.isPlainObject(t),n=i&&t.internal===!0,r=this._matrix=new st,s=i&&t.project||Q.project,h=Q.settings;return this._id=n?null:jt.get(),this._parent=this._index=null,this._applyMatrix=this._canApplyMatrix&&h.applyMatrix,e&&r.translate(e),r._owner=this,this._style=new he(s._currentStyle,this,s),n||i&&t.insert==!1||!h.insertItems&&!(i&&t.insert==!0)?this._setProject(s):(i&&t.parent||s)._insertItem(D,this,!0),i&&t!==X.NO_INSERT&&t!==X.INSERT&&this.set(t,{internal:!0,insert:!0,project:!0,parent:!0}),i},_serialize:function(t,e){var i={},n=this;function r(s){for(var h in s){var a=n[h];T.equals(a,h==="leading"?s.fontSize*1.2:s[h])||(i[h]=T.serialize(a,t,h!=="data",e))}}return r(this._serializeFields),this instanceof At||r(this._style._defaults),[this._class,i]},_changed:function(t){var e=this._symbol,i=this._parent||e,n=this._project;t&8&&(this._bounds=this._position=this._decomposed=D),t&16&&(this._globalMatrix=D),i&&t&72&&X._clearBoundsCache(i),t&2&&X._clearBoundsCache(this),n&&n._changed(t,this),e&&e._changed(t)},getId:function(){return this._id},getName:function(){return this._name},setName:function(t){if(this._name&&this._removeNamed(),t===+t+"")throw new Error("Names consisting only of numbers are not supported.");var e=this._getOwner();if(t&&e){var i=e._children,n=e._namedChildren;(n[t]=n[t]||[]).push(this),t in i||(i[t]=this)}this._name=t||D,this._changed(256)},getStyle:function(){return this._style},setStyle:function(t){this.getStyle().set(t)}},T.each(["locked","visible","blendMode","opacity","guide"],function(t){var e=T.capitalize(t),i="_"+t,n={locked:256,visible:265};this["get"+e]=function(){return this[i]},this["set"+e]=function(r){r!=this[i]&&(this[i]=r,this._changed(n[t]||257))}},{}),{beans:!0,getSelection:function(){return this._selection},setSelection:function(t){if(t!==this._selection){this._selection=t;var e=this._project;e&&(e._updateSelection(this),this._changed(257))}},_changeSelection:function(t,e){var i=this._selection;this.setSelection(e?i|t:i&~t)},isSelected:function(){if(this._selectChildren){for(var t=this._children,e=0,i=t.length;e<i;e++)if(t[e].isSelected())return!0}return!!(this._selection&1)},setSelected:function(t){if(this._selectChildren)for(var e=this._children,i=0,n=e.length;i<n;i++)e[i].setSelected(t);this._changeSelection(1,t)},isFullySelected:function(){var t=this._children,e=!!(this._selection&1);if(t&&e){for(var i=0,n=t.length;i<n;i++)if(!t[i].isFullySelected())return!1;return!0}return e},setFullySelected:function(t){var e=this._children;if(e)for(var i=0,n=e.length;i<n;i++)e[i].setFullySelected(t);this._changeSelection(1,t)},isClipMask:function(){return this._clipMask},setClipMask:function(t){this._clipMask!=(t=!!t)&&(this._clipMask=t,t&&(this.setFillColor(null),this.setStrokeColor(null)),this._changed(257),this._parent&&this._parent._changed(2048))},getData:function(){return this._data||(this._data={}),this._data},setData:function(t){this._data=t},getPosition:function(t){var e=t?L:Nt,i=this._position||(this._position=this._getPositionFromBounds());return new e(i.x,i.y,this,"setPosition")},setPosition:function(){this.translate(L.read(arguments).subtract(this.getPosition(!0)))},_getPositionFromBounds:function(t){return this._pivot?this._matrix._transformPoint(this._pivot):(t||this.getBounds()).getCenter(!0)},getPivot:function(){var t=this._pivot;return t?new Nt(t.x,t.y,this,"setPivot"):null},setPivot:function(){this._pivot=L.read(arguments,0,{clone:!0,readNull:!0}),this._position=D}},T.each({getStrokeBounds:{stroke:!0},getHandleBounds:{handle:!0},getInternalBounds:{internal:!0}},function(t,e){this[e]=function(i){return this.getBounds(i,t)}},{beans:!0,getBounds:function(t,e){var i=e||t instanceof st,n=T.set({},i?e:t,this._boundsOptions);(!n.stroke||this.getStrokeScaling())&&(n.cacheItem=this);var r=this._getCachedBounds(i&&t,n).rect;return arguments.length?r:new ce(r.x,r.y,r.width,r.height,this,"setBounds")},setBounds:function(){var t=K.read(arguments),e=this.getBounds(),i=this._matrix,n=new st,r=t.getCenter();n.translate(r),(t.width!=e.width||t.height!=e.height)&&(i.isInvertible()||(i.set(i._backup||new st().translate(i.getTranslation())),e=this.getBounds()),n.scale(e.width!==0?t.width/e.width:0,e.height!==0?t.height/e.height:0)),r=e.getCenter(),n.translate(-r.x,-r.y),this.transform(n)},_getBounds:function(t,e){var i=this._children;return!i||!i.length?new K:(X._updateBoundsCache(this,e.cacheItem),X._getBounds(i,t,e))},_getBoundsCacheKey:function(t,e){return[t.stroke?1:0,t.handle?1:0,e?1:0].join("")},_getCachedBounds:function(t,e,i){t=t&&t._orNullIfIdentity();var n=e.internal&&!i,r=e.cacheItem,s=n?null:this._matrix._orNullIfIdentity(),h=r&&(!t||t.equals(s))&&this._getBoundsCacheKey(e,n),a=this._bounds;if(X._updateBoundsCache(this._parent||this._symbol,r),h&&a&&h in a){var o=a[h];return{rect:o.rect.clone(),nonscaling:o.nonscaling}}var u=this._getBounds(t||s,e),_=u.rect||u,d=this._style,c=u.nonscaling||d.hasStroke()&&!d.getStrokeScaling();if(h){a||(this._bounds=a={});var o=a[h]={rect:_.clone(),nonscaling:c,internal:n}}return{rect:_,nonscaling:c}},_getStrokeMatrix:function(t,e){var i=this.getStrokeScaling()?null:e&&e.internal?this:this._parent||this._symbol&&this._symbol._item,n=i?i.getViewMatrix().invert():t;return n&&n._shiftless()},statics:{_updateBoundsCache:function(t,e){if(t&&e){var i=e._id,n=t._boundsCache=t._boundsCache||{ids:{},list:[]};n.ids[i]||(n.list.push(e),n.ids[i]=e)}},_clearBoundsCache:function(t){var e=t._boundsCache;if(e){t._bounds=t._position=t._boundsCache=D;for(var i=0,n=e.list,r=n.length;i<r;i++){var s=n[i];s!==t&&(s._bounds=s._position=D,s._boundsCache&&X._clearBoundsCache(s))}}},_getBounds:function(t,e,i){var n=1/0,r=-n,s=n,h=r,a=!1;i=i||{};for(var o=0,u=t.length;o<u;o++){var _=t[o];if(_._visible&&!_.isEmpty(!0)){var d=_._getCachedBounds(e&&e.appended(_._matrix),i,!0),c=d.rect;n=Math.min(c.x,n),s=Math.min(c.y,s),r=Math.max(c.x+c.width,r),h=Math.max(c.y+c.height,h),d.nonscaling&&(a=!0)}}return{rect:isFinite(n)?new K(n,s,r-n,h-s):new K,nonscaling:a}}}}),{beans:!0,_decompose:function(){return this._applyMatrix?null:this._decomposed||(this._decomposed=this._matrix.decompose())},getRotation:function(){var t=this._decompose();return t?t.rotation:0},setRotation:function(t){var e=this.getRotation();if(e!=null&&t!=null){var i=this._decomposed;this.rotate(t-e),i&&(i.rotation=t,this._decomposed=i)}},getScaling:function(){var t=this._decompose(),e=t&&t.scaling;return new Nt(e?e.x:1,e?e.y:1,this,"setScaling")},setScaling:function(){var t=this.getScaling(),e=L.read(arguments,0,{clone:!0,readNull:!0});if(t&&e&&!t.equals(e)){var i=this.getRotation(),n=this._decomposed,r=new st,s=et.isZero;if(s(t.x)||s(t.y))r.translate(n.translation),i&&r.rotate(i),r.scale(e.x,e.y),this._matrix.set(r);else{var h=this.getPosition(!0);r.translate(h),i&&r.rotate(i),r.scale(e.x/t.x,e.y/t.y),i&&r.rotate(-i),r.translate(h.negate()),this.transform(r)}n&&(n.scaling=e,this._decomposed=n)}},getMatrix:function(){return this._matrix},setMatrix:function(){var t=this._matrix;t.set.apply(t,arguments)},getGlobalMatrix:function(t){var e=this._globalMatrix;if(e)for(var i=this._parent,n=[];i;){if(!i._globalMatrix){e=null;for(var r=0,s=n.length;r<s;r++)n[r]._globalMatrix=null;break}n.push(i),i=i._parent}if(!e){e=this._globalMatrix=this._matrix.clone();var i=this._parent;i&&e.prepend(i.getGlobalMatrix(!0))}return t?e:e.clone()},getViewMatrix:function(){return this.getGlobalMatrix().prepend(this.getView()._matrix)},getApplyMatrix:function(){return this._applyMatrix},setApplyMatrix:function(t){(this._applyMatrix=this._canApplyMatrix&&!!t)&&this.transform(null,!0)},getTransformContent:"#getApplyMatrix",setTransformContent:"#setApplyMatrix"},{getProject:function(){return this._project},_setProject:function(t,e){if(this._project!==t){this._project&&this._installEvents(!1),this._project=t;for(var i=this._children,n=0,r=i&&i.length;n<r;n++)i[n]._setProject(t);e=!0}e&&this._installEvents(!0)},getView:function(){return this._project._view},_installEvents:function t(e){t.base.call(this,e);for(var i=this._children,n=0,r=i&&i.length;n<r;n++)i[n]._installEvents(e)},getLayer:function(){for(var t=this;t=t._parent;)if(t instanceof Gt)return t;return null},getParent:function(){return this._parent},setParent:function(t){return t.addChild(this)},_getOwner:"#getParent",getChildren:function(){return this._children},setChildren:function(t){this.removeChildren(),this.addChildren(t)},getFirstChild:function(){return this._children&&this._children[0]||null},getLastChild:function(){return this._children&&this._children[this._children.length-1]||null},getNextSibling:function(){var t=this._getOwner();return t&&t._children[this._index+1]||null},getPreviousSibling:function(){var t=this._getOwner();return t&&t._children[this._index-1]||null},getIndex:function(){return this._index},setIndex:function(t){var e=this._parent,i=e&&e._children;i&&e.insertChildren(t in i?t:D,[this])},equals:function(t){return t===this||t&&this._class===t._class&&this._style.equals(t._style)&&this._matrix.equals(t._matrix)&&this._locked===t._locked&&this._visible===t._visible&&this._blendMode===t._blendMode&&this._opacity===t._opacity&&this._clipMask===t._clipMask&&this._guide===t._guide&&this._equals(t)||!1},_equals:function(t){return T.equals(this._children,t._children)},clone:function(t){var e=new this.constructor(X.NO_INSERT),i=this._children,n=T.pick(t?t.insert:D,t===D||t===!0),r=T.pick(t?t.deep:D,!0);i&&e.copyAttributes(this),(!i||r)&&e.copyContent(this),i||e.copyAttributes(this),n&&e.insertAbove(this);var s=this._name,h=this._parent;if(s&&h){for(var i=h._children,a=s,o=1;i[s];)s=a+" "+o++;s!==a&&e.setName(s)}return e},copyContent:function(t){for(var e=t._children,i=0,n=e&&e.length;i<n;i++)this.addChild(e[i].clone(!1),!0)},copyAttributes:function(t,e){this.setStyle(t._style);for(var i=["_locked","_visible","_blendMode","_opacity","_clipMask","_guide"],n=0,r=i.length;n<r;n++){var s=i[n];t.hasOwnProperty(s)&&(this[s]=t[s])}e||this._matrix.set(t._matrix,!0),this.setApplyMatrix(t._applyMatrix),this.setPivot(t._pivot),this.setSelection(t._selection);var h=t._data,a=t._name;this._data=h?T.clone(h):null,a&&this.setName(a)},rasterize:function(t,e){var i,n,r;T.isPlainObject(t)?(i=t.resolution,n=t.insert,r=t.raster):(i=t,n=e),r||(r=new Kt(X.NO_INSERT));var s=this.getStrokeBounds(),h=(i||this.getView().getResolution())/72,a=s.getTopLeft().floor(),o=s.getBottomRight().ceil(),u=new Z(o.subtract(a)),_=u.multiply(h);if(r.setSize(_,!0),!_.isZero()){var d=r.getContext(!0),c=new st().scale(h).translate(a.negate());d.save(),c.applyToContext(d),this.draw(d,new T({matrices:[c]})),d.restore()}return r._matrix.set(new st().translate(a.add(u.divide(2))).scale(1/h)),(n===D||n)&&r.insertAbove(this),r},contains:function(){var t=this._matrix;return t.isInvertible()&&!!this._contains(t._inverseTransform(L.read(arguments)))},_contains:function(t){var e=this._children;if(e){for(var i=e.length-1;i>=0;i--)if(e[i].contains(t))return!0;return!1}return t.isInside(this.getInternalBounds())},isInside:function(){return K.read(arguments).contains(this.getBounds())},_asPathItem:function(){return new nt.Rectangle({rectangle:this.getInternalBounds(),matrix:this._matrix,insert:!1})},intersects:function(t,e){return t instanceof X?this._asPathItem().getIntersections(t._asPathItem(),null,e,!0).length>0:!1}},new function(){function t(){var n=arguments;return this._hitTest(L.read(n),Lt.getOptions(n))}function e(){var n=arguments,r=L.read(n),s=Lt.getOptions(n),h=[];return this._hitTest(r,new T({all:h},s)),h}function i(n,r,s,h){var a=this._children;if(a)for(var o=a.length-1;o>=0;o--){var u=a[o],_=u!==h&&u._hitTest(n,r,s);if(_&&!r.all)return _}return null}return Qt.inject({hitTest:t,hitTestAll:e,_hitTest:i}),{hitTest:t,hitTestAll:e,_hitTestChildren:i}},{_hitTest:function(t,e,i){if(this._locked||!this._visible||this._guide&&!e.guides||this.isEmpty())return null;var n=this._matrix,r=i?i.appended(n):this.getGlobalMatrix().prepend(this.getView()._matrix),s=Math.max(e.tolerance,1e-12),h=e._tolerancePadding=new Z(nt._getStrokePadding(s,n._shiftless().invert()));if(t=n._inverseTransform(t),!t||!this._children&&!this.getBounds({internal:!0,stroke:!0,handle:!0}).expand(h.multiply(2))._containsPoint(t))return null;var a=!(e.guides&&!this._guide||e.selected&&!this.isSelected()||e.type&&e.type!==T.hyphenate(this._class)||e.class&&!(this instanceof e.class)),o=e.match,u=this,_,d;function c(f){return f&&o&&!o(f)&&(f=null),f&&e.all&&e.all.push(f),f}function l(f,g){var w=g?_["get"+g]():u.getPosition();if(t.subtract(w).divide(h).length<=1)return new Lt(f,u,{name:g?T.hyphenate(g):f,point:w})}var p=e.position,y=e.center,C=e.bounds;if(a&&this._parent&&(p||y||C)){if((y||C)&&(_=this.getInternalBounds()),d=p&&l("position")||y&&l("center","Center"),!d&&C)for(var v=["TopLeft","TopRight","BottomLeft","BottomRight","LeftCenter","TopCenter","RightCenter","BottomCenter"],m=0;m<8&&!d;m++)d=l("bounds",v[m]);d=c(d)}return d||(d=this._hitTestChildren(t,e,r)||a&&c(this._hitTestSelf(t,e,r,this.getStrokeScaling()?null:r._shiftless().invert()))||null),d&&d.point&&(d.point=n.transform(d.point)),d},_hitTestSelf:function(t,e){if(e.fill&&this.hasFill()&&this._contains(t))return new Lt("fill",this)},matches:function(t,e){function i(h,a){for(var o in h)if(h.hasOwnProperty(o)){var u=h[o],_=a[o];if(T.isPlainObject(u)&&T.isPlainObject(_)){if(!i(u,_))return!1}else if(!T.equals(u,_))return!1}return!0}var n=typeof t;if(n==="object"){for(var r in t)if(t.hasOwnProperty(r)&&!this.matches(r,t[r]))return!1;return!0}else{if(n==="function")return t(this);if(t==="match")return e(this);var s=/^(empty|editable)$/.test(t)?this["is"+T.capitalize(t)]():t==="type"?T.hyphenate(this._class):this[t];if(t==="class"){if(typeof e=="function")return this instanceof e;s=this._class}if(typeof e=="function")return!!e(s);if(e){if(e.test)return e.test(s);if(T.isPlainObject(e))return i(e,s)}return T.equals(s,e)}},getItems:function(t){return X._getItems(this,t,this._matrix)},getItem:function(t){return X._getItems(this,t,this._matrix,null,!0)[0]||null},statics:{_getItems:function t(e,i,n,r,s){if(!r){var h=typeof i=="object"&&i,a=h&&h.overlapping,o=h&&h.inside,u=a||o,c=u&&K.read([u]);r={items:[],recursive:h&&h.recursive!==!1,inside:!!o,overlapping:!!a,rect:c,path:a&&new nt.Rectangle({rectangle:c,insert:!1})},h&&(i=T.filter({},i,{recursive:!0,inside:!0,overlapping:!0}))}var _=e._children,d=r.items,c=r.rect;n=c&&(n||new st);for(var l=0,p=_&&_.length;l<p;l++){var y=_[l],C=n&&n.appended(y._matrix),v=!0;if(c){var u=y.getBounds(C);if(!c.intersects(u))continue;c.contains(u)||r.overlapping&&(u.contains(c)||r.path.intersects(y,C))||(v=!1)}if(v&&y.matches(i)&&(d.push(y),s)||(r.recursive!==!1&&t(y,i,C,r,s),s&&d.length>0))break}return d}}},{importJSON:function(t){var e=T.importJSON(t,this);return e!==this?this.addChild(e):e},addChild:function(t){return this.insertChild(D,t)},insertChild:function(t,e){var i=e?this.insertChildren(t,[e]):null;return i&&i[0]},addChildren:function(t){return this.insertChildren(this._children.length,t)},insertChildren:function(t,e){var i=this._children;if(i&&e&&e.length>0){e=T.slice(e);for(var n={},r=e.length-1;r>=0;r--){var s=e[r],h=s&&s._id;!s||n[h]?e.splice(r,1):(s._remove(!1,!0),n[h]=!0)}T.splice(i,e,t,0);for(var a=this._project,o=a._changes,r=0,u=e.length;r<u;r++){var s=e[r],_=s._name;s._parent=this,s._setProject(a,!0),_&&s.setName(_),o&&s._changed(5)}this._changed(11)}else e=null;return e},_insertItem:"#insertChild",_insertAt:function(t,e){var i=t&&t._getOwner(),n=t!==this&&i?this:null;return n&&(n._remove(!1,!0),i._insertItem(t._index+e,n)),n},insertAbove:function(t){return this._insertAt(t,1)},insertBelow:function(t){return this._insertAt(t,0)},sendToBack:function(){var t=this._getOwner();return t?t._insertItem(0,this):null},bringToFront:function(){var t=this._getOwner();return t?t._insertItem(D,this):null},appendTop:"#addChild",appendBottom:function(t){return this.insertChild(0,t)},moveAbove:"#insertAbove",moveBelow:"#insertBelow",addTo:function(t){return t._insertItem(D,this)},copyTo:function(t){return this.clone(!1).addTo(t)},reduce:function(t){var e=this._children;if(e&&e.length===1){var i=e[0].reduce(t);return this._parent?(i.insertAbove(this),this.remove()):i.remove(),i}return this},_removeNamed:function(){var t=this._getOwner();if(t){var e=t._children,i=t._namedChildren,n=this._name,r=i[n],s=r?r.indexOf(this):-1;s!==-1&&(e[n]==this&&delete e[n],r.splice(s,1),r.length?e[n]=r[0]:delete i[n])}},_remove:function(t,e){var i=this._getOwner(),n=this._project,r=this._index;return this._style&&this._style._dispose(),i?(this._name&&this._removeNamed(),r!=null&&(n._activeLayer===this&&(n._activeLayer=this.getNextSibling()||this.getPreviousSibling()),T.splice(i._children,null,r,1)),this._installEvents(!1),t&&n._changes&&this._changed(5),e&&i._changed(11,this),this._parent=null,!0):!1},remove:function(){return this._remove(!0,!0)},replaceWith:function(t){var e=t&&t.insertBelow(this);return e&&this.remove(),e},removeChildren:function(t,e){if(!this._children)return null;t=t||0,e=T.pick(e,this._children.length);for(var i=T.splice(this._children,null,t,e-t),n=i.length-1;n>=0;n--)i[n]._remove(!0,!1);return i.length>0&&this._changed(11),i},clear:"#removeChildren",reverseChildren:function(){if(this._children){this._children.reverse();for(var t=0,e=this._children.length;t<e;t++)this._children[t]._index=t;this._changed(11)}},isEmpty:function(t){var e=this._children,i=e?e.length:0;if(t){for(var n=0;n<i;n++)if(!e[n].isEmpty(t))return!1;return!0}return!i},isEditable:function(){for(var t=this;t;){if(!t._visible||t._locked)return!1;t=t._parent}return!0},hasFill:function(){return this.getStyle().hasFill()},hasStroke:function(){return this.getStyle().hasStroke()},hasShadow:function(){return this.getStyle().hasShadow()},_getOrder:function(t){function e(h){var a=[];do a.unshift(h);while(h=h._parent);return a}for(var i=e(this),n=e(t),r=0,s=Math.min(i.length,n.length);r<s;r++)if(i[r]!=n[r])return i[r]._index<n[r]._index?1:-1;return 0},hasChildren:function(){return this._children&&this._children.length>0},isInserted:function(){return this._parent?this._parent.isInserted():!1},isAbove:function(t){return this._getOrder(t)===-1},isBelow:function(t){return this._getOrder(t)===1},isParent:function(t){return this._parent===t},isChild:function(t){return t&&t._parent===this},isDescendant:function(t){for(var e=this;e=e._parent;)if(e===t)return!0;return!1},isAncestor:function(t){return t?t.isDescendant(this):!1},isSibling:function(t){return this._parent===t._parent},isGroupedWith:function(t){for(var e=this._parent;e;){if(e._parent&&/^(Group|Layer|CompoundPath)$/.test(e._class)&&t.isDescendant(e))return!0;e=e._parent}return!1}},T.each(["rotate","scale","shear","skew"],function(t){var e=t==="rotate";this[t]=function(){var i=arguments,n=(e?T:L).read(i),r=L.read(i,0,{readNull:!0});return this.transform(new st()[t](n,r||this.getPosition(!0)))}},{translate:function(){var t=new st;return this.transform(t.translate.apply(t,arguments))},transform:function(t,e,i){var n=this._matrix,r=t&&!t.isIdentity(),s=i&&this._canApplyMatrix||this._applyMatrix&&(r||!n.isIdentity()||e&&this._children);if(!r&&!s)return this;if(r){!t.isInvertible()&&n.isInvertible()&&(n._backup=n.getValues()),n.prepend(t,!0);var h=this._style,a=h.getFillColor(!0),o=h.getStrokeColor(!0);a&&a.transform(t),o&&o.transform(t)}if(s&&(s=this._transformContent(n,e,i))){var u=this._pivot;u&&n._transformPoint(u,u,!0),n.reset(!0),i&&this._canApplyMatrix&&(this._applyMatrix=!0)}var _=this._bounds,d=this._position;(r||s)&&this._changed(25);var c=r&&_&&t.decompose();if(c&&c.skewing.isZero()&&c.rotation%90===0){for(var l in _){var p=_[l];if(p.nonscaling)delete _[l];else if(s||!p.internal){var y=p.rect;t._transformBounds(y,y)}}this._bounds=_;var C=_[this._getBoundsCacheKey(this._boundsOptions||{})];C&&(this._position=this._getPositionFromBounds(C.rect))}else r&&d&&this._pivot&&(this._position=t._transformPoint(d,d));return this},_transformContent:function(t,e,i){var n=this._children;if(n){for(var r=0,s=n.length;r<s;r++)n[r].transform(t,e,i);return!0}},globalToLocal:function(){return this.getGlobalMatrix(!0)._inverseTransform(L.read(arguments))},localToGlobal:function(){return this.getGlobalMatrix(!0)._transformPoint(L.read(arguments))},parentToLocal:function(){return this._matrix._inverseTransform(L.read(arguments))},localToParent:function(){return this._matrix._transformPoint(L.read(arguments))},fitBounds:function(t,e){t=K.read(arguments);var i=this.getBounds(),n=i.height/i.width,r=t.height/t.width,s=(e?n>r:n<r)?t.width/i.width:t.height/i.height,h=new K(new L,new Z(i.width*s,i.height*s));h.setCenter(t.getCenter()),this.setBounds(h)}}),{_setStyles:function(t,e,i){var n=this._style,r=this._matrix;if(n.hasFill()&&(t.fillStyle=n.getFillColor().toCanvasStyle(t,r)),n.hasStroke()){t.strokeStyle=n.getStrokeColor().toCanvasStyle(t,r),t.lineWidth=n.getStrokeWidth();var s=n.getStrokeJoin(),h=n.getStrokeCap(),a=n.getMiterLimit();if(s&&(t.lineJoin=s),h&&(t.lineCap=h),a&&(t.miterLimit=a),Q.support.nativeDash){var o=n.getDashArray(),u=n.getDashOffset();o&&o.length&&("setLineDash"in t?(t.setLineDash(o),t.lineDashOffset=u):(t.mozDash=o,t.mozDashOffset=u))}}if(n.hasShadow()){var _=e.pixelRatio||1,d=i._shiftless().prepend(new st().scale(_,_)),c=d.transform(new L(n.getShadowBlur(),0)),l=d.transform(this.getShadowOffset());t.shadowColor=n.getShadowColor().toCanvasStyle(t),t.shadowBlur=c.getLength(),t.shadowOffsetX=l.x,t.shadowOffsetY=l.y}},draw:function(t,e,i){var n=this._updateVersion=this._project._updateVersion;if(!(!this._visible||this._opacity===0)){var r=e.matrices,s=e.viewMatrix,h=this._matrix,a=r[r.length-1].appended(h);if(a.isInvertible()){s=s?s.appended(a):a,r.push(a),e.updateMatrix&&(this._globalMatrix=a);var o=this._blendMode,u=et.clamp(this._opacity,0,1),_=o==="normal",d=oe.nativeModes[o],c=_&&u===1||e.dontStart||e.clip||(d||_&&u<1)&&this._canComposite(),l=e.pixelRatio||1,p,y,C;if(!c){var v=this.getStrokeBounds(s);if(!v.width||!v.height){r.pop();return}C=e.offset,y=e.offset=v.getTopLeft().floor(),p=t,t=ft.getContext(v.getSize().ceil().add(1).multiply(l)),l!==1&&t.scale(l,l)}t.save();var m=i?i.appended(h):this._canScaleStroke&&!this.getStrokeScaling(!0)&&s,f=!c&&e.clipItem,g=!m||f;if(c?(t.globalAlpha=u,d&&(t.globalCompositeOperation=o)):g&&t.translate(-y.x,-y.y),g&&(c?h:s).applyToContext(t),f&&e.clipItem.draw(t,e.extend({clip:!0})),m){t.setTransform(l,0,0,l,0,0);var w=e.offset;w&&t.translate(-w.x,-w.y)}this._draw(t,e,s,m),t.restore(),r.pop(),e.clip&&!e.dontFinish&&t.clip(this.getFillRule()),c||(oe.process(o,t,p,u,y.subtract(C).multiply(l)),ft.release(t),e.offset=C)}}},_isUpdated:function(t){var e=this._parent;if(e instanceof Mt)return e._isUpdated(t);var i=this._updateVersion===t;return!i&&e&&e._visible&&e._isUpdated(t)&&(this._updateVersion=t,i=!0),i},_drawSelection:function(t,e,i,n,r){var s=this._selection,h=s&1,a=s&2||h&&this._selectBounds,o=s&4;if(this._drawSelected||(h=!1),(h||a||o)&&this._isUpdated(r)){var u,_=this.getSelectedColor(!0)||(u=this.getLayer())&&u.getSelectedColor(!0),d=e.appended(this.getGlobalMatrix(!0)),c=i/2;if(t.strokeStyle=t.fillStyle=_?_.toCanvasStyle(t):"#009dec",h&&this._drawSelected(t,d,n),o){var l=this.getPosition(!0),p=this._parent,y=p?p.localToGlobal(l):l,C=y.x,v=y.y;t.beginPath(),t.arc(C,v,c,0,Math.PI*2,!0),t.stroke();for(var m=[[0,-1],[1,0],[0,1],[-1,0]],f=c,g=i+1,w=0;w<4;w++){var S=m[w],x=S[0],b=S[1];t.moveTo(C+x*f,v+b*f),t.lineTo(C+x*g,v+b*g),t.stroke()}}if(a){var P=d._transformCorners(this.getInternalBounds());t.beginPath();for(var w=0;w<8;w++)t[w?"lineTo":"moveTo"](P[w],P[++w]);t.closePath(),t.stroke();for(var w=0;w<8;w++)t.fillRect(P[w]-c,P[++w]-c,i,i)}}},_canComposite:function(){return!1}},T.each(["down","drag","up","move"],function(t){this["removeOn"+T.capitalize(t)]=function(){var e={};return e[t]=!0,this.removeOn(e)}},{removeOn:function(t){for(var e in t)if(t[e]){var i="mouse"+e,n=this._project,r=n._removeSets=n._removeSets||{};r[i]=r[i]||{},r[i][this._id]=this}return this}}),{tween:function(t,e,i){i||(i=e,e=t,t=null,i||(i=e,e=null));var n=i&&i.easing,r=i&&i.start,s=i!=null&&(typeof i=="number"?i:i.duration),h=new we(this,t,e,s,n,r);function a(o){h._handleFrame(o.time*1e3),h.running||this.off("frame",a)}return s&&this.on("frame",a),h},tweenTo:function(t,e){return this.tween(null,t,e)},tweenFrom:function(t,e){return this.tween(t,null,e)}}),At=X.extend({_class:"Group",_selectBounds:!1,_selectChildren:!0,_serializeFields:{children:[]},initialize:function(e){this._children=[],this._namedChildren={},this._initialize(e)||this.addChildren(Array.isArray(e)?e:arguments)},_changed:function t(e){t.base.call(this,e),e&2050&&(this._clipItem=D)},_getClipItem:function(){var t=this._clipItem;if(t===D){t=null;for(var e=this._children,i=0,n=e.length;i<n;i++)if(e[i]._clipMask){t=e[i];break}this._clipItem=t}return t},isClipped:function(){return!!this._getClipItem()},setClipped:function(t){var e=this.getFirstChild();e&&e.setClipMask(t)},_getBounds:function t(e,i){var n=this._getClipItem();return n?n._getCachedBounds(n._matrix.prepended(e),T.set({},i,{stroke:!1})):t.base.call(this,e,i)},_hitTestChildren:function t(e,i,n){var r=this._getClipItem();return(!r||r.contains(e))&&t.base.call(this,e,i,n,r)},_draw:function(t,e){var i=e.clip,n=!i&&this._getClipItem();e=e.extend({clipItem:n,clip:!1}),i?(t.beginPath(),e.dontStart=e.dontFinish=!0):n&&n.draw(t,e.extend({clip:!0}));for(var r=this._children,s=0,h=r.length;s<h;s++){var a=r[s];a!==n&&a.draw(t,e)}}}),Gt=At.extend({_class:"Layer",initialize:function(){At.apply(this,arguments)},_getOwner:function(){return this._parent||this._index!=null&&this._project},isInserted:function t(){return this._parent?t.base.call(this):this._index!=null},activate:function(){this._project._activeLayer=this},_hitTestSelf:function(){}}),xt=X.extend({_class:"Shape",_applyMatrix:!1,_canApplyMatrix:!1,_canScaleStroke:!0,_serializeFields:{type:null,size:null,radius:null},initialize:function(e,i){this._initialize(e,i)},_equals:function(t){return this._type===t._type&&this._size.equals(t._size)&&T.equals(this._radius,t._radius)},copyContent:function(t){this.setType(t._type),this.setSize(t._size),this.setRadius(t._radius)},getType:function(){return this._type},setType:function(t){this._type=t},getShape:"#getType",setShape:"#setType",getSize:function(){var t=this._size;return new Wt(t.width,t.height,this,"setSize")},setSize:function(){var t=Z.read(arguments);if(!this._size)this._size=t.clone();else if(!this._size.equals(t)){var e=this._type,i=t.width,n=t.height;e==="rectangle"?this._radius.set(Z.min(this._radius,t.divide(2).abs())):e==="circle"?(i=n=(i+n)/2,this._radius=i/2):e==="ellipse"&&this._radius._set(i/2,n/2),this._size._set(i,n),this._changed(9)}},getRadius:function(){var t=this._radius;return this._type==="circle"?t:new Wt(t.width,t.height,this,"setRadius")},setRadius:function(t){var e=this._type;if(e==="circle"){if(t===this._radius)return;var i=t*2;this._radius=t,this._size._set(i,i)}else if(t=Z.read(arguments),!this._radius)this._radius=t.clone();else{if(this._radius.equals(t))return;if(this._radius.set(t),e==="rectangle"){var i=Z.max(this._size,t.multiply(2));this._size.set(i)}else e==="ellipse"&&this._size._set(t.width*2,t.height*2)}this._changed(9)},isEmpty:function(){return!1},toPath:function(t){var e=new nt[T.capitalize(this._type)]({center:new L,size:this._size,radius:this._radius,insert:!1});return e.copyAttributes(this),Q.settings.applyMatrix&&e.setApplyMatrix(!0),(t===D||t)&&e.insertAbove(this),e},toShape:"#clone",_asPathItem:function(){return this.toPath(!1)},_draw:function(t,e,i,n){var r=this._style,s=r.hasFill(),h=r.hasStroke(),a=e.dontFinish||e.clip,o=!n;if(s||h||a){var u=this._type,_=this._radius,d=u==="circle";if(e.dontStart||t.beginPath(),o&&d)t.arc(0,0,_,0,Math.PI*2,!0);else{var c=d?_:_.width,l=d?_:_.height,p=this._size,y=p.width,C=p.height;if(o&&u==="rectangle"&&c===0&&l===0)t.rect(-y/2,-C/2,y,C);else{var v=y/2,m=C/2,f=1-.5522847498307936,g=c*f,w=l*f,S=[-v,-m+l,-v,-m+w,-v+g,-m,-v+c,-m,v-c,-m,v-g,-m,v,-m+w,v,-m+l,v,m-l,v,m-w,v-g,m,v-c,m,-v+c,m,-v+g,m,-v,m-w,-v,m-l];n&&n.transform(S,S,32),t.moveTo(S[0],S[1]),t.bezierCurveTo(S[2],S[3],S[4],S[5],S[6],S[7]),v!==c&&t.lineTo(S[8],S[9]),t.bezierCurveTo(S[10],S[11],S[12],S[13],S[14],S[15]),m!==l&&t.lineTo(S[16],S[17]),t.bezierCurveTo(S[18],S[19],S[20],S[21],S[22],S[23]),v!==c&&t.lineTo(S[24],S[25]),t.bezierCurveTo(S[26],S[27],S[28],S[29],S[30],S[31])}}t.closePath()}!a&&(s||h)&&(this._setStyles(t,e,i),s&&(t.fill(r.getFillRule()),t.shadowColor="rgba(0,0,0,0)"),h&&t.stroke())},_canComposite:function(){return!(this.hasFill()&&this.hasStroke())},_getBounds:function(t,e){var i=new K(this._size).setCenter(0,0),n=this._style,r=e.stroke&&n.hasStroke()&&n.getStrokeWidth();return t&&(i=t._transformBounds(i)),r?i.expand(nt._getStrokePadding(r,this._getStrokeMatrix(t,e))):i}},new function(){function t(i,n,r){var s=i._radius;if(!s.isZero())for(var h=i._size.divide(2),a=1;a<=4;a++){var o=new L(a>1&&a<4?-1:1,a>2?-1:1),u=o.multiply(h),_=u.subtract(o.multiply(s)),d=new K(r?u.add(o.multiply(r)):u,_);if(d.contains(n))return{point:_,quadrant:a}}}function e(i,n,r,s){var h=i.divide(n);return(!s||h.isInQuadrant(s))&&h.subtract(h.normalize()).multiply(n).divide(r).length<=1}return{_contains:function i(n){if(this._type==="rectangle"){var r=t(this,n);return r?n.subtract(r.point).divide(this._radius).getLength()<=1:i.base.call(this,n)}else return n.divide(this.size).getLength()<=.5},_hitTestSelf:function i(n,r,s,h){var a=!1,o=this._style,u=r.stroke&&o.hasStroke(),_=r.fill&&o.hasFill();if(u||_){var d=this._type,c=this._radius,l=u?o.getStrokeWidth()/2:0,p=r._tolerancePadding.add(nt._getStrokePadding(l,!o.getStrokeScaling()&&h));if(d==="rectangle"){var y=p.multiply(2),C=t(this,n,y);if(C)a=e(n.subtract(C.point),c,p,C.quadrant);else{var v=new K(this._size).setCenter(0,0),m=v.expand(y),f=v.expand(y.negate());a=m._containsPoint(n)&&!f._containsPoint(n)}}else a=e(n,c,p)}return a?new Lt(u?"stroke":"fill",this):i.base.apply(this,arguments)}}},{statics:new function(){function t(e,i,n,r,s){var h=T.create(xt.prototype);return h._type=e,h._size=n,h._radius=r,h._initialize(T.getNamed(s),i),h}return{Circle:function(){var e=arguments,i=L.readNamed(e,"center"),n=T.readNamed(e,"radius");return t("circle",i,new Z(n*2),n,e)},Rectangle:function(){var e=arguments,i=K.readNamed(e,"rectangle"),n=Z.min(Z.readNamed(e,"radius"),i.getSize(!0).divide(2));return t("rectangle",i.getCenter(!0),i.getSize(!0),n,e)},Ellipse:function(){var e=arguments,i=xt._readEllipse(e),n=i.radius;return t("ellipse",i.center,n.multiply(2),n,e)},_readEllipse:function(e){var i,n;if(T.hasNamed(e,"radius"))i=L.readNamed(e,"center"),n=Z.readNamed(e,"radius");else{var r=K.readNamed(e,"rectangle");i=r.getCenter(!0),n=r.getSize(!0).divide(2)}return{center:i,radius:n}}}}}),Kt=X.extend({_class:"Raster",_applyMatrix:!1,_canApplyMatrix:!1,_boundsOptions:{stroke:!1,handle:!1},_serializeFields:{crossOrigin:null,source:null},_prioritize:["crossOrigin"],_smoothing:"low",beans:!0,initialize:function(e,i){if(!this._initialize(e,i!==D&&L.read(arguments))){var n,r=typeof e,s=r==="string"?vt.getElementById(e):r==="object"?e:null;if(s&&s!==X.NO_INSERT){if(s.getContext||s.naturalHeight!=null)n=s;else if(s){var h=Z.read(arguments);h.isZero()||(n=ft.getCanvas(h))}}n?this.setImage(n):this.setSource(e)}this._size||(this._size=new Z,this._loaded=!1)},_equals:function(t){return this.getSource()===t.getSource()},copyContent:function(t){var e=t._image,i=t._canvas;if(e)this._setImage(e);else if(i){var n=ft.getCanvas(t._size);n.getContext("2d").drawImage(i,0,0),this._setImage(n)}this._crossOrigin=t._crossOrigin},getSize:function(){var t=this._size;return new Wt(t?t.width:0,t?t.height:0,this,"setSize")},setSize:function(t,e){var i=Z.read(arguments);if(i.equals(this._size))e&&this.clear();else if(i.width>0&&i.height>0){var n=!e&&this.getElement();this._setImage(ft.getCanvas(i)),n&&this.getContext(!0).drawImage(n,0,0,i.width,i.height)}else this._canvas&&ft.release(this._canvas),this._size=i.clone()},getWidth:function(){return this._size?this._size.width:0},setWidth:function(t){this.setSize(t,this.getHeight())},getHeight:function(){return this._size?this._size.height:0},setHeight:function(t){this.setSize(this.getWidth(),t)},getLoaded:function(){return this._loaded},isEmpty:function(){var t=this._size;return!t||t.width===0&&t.height===0},getResolution:function(){var t=this._matrix,e=new L(0,0).transform(t),i=new L(1,0).transform(t).subtract(e),n=new L(0,1).transform(t).subtract(e);return new Z(72/i.getLength(),72/n.getLength())},getPpi:"#getResolution",getImage:function(){return this._image},setImage:function(t){var e=this;function i(n){var r=e.getView(),s=n&&n.type||"load";r&&e.responds(s)&&(Q=r._scope,e.emit(s,new ie(n)))}this._setImage(t),this._loaded?setTimeout(i,0):t&&_t.add(t,{load:function(n){e._setImage(t),i(n)},error:i})},_setImage:function(t){this._canvas&&ft.release(this._canvas),t&&t.getContext?(this._image=null,this._canvas=t,this._loaded=!0):(this._image=t,this._canvas=null,this._loaded=!!(t&&t.src&&t.complete)),this._size=new Z(t?t.naturalWidth||t.width:0,t?t.naturalHeight||t.height:0),this._context=null,this._changed(1033)},getCanvas:function(){if(!this._canvas){var t=ft.getContext(this._size);try{this._image&&t.drawImage(this._image,0,0),this._canvas=t.canvas}catch(e){ft.release(t)}}return this._canvas},setCanvas:"#setImage",getContext:function(t){return this._context||(this._context=this.getCanvas().getContext("2d")),t&&(this._image=null,this._changed(1025)),this._context},setContext:function(t){this._context=t},getSource:function(){var t=this._image;return t&&t.src||this.toDataURL()},setSource:function(t){var e=new zt.Image,i=this._crossOrigin;i&&(e.crossOrigin=i),t&&(e.src=t),this.setImage(e)},getCrossOrigin:function(){var t=this._image;return t&&t.crossOrigin||this._crossOrigin||""},setCrossOrigin:function(t){this._crossOrigin=t;var e=this._image;e&&(e.crossOrigin=t)},getSmoothing:function(){return this._smoothing},setSmoothing:function(t){this._smoothing=typeof t=="string"?t:t?"low":"off",this._changed(257)},getElement:function(){return this._canvas||this._loaded&&this._image}},{beans:!1,getSubCanvas:function(){var t=K.read(arguments),e=ft.getContext(t.getSize());return e.drawImage(this.getCanvas(),t.x,t.y,t.width,t.height,0,0,t.width,t.height),e.canvas},getSubRaster:function(){var t=K.read(arguments),e=new Kt(X.NO_INSERT);return e._setImage(this.getSubCanvas(t)),e.translate(t.getCenter().subtract(this.getSize().divide(2))),e._matrix.prepend(this._matrix),e.insertAbove(this),e},toDataURL:function(){var t=this._image,e=t&&t.src;if(/^data:/.test(e))return e;var i=this.getCanvas();return i?i.toDataURL.apply(i,arguments):null},drawImage:function(t){var e=L.read(arguments,1);this.getContext(!0).drawImage(t,e.x,e.y)},getAverageColor:function(t){var e,i;if(t?t instanceof Zt?(i=t,e=t.getBounds()):typeof t=="object"&&("width"in t?e=new K(t):"x"in t&&(e=new K(t.x-.5,t.y-.5,1,1))):e=this.getBounds(),!e)return null;var n=32,r=Math.min(e.width,n),s=Math.min(e.height,n),h=Kt._sampleContext;h?h.clearRect(0,0,n+1,n+1):h=Kt._sampleContext=ft.getContext(new Z(n)),h.save();var a=new st().scale(r/e.width,s/e.height).translate(-e.x,-e.y);a.applyToContext(h),i&&i.draw(h,new T({clip:!0,matrices:[a]})),this._matrix.applyToContext(h);var o=this.getElement(),u=this._size;o&&h.drawImage(o,-u.width/2,-u.height/2),h.restore();for(var _=h.getImageData(.5,.5,Math.ceil(r),Math.ceil(s)).data,d=[0,0,0],c=0,l=0,p=_.length;l<p;l+=4){var y=_[l+3];c+=y,y/=255,d[0]+=_[l]*y,d[1]+=_[l+1]*y,d[2]+=_[l+2]*y}for(var l=0;l<3;l++)d[l]/=c;return c?ct.read(d):null},getPixel:function(){var t=L.read(arguments),e=this.getContext().getImageData(t.x,t.y,1,1).data;return new ct("rgb",[e[0]/255,e[1]/255,e[2]/255],e[3]/255)},setPixel:function(){var t=arguments,e=L.read(t),i=ct.read(t),n=i._convert("rgb"),r=i._alpha,s=this.getContext(!0),h=s.createImageData(1,1),a=h.data;a[0]=n[0]*255,a[1]=n[1]*255,a[2]=n[2]*255,a[3]=r!=null?r*255:255,s.putImageData(h,e.x,e.y)},clear:function(){var t=this._size;this.getContext(!0).clearRect(0,0,t.width+1,t.height+1)},createImageData:function(){var t=Z.read(arguments);return this.getContext().createImageData(t.width,t.height)},getImageData:function(){var t=K.read(arguments);return t.isEmpty()&&(t=new K(this._size)),this.getContext().getImageData(t.x,t.y,t.width,t.height)},putImageData:function(t){var e=L.read(arguments,1);this.getContext(!0).putImageData(t,e.x,e.y)},setImageData:function(t){this.setSize(t),this.getContext(!0).putImageData(t,0,0)},_getBounds:function(t,e){var i=new K(this._size).setCenter(0,0);return t?t._transformBounds(i):i},_hitTestSelf:function(t){if(this._contains(t)){var e=this;return new Lt("pixel",e,{offset:t.add(e._size.divide(2)).round(),color:{get:function(){return e.getPixel(this.offset)}}})}},_draw:function(t,e,i){var n=this.getElement();if(n&&n.width>0&&n.height>0){t.globalAlpha=et.clamp(this._opacity,0,1),this._setStyles(t,e,i);var r=this._smoothing,s=r==="off";ht.setPrefixed(t,s?"imageSmoothingEnabled":"imageSmoothingQuality",s?!1:r),t.drawImage(n,-this._size.width/2,-this._size.height/2)}},_canComposite:function(){return!0}}),re=X.extend({_class:"SymbolItem",_applyMatrix:!1,_canApplyMatrix:!1,_boundsOptions:{stroke:!0},_serializeFields:{symbol:null},initialize:function(e,i){this._initialize(e,i!==D&&L.read(arguments,1))||this.setDefinition(e instanceof Bt?e:new Bt(e))},_equals:function(t){return this._definition===t._definition},copyContent:function(t){this.setDefinition(t._definition)},getDefinition:function(){return this._definition},setDefinition:function(t){this._definition=t,this._changed(9)},getSymbol:"#getDefinition",setSymbol:"#setDefinition",isEmpty:function(){return this._definition._item.isEmpty()},_getBounds:function(t,e){var i=this._definition._item;return i._getCachedBounds(i._matrix.prepended(t),e)},_hitTestSelf:function(t,e,i){var n=e.extend({all:!1}),r=this._definition._item._hitTest(t,n,i);return r&&(r.item=this),r},_draw:function(t,e){this._definition._item.draw(t,e)}}),Bt=T.extend({_class:"SymbolDefinition",initialize:function(e,i){this._id=jt.get(),this.project=Q.project,e&&this.setItem(e,i)},_serialize:function(t,e){return e.add(this,function(){return T.serialize([this._class,this._item],t,!1,e)})},_changed:function(t){t&8&&X._clearBoundsCache(this),t&1&&this.project._changed(t)},getItem:function(){return this._item},setItem:function(t,e){t._symbol&&(t=t.clone()),this._item&&(this._item._symbol=null),this._item=t,t.remove(),t.setSelected(!1),e||t.setPosition(new L),t._symbol=this,this._changed(9)},getDefinition:"#getItem",setDefinition:"#setItem",place:function(t){return new re(this,t)},clone:function(){return new Bt(this._item.clone(!1))},equals:function(t){return t===this||t&&this._item.equals(t._item)||!1}}),Lt=T.extend({_class:"HitResult",initialize:function(e,i,n){this.type=e,this.item=i,n&&this.inject(n)},statics:{getOptions:function(t){var e=t&&T.read(t);return new T({type:null,tolerance:Q.settings.hitTolerance,fill:!e,stroke:!e,segments:!e,handles:!1,ends:!1,position:!1,center:!1,bounds:!1,guides:!1,selected:!1},e)}}}),G=T.extend({_class:"Segment",beans:!0,_selection:0,initialize:function(e,i,n,r,s,h){var a=arguments.length,o,u,_,d;a>0&&(e==null||typeof e=="object"?a===1&&e&&"point"in e?(o=e.point,u=e.handleIn,_=e.handleOut,d=e.selection):(o=e,u=i,_=n,d=r):(o=[e,i],u=n!==D?[n,r]:null,_=s!==D?[s,h]:null)),new se(o,this,"_point"),new se(u,this,"_handleIn"),new se(_,this,"_handleOut"),d&&this.setSelection(d)},_serialize:function(t,e){var i=this._point,n=this._selection,r=n||this.hasHandles()?[i,this._handleIn,this._handleOut]:i;return n&&r.push(n),T.serialize(r,t,!0,e)},_changed:function(t){var e=this._path;if(e){var i=e._curves,n=this._index,r;i&&((!t||t===this._point||t===this._handleIn)&&(r=n>0?i[n-1]:e._closed?i[i.length-1]:null)&&r._changed(),(!t||t===this._point||t===this._handleOut)&&(r=i[n])&&r._changed()),e._changed(41)}},getPoint:function(){return this._point},setPoint:function(){this._point.set(L.read(arguments))},getHandleIn:function(){return this._handleIn},setHandleIn:function(){this._handleIn.set(L.read(arguments))},getHandleOut:function(){return this._handleOut},setHandleOut:function(){this._handleOut.set(L.read(arguments))},hasHandles:function(){return!this._handleIn.isZero()||!this._handleOut.isZero()},isSmooth:function(){var t=this._handleIn,e=this._handleOut;return!t.isZero()&&!e.isZero()&&t.isCollinear(e)},clearHandles:function(){this._handleIn._set(0,0),this._handleOut._set(0,0)},getSelection:function(){return this._selection},setSelection:function(t){var e=this._selection,i=this._path;this._selection=t=t||0,i&&t!==e&&(i._updateSelection(this,e,t),i._changed(257))},_changeSelection:function(t,e){var i=this._selection;this.setSelection(e?i|t:i&~t)},isSelected:function(){return!!(this._selection&7)},setSelected:function(t){this._changeSelection(7,t)},getIndex:function(){return this._index!==D?this._index:null},getPath:function(){return this._path||null},getCurve:function(){var t=this._path,e=this._index;return t?(e>0&&!t._closed&&e===t._segments.length-1&&e--,t.getCurves()[e]||null):null},getLocation:function(){var t=this.getCurve();return t?new Tt(t,this===t._segment1?0:1):null},getNext:function(){var t=this._path&&this._path._segments;return t&&(t[this._index+1]||this._path._closed&&t[0])||null},smooth:function(t,e,i){var n=t||{},r=n.type,s=n.factor,h=this.getPrevious(),a=this.getNext(),o=(h||this)._point,u=this._point,_=(a||this)._point,d=o.getDistance(u),c=u.getDistance(_);if(!r||r==="catmull-rom"){var l=s===D?.5:s,p=Math.pow(d,l),y=p*p,C=Math.pow(c,l),v=C*C;if(!e&&h){var m=2*v+3*C*p+y,f=3*C*(C+p);this.setHandleIn(f!==0?new L((v*o._x+m*u._x-y*_._x)/f-u._x,(v*o._y+m*u._y-y*_._y)/f-u._y):new L)}if(!i&&a){var m=2*y+3*p*C+v,f=3*p*(p+C);this.setHandleOut(f!==0?new L((y*_._x+m*u._x-v*o._x)/f-u._x,(y*_._y+m*u._y-v*o._y)/f-u._y):new L)}}else if(r==="geometric"){if(h&&a){var g=o.subtract(_),w=s===D?.4:s,S=w*d/(d+c);e||this.setHandleIn(g.multiply(S)),i||this.setHandleOut(g.multiply(S-w))}}else throw new Error("Smoothing method '"+r+"' not supported.")},getPrevious:function(){var t=this._path&&this._path._segments;return t&&(t[this._index-1]||this._path._closed&&t[t.length-1])||null},isFirst:function(){return!this._index},isLast:function(){var t=this._path;return t&&this._index===t._segments.length-1||!1},reverse:function(){var t=this._handleIn,e=this._handleOut,i=t.clone();t.set(e),e.set(i)},reversed:function(){return new G(this._point,this._handleOut,this._handleIn)},remove:function(){return this._path?!!this._path.removeSegment(this._index):!1},clone:function(){return new G(this._point,this._handleIn,this._handleOut)},equals:function(t){return t===this||t&&this._class===t._class&&this._point.equals(t._point)&&this._handleIn.equals(t._handleIn)&&this._handleOut.equals(t._handleOut)||!1},toString:function(){var t=["point: "+this._point];return this._handleIn.isZero()||t.push("handleIn: "+this._handleIn),this._handleOut.isZero()||t.push("handleOut: "+this._handleOut),"{ "+t.join(", ")+" }"},transform:function(t){this._transformCoordinates(t,new Array(6),!0),this._changed()},interpolate:function(t,e,i){var n=1-i,r=i,s=t._point,h=e._point,a=t._handleIn,o=e._handleIn,u=e._handleOut,_=t._handleOut;this._point._set(n*s._x+r*h._x,n*s._y+r*h._y,!0),this._handleIn._set(n*a._x+r*o._x,n*a._y+r*o._y,!0),this._handleOut._set(n*_._x+r*u._x,n*_._y+r*u._y,!0),this._changed()},_transformCoordinates:function(t,e,i){var n=this._point,r=!i||!this._handleIn.isZero()?this._handleIn:null,s=!i||!this._handleOut.isZero()?this._handleOut:null,h=n._x,a=n._y,o=2;return e[0]=h,e[1]=a,r&&(e[o++]=r._x+h,e[o++]=r._y+a),s&&(e[o++]=s._x+h,e[o++]=s._y+a),t&&(t._transformCoordinates(e,e,o/2),h=e[0],a=e[1],i?(n._x=h,n._y=a,o=2,r&&(r._x=e[o++]-h,r._y=e[o++]-a),s&&(s._x=e[o++]-h,s._y=e[o++]-a)):(r||(e[o++]=h,e[o++]=a),s||(e[o++]=h,e[o++]=a))),e}}),se=L.extend({initialize:function(e,i,n){var r,s,h;if(!e)r=s=0;else if((r=e[0])!==D)s=e[1];else{var a=e;(r=a.x)===D&&(a=L.read(arguments),r=a.x),s=a.y,h=a.selected}this._x=r,this._y=s,this._owner=i,i[n]=this,h&&this.setSelected(!0)},_set:function(t,e){return this._x=t,this._y=e,this._owner._changed(this),this},getX:function(){return this._x},setX:function(t){this._x=t,this._owner._changed(this)},getY:function(){return this._y},setY:function(t){this._y=t,this._owner._changed(this)},isZero:function(){var t=et.isZero;return t(this._x)&&t(this._y)},isSelected:function(){return!!(this._owner._selection&this._getSelection())},setSelected:function(t){this._owner._changeSelection(this._getSelection(),t)},_getSelection:function(){var t=this._owner;return this===t._point?1:this===t._handleIn?2:this===t._handleOut?4:0}}),R=T.extend({_class:"Curve",beans:!0,initialize:function(e,i,n,r,s,h,a,o){var u=arguments.length,_,d,c,l,p,y;u===3?(this._path=e,_=i,d=n):u?u===1?"segment1"in e?(_=new G(e.segment1),d=new G(e.segment2)):"point1"in e?(c=e.point1,p=e.handle1,y=e.handle2,l=e.point2):Array.isArray(e)&&(c=[e[0],e[1]],l=[e[6],e[7]],p=[e[2]-e[0],e[3]-e[1]],y=[e[4]-e[6],e[5]-e[7]]):u===2?(_=new G(e),d=new G(i)):u===4?(c=e,p=i,y=n,l=r):u===8&&(c=[e,i],l=[a,o],p=[n-e,r-i],y=[s-a,h-o]):(_=new G,d=new G),this._segment1=_||new G(c,null,p),this._segment2=d||new G(l,y,null)},_serialize:function(t,e){return T.serialize(this.hasHandles()?[this.getPoint1(),this.getHandle1(),this.getHandle2(),this.getPoint2()]:[this.getPoint1(),this.getPoint2()],t,!0,e)},_changed:function(){this._length=this._bounds=D},clone:function(){return new R(this._segment1,this._segment2)},toString:function(){var t=["point1: "+this._segment1._point];return this._segment1._handleOut.isZero()||t.push("handle1: "+this._segment1._handleOut),this._segment2._handleIn.isZero()||t.push("handle2: "+this._segment2._handleIn),t.push("point2: "+this._segment2._point),"{ "+t.join(", ")+" }"},classify:function(){return R.classify(this.getValues())},remove:function(){var t=!1;if(this._path){var e=this._segment2,i=e._handleOut;t=e.remove(),t&&this._segment1._handleOut.set(i)}return t},getPoint1:function(){return this._segment1._point},setPoint1:function(){this._segment1._point.set(L.read(arguments))},getPoint2:function(){return this._segment2._point},setPoint2:function(){this._segment2._point.set(L.read(arguments))},getHandle1:function(){return this._segment1._handleOut},setHandle1:function(){this._segment1._handleOut.set(L.read(arguments))},getHandle2:function(){return this._segment2._handleIn},setHandle2:function(){this._segment2._handleIn.set(L.read(arguments))},getSegment1:function(){return this._segment1},getSegment2:function(){return this._segment2},getPath:function(){return this._path},getIndex:function(){return this._segment1._index},getNext:function(){var t=this._path&&this._path._curves;return t&&(t[this._segment1._index+1]||this._path._closed&&t[0])||null},getPrevious:function(){var t=this._path&&this._path._curves;return t&&(t[this._segment1._index-1]||this._path._closed&&t[t.length-1])||null},isFirst:function(){return!this._segment1._index},isLast:function(){var t=this._path;return t&&this._segment1._index===t._curves.length-1||!1},isSelected:function(){return this.getPoint1().isSelected()&&this.getHandle1().isSelected()&&this.getHandle2().isSelected()&&this.getPoint2().isSelected()},setSelected:function(t){this.getPoint1().setSelected(t),this.getHandle1().setSelected(t),this.getHandle2().setSelected(t),this.getPoint2().setSelected(t)},getValues:function(t){return R.getValues(this._segment1,this._segment2,t)},getPoints:function(){for(var t=this.getValues(),e=[],i=0;i<8;i+=2)e.push(new L(t[i],t[i+1]));return e}},{getLength:function(){return this._length==null&&(this._length=R.getLength(this.getValues(),0,1)),this._length},getArea:function(){return R.getArea(this.getValues())},getLine:function(){return new pt(this._segment1._point,this._segment2._point)},getPart:function(t,e){return new R(R.getPart(this.getValues(),t,e))},getPartLength:function(t,e){return R.getLength(this.getValues(),t,e)},divideAt:function(t){return this.divideAtTime(t&&t.curve===this?t.time:this.getTimeAt(t))},divideAtTime:function(t,e){var i=1e-8,n=1-i,r=null;if(t>=i&&t<=n){var s=R.subdivide(this.getValues(),t),h=s[0],a=s[1],o=e||this.hasHandles(),u=this._segment1,_=this._segment2,d=this._path;o&&(u._handleOut._set(h[2]-h[0],h[3]-h[1]),_._handleIn._set(a[4]-a[6],a[5]-a[7]));var c=h[6],l=h[7],p=new G(new L(c,l),o&&new L(h[4]-c,h[5]-l),o&&new L(a[2]-c,a[3]-l));d?(d.insert(u._index+1,p),r=this.getNext()):(this._segment2=p,this._changed(),r=new R(p,_))}return r},splitAt:function(t){var e=this._path;return e?e.splitAt(t):null},splitAtTime:function(t){return this.splitAt(this.getLocationAtTime(t))},divide:function(t,e){return this.divideAtTime(t===D?.5:e?t:this.getTimeAt(t))},split:function(t,e){return this.splitAtTime(t===D?.5:e?t:this.getTimeAt(t))},reversed:function(){return new R(this._segment2.reversed(),this._segment1.reversed())},clearHandles:function(){this._segment1._handleOut._set(0,0),this._segment2._handleIn._set(0,0)},statics:{getValues:function(t,e,i,n){var r=t._point,s=t._handleOut,h=e._handleIn,a=e._point,o=r.x,u=r.y,_=a.x,d=a.y,c=n?[o,u,o,u,_,d,_,d]:[o,u,o+s._x,u+s._y,_+h._x,d+h._y,_,d];return i&&i._transformCoordinates(c,c,4),c},subdivide:function(t,e){var i=t[0],n=t[1],r=t[2],s=t[3],h=t[4],a=t[5],o=t[6],u=t[7];e===D&&(e=.5);var _=1-e,d=_*i+e*r,c=_*n+e*s,l=_*r+e*h,p=_*s+e*a,y=_*h+e*o,C=_*a+e*u,v=_*d+e*l,m=_*c+e*p,f=_*l+e*y,g=_*p+e*C,w=_*v+e*f,S=_*m+e*g;return[[i,n,d,c,v,m,w,S],[w,S,f,g,y,C,o,u]]},getMonoCurves:function(t,e){var i=[],n=e?0:1,r=t[n+0],s=t[n+2],h=t[n+4],a=t[n+6];if(r>=s==s>=h&&s>=h==h>=a||R.isStraight(t))i.push(t);else{var o=3*(s-h)-r+a,u=2*(r+h)-4*s,_=s-r,d=1e-8,c=1-d,l=[],p=et.solveQuadratic(o,u,_,l,d,c);if(!p)i.push(t);else{l.sort();var y=l[0],C=R.subdivide(t,y);i.push(C[0]),p>1&&(y=(l[1]-y)/(1-y),C=R.subdivide(C[1],y),i.push(C[0])),i.push(C[1])}}return i},solveCubic:function(t,e,i,n,r,s){var h=t[e],a=t[e+2],o=t[e+4],u=t[e+6],_=0;if(!(h<i&&u<i&&a<i&&o<i||h>i&&u>i&&a>i&&o>i)){var d=3*(a-h),c=3*(o-a)-d,l=u-h-d-c;_=et.solveCubic(l,c,d,h-i,n,r,s)}return _},getTimeOf:function(t,e){var i=new L(t[0],t[1]),n=new L(t[6],t[7]),r=1e-12,s=1e-7,h=e.isClose(i,r)?0:e.isClose(n,r)?1:null;if(h===null)for(var a=[e.x,e.y],o=[],u=0;u<2;u++)for(var _=R.solveCubic(t,u,a[u],o,0,1),d=0;d<_;d++){var c=o[d];if(e.isClose(R.getPoint(t,c),s))return c}return e.isClose(i,s)?0:e.isClose(n,s)?1:null},getNearestTime:function(t,e){if(R.isStraight(t)){var i=t[0],n=t[1],r=t[6],s=t[7],h=r-i,a=s-n,o=h*h+a*a;if(o===0)return 0;var u=((e.x-i)*h+(e.y-n)*a)/o;return u<1e-12?0:u>.999999999999?1:R.getTimeOf(t,new L(i+u*h,n+u*a))}var _=100,d=1/0,c=0;function l(C){if(C>=0&&C<=1){var v=e.getDistance(R.getPoint(t,C),!0);if(v<d)return d=v,c=C,!0}}for(var p=0;p<=_;p++)l(p/_);for(var y=1/(_*2);y>1e-8;)!l(c-y)&&!l(c+y)&&(y/=2);return c},getPart:function(t,e,i){var n=e>i;if(n){var r=e;e=i,i=r}return e>0&&(t=R.subdivide(t,e)[1]),i<1&&(t=R.subdivide(t,(i-e)/(1-e))[0]),n?[t[6],t[7],t[4],t[5],t[2],t[3],t[0],t[1]]:t},isFlatEnough:function(t,e){var i=t[0],n=t[1],r=t[2],s=t[3],h=t[4],a=t[5],o=t[6],u=t[7],_=3*r-2*i-o,d=3*s-2*n-u,c=3*h-2*o-i,l=3*a-2*u-n;return Math.max(_*_,c*c)+Math.max(d*d,l*l)<=16*e*e},getArea:function(t){var e=t[0],i=t[1],n=t[2],r=t[3],s=t[4],h=t[5],a=t[6],o=t[7];return 3*((o-i)*(n+s)-(a-e)*(r+h)+r*(e-s)-n*(i-h)+o*(s+e/3)-a*(h+i/3))/20},getBounds:function(t){for(var e=t.slice(0,2),i=e.slice(),n=[0,0],r=0;r<2;r++)R._addBounds(t[r],t[r+2],t[r+4],t[r+6],r,0,e,i,n);return new K(e[0],e[1],i[0]-e[0],i[1]-e[1])},_addBounds:function(t,e,i,n,r,s,h,a,o){function u(w,S){var x=w-S,b=w+S;x<h[r]&&(h[r]=x),b>a[r]&&(a[r]=b)}s/=2;var _=h[r]+s,d=a[r]-s;if(t<_||e<_||i<_||n<_||t>d||e>d||i>d||n>d)if(e<t!=e<n&&i<t!=i<n)u(t,0),u(n,0);else{var c=3*(e-i)-t+n,l=2*(t+i)-4*e,p=e-t,y=et.solveQuadratic(c,l,p,o),C=1e-8,v=1-C;u(n,0);for(var m=0;m<y;m++){var f=o[m],g=1-f;C<=f&&f<=v&&u(g*g*g*t+3*g*g*f*e+3*g*f*f*i+f*f*f*n,s)}}}}},T.each(["getBounds","getStrokeBounds","getHandleBounds"],function(t){this[t]=function(){this._bounds||(this._bounds={});var e=this._bounds[t];return e||(e=this._bounds[t]=nt[t]([this._segment1,this._segment2],!1,this._path)),e.clone()}},{}),T.each({isStraight:function(t,e,i,n){if(e.isZero()&&i.isZero())return!0;var r=n.subtract(t);if(r.isZero())return!1;if(r.isCollinear(e)&&r.isCollinear(i)){var s=new pt(t,n),h=1e-7;if(s.getDistance(t.add(e))<h&&s.getDistance(n.add(i))<h){var a=r.dot(r),o=r.dot(e)/a,u=r.dot(i)/a;return o>=0&&o<=1&&u<=0&&u>=-1}}return!1},isLinear:function(t,e,i,n){var r=n.subtract(t).divide(3);return e.equals(r)&&i.negate().equals(r)}},function(t,e){this[e]=function(i){var n=this._segment1,r=this._segment2;return t(n._point,n._handleOut,r._handleIn,r._point,i)},this.statics[e]=function(i,n){var r=i[0],s=i[1],h=i[6],a=i[7];return t(new L(r,s),new L(i[2]-r,i[3]-s),new L(i[4]-h,i[5]-a),new L(h,a),n)}},{statics:{},hasHandles:function(){return!this._segment1._handleOut.isZero()||!this._segment2._handleIn.isZero()},hasLength:function(t){return(!this.getPoint1().equals(this.getPoint2())||this.hasHandles())&&this.getLength()>(t||0)},isCollinear:function(t){return t&&this.isStraight()&&t.isStraight()&&this.getLine().isCollinear(t.getLine())},isHorizontal:function(){return this.isStraight()&&Math.abs(this.getTangentAtTime(.5).y)<1e-8},isVertical:function(){return this.isStraight()&&Math.abs(this.getTangentAtTime(.5).x)<1e-8}}),{beans:!1,getLocationAt:function(t,e){return this.getLocationAtTime(e?t:this.getTimeAt(t))},getLocationAtTime:function(t){return t!=null&&t>=0&&t<=1?new Tt(this,t):null},getTimeAt:function(t,e){return R.getTimeAt(this.getValues(),t,e)},getParameterAt:"#getTimeAt",getTimesWithTangent:function(){var t=L.read(arguments);return t.isZero()?[]:R.getTimesWithTangent(this.getValues(),t)},getOffsetAtTime:function(t){return this.getPartLength(0,t)},getLocationOf:function(){return this.getLocationAtTime(this.getTimeOf(L.read(arguments)))},getOffsetOf:function(){var t=this.getLocationOf.apply(this,arguments);return t?t.getOffset():null},getTimeOf:function(){return R.getTimeOf(this.getValues(),L.read(arguments))},getParameterOf:"#getTimeOf",getNearestLocation:function(){var t=L.read(arguments),e=this.getValues(),i=R.getNearestTime(e,t),n=R.getPoint(e,i);return new Tt(this,i,n,null,t.getDistance(n))},getNearestPoint:function(){var t=this.getNearestLocation.apply(this,arguments);return t&&t.getPoint()}},new function(){var t=["getPoint","getTangent","getNormal","getWeightedTangent","getWeightedNormal","getCurvature"];return T.each(t,function(e){this[e+"At"]=function(i,n){var r=this.getValues();return R[e](r,n?i:R.getTimeAt(r,i))},this[e+"AtTime"]=function(i){return R[e](this.getValues(),i)}},{statics:{_evaluateMethods:t}})},new function(){function t(n){var r=n[0],s=n[1],h=n[2],a=n[3],o=n[4],u=n[5],_=n[6],d=n[7],c=9*(h-o)+3*(_-r),l=6*(r+o)-12*h,p=3*(h-r),y=9*(a-u)+3*(d-s),C=6*(s+u)-12*a,v=3*(a-s);return function(m){var f=(c*m+l)*m+p,g=(y*m+C)*m+v;return Math.sqrt(f*f+g*g)}}function e(n,r){return Math.max(2,Math.min(16,Math.ceil(Math.abs(r-n)*32)))}function i(n,r,s,h){if(r==null||r<0||r>1)return null;var a=n[0],o=n[1],u=n[2],_=n[3],d=n[4],c=n[5],l=n[6],p=n[7],y=et.isZero;y(u-a)&&y(_-o)&&(u=a,_=o),y(d-l)&&y(c-p)&&(d=l,c=p);var C=3*(u-a),v=3*(d-u)-C,m=l-a-C-v,f=3*(_-o),g=3*(c-_)-f,w=p-o-f-g,S,x;if(s===0)S=r===0?a:r===1?l:((m*r+v)*r+C)*r+a,x=r===0?o:r===1?p:((w*r+g)*r+f)*r+o;else{var b=1e-8,P=1-b;if(r<b?(S=C,x=f):r>P?(S=3*(l-d),x=3*(p-c)):(S=(3*m*r+2*v)*r+C,x=(3*w*r+2*g)*r+f),h){S===0&&x===0&&(r<b||r>P)&&(S=d-u,x=c-_);var I=Math.sqrt(S*S+x*x);I&&(S/=I,x/=I)}if(s===3){var d=6*m*r+2*v,c=6*w*r+2*g,O=Math.pow(S*S+x*x,3/2);S=O!==0?(S*c-x*d)/O:0,x=0}}return s===2?new L(x,-S):new L(S,x)}return{statics:{classify:function(n){var r=n[0],s=n[1],h=n[2],a=n[3],o=n[4],u=n[5],_=n[6],d=n[7],c=r*(d-u)+s*(o-_)+_*u-d*o,l=h*(s-d)+a*(_-r)+r*d-s*_,p=o*(a-s)+u*(r-h)+h*s-a*r,y=3*p,C=y-l,v=C-l+c,m=Math.sqrt(v*v+C*C+y*y),f=m!==0?1/m:0,g=et.isZero,w="serpentine";v*=f,C*=f,y*=f;function S(I,O,A){var z=O!==D,M=z&&O>0&&O<1,k=z&&A>0&&A<1;return z&&(!(M||k)||I==="loop"&&!(M&&k))&&(I="arch",M=k=!1),{type:I,roots:M||k?M&&k?O<A?[O,A]:[A,O]:[M?O:A]:null}}if(g(v))return g(C)?S(g(y)?"line":"quadratic"):S(w,y/(3*C));var x=3*C*C-4*v*y;if(g(x))return S("cusp",C/(2*v));var b=x>0?Math.sqrt(x/3):Math.sqrt(-x),P=2*v;return S(x>0?w:"loop",(C+b)/P,(C-b)/P)},getLength:function(n,r,s,h){if(r===D&&(r=0),s===D&&(s=1),R.isStraight(n)){var a=n;s<1&&(a=R.subdivide(a,s)[0],r/=s),r>0&&(a=R.subdivide(a,r)[1]);var o=a[6]-a[0],u=a[7]-a[1];return Math.sqrt(o*o+u*u)}return et.integrate(h||t(n),r,s,e(r,s))},getTimeAt:function(n,r,s){if(s===D&&(s=r<0?1:0),r===0)return s;var h=Math.abs,a=1e-12,o=r>0,u=o?s:0,_=o?1:s,d=t(n),c=R.getLength(n,u,_,d),l=h(r)-c;if(h(l)<a)return o?_:u;if(l>a)return null;var p=r/c,y=0;function C(v){return y+=et.integrate(d,s,v,e(s,v)),s=v,y-r}return et.findRoot(C,d,s+p,u,_,32,1e-12)},getPoint:function(n,r){return i(n,r,0,!1)},getTangent:function(n,r){return i(n,r,1,!0)},getWeightedTangent:function(n,r){return i(n,r,1,!1)},getNormal:function(n,r){return i(n,r,2,!0)},getWeightedNormal:function(n,r){return i(n,r,2,!1)},getCurvature:function(n,r){return i(n,r,3,!1).x},getPeaks:function(n){var r=n[0],s=n[1],h=n[2],a=n[3],o=n[4],u=n[5],_=n[6],d=n[7],c=-r+3*h-3*o+_,l=3*r-6*h+3*o,p=-3*r+3*h,y=-s+3*a-3*u+d,C=3*s-6*a+3*u,v=-3*s+3*a,m=1e-8,f=1-m,g=[];return et.solveCubic(9*(c*c+y*y),9*(c*l+C*y),2*(l*l+C*C)+3*(p*c+v*y),p*l+C*v,g,m,f),g.sort()}}}},new function(){function t(l,p,y,C,v,m,f){var g=!f&&y.getPrevious()===v,w=!f&&y!==v&&y.getNext()===v,S=1e-8,x=1-S;if(C!==null&&C>=(g?S:0)&&C<=(w?x:1)&&m!==null&&m>=(w?S:0)&&m<=(g?x:1)){var b=new Tt(y,C,null,f),P=new Tt(v,m,null,f);b._intersection=P,P._intersection=b,(!p||p(b))&&Tt.insert(l,b,!0)}}function e(l,p,y,C,v,m,f,g,w,S,x,b,P){if(++w>=4096||++g>=40)return w;var I=1e-9,O=p[0],A=p[1],z=p[6],M=p[7],k=pt.getSignedDistance,E=k(O,A,z,M,p[2],p[3]),N=k(O,A,z,M,p[4],p[5]),B=E*N>0?3/4:4/9,F=B*Math.min(0,E,N),V=B*Math.max(0,E,N),W=k(O,A,z,M,l[0],l[1]),H=k(O,A,z,M,l[2],l[3]),J=k(O,A,z,M,l[4],l[5]),q=k(O,A,z,M,l[6],l[7]),$=i(W,H,J,q),U=$[0],rt=$[1],Y,tt;if(E===0&&N===0&&W===0&&H===0&&J===0&&q===0||(Y=n(U,rt,F,V))==null||(tt=n(U.reverse(),rt.reverse(),F,V))==null)return w;var it=S+(x-S)*Y,j=S+(x-S)*tt;if(Math.max(P-b,j-it)<I){var mt=(it+j)/2,wt=(b+P)/2;t(v,m,f?C:y,f?wt:mt,f?y:C,f?mt:wt)}else{l=R.getPart(l,Y,tt);var St=P-b;if(tt-Y>.8)if(j-it>St){var dt=R.subdivide(l,.5),mt=(it+j)/2;w=e(p,dt[0],C,y,v,m,!f,g,w,b,P,it,mt),w=e(p,dt[1],C,y,v,m,!f,g,w,b,P,mt,j)}else{var dt=R.subdivide(p,.5),wt=(b+P)/2;w=e(dt[0],l,C,y,v,m,!f,g,w,b,wt,it,j),w=e(dt[1],l,C,y,v,m,!f,g,w,wt,P,it,j)}else St===0||St>=I?w=e(p,l,C,y,v,m,!f,g,w,b,P,it,j):w=e(l,p,y,C,v,m,f,g,w,it,j,b,P)}return w}function i(l,p,y,C){var v=[0,l],m=[1/3,p],f=[2/3,y],g=[1,C],w=p-(2*l+C)/3,S=y-(l+2*C)/3,x;if(w*S<0)x=[[v,m,g],[v,f,g]];else{var b=w/S;x=[b>=2?[v,m,g]:b<=.5?[v,f,g]:[v,m,f,g],[v,g]]}return(w||S)<0?x.reverse():x}function n(l,p,y,C){return l[0][1]<y?r(l,!0,y):p[0][1]>C?r(p,!1,C):l[0][0]}function r(l,p,y){for(var C=l[0][0],v=l[0][1],m=1,f=l.length;m<f;m++){var g=l[m][0],w=l[m][1];if(p?w>=y:w<=y)return w===y?g:C+(y-v)*(g-C)/(w-v);C=g,v=w}return null}function s(l,p,y,C,v){var m=et.isZero;if(m(C)&&m(v)){var f=R.getTimeOf(l,new L(p,y));return f===null?[]:[f]}for(var g=Math.atan2(-v,C),w=Math.sin(g),S=Math.cos(g),x=[],b=[],P=0;P<8;P+=2){var I=l[P]-p,O=l[P+1]-y;x.push(I*S-O*w,I*w+O*S)}return R.solveCubic(x,1,0,b,0,1),b}function h(l,p,y,C,v,m,f){for(var g=p[0],w=p[1],S=p[6],x=p[7],b=s(l,g,w,S-g,x-w),P=0,I=b.length;P<I;P++){var O=b[P],A=R.getPoint(l,O),z=R.getTimeOf(p,A);z!==null&&t(v,m,f?C:y,f?z:O,f?y:C,f?O:z)}}function a(l,p,y,C,v,m){var f=pt.intersect(l[0],l[1],l[6],l[7],p[0],p[1],p[6],p[7]);f&&t(v,m,y,R.getTimeOf(l,f),C,R.getTimeOf(p,f))}function o(l,p,y,C,v,m){var f=1e-12,g=Math.min,w=Math.max;if(w(l[0],l[2],l[4],l[6])+f>g(p[0],p[2],p[4],p[6])&&g(l[0],l[2],l[4],l[6])-f<w(p[0],p[2],p[4],p[6])&&w(l[1],l[3],l[5],l[7])+f>g(p[1],p[3],p[5],p[7])&&g(l[1],l[3],l[5],l[7])-f<w(p[1],p[3],p[5],p[7])){var S=d(l,p);if(S)for(var x=0;x<2;x++){var b=S[x];t(v,m,y,b[0],C,b[1],!0)}else{var P=R.isStraight(l),I=R.isStraight(p),O=P&&I,A=P&&!I,z=v.length;if((O?a:P||I?h:e)(A?p:l,A?l:p,A?C:y,A?y:C,v,m,A,0,0,0,1,0,1),!O||v.length===z)for(var x=0;x<4;x++){var M=x>>1,k=x&1,E=M*6,N=k*6,B=new L(l[E],l[E+1]),F=new L(p[N],p[N+1]);B.isClose(F,f)&&t(v,m,y,M,C,k)}}}return v}function u(l,p,y,C){var v=R.classify(l);if(v.type==="loop"){var m=v.roots;t(y,C,p,m[0],p,m[1])}return y}function _(l,p,y,C,v,m){var f=1e-7,g=!p;g&&(p=l);for(var w=l.length,S=p.length,x=new Array(w),b=g?x:new Array(S),P=[],I=0;I<w;I++)x[I]=l[I].getValues(C);if(!g)for(var I=0;I<S;I++)b[I]=p[I].getValues(v);for(var O=Yt.findCurveBoundsCollisions(x,b,f),A=0;A<w;A++){var z=l[A],M=x[A];g&&u(M,z,P,y);var k=O[A];if(k)for(var E=0;E<k.length;E++){if(m&&P.length)return P;var N=k[E];if(!g||N>A){var B=p[N],F=b[N];o(M,F,z,B,P,y)}}}return P}function d(l,p){function y(q){var $=q[6]-q[0],U=q[7]-q[1];return $*$+U*U}var C=Math.abs,v=pt.getDistance,m=1e-8,f=1e-7,g=R.isStraight(l),w=R.isStraight(p),S=g&&w,x=y(l)<y(p),b=x?p:l,P=x?l:p,I=b[0],O=b[1],A=b[6]-I,z=b[7]-O;if(v(I,O,A,z,P[0],P[1],!0)<f&&v(I,O,A,z,P[6],P[7],!0)<f)!S&&v(I,O,A,z,b[2],b[3],!0)<f&&v(I,O,A,z,b[4],b[5],!0)<f&&v(I,O,A,z,P[2],P[3],!0)<f&&v(I,O,A,z,P[4],P[5],!0)<f&&(g=w=S=!0);else if(S)return null;if(g^w)return null;for(var M=[l,p],k=[],E=0;E<4&&k.length<2;E++){var N=E&1,B=N^1,F=E>>1,V=R.getTimeOf(M[N],new L(M[B][F?6:0],M[B][F?7:1]));if(V!=null){var W=N?[F,V]:[V,F];(!k.length||C(W[0]-k[0][0])>m&&C(W[1]-k[0][1])>m)&&k.push(W)}if(E>2&&!k.length)break}if(k.length!==2)k=null;else if(!S){var H=R.getPart(l,k[0][0],k[1][0]),J=R.getPart(p,k[0][1],k[1][1]);(C(J[2]-H[2])>f||C(J[3]-H[3])>f||C(J[4]-H[4])>f||C(J[5]-H[5])>f)&&(k=null)}return k}function c(l,p){var y=l[0],C=l[1],v=l[2],m=l[3],f=l[4],g=l[5],w=l[6],S=l[7],x=p.normalize(),b=x.x,P=x.y,I=3*w-9*f+9*v-3*y,O=3*S-9*g+9*m-3*C,A=6*f-12*v+6*y,z=6*g-12*m+6*C,M=3*v-3*y,k=3*m-3*C,E=2*I*P-2*O*b,N=[];if(Math.abs(E)<et.CURVETIME_EPSILON){var B=I*k-O*M,E=I*z-O*A;if(E!=0){var F=-B/E;F>=0&&F<=1&&N.push(F)}}else{var V=(A*A-4*I*M)*P*P+(-2*A*z+4*O*M+4*I*k)*b*P+(z*z-4*O*k)*b*b,W=A*P-z*b;if(V>=0&&E!=0){var H=Math.sqrt(V),J=-(W+H)/E,q=(-W+H)/E;J>=0&&J<=1&&N.push(J),q>=0&&q<=1&&N.push(q)}}return N}return{getIntersections:function(l){var p=this.getValues(),y=l&&l!==this&&l.getValues();return y?o(p,y,this,l,[]):u(p,this,[])},statics:{getOverlaps:d,getIntersections:_,getCurveLineIntersections:s,getTimesWithTangent:c}}}),Tt=T.extend({_class:"CurveLocation",initialize:function(e,i,n,r,s){if(i>=.99999999){var h=e.getNext();h&&(i=0,e=h)}this._setCurve(e),this._time=i,this._point=n||e.getPointAtTime(i),this._overlap=r,this._distance=s,this._intersection=this._next=this._previous=null},_setPath:function(t){this._path=t,this._version=t?t._version:0},_setCurve:function(t){this._setPath(t._path),this._curve=t,this._segment=null,this._segment1=t._segment1,this._segment2=t._segment2},_setSegment:function(t){var e=t.getCurve();e?this._setCurve(e):(this._setPath(t._path),this._segment1=t,this._segment2=null),this._segment=t,this._time=t===this._segment1?0:1,this._point=t._point.clone()},getSegment:function(){var t=this._segment;if(!t){var e=this.getCurve(),i=this.getTime();i===0?t=e._segment1:i===1?t=e._segment2:i!=null&&(t=e.getPartLength(0,i)<e.getPartLength(i,1)?e._segment1:e._segment2),this._segment=t}return t},getCurve:function(){var t=this._path,e=this;t&&t._version!==this._version&&(this._time=this._offset=this._curveOffset=this._curve=null);function i(n){var r=n&&n.getCurve();if(r&&(e._time=r.getTimeOf(e._point))!=null)return e._setCurve(r),r}return this._curve||i(this._segment)||i(this._segment1)||i(this._segment2.getPrevious())},getPath:function(){var t=this.getCurve();return t&&t._path},getIndex:function(){var t=this.getCurve();return t&&t.getIndex()},getTime:function(){var t=this.getCurve(),e=this._time;return t&&e==null?this._time=t.getTimeOf(this._point):e},getParameter:"#getTime",getPoint:function(){return this._point},getOffset:function(){var t=this._offset;if(t==null){t=0;var e=this.getPath(),i=this.getIndex();if(e&&i!=null)for(var n=e.getCurves(),r=0;r<i;r++)t+=n[r].getLength();this._offset=t+=this.getCurveOffset()}return t},getCurveOffset:function(){var t=this._curveOffset;if(t==null){var e=this.getCurve(),i=this.getTime();this._curveOffset=t=i!=null&&e&&e.getPartLength(0,i)}return t},getIntersection:function(){return this._intersection},getDistance:function(){return this._distance},divide:function(){var t=this.getCurve(),e=t&&t.divideAtTime(this.getTime());return e&&this._setSegment(e._segment1),e},split:function(){var t=this.getCurve(),e=t._path,i=t&&t.splitAtTime(this.getTime());return i&&this._setSegment(e.getLastSegment()),i},equals:function(t,e){var i=this===t;if(!i&&t instanceof Tt){var n=this.getCurve(),r=t.getCurve(),s=n._path,h=r._path;if(s===h){var a=Math.abs,o=1e-7,u=a(this.getOffset()-t.getOffset()),_=!e&&this._intersection,d=!e&&t._intersection;i=(u<o||s&&a(s.getLength()-u)<o)&&(!_&&!d||_&&d&&_.equals(d,!0))}}return i},toString:function(){var t=[],e=this.getPoint(),i=bt.instance;e&&t.push("point: "+e);var n=this.getIndex();n!=null&&t.push("index: "+n);var r=this.getTime();return r!=null&&t.push("time: "+i.number(r)),this._distance!=null&&t.push("distance: "+i.number(this._distance)),"{ "+t.join(", ")+" }"},isTouching:function(){var t=this._intersection;if(t&&this.getTangent().isCollinear(t.getTangent())){var e=this.getCurve(),i=t.getCurve();return!(e.isStraight()&&i.isStraight()&&e.getLine().intersect(i.getLine()))}return!1},isCrossing:function(){var t=this._intersection;if(!t)return!1;var e=this.getTime(),i=t.getTime(),n=1e-8,r=1-n,s=e>=n&&e<=r,h=i>=n&&i<=r;if(s&&h)return!this.isTouching();var a=this.getCurve(),o=a&&e<n?a.getPrevious():a,u=t.getCurve(),_=u&&i<n?u.getPrevious():u;if(e>r&&(a=a.getNext()),i>r&&(u=u.getNext()),!o||!a||!_||!u)return!1;var d=[];function c(b,P){var I=b.getValues(),O=R.classify(I).roots||R.getPeaks(I),A=O.length,z=R.getLength(I,P&&A?O[A-1]:0,!P&&A?O[0]:1);d.push(A?z:z/32)}function l(b,P,I){return P<I?b>P&&b<I:b>P||b<I}s||(c(o,!0),c(a,!1)),h||(c(_,!0),c(u,!1));var p=this.getPoint(),y=Math.min.apply(Math,d),C=s?a.getTangentAtTime(e):a.getPointAt(y).subtract(p),v=s?C.negate():o.getPointAt(-y).subtract(p),m=h?u.getTangentAtTime(i):u.getPointAt(y).subtract(p),f=h?m.negate():_.getPointAt(-y).subtract(p),g=v.getAngle(),w=C.getAngle(),S=f.getAngle(),x=m.getAngle();return!!(s?l(g,S,x)^l(w,S,x)&&l(g,x,S)^l(w,x,S):l(S,g,w)^l(x,g,w)&&l(S,w,g)^l(x,w,g))},hasOverlap:function(){return!!this._overlap}},T.each(R._evaluateMethods,function(t){var e=t+"At";this[t]=function(){var i=this.getCurve(),n=this.getTime();return n!=null&&i&&i[e](n,!0)}},{preserve:!0}),new function(){function t(e,i,n){var r=e.length,s=0,h=r-1;function a(p,y){for(var C=p+y;C>=-1&&C<=r;C+=y){var v=e[(C%r+r)%r];if(!i.getPoint().isClose(v.getPoint(),1e-7))break;if(i.equals(v))return v}return null}for(;s<=h;){var o=s+h>>>1,u=e[o],_;if(n&&(_=i.equals(u)?u:a(o,-1)||a(o,1)))return i._overlap&&(_._overlap=_._intersection._overlap=!0),_;var d=i.getPath(),c=u.getPath(),l=d!==c?d._id-c._id:i.getIndex()+i.getTime()-(u.getIndex()+u.getTime());l<0?h=o-1:s=o+1}return e.splice(s,0,i),i}return{statics:{insert:t,expand:function(e){for(var i=e.slice(),n=e.length-1;n>=0;n--)t(i,e[n]._intersection,!1);return i}}}}),Zt=X.extend({_class:"PathItem",_selectBounds:!1,_canScaleStroke:!0,beans:!0,initialize:function(){},statics:{create:function(t){var e,i,n;if(T.isPlainObject(t)?(i=t.segments,e=t.pathData):Array.isArray(t)?i=t:typeof t=="string"&&(e=t),i){var r=i[0];n=r&&Array.isArray(r[0])}else e&&(n=(e.match(/m/gi)||[]).length>1||/z\s*\S+/i.test(e));var s=n?Mt:nt;return new s(t)}},_asPathItem:function(){return this},isClockwise:function(){return this.getArea()>=0},setClockwise:function(t){this.isClockwise()!=(t=!!t)&&this.reverse()},setPathData:function(t){var e=t&&t.match(/[mlhvcsqtaz][^mlhvcsqtaz]*/ig),i,n=!1,r,s,h=new L,a=new L;function o(f,g){var w=+i[f];return n&&(w+=h[g]),w}function u(f){return new L(o(f,"x"),o(f+1,"y"))}this.clear();for(var _=0,d=e&&e.length;_<d;_++){var c=e[_],l=c[0],p=l.toLowerCase();i=c.match(/[+-]?(?:\d*\.\d+|\d+\.?)(?:[eE][+-]?\d+)?/g);var y=i&&i.length;switch(n=l===p,r==="z"&&!/[mz]/.test(p)&&this.moveTo(h),p){case"m":case"l":for(var C=p==="m",v=0;v<y;v+=2)this[C?"moveTo":"lineTo"](h=u(v)),C&&(a=h,C=!1);s=h;break;case"h":case"v":var m=p==="h"?"x":"y";h=h.clone();for(var v=0;v<y;v++)h[m]=o(v,m),this.lineTo(h);s=h;break;case"c":for(var v=0;v<y;v+=6)this.cubicCurveTo(u(v),s=u(v+2),h=u(v+4));break;case"s":for(var v=0;v<y;v+=4)this.cubicCurveTo(/[cs]/.test(r)?h.multiply(2).subtract(s):h,s=u(v),h=u(v+2)),r=p;break;case"q":for(var v=0;v<y;v+=4)this.quadraticCurveTo(s=u(v),h=u(v+2));break;case"t":for(var v=0;v<y;v+=2)this.quadraticCurveTo(s=/[qt]/.test(r)?h.multiply(2).subtract(s):h,h=u(v)),r=p;break;case"a":for(var v=0;v<y;v+=7)this.arcTo(h=u(v+5),new Z(+i[v],+i[v+1]),+i[v+2],+i[v+4],+i[v+3]);break;case"z":this.closePath(1e-12),h=a;break}r=p}},_canComposite:function(){return!(this.hasFill()&&this.hasStroke())},_contains:function(t){var e=t.isInside(this.getBounds({internal:!0,handle:!0}))?this._getWinding(t):{};return e.onPath||!!(this.getFillRule()==="evenodd"?e.windingL&1||e.windingR&1:e.winding)},getIntersections:function(t,e,i,n){var r=this===t||!t,s=this._matrix._orNullIfIdentity(),h=r?s:(i||t._matrix)._orNullIfIdentity();return r||this.getBounds(s).intersects(t.getBounds(h),1e-12)?R.getIntersections(this.getCurves(),!r&&t.getCurves(),e,s,h,n):[]},getCrossings:function(t){return this.getIntersections(t,function(e){return e.isCrossing()})},getNearestLocation:function(){for(var t=L.read(arguments),e=this.getCurves(),i=1/0,n=null,r=0,s=e.length;r<s;r++){var h=e[r].getNearestLocation(t);h._distance<i&&(i=h._distance,n=h)}return n},getNearestPoint:function(){var t=this.getNearestLocation.apply(this,arguments);return t&&t.getPoint()},interpolate:function(t,e,i){var n=!this._children,r=n?"_segments":"_children",s=t[r],h=e[r],a=this[r];if(!s||!h||s.length!==h.length)throw new Error("Invalid operands in interpolate() call: "+t+", "+e);var o=a.length,u=h.length;if(o<u)for(var _=n?G:nt,d=o;d<u;d++)this.add(new _);else o>u&&this[n?"removeSegments":"removeChildren"](u,o);for(var d=0;d<u;d++)a[d].interpolate(s[d],h[d],i);n&&(this.setClosed(t._closed),this._changed(9))},compare:function(t){var e=!1;if(t){var i=this._children||[this],n=t._children?t._children.slice():[t],r=i.length,s=n.length,h=[],a=0;e=!0;for(var o=Yt.findItemBoundsCollisions(i,n,et.GEOMETRIC_EPSILON),u=r-1;u>=0&&e;u--){var _=i[u];e=!1;var d=o[u];if(d)for(var c=d.length-1;c>=0&&!e;c--)_.compare(n[d[c]])&&(h[d[c]]||(h[d[c]]=!0,a++),e=!0)}e=e&&a===s}return e}}),nt=Zt.extend({_class:"Path",_serializeFields:{segments:[],closed:!1},initialize:function(e){this._closed=!1,this._segments=[],this._version=0;var i=arguments,n=Array.isArray(e)?typeof e[0]=="object"?e:i:e&&e.size===D&&(e.x!==D||e.point!==D)?i:null;n&&n.length>0?this.setSegments(n):(this._curves=D,this._segmentSelection=0,!n&&typeof e=="string"&&(this.setPathData(e),e=null)),this._initialize(!n&&e)},_equals:function(t){return this._closed===t._closed&&T.equals(this._segments,t._segments)},copyContent:function(t){this.setSegments(t._segments),this._closed=t._closed},_changed:function t(e){if(t.base.call(this,e),e&8){if(this._length=this._area=D,e&32)this._version++;else if(this._curves)for(var i=0,n=this._curves.length;i<n;i++)this._curves[i]._changed()}else e&64&&(this._bounds=D)},getStyle:function(){var t=this._parent;return(t instanceof Mt?t:this)._style},getSegments:function(){return this._segments},setSegments:function(t){var e=this.isFullySelected(),i=t&&t.length;if(this._segments.length=0,this._segmentSelection=0,this._curves=D,i){var n=t[i-1];typeof n=="boolean"&&(this.setClosed(n),i--),this._add(G.readList(t,0,{},i))}e&&this.setFullySelected(!0)},getFirstSegment:function(){return this._segments[0]},getLastSegment:function(){return this._segments[this._segments.length-1]},getCurves:function(){var t=this._curves,e=this._segments;if(!t){var i=this._countCurves();t=this._curves=new Array(i);for(var n=0;n<i;n++)t[n]=new R(this,e[n],e[n+1]||e[0])}return t},getFirstCurve:function(){return this.getCurves()[0]},getLastCurve:function(){var t=this.getCurves();return t[t.length-1]},isClosed:function(){return this._closed},setClosed:function(t){if(this._closed!=(t=!!t)){if(this._closed=t,this._curves){var e=this._curves.length=this._countCurves();t&&(this._curves[e-1]=new R(this,this._segments[e-1],this._segments[0]))}this._changed(41)}}},{beans:!0,getPathData:function(t,e){var i=this._segments,n=i.length,r=new bt(e),s=new Array(6),h=!0,a,o,u,_,d,c,l,p,y=[];function C(m,f){if(m._transformCoordinates(t,s),a=s[0],o=s[1],h)y.push("M"+r.pair(a,o)),h=!1;else if(d=s[2],c=s[3],d===a&&c===o&&l===u&&p===_){if(!f){var g=a-u,w=o-_;y.push(g===0?"v"+r.number(w):w===0?"h"+r.number(g):"l"+r.pair(g,w))}}else y.push("c"+r.pair(l-u,p-_)+" "+r.pair(d-u,c-_)+" "+r.pair(a-u,o-_));u=a,_=o,l=s[4],p=s[5]}if(!n)return"";for(var v=0;v<n;v++)C(i[v]);return this._closed&&n>0&&(C(i[0],!0),y.push("z")),y.join("")},isEmpty:function(){return!this._segments.length},_transformContent:function(t){for(var e=this._segments,i=new Array(6),n=0,r=e.length;n<r;n++)e[n]._transformCoordinates(t,i,!0);return!0},_add:function(t,h){for(var i=this._segments,n=this._curves,r=t.length,s=h==null,h=s?i.length:h,a=0;a<r;a++){var o=t[a];o._path&&(o=t[a]=o.clone()),o._path=this,o._index=h+a,o._selection&&this._updateSelection(o,0,o._selection)}if(s)T.push(i,t);else{i.splice.apply(i,[h,0].concat(t));for(var a=h+r,u=i.length;a<u;a++)i[a]._index=a}if(n){var _=this._countCurves(),d=h>0&&h+r-1===_?h-1:h,c=d,l=Math.min(d+r,_);t._curves&&(n.splice.apply(n,[d,0].concat(t._curves)),c+=t._curves.length);for(var a=c;a<l;a++)n.splice(a,0,new R(this,null,null));this._adjustCurves(d,l)}return this._changed(41),t},_adjustCurves:function(t,e){for(var i=this._segments,n=this._curves,r,s=t;s<e;s++)r=n[s],r._path=this,r._segment1=i[s],r._segment2=i[s+1]||i[0],r._changed();(r=n[this._closed&&!t?i.length-1:t-1])&&(r._segment2=i[t]||i[0],r._changed()),(r=n[e])&&(r._segment1=i[e],r._changed())},_countCurves:function(){var t=this._segments.length;return!this._closed&&t>0?t-1:t},add:function(t){var e=arguments;return e.length>1&&typeof t!="number"?this._add(G.readList(e)):this._add([G.read(e)])[0]},insert:function(t,e){var i=arguments;return i.length>2&&typeof e!="number"?this._add(G.readList(i,1),t):this._add([G.read(i,1)],t)[0]},addSegment:function(){return this._add([G.read(arguments)])[0]},insertSegment:function(t){return this._add([G.read(arguments,1)],t)[0]},addSegments:function(t){return this._add(G.readList(t))},insertSegments:function(t,e){return this._add(G.readList(e),t)},removeSegment:function(t){return this.removeSegments(t,t+1)[0]||null},removeSegments:function(t,e,i){t=t||0,e=T.pick(e,this._segments.length);var n=this._segments,r=this._curves,s=n.length,h=n.splice(t,e-t),a=h.length;if(!a)return h;for(var o=0;o<a;o++){var u=h[o];u._selection&&this._updateSelection(u,u._selection,0),u._index=u._path=null}for(var o=t,_=n.length;o<_;o++)n[o]._index=o;if(r){for(var d=t>0&&e===s+(this._closed?1:0)?t-1:t,r=r.splice(d,a),o=r.length-1;o>=0;o--)r[o]._path=null;i&&(h._curves=r.slice(1)),this._adjustCurves(d,d)}return this._changed(41),h},clear:"#removeSegments",hasHandles:function(){for(var t=this._segments,e=0,i=t.length;e<i;e++)if(t[e].hasHandles())return!0;return!1},clearHandles:function(){for(var t=this._segments,e=0,i=t.length;e<i;e++)t[e].clearHandles()},getLength:function(){if(this._length==null){for(var t=this.getCurves(),e=0,i=0,n=t.length;i<n;i++)e+=t[i].getLength();this._length=e}return this._length},getArea:function(){var t=this._area;if(t==null){var e=this._segments,i=this._closed;t=0;for(var n=0,r=e.length;n<r;n++){var s=n+1===r;t+=R.getArea(R.getValues(e[n],e[s?0:n+1],null,s&&!i))}this._area=t}return t},isFullySelected:function(){var t=this._segments.length;return this.isSelected()&&t>0&&this._segmentSelection===t*7},setFullySelected:function(t){t&&this._selectSegments(!0),this.setSelected(t)},setSelection:function t(e){e&1||this._selectSegments(!1),t.base.call(this,e)},_selectSegments:function(t){var e=this._segments,i=e.length,n=t?7:0;this._segmentSelection=n*i;for(var r=0;r<i;r++)e[r]._selection=n},_updateSelection:function(t,e,i){t._selection=i;var n=this._segmentSelection+=i-e;n>0&&this.setSelected(!0)},divideAt:function(t){var e=this.getLocationAt(t),i;return e&&(i=e.getCurve().divideAt(e.getCurveOffset()))?i._segment1:null},splitAt:function(t){var e=this.getLocationAt(t),i=e&&e.index,n=e&&e.time,r=1e-8,s=1-r;n>s&&(i++,n=0);var h=this.getCurves();if(i>=0&&i<h.length){n>=r&&h[i++].divideAtTime(n);var a=this.removeSegments(i,this._segments.length,!0),o;return this._closed?(this.setClosed(!1),o=this):(o=new nt(X.NO_INSERT),o.insertAbove(this),o.copyAttributes(this)),o._add(a,0),this.addSegment(a[0]),o}return null},split:function(t,e){var i,n=e===D?t:(i=this.getCurves()[t])&&i.getLocationAtTime(e);return n!=null?this.splitAt(n):null},join:function(t,e){var i=e||0;if(t&&t!==this){var n=t._segments,r=this.getLastSegment(),s=t.getLastSegment();if(!s)return this;r&&r._point.isClose(s._point,i)&&t.reverse();var h=t.getFirstSegment();if(r&&r._point.isClose(h._point,i))r.setHandleOut(h._handleOut),this._add(n.slice(1));else{var a=this.getFirstSegment();a&&a._point.isClose(h._point,i)&&t.reverse(),s=t.getLastSegment(),a&&a._point.isClose(s._point,i)?(a.setHandleIn(s._handleIn),this._add(n.slice(0,n.length-1),0)):this._add(n.slice())}t._closed&&this._add([n[0]]),t.remove()}var o=this.getFirstSegment(),u=this.getLastSegment();return o!==u&&o._point.isClose(u._point,i)&&(o.setHandleIn(u._handleIn),u.remove(),this.setClosed(!0)),this},reduce:function(t){for(var e=this.getCurves(),i=t&&t.simplify,n=i?1e-7:0,r=e.length-1;r>=0;r--){var s=e[r];!s.hasHandles()&&(!s.hasLength(n)||i&&s.isCollinear(s.getNext()))&&s.remove()}return this},reverse:function(){this._segments.reverse();for(var t=0,e=this._segments.length;t<e;t++){var i=this._segments[t],n=i._handleIn;i._handleIn=i._handleOut,i._handleOut=n,i._index=t}this._curves=null,this._changed(9)},flatten:function(t){for(var e=new ue(this,t||.25,256,!0),i=e.parts,n=i.length,r=[],s=0;s<n;s++)r.push(new G(i[s].curve.slice(0,2)));!this._closed&&n>0&&r.push(new G(i[n-1].curve.slice(6))),this.setSegments(r)},simplify:function(t){var e=new _e(this).fit(t||2.5);return e&&this.setSegments(e),!!e},smooth:function(t){var e=this,i=t||{},n=i.type||"asymmetric",r=this._segments,s=r.length,h=this._closed;function a(U,rt){var Y=U&&U.index;if(Y!=null){var tt=U.path;if(tt&&tt!==e)throw new Error(U._class+" "+Y+" of "+tt+" is not part of "+e);rt&&U instanceof R&&Y++}else Y=typeof U=="number"?U:rt;return Math.min(Y<0&&h?Y%s:Y<0?Y+s:Y,s-1)}var o=h&&i.from===D&&i.to===D,u=a(i.from,0),_=a(i.to,s-1);if(u>_)if(h)u-=s;else{var d=u;u=_,_=d}if(/^(?:asymmetric|continuous)$/.test(n)){var c=n==="asymmetric",l=Math.min,p=_-u+1,y=p-1,C=o?l(p,4):1,v=C,m=C,f=[];if(h||(v=l(1,u),m=l(1,s-_-1)),y+=v+m,y<=1)return;for(var g=0,w=u-v;g<=y;g++,w++)f[g]=r[(w<0?w+s:w)%s]._point;for(var S=f[0]._x+2*f[1]._x,x=f[0]._y+2*f[1]._y,b=2,P=y-1,I=[S],O=[x],A=[b],z=[],M=[],g=1;g<y;g++){var k=g<P,E=k||c?1:2,N=k?4:c?2:7,B=k?4:c?3:8,F=k?2:c?0:1,V=E/b;b=A[g]=N-V,S=I[g]=B*f[g]._x+F*f[g+1]._x-V*S,x=O[g]=B*f[g]._y+F*f[g+1]._y-V*x}z[P]=I[P]/A[P],M[P]=O[P]/A[P];for(var g=y-2;g>=0;g--)z[g]=(I[g]-z[g+1])/A[g],M[g]=(O[g]-M[g+1])/A[g];z[y]=(3*f[y]._x-z[P])/2,M[y]=(3*f[y]._y-M[P])/2;for(var g=v,W=y-m,w=u;g<=W;g++,w++){var H=r[w<0?w+s:w],J=H._point,q=z[g]-J._x,$=M[g]-J._y;(o||g<W)&&H.setHandleOut(q,$),(o||g>v)&&H.setHandleIn(-q,-$)}}else for(var g=u;g<=_;g++)r[g<0?g+s:g].smooth(i,!o&&g===u,!o&&g===_)},toShape:function(t){if(!this._closed)return null;var e=this._segments,i,n,r,s;function h(c,l){var p=e[c],y=p.getNext(),C=e[l],v=C.getNext();return p._handleOut.isZero()&&y._handleIn.isZero()&&C._handleOut.isZero()&&v._handleIn.isZero()&&y._point.subtract(p._point).isCollinear(v._point.subtract(C._point))}function a(c){var l=e[c],p=l.getPrevious(),y=l.getNext();return p._handleOut.isZero()&&l._handleIn.isZero()&&l._handleOut.isZero()&&y._handleIn.isZero()&&l._point.subtract(p._point).isOrthogonal(y._point.subtract(l._point))}function o(c){var l=e[c],p=l.getNext(),y=l._handleOut,C=p._handleIn,v=.5522847498307936;if(y.isOrthogonal(C)){var m=l._point,f=p._point,g=new pt(m,y,!0).intersect(new pt(f,C,!0),!0);return g&&et.isZero(y.getLength()/g.subtract(m).getLength()-v)&&et.isZero(C.getLength()/g.subtract(f).getLength()-v)}return!1}function u(c,l){return e[c]._point.getDistance(e[l]._point)}if(!this.hasHandles()&&e.length===4&&h(0,2)&&h(1,3)&&a(1)?(i=xt.Rectangle,n=new Z(u(0,3),u(0,1)),s=e[1]._point.add(e[2]._point).divide(2)):e.length===8&&o(0)&&o(2)&&o(4)&&o(6)&&h(1,5)&&h(3,7)?(i=xt.Rectangle,n=new Z(u(1,6),u(0,3)),r=n.subtract(new Z(u(0,7),u(1,2))).divide(2),s=e[3]._point.add(e[4]._point).divide(2)):e.length===4&&o(0)&&o(1)&&o(2)&&o(3)&&(et.isZero(u(0,2)-u(1,3))?(i=xt.Circle,r=u(0,2)/2):(i=xt.Ellipse,r=new Z(u(2,0)/2,u(3,1)/2)),s=e[1]._point),i){var _=this.getPosition(!0),d=new i({center:_,size:n,radius:r,insert:!1});return d.copyAttributes(this,!0),d._matrix.prepend(this._matrix),d.rotate(s.subtract(_).getAngle()+90),(t===D||t)&&d.insertAbove(this),d}return null},toPath:"#clone",compare:function t(e){if(!e||e instanceof Mt)return t.base.call(this,e);var i=this.getCurves(),n=e.getCurves(),r=i.length,s=n.length;if(!r||!s)return r==s;for(var h=i[0].getValues(),a=[],o=0,u,_=0,d,c=0;c<s;c++){var C=n[c].getValues();a.push(C);var l=R.getOverlaps(h,C);if(l){u=!c&&l[0][0]>0?s-1:c,d=l[0][1];break}}for(var p=Math.abs,y=1e-8,C=a[u],v;h&&C;){var l=R.getOverlaps(h,C);if(l){var m=l[0][0];if(p(m-_)<y){_=l[1][0],_===1&&(h=++o<r?i[o].getValues():null,_=0);var f=l[0][1];if(p(f-d)<y){if(v||(v=[u,f]),d=l[1][1],d===1&&(++u>=s&&(u=0),C=a[u]||n[u].getValues(),d=0),!h)return v[0]===u&&v[1]===d;continue}}}break}return!1},_hitTestSelf:function(t,e,i,n){var r=this,s=this.getStyle(),h=this._segments,a=h.length,o=this._closed,u=e._tolerancePadding,_=u,d,c,l,p,y,C,v=e.stroke&&s.hasStroke(),m=e.fill&&s.hasFill(),f=e.curves,g=v?s.getStrokeWidth()/2:m&&e.tolerance>0||f?0:null;g!==null&&(g>0?(d=s.getStrokeJoin(),c=s.getStrokeCap(),l=s.getMiterLimit(),_=_.add(nt._getStrokePadding(g,n))):d=c="round");function w(z,M){return t.subtract(z).divide(M).length<=1}function S(z,M,k){if(!e.selected||M.isSelected()){var E=z._point;if(M!==E&&(M=M.add(E)),w(M,_))return new Lt(k,r,{segment:z,point:M})}}function x(z,M){return(M||e.segments)&&S(z,z._point,"segment")||!M&&e.handles&&(S(z,z._handleIn,"handle-in")||S(z,z._handleOut,"handle-out"))}function b(z){p.add(z)}function P(z){var M=o||z._index>0&&z._index<a-1;if((M?d:c)==="round")return w(z._point,_);if(p=new nt({internal:!0,closed:!0}),M?z.isSmooth()||nt._addBevelJoin(z,d,g,l,null,n,b,!0):c==="square"&&nt._addSquareCap(z,c,g,null,n,b,!0),!p.isEmpty()){var k;return p.contains(t)||(k=p.getNearestLocation(t))&&w(k.getPoint(),u)}}if(e.ends&&!e.segments&&!o){if(C=x(h[0],!0)||x(h[a-1],!0))return C}else if(e.segments||e.handles){for(var I=0;I<a;I++)if(C=x(h[I]))return C}if(g!==null){if(y=this.getNearestLocation(t),y){var O=y.getTime();O===0||O===1&&a>1?P(y.getSegment())||(y=null):w(y.getPoint(),_)||(y=null)}if(!y&&d==="miter"&&a>1)for(var I=0;I<a;I++){var A=h[I];if(t.getDistance(A._point)<=l*g&&P(A)){y=A.getLocation();break}}}return!y&&m&&this._contains(t)||y&&!v&&!f?new Lt("fill",this):y?new Lt(v?"stroke":"curve",this,{location:y,point:y.getPoint()}):null}},T.each(R._evaluateMethods,function(t){this[t+"At"]=function(e){var i=this.getLocationAt(e);return i&&i[t]()}},{beans:!1,getLocationOf:function(){for(var t=L.read(arguments),e=this.getCurves(),i=0,n=e.length;i<n;i++){var r=e[i].getLocationOf(t);if(r)return r}return null},getOffsetOf:function(){var t=this.getLocationOf.apply(this,arguments);return t?t.getOffset():null},getLocationAt:function(t){if(typeof t=="number"){for(var e=this.getCurves(),i=0,n=0,r=e.length;n<r;n++){var s=i,h=e[n];if(i+=h.getLength(),i>t)return h.getLocationAt(t-s)}if(e.length>0&&t<=this.getLength())return new Tt(e[e.length-1],1)}else if(t&&t.getPath&&t.getPath()===this)return t;return null},getOffsetsWithTangent:function(){var t=L.read(arguments);if(t.isZero())return[];for(var e=[],i=0,n=this.getCurves(),r=0,s=n.length;r<s;r++){for(var h=n[r],a=h.getTimesWithTangent(t),o=0,u=a.length;o<u;o++){var _=i+h.getOffsetAtTime(a[o]);e.indexOf(_)<0&&e.push(_)}i+=h.length}return e}}),new function(){function t(i,n,r,s){if(s<=0)return;var h=s/2,a=s-2,o=h-1,u=new Array(6),_,d;function c(m){var f=u[m],g=u[m+1];(_!=f||d!=g)&&(i.beginPath(),i.moveTo(_,d),i.lineTo(f,g),i.stroke(),i.beginPath(),i.arc(f,g,h,0,Math.PI*2,!0),i.fill())}for(var l=0,p=n.length;l<p;l++){var y=n[l],C=y._selection;if(y._transformCoordinates(r,u),_=u[0],d=u[1],C&2&&c(2),C&4&&c(4),i.fillRect(_-h,d-h,s,s),a>0&&!(C&1)){var v=i.fillStyle;i.fillStyle="#ffffff",i.fillRect(_-o,d-o,a,a),i.fillStyle=v}}}function e(i,n,r){var s=n._segments,h=s.length,a=new Array(6),o=!0,u,_,d,c,l,p,y,C;function v(f){if(r)f._transformCoordinates(r,a),u=a[0],_=a[1];else{var g=f._point;u=g._x,_=g._y}if(o)i.moveTo(u,_),o=!1;else{if(r)l=a[2],p=a[3];else{var w=f._handleIn;l=u+w._x,p=_+w._y}l===u&&p===_&&y===d&&C===c?i.lineTo(u,_):i.bezierCurveTo(y,C,l,p,u,_)}if(d=u,c=_,r)y=a[4],C=a[5];else{var w=f._handleOut;y=d+w._x,C=c+w._y}}for(var m=0;m<h;m++)v(s[m]);n._closed&&h>0&&v(s[0])}return{_draw:function(i,n,r,s){var h=n.dontStart,a=n.dontFinish||n.clip,o=this.getStyle(),u=o.hasFill(),_=o.hasStroke(),d=o.getDashArray(),c=!Q.support.nativeDash&&_&&d&&d.length;h||i.beginPath(),(u||_&&!c||a)&&(e(i,this,s),this._closed&&i.closePath());function l(f){return d[(f%c+c)%c]}if(!a&&(u||_)&&(this._setStyles(i,n,r),u&&(i.fill(o.getFillRule()),i.shadowColor="rgba(0,0,0,0)"),_)){if(c){h||i.beginPath();for(var p=new ue(this,.25,32,!1,s),y=p.length,C=-o.getDashOffset(),v,m=0;C>0;)C-=l(m--)+l(m--);for(;C<y;)v=C+l(m++),(C>0||v>0)&&p.drawPart(i,Math.max(C,0),Math.max(v,0)),C=v+l(m++)}i.stroke()}},_drawSelected:function(i,n){i.beginPath(),e(i,this,n),i.stroke(),t(i,this._segments,n,Q.settings.handleSize)}}},new function(){function t(e){var i=e._segments;if(!i.length)throw new Error("Use a moveTo() command first");return i[i.length-1]}return{moveTo:function(){var e=this._segments;e.length===1&&this.removeSegment(0),e.length||this._add([new G(L.read(arguments))])},moveBy:function(){throw new Error("moveBy() is unsupported on Path items.")},lineTo:function(){this._add([new G(L.read(arguments))])},cubicCurveTo:function(){var e=arguments,i=L.read(e),n=L.read(e),r=L.read(e),s=t(this);s.setHandleOut(i.subtract(s._point)),this._add([new G(r,n.subtract(r))])},quadraticCurveTo:function(){var e=arguments,i=L.read(e),n=L.read(e),r=t(this)._point;this.cubicCurveTo(i.add(r.subtract(i).multiply(1/3)),i.add(n.subtract(i).multiply(1/3)),n)},curveTo:function(){var e=arguments,i=L.read(e),n=L.read(e),r=T.pick(T.read(e),.5),s=1-r,h=t(this)._point,a=i.subtract(h.multiply(s*s)).subtract(n.multiply(r*r)).divide(2*r*s);if(a.isNaN())throw new Error("Cannot put a curve through points with parameter = "+r);this.quadraticCurveTo(a,n)},arcTo:function(){var e=arguments,i=Math.abs,n=Math.sqrt,r=t(this),s=r._point,h=L.read(e),a,o=T.peek(e),u=T.pick(o,!0),_,d,c,l;if(typeof u=="boolean")var p=s.add(h).divide(2),a=p.add(p.subtract(s).rotate(u?-90:90));else if(T.remain(e)<=2)a=h,h=L.read(e);else if(!s.equals(h)){var y=Z.read(e),C=et.isZero;if(C(y.width)||C(y.height))return this.lineTo(h);var v=T.read(e),u=!!T.read(e),m=!!T.read(e),p=s.add(h).divide(2),f=s.subtract(p).rotate(-v),g=f.x,w=f.y,S=i(y.width),x=i(y.height),b=S*S,P=x*x,I=g*g,O=w*w,A=n(I/b+O/P);if(A>1&&(S*=A,x*=A,b=S*S,P=x*x),A=(b*P-b*O-P*I)/(b*O+P*I),i(A)<1e-12&&(A=0),A<0)throw new Error("Cannot create an arc with the given arguments");_=new L(S*w/x,-x*g/S).multiply((m===u?-1:1)*n(A)).rotate(v).add(p),l=new st().translate(_).rotate(v).scale(S,x),c=l._inverseTransform(s),d=c.getDirectedAngle(l._inverseTransform(h)),!u&&d>0?d-=360:u&&d<0&&(d+=360)}if(a){var z=new pt(s.add(a).divide(2),a.subtract(s).rotate(90),!0),M=new pt(a.add(h).divide(2),h.subtract(a).rotate(90),!0),k=new pt(s,h),E=k.getSide(a);if(_=z.intersect(M,!0),!_){if(!E)return this.lineTo(h);throw new Error("Cannot create an arc with the given arguments")}c=s.subtract(_),d=c.getDirectedAngle(h.subtract(_));var N=k.getSide(_,!0);N===0?d=E*i(d):E===N&&(d+=d<0?360:-360)}if(d){for(var B=1e-5,F=i(d),V=F>=360?4:Math.ceil((F-B)/90),W=d/V,H=W*Math.PI/360,J=4/3*Math.sin(H)/(1+Math.cos(H)),q=[],$=0;$<=V;$++){var f=h,U=null;if($<V&&(U=c.rotate(90).multiply(J),l?(f=l._transformPoint(c),U=l._transformPoint(c.add(U)).subtract(f)):f=_.add(c)),!$)r.setHandleOut(U);else{var rt=c.rotate(-90).multiply(J);l&&(rt=l._transformPoint(c.add(rt)).subtract(f)),q.push(new G(f,rt,U))}c=c.rotate(W)}this._add(q)}},lineBy:function(){var e=L.read(arguments),i=t(this)._point;this.lineTo(i.add(e))},curveBy:function(){var e=arguments,i=L.read(e),n=L.read(e),r=T.read(e),s=t(this)._point;this.curveTo(s.add(i),s.add(n),r)},cubicCurveBy:function(){var e=arguments,i=L.read(e),n=L.read(e),r=L.read(e),s=t(this)._point;this.cubicCurveTo(s.add(i),s.add(n),s.add(r))},quadraticCurveBy:function(){var e=arguments,i=L.read(e),n=L.read(e),r=t(this)._point;this.quadraticCurveTo(r.add(i),r.add(n))},arcBy:function(){var e=arguments,i=t(this)._point,n=i.add(L.read(e)),r=T.pick(T.peek(e),!0);typeof r=="boolean"?this.arcTo(n,r):this.arcTo(n,i.add(L.read(e)))},closePath:function(e){this.setClosed(!0),this.join(this,e)}}},{_getBounds:function(t,e){var i=e.handle?"getHandleBounds":e.stroke?"getStrokeBounds":"getBounds";return nt[i](this._segments,this._closed,this,t,e)},statics:{getBounds:function(t,e,i,n,r,s){var h=t[0];if(!h)return new K;var a=new Array(6),o=h._transformCoordinates(n,new Array(6)),u=o.slice(0,2),_=u.slice(),d=new Array(2);function c(y){y._transformCoordinates(n,a);for(var C=0;C<2;C++)R._addBounds(o[C],o[C+4],a[C+2],a[C],C,s?s[C]:0,u,_,d);var v=o;o=a,a=v}for(var l=1,p=t.length;l<p;l++)c(t[l]);return e&&c(h),new K(u[0],u[1],_[0]-u[0],_[1]-u[1])},getStrokeBounds:function(t,e,i,n,r){var s=i.getStyle(),h=s.hasStroke(),a=s.getStrokeWidth(),o=h&&i._getStrokeMatrix(n,r),u=h&&nt._getStrokePadding(a,o),_=nt.getBounds(t,e,i,n,r,u);if(!h)return _;var d=a/2,c=s.getStrokeJoin(),l=s.getStrokeCap(),p=s.getMiterLimit(),y=new K(new Z(u));function C(S){_=_.include(S)}function v(S){_=_.unite(y.setCenter(S._point.transform(n)))}function m(S,x){x==="round"||S.isSmooth()?v(S):nt._addBevelJoin(S,x,d,p,n,o,C)}function f(S,x){x==="round"?v(S):nt._addSquareCap(S,x,d,n,o,C)}var g=t.length-(e?0:1);if(g>0){for(var w=1;w<g;w++)m(t[w],c);e?m(t[0],c):(f(t[0],l),f(t[t.length-1],l))}return _},_getStrokePadding:function(t,e){if(!e)return[t,t];var i=new L(t,0).transform(e),n=new L(0,t).transform(e),r=i.getAngleInRadians(),s=i.getLength(),h=n.getLength(),a=Math.sin(r),o=Math.cos(r),u=Math.tan(r),_=Math.atan2(h*u,s),d=Math.atan2(h,u*s);return[Math.abs(s*Math.cos(_)*o+h*Math.sin(_)*a),Math.abs(h*Math.sin(d)*o+s*Math.cos(d)*a)]},_addBevelJoin:function(t,e,i,n,r,s,h,a){var o=t.getCurve(),u=o.getPrevious(),_=o.getPoint1().transform(r),d=u.getNormalAtTime(1).multiply(i).transform(s),c=o.getNormalAtTime(0).multiply(i).transform(s),l=d.getDirectedAngle(c);if((l<0||l>=180)&&(d=d.negate(),c=c.negate()),a&&h(_),h(_.add(d)),e==="miter"){var p=new pt(_.add(d),new L(-d.y,d.x),!0).intersect(new pt(_.add(c),new L(-c.y,c.x),!0),!0);p&&_.getDistance(p)<=n*i&&h(p)}h(_.add(c))},_addSquareCap:function(t,e,i,n,r,s,h){var a=t._point.transform(n),o=t.getLocation(),u=o.getNormal().multiply(o.getTime()===0?i:-i).transform(r);e==="square"&&(h&&(s(a.subtract(u)),s(a.add(u))),a=a.add(u.rotate(-90))),s(a.add(u)),s(a.subtract(u))},getHandleBounds:function(t,e,i,n,r){var s=i.getStyle(),h=r.stroke&&s.hasStroke(),a,o;if(h){var u=i._getStrokeMatrix(n,r),_=s.getStrokeWidth()/2,d=_;s.getStrokeJoin()==="miter"&&(d=_*s.getMiterLimit()),s.getStrokeCap()==="square"&&(d=Math.max(d,_*Math.SQRT2)),a=nt._getStrokePadding(_,u),o=nt._getStrokePadding(d,u)}for(var c=new Array(6),l=1/0,p=-l,y=l,C=p,v=0,m=t.length;v<m;v++){var f=t[v];f._transformCoordinates(n,c);for(var g=0;g<6;g+=2){var w=g?a:o,S=w?w[0]:0,x=w?w[1]:0,b=c[g],P=c[g+1],I=b-S,O=b+S,A=P-x,z=P+x;I<l&&(l=I),O>p&&(p=O),A<y&&(y=A),z>C&&(C=z)}}return new K(l,y,p-l,C-y)}}});nt.inject({statics:new function(){var t=.5522847498307936,e=[new G([-1,0],[0,t],[0,-t]),new G([0,-1],[-t,0],[t,0]),new G([1,0],[0,-t],[0,t]),new G([0,1],[t,0],[-t,0])];function i(r,s,h){var a=T.getNamed(h),o=new nt(a&&(a.insert==!0?X.INSERT:a.insert==!1?X.NO_INSERT:null));return o._add(r),o._closed=s,o.set(a,X.INSERT)}function n(r,s,h){for(var a=new Array(4),o=0;o<4;o++){var u=e[o];a[o]=new G(u._point.multiply(s).add(r),u._handleIn.multiply(s),u._handleOut.multiply(s))}return i(a,!0,h)}return{Line:function(){var r=arguments;return i([new G(L.readNamed(r,"from")),new G(L.readNamed(r,"to"))],!1,r)},Circle:function(){var r=arguments,s=L.readNamed(r,"center"),h=T.readNamed(r,"radius");return n(s,new Z(h),r)},Rectangle:function(){var r=arguments,s=K.readNamed(r,"rectangle"),h=Z.readNamed(r,"radius",0,{readNull:!0}),a=s.getBottomLeft(!0),o=s.getTopLeft(!0),u=s.getTopRight(!0),_=s.getBottomRight(!0),d;if(!h||h.isZero())d=[new G(a),new G(o),new G(u),new G(_)];else{h=Z.min(h,s.getSize(!0).divide(2));var c=h.width,l=h.height,p=c*t,y=l*t;d=[new G(a.add(c,0),null,[-p,0]),new G(a.subtract(0,l),[0,y]),new G(o.add(0,l),null,[0,-y]),new G(o.add(c,0),[-p,0],null),new G(u.subtract(c,0),null,[p,0]),new G(u.add(0,l),[0,-y],null),new G(_.subtract(0,l),null,[0,y]),new G(_.subtract(c,0),[p,0])]}return i(d,!0,r)},RoundRectangle:"#Rectangle",Ellipse:function(){var r=arguments,s=xt._readEllipse(r);return n(s.center,s.radius,r)},Oval:"#Ellipse",Arc:function(){var r=arguments,s=L.readNamed(r,"from"),h=L.readNamed(r,"through"),a=L.readNamed(r,"to"),o=T.getNamed(r),u=new nt(o&&o.insert==!1&&X.NO_INSERT);return u.moveTo(s),u.arcTo(h,a),u.set(o)},RegularPolygon:function(){for(var r=arguments,s=L.readNamed(r,"center"),h=T.readNamed(r,"sides"),a=T.readNamed(r,"radius"),o=360/h,u=h%3===0,_=new L(0,u?-a:a),d=u?-1:.5,c=new Array(h),l=0;l<h;l++)c[l]=new G(s.add(_.rotate((l+d)*o)));return i(c,!0,r)},Star:function(){for(var r=arguments,s=L.readNamed(r,"center"),h=T.readNamed(r,"points")*2,a=T.readNamed(r,"radius1"),o=T.readNamed(r,"radius2"),u=360/h,_=new L(0,-1),d=new Array(h),c=0;c<h;c++)d[c]=new G(s.add(_.rotate(u*c).multiply(c%2?o:a)));return i(d,!0,r)}}}});var Mt=Zt.extend({_class:"CompoundPath",_serializeFields:{children:[]},beans:!0,initialize:function(e){this._children=[],this._namedChildren={},this._initialize(e)||(typeof e=="string"?this.setPathData(e):this.addChildren(Array.isArray(e)?e:arguments))},insertChildren:function t(e,i){var n=i,r=n[0];r&&typeof r[0]=="number"&&(n=[n]);for(var s=i.length-1;s>=0;s--){var h=n[s];n===i&&!(h instanceof nt)&&(n=T.slice(n)),Array.isArray(h)?n[s]=new nt({segments:h,insert:!1}):h instanceof Mt&&(n.splice.apply(n,[s,1].concat(h.removeChildren())),h.remove())}return t.base.call(this,e,n)},reduce:function t(e){for(var i=this._children,n=i.length-1;n>=0;n--){var r=i[n].reduce(e);r.isEmpty()&&r.remove()}if(!i.length){var r=new nt(X.NO_INSERT);return r.copyAttributes(this),r.insertAbove(this),this.remove(),r}return t.base.call(this)},isClosed:function(){for(var t=this._children,e=0,i=t.length;e<i;e++)if(!t[e]._closed)return!1;return!0},setClosed:function(t){for(var e=this._children,i=0,n=e.length;i<n;i++)e[i].setClosed(t)},getFirstSegment:function(){var t=this.getFirstChild();return t&&t.getFirstSegment()},getLastSegment:function(){var t=this.getLastChild();return t&&t.getLastSegment()},getCurves:function(){for(var t=this._children,e=[],i=0,n=t.length;i<n;i++)T.push(e,t[i].getCurves());return e},getFirstCurve:function(){var t=this.getFirstChild();return t&&t.getFirstCurve()},getLastCurve:function(){var t=this.getLastChild();return t&&t.getLastCurve()},getArea:function(){for(var t=this._children,e=0,i=0,n=t.length;i<n;i++)e+=t[i].getArea();return e},getLength:function(){for(var t=this._children,e=0,i=0,n=t.length;i<n;i++)e+=t[i].getLength();return e},getPathData:function(t,e){for(var i=this._children,n=[],r=0,s=i.length;r<s;r++){var h=i[r],a=h._matrix;n.push(h.getPathData(t&&!a.isIdentity()?t.appended(a):t,e))}return n.join("")},_hitTestChildren:function t(e,i,n){return t.base.call(this,e,i.class===nt||i.type==="path"?i:T.set({},i,{fill:!1}),n)},_draw:function(t,e,i,n){var r=this._children;if(r.length){e=e.extend({dontStart:!0,dontFinish:!0}),t.beginPath();for(var s=0,h=r.length;s<h;s++)r[s].draw(t,e,n);if(!e.clip){this._setStyles(t,e,i);var a=this._style;a.hasFill()&&(t.fill(a.getFillRule()),t.shadowColor="rgba(0,0,0,0)"),a.hasStroke()&&t.stroke()}}},_drawSelected:function(t,e,i){for(var n=this._children,r=0,s=n.length;r<s;r++){var h=n[r],a=h._matrix;i[h._id]||h._drawSelected(t,a.isIdentity()?e:e.appended(a))}}},new function(){function t(e,i){var n=e._children;if(i&&!n.length)throw new Error("Use a moveTo() command first");return n[n.length-1]}return T.each(["lineTo","cubicCurveTo","quadraticCurveTo","curveTo","arcTo","lineBy","cubicCurveBy","quadraticCurveBy","curveBy","arcBy"],function(e){this[e]=function(){var i=t(this,!0);i[e].apply(i,arguments)}},{moveTo:function(){var e=t(this),i=e&&e.isEmpty()?e:new nt(X.NO_INSERT);i!==e&&this.addChild(i),i.moveTo.apply(i,arguments)},moveBy:function(){var e=t(this,!0),i=e&&e.getLastSegment(),n=L.read(arguments);this.moveTo(i?n.add(i._point):n)},closePath:function(e){t(this,!0).closePath(e)}})},T.each(["reverse","flatten","simplify","smooth"],function(t){this[t]=function(e){for(var i=this._children,n,r=0,s=i.length;r<s;r++)n=i[r][t](e)||n;return n}},{}));Zt.inject(new function(){var t=Math.min,e=Math.max,i=Math.abs,n={unite:{1:!0,2:!0},intersect:{2:!0},subtract:{1:!0},exclude:{1:!0,"-1":!0}};function r(v){return v._children||[v]}function s(v,m){var f=v.clone(!1).reduce({simplify:!0}).transform(null,!0,!0);if(m){for(var g=r(f),w=0,S=g.length;w<S;w++){var v=g[w];!v._closed&&!v.isEmpty()&&(v.closePath(1e-12),v.getFirstSegment().setHandleIn(0,0),v.getLastSegment().setHandleOut(0,0))}f=f.resolveCrossings().reorient(f.getFillRule()==="nonzero",!0)}return f}function h(v,m,f,g,w){var S=new Mt(X.NO_INSERT);return S.addChildren(v,!0),S=S.reduce({simplify:m}),w&&w.insert==!1||S.insertAbove(g&&f.isSibling(g)&&f.getIndex()<g.getIndex()?g:f),S.copyAttributes(f,!0),S}function a(v){return v.hasOverlap()||v.isCrossing()}function o(v,m,f,g){if(g&&(g.trace==!1||g.stroke)&&/^(subtract|intersect)$/.test(f))return u(v,m,f);var w=s(v,!0),S=m&&v!==m&&s(m,!0),x=n[f];x[f]=!0,S&&(x.subtract||x.exclude)^(S.isClockwise()^w.isClockwise())&&S.reverse();var b=l(Tt.expand(w.getIntersections(S,a))),P=r(w),I=S&&r(S),O=[],A=[],z;function M(U){for(var rt=0,Y=U.length;rt<Y;rt++){var tt=U[rt];T.push(O,tt._segments),T.push(A,tt.getCurves()),tt._overlapsOnly=!0}}function k(U){for(var rt=[],Y=0,tt=U&&U.length;Y<tt;Y++)rt.push(A[U[Y]]);return rt}if(b.length){M(P),I&&M(I);for(var E=new Array(A.length),N=0,B=A.length;N<B;N++)E[N]=A[N].getValues();for(var F=Yt.findCurveBoundsCollisions(E,E,0,!0),V={},N=0;N<A.length;N++){var W=A[N],H=W._path._id,J=V[H]=V[H]||{};J[W.getIndex()]={hor:k(F[N].hor),ver:k(F[N].ver)}}for(var N=0,B=b.length;N<B;N++)y(b[N]._segment,w,S,V,x);for(var N=0,B=O.length;N<B;N++){var q=O[N],$=q._intersection;q._winding||y(q,w,S,V,x),$&&$._overlap||(q._path._overlapsOnly=!1)}z=C(O,x)}else z=c(I?P.concat(I):P.slice(),function(U){return!!x[U]});return h(z,!0,v,m,g)}function u(v,m,f){var g=s(v),w=s(m),S=g.getIntersections(w,a),x=f==="subtract",b=f==="divide",P={},I=[];function O(M){if(!P[M._id]&&(b||w.contains(M.getPointAt(M.getLength()/2))^x))return I.unshift(M),P[M._id]=!0}for(var A=S.length-1;A>=0;A--){var z=S[A].split();z&&(O(z)&&z.getFirstSegment().setHandleIn(0,0),g.getLastSegment().setHandleOut(0,0))}return O(g),h(I,!1,v,m)}function _(v,m){for(var f=v;f;){if(f===m)return;f=f._previous}for(;v._next&&v._next!==m;)v=v._next;if(!v._next){for(;m._previous;)m=m._previous;v._next=m,m._previous=v}}function d(v){for(var m=v.length-1;m>=0;m--)v[m].clearHandles()}function c(v,m,f){var g=v&&v.length;if(g){var w=T.each(v,function(F,V){this[F._id]={container:null,winding:F.isClockwise()?1:-1,index:V}},{}),S=v.slice().sort(function(F,V){return i(V.getArea())-i(F.getArea())}),x=S[0],b=Yt.findItemBoundsCollisions(S,null,et.GEOMETRIC_EPSILON);f==null&&(f=x.isClockwise());for(var P=0;P<g;P++){var I=S[P],O=w[I._id],A=0,z=b[P];if(z){for(var M=null,k=z.length-1;k>=0;k--)if(z[k]<P){M=M||I.getInteriorPoint();var E=S[z[k]];if(E.contains(M)){var N=w[E._id];A=N.winding,O.winding+=A,O.container=N.exclude?N.container:E;break}}}if(m(O.winding)===m(A))O.exclude=!0,v[O.index]=null;else{var B=O.container;I.setClockwise(B?!B.isClockwise():f)}}}return v}function l(v,m,f){var g=m&&[],w=1e-8,S=1-w,x=!1,b=f||[],P=f&&{},I,O,A;function z(rt){return rt._path._id+"."+rt._segment1._index}for(var M=(f&&f.length)-1;M>=0;M--){var k=f[M];k._path&&(P[z(k)]=!0)}for(var M=v.length-1;M>=0;M--){var E=v[M],N=E._time,B=N,F=m&&!m(E),k=E._curve,V;if(k&&(k!==O?(x=!k.hasHandles()||P&&P[z(k)],I=[],A=null,O=k):A>=w&&(N/=A)),F){I&&I.push(E);continue}else m&&g.unshift(E);if(A=B,N<w)V=k._segment1;else if(N>S)V=k._segment2;else{var W=k.divideAtTime(N,!0);x&&b.push(k,W),V=W._segment1;for(var H=I.length-1;H>=0;H--){var J=I[H];J._time=(J._time-N)/(1-N)}}E._setSegment(V);var q=V._intersection,$=E._intersection;if(q){_(q,$);for(var U=q;U;)_(U._intersection,q),U=U._next}else V._intersection=$}return f||d(b),g||v}function p(v,m,f,g,w){var S=Array.isArray(m)?m:m[f?"hor":"ver"],x=f?1:0,b=x^1,P=[v.x,v.y],I=P[x],O=P[b],A=1e-9,z=1e-6,M=I-A,k=I+A,E=0,N=0,B=0,F=0,V=!1,W=!1,H=1,J=[],q,$;function U(at){var gt=at[b+0],Pt=at[b+6];if(!(O<t(gt,Pt)||O>e(gt,Pt))){var yt=at[x+0],Ft=at[x+2],Jt=at[x+4],Dt=at[x+6];if(gt===Pt){(yt<k&&Dt>M||Dt<k&&yt>M)&&(V=!0);return}var Vt=O===gt?0:O===Pt||M>e(yt,Ft,Jt,Dt)||k<t(yt,Ft,Jt,Dt)?1:R.solveCubic(at,b,O,J,0,1)>0?J[0]:1,It=Vt===0?yt:Vt===1?Dt:R.getPoint(at,Vt)[f?"y":"x"],Ot=gt>Pt?1:-1,ne=q[b]>q[b+6]?1:-1,qt=q[x+6];return O!==gt?(It<M?B+=Ot:It>k?F+=Ot:V=!0,It>I-z&&It<I+z&&(H/=2)):(Ot!==ne?yt<M?B+=Ot:yt>k&&(F+=Ot):yt!=qt&&(qt<k&&It>k?(F+=Ot,V=!0):qt>M&&It<M&&(B+=Ot,V=!0)),H/=4),q=at,!w&&It>M&&It<k&&R.getTangent(at,Vt)[f?"x":"y"]===0&&p(v,m,!f,g,!0)}}function rt(at){var gt=at[b+0],Pt=at[b+2],yt=at[b+4],Ft=at[b+6];if(O<=e(gt,Pt,yt,Ft)&&O>=t(gt,Pt,yt,Ft)){for(var Jt=at[x+0],Dt=at[x+2],Vt=at[x+4],It=at[x+6],Ot=M>e(Jt,Dt,Vt,It)||k<t(Jt,Dt,Vt,It)?[at]:R.getMonoCurves(at,f),ne,qt=0,Se=Ot.length;qt<Se;qt++)if(ne=U(Ot[qt]))return ne}}for(var Y=0,tt=S.length;Y<tt;Y++){var it=S[Y],j=it._path,mt=it.getValues(),wt;if((!Y||S[Y-1]._path!==j)&&(q=null,j._closed||($=R.getValues(j.getLastCurve().getSegment2(),it.getSegment1(),null,!g),$[b]!==$[b+6]&&(q=$)),!q)){q=mt;for(var St=j.getLastCurve();St&&St!==it;){var dt=St.getValues();if(dt[b]!==dt[b+6]){q=dt;break}St=St.getPrevious()}}if(wt=rt(mt))return wt;if(Y+1===tt||S[Y+1]._path!==j){if($&&(wt=rt($)))return wt;V&&!B&&!F&&(B=F=j.isClockwise(g)^f?1:-1),E+=B,N+=F,B=F=0,V&&(W=!0,V=!1),$=null}}return E=i(E),N=i(N),{winding:e(E,N),windingL:E,windingR:N,quality:H,onPath:W}}function y(v,m,f,g,w){var S=[],x=v,b=0,A;do{var P=v.getCurve();if(P){var I=P.getLength();S.push({segment:v,curve:P,length:I}),b+=I}v=v.getNext()}while(v&&!v._intersection&&v!==x);for(var O=[.5,.25,.75],A={winding:0,quality:-1},z=.001,M=1-z,k=0;k<O.length&&A.quality<.5;k++)for(var I=b*O[k],E=0,N=S.length;E<N;E++){var B=S[E],F=B.length;if(I<=F){var P=B.curve,V=P._path,W=V._parent,H=W instanceof Mt?W:V,J=et.clamp(P.getTimeAt(I),z,M),q=P.getPointAtTime(J),$=i(P.getTangentAtTime(J).y)<Math.SQRT1_2,U=null;if(w.subtract&&f){var rt=H===m?f:m,Y=rt._getWinding(q,$,!0);if(H===m&&Y.winding||H===f&&!Y.winding){if(Y.quality<1)continue;U={winding:0,quality:1}}}U=U||p(q,g[V._id][P.getIndex()],$,!0),U.quality>A.quality&&(A=U);break}I-=F}for(var E=S.length-1;E>=0;E--)S[E].segment._winding=A}function C(v,m){var f=[],g;function w(tt){var it;return!!(tt&&!tt._visited&&(!m||m[(it=tt._winding||{}).winding]&&!(m.unite&&it.winding===2&&it.windingL&&it.windingR)))}function S(tt){if(tt){for(var it=0,j=g.length;it<j;it++)if(tt===g[it])return!0}return!1}function x(tt){for(var it=tt._segments,j=0,mt=it.length;j<mt;j++)it[j]._visited=!0}function b(tt,it){var j=tt._intersection,mt=j,wt=[];it&&(g=[tt]);function St(dt,at){for(;dt&&dt!==at;){var gt=dt._segment,Pt=gt&&gt._path;if(Pt){var yt=gt.getNext()||Pt.getFirstSegment(),Ft=yt._intersection;gt!==tt&&(S(gt)||S(yt)||yt&&w(gt)&&(w(yt)||Ft&&w(Ft._segment)))&&wt.push(gt),it&&g.push(gt)}dt=dt._next}}if(j){for(St(j);j&&j._previous;)j=j._previous;St(j,mt)}return wt}v.sort(function(tt,it){var j=tt._intersection,mt=it._intersection,wt=!!(j&&j._overlap),St=!!(mt&&mt._overlap),dt=tt._path,at=it._path;return wt^St?wt?1:-1:!j^!mt?j?1:-1:dt!==at?dt._id-at._id:tt._index-it._index});for(var P=0,I=v.length;P<I;P++){var O=v[P],A=w(O),z=null,M=!1,k=!0,E=[],N,B,F;if(A&&O._path._overlapsOnly){var V=O._path,W=O._intersection._segment._path;V.compare(W)&&(V.getArea()&&f.push(V.clone(!1)),x(V),x(W),A=!1)}for(;A;){var H=!z,J=b(O,H),q=J.shift(),M=!H&&(S(O)||S(q)),$=!M&&q;if(H&&(z=new nt(X.NO_INSERT),N=null),M){(O.isFirst()||O.isLast())&&(k=O._path._closed),O._visited=!0;break}if($&&N&&(E.push(N),N=null),N||($&&J.push(O),N={start:z._segments.length,crossings:J,visited:B=[],handleIn:F}),$&&(O=q),!w(O)){z.removeSegments(N.start);for(var U=0,rt=B.length;U<rt;U++)B[U]._visited=!1;B.length=0;do O=N&&N.crossings.shift(),(!O||!O._path)&&(O=null,N=E.pop(),N&&(B=N.visited,F=N.handleIn));while(N&&!w(O));if(!O)break}var Y=O.getNext();z.add(new G(O._point,F,Y&&O._handleOut)),O._visited=!0,B.push(O),O=Y||O._path.getFirstSegment(),F=Y&&Y._handleIn}M&&(k&&(z.getFirstSegment().setHandleIn(F),z.setClosed(k)),z.getArea()!==0&&f.push(z))}return f}return{_getWinding:function(v,m,f){return p(v,this.getCurves(),m,f)},unite:function(v,m){return o(this,v,"unite",m)},intersect:function(v,m){return o(this,v,"intersect",m)},subtract:function(v,m){return o(this,v,"subtract",m)},exclude:function(v,m){return o(this,v,"exclude",m)},divide:function(v,m){return m&&(m.trace==!1||m.stroke)?u(this,v,"divide"):h([this.subtract(v,m),this.intersect(v,m)],!0,this,v,m)},resolveCrossings:function(){var v=this._children,m=v||[this];function f(N,B){var F=N&&N._intersection;return F&&F._overlap&&F._path===B}var g=!1,w=!1,S=this.getIntersections(null,function(N){return N.hasOverlap()&&(g=!0)||N.isCrossing()&&(w=!0)}),x=g&&w&&[];if(S=Tt.expand(S),g)for(var b=l(S,function(N){return N.hasOverlap()},x),P=b.length-1;P>=0;P--){var I=b[P],O=I._path,A=I._segment,z=A.getPrevious(),M=A.getNext();f(z,O)&&f(M,O)&&(A.remove(),z._handleOut._set(0,0),M._handleIn._set(0,0),z!==A&&!z.getCurve().hasLength()&&(M._handleIn.set(z._handleIn),z.remove()))}w&&(l(S,g&&function(N){var B=N.getCurve(),F=N.getSegment(),V=N._intersection,W=V._curve,H=V._segment;if(B&&W&&B._path&&W._path)return!0;F&&(F._intersection=null),H&&(H._intersection=null)},x),x&&d(x),m=C(T.each(m,function(N){T.push(this,N._segments)},[])));var k=m.length,E;return k>1&&v?(m!==v&&this.setChildren(m),E=this):k===1&&!v&&(m[0]!==this&&this.setSegments(m[0].removeSegments()),E=this),E||(E=new Mt(X.NO_INSERT),E.addChildren(m),E=E.reduce(),E.copyAttributes(this),this.replaceWith(E)),E},reorient:function(v,m){var f=this._children;return f&&f.length?this.setChildren(c(this.removeChildren(),function(g){return!!(v?g:g&1)},m)):m!==D&&this.setClockwise(m),this},getInteriorPoint:function(){var v=this.getBounds(),m=v.getCenter(!0);if(!this.contains(m)){for(var f=this.getCurves(),g=m.y,w=[],S=[],x=0,b=f.length;x<b;x++){var P=f[x].getValues(),I=P[1],O=P[3],A=P[5],z=P[7];if(g>=t(I,O,A,z)&&g<=e(I,O,A,z))for(var M=R.getMonoCurves(P),k=0,E=M.length;k<E;k++){var N=M[k],B=N[1],F=N[7];if(B!==F&&(g>=B&&g<=F||g>=F&&g<=B)){var V=g===B?N[0]:g===F?N[6]:R.solveCubic(N,1,g,S,0,1)===1?R.getPoint(N,S[0]).x:(N[0]+N[6])/2;w.push(V)}}}w.length>1&&(w.sort(function(W,H){return W-H}),m.x=(w[0]+w[1])/2)}return m}}});var ue=T.extend({_class:"PathFlattener",initialize:function(t,e,i,n,r){var s=[],h=[],a=0,o=1/(i||32),u=t._segments,_=u[0],d;function c(C,v){var m=R.getValues(C,v,r);s.push(m),l(m,C._index,0,1)}function l(C,v,m,f){if(f-m>o&&!(n&&R.isStraight(C))&&!R.isFlatEnough(C,e||.25)){var g=R.subdivide(C,.5),w=(m+f)/2;l(g[0],v,m,w),l(g[1],v,w,f)}else{var S=C[6]-C[0],x=C[7]-C[1],b=Math.sqrt(S*S+x*x);b>0&&(a+=b,h.push({offset:a,curve:C,index:v,time:f}))}}for(var p=1,y=u.length;p<y;p++)d=u[p],c(_,d),_=d;t._closed&&c(d||_,u[0]),this.curves=s,this.parts=h,this.length=a,this.index=0},_get:function(t){for(var e=this.parts,i=e.length,n,r,s=this.index;r=s,!(!s||e[--s].offset<t););for(;r<i;r++){var h=e[r];if(h.offset>=t){this.index=r;var a=e[r-1],o=a&&a.index===h.index?a.time:0,u=a?a.offset:0;return{index:h.index,time:o+(h.time-o)*(t-u)/(h.offset-u)}}}return{index:e[i-1].index,time:1}},drawPart:function(t,e,i){for(var n=this._get(e),r=this._get(i),s=n.index,h=r.index;s<=h;s++){var a=R.getPart(this.curves[s],s===n.index?n.time:0,s===r.index?r.time:1);s===n.index&&t.moveTo(a[0],a[1]),t.bezierCurveTo.apply(t,a.slice(2))}}},T.each(R._evaluateMethods,function(t){this[t+"At"]=function(e){var i=this._get(e);return R[t](this.curves[i.index],i.time)}},{})),_e=T.extend({initialize:function(t){for(var e=this.points=[],i=t._segments,n=t._closed,r=0,s,h=i.length;r<h;r++){var a=i[r].point;(!s||!s.equals(a))&&e.push(s=a.clone())}n&&(e.unshift(e[e.length-1]),e.push(e[1])),this.closed=n},fit:function(t){var e=this.points,i=e.length,n=null;return i>0&&(n=[new G(e[0])],i>1&&(this.fitCubic(n,t,0,i-1,e[1].subtract(e[0]),e[i-2].subtract(e[i-1])),this.closed&&(n.shift(),n.pop()))),n},fitCubic:function(t,e,i,n,r,s){var h=this.points;if(n-i===1){var a=h[i],o=h[n],u=a.getDistance(o)/3;this.addCurve(t,[a,a.add(r.normalize(u)),o.add(s.normalize(u)),o]);return}for(var _=this.chordLengthParameterize(i,n),d=Math.max(e,e*e),c,l=!0,p=0;p<=4;p++){var y=this.generateBezier(i,n,_,r,s),C=this.findMaxError(i,n,y,_);if(C.error<e&&l){this.addCurve(t,y);return}if(c=C.index,C.error>=d)break;l=this.reparameterize(i,n,_,y),d=C.error}var v=h[c-1].subtract(h[c+1]);this.fitCubic(t,e,i,c,r,v),this.fitCubic(t,e,c,n,v.negate(),s)},addCurve:function(t,e){var i=t[t.length-1];i.setHandleOut(e[1].subtract(e[0])),t.push(new G(e[3],e[2].subtract(e[3])))},generateBezier:function(t,e,i,n,r){for(var s=1e-12,h=Math.abs,a=this.points,o=a[t],u=a[e],_=[[0,0],[0,0]],d=[0,0],c=0,l=e-t+1;c<l;c++){var p=i[c],y=1-p,C=3*p*y,v=y*y*y,m=C*y,f=C*p,g=p*p*p,w=n.normalize(m),S=r.normalize(f),x=a[t+c].subtract(o.multiply(v+m)).subtract(u.multiply(f+g));_[0][0]+=w.dot(w),_[0][1]+=w.dot(S),_[1][0]=_[0][1],_[1][1]+=S.dot(S),d[0]+=w.dot(x),d[1]+=S.dot(x)}var b=_[0][0]*_[1][1]-_[1][0]*_[0][1],P,I;if(h(b)>s){var O=_[0][0]*d[1]-_[1][0]*d[0],A=d[0]*_[1][1]-d[1]*_[0][1];P=A/b,I=O/b}else{var z=_[0][0]+_[0][1],M=_[1][0]+_[1][1];P=I=h(z)>s?d[0]/z:h(M)>s?d[1]/M:0}var k=u.getDistance(o),E=s*k,N,B;if(P<E||I<E)P=I=k/3;else{var F=u.subtract(o);N=n.normalize(P),B=r.normalize(I),N.dot(F)-B.dot(F)>k*k&&(P=I=k/3,N=B=null)}return[o,o.add(N||n.normalize(P)),u.add(B||r.normalize(I)),u]},reparameterize:function(t,e,i,n){for(var r=t;r<=e;r++)i[r-t]=this.findRoot(n,this.points[r],i[r-t]);for(var r=1,s=i.length;r<s;r++)if(i[r]<=i[r-1])return!1;return!0},findRoot:function(t,e,i){for(var n=[],r=[],s=0;s<=2;s++)n[s]=t[s+1].subtract(t[s]).multiply(3);for(var s=0;s<=1;s++)r[s]=n[s+1].subtract(n[s]).multiply(2);var h=this.evaluate(3,t,i),a=this.evaluate(2,n,i),o=this.evaluate(1,r,i),u=h.subtract(e),_=a.dot(a)+u.dot(o);return et.isMachineZero(_)?i:i-u.dot(a)/_},evaluate:function(t,e,i){for(var n=e.slice(),r=1;r<=t;r++)for(var s=0;s<=t-r;s++)n[s]=n[s].multiply(1-i).add(n[s+1].multiply(i));return n[0]},chordLengthParameterize:function(t,e){for(var i=[0],n=t+1;n<=e;n++)i[n-t]=i[n-t-1]+this.points[n].getDistance(this.points[n-1]);for(var n=1,r=e-t;n<=r;n++)i[n]/=i[r];return i},findMaxError:function(t,e,i,n){for(var r=Math.floor((e-t+1)/2),s=0,h=t+1;h<e;h++){var a=this.evaluate(3,i,n[h-t]),o=a.subtract(this.points[h]),u=o.x*o.x+o.y*o.y;u>=s&&(s=u,r=h)}return{error:s,index:r}}}),te=X.extend({_class:"TextItem",_applyMatrix:!1,_canApplyMatrix:!1,_serializeFields:{content:null},_boundsOptions:{stroke:!1,handle:!1},initialize:function(e){this._content="",this._lines=[];var i=e&&T.isPlainObject(e)&&e.x===D&&e.y===D;this._initialize(i&&e,!i&&L.read(arguments))},_equals:function(t){return this._content===t._content},copyContent:function(t){this.setContent(t._content)},getContent:function(){return this._content},setContent:function(t){this._content=""+t,this._lines=this._content.split(/\r\n|\n|\r/mg),this._changed(521)},isEmpty:function(){return!this._content},getCharacterStyle:"#getStyle",setCharacterStyle:"#setStyle",getParagraphStyle:"#getStyle",setParagraphStyle:"#setStyle"}),de=te.extend({_class:"PointText",initialize:function(){te.apply(this,arguments)},getPoint:function(){var t=this._matrix.getTranslation();return new Nt(t.x,t.y,this,"setPoint")},setPoint:function(){var t=L.read(arguments);this.translate(t.subtract(this._matrix.getTranslation()))},_draw:function(t,e,i){if(this._content){this._setStyles(t,e,i);var n=this._lines,r=this._style,s=r.hasFill(),h=r.hasStroke(),a=r.getLeading(),o=t.shadowColor;t.font=r.getFontStyle(),t.textAlign=r.getJustification();for(var u=0,_=n.length;u<_;u++){t.shadowColor=o;var d=n[u];s&&(t.fillText(d,0,0),t.shadowColor="rgba(0,0,0,0)"),h&&t.strokeText(d,0,0),t.translate(0,a)}}},_getBounds:function(t,e){var i=this._style,n=this._lines,r=n.length,s=i.getJustification(),h=i.getLeading(),a=this.getView().getTextWidth(i.getFontStyle(),n),o=0;s!=="left"&&(o-=a/(s==="center"?2:1));var u=new K(o,r?-.75*h:0,a,r*h);return t?t._transformBounds(u,u):u}}),ct=T.extend(new function(){var t={gray:["gray"],rgb:["red","green","blue"],hsb:["hue","saturation","brightness"],hsl:["hue","saturation","lightness"],gradient:["gradient","origin","destination","highlight"]},e={},i={transparent:[0,0,0,0]},n;function r(a){var o=a.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})([\da-f]{2})?$/i)||a.match(/^#([\da-f])([\da-f])([\da-f])([\da-f])?$/i),u="rgb",_;if(o){var d=o[4]?4:3;_=new Array(d);for(var c=0;c<d;c++){var l=o[c+1];_[c]=parseInt(l.length==1?l+l:l,16)/255}}else if(o=a.match(/^(rgb|hsl)a?\((.*)\)$/)){u=o[1],_=o[2].trim().split(/[,\s]+/g);for(var p=u==="hsl",c=0,y=Math.min(_.length,4);c<y;c++){var C=_[c],l=parseFloat(C);if(p)if(c===0){var v=C.match(/([a-z]*)$/)[1];l*={turn:360,rad:180/Math.PI,grad:.9}[v]||1}else c<3&&(l/=100);else c<3&&(l/=/%$/.test(C)?100:255);_[c]=l}}else{var m=i[a];if(!m)if(lt){n||(n=ft.getContext(1,1,{willReadFrequently:!0}),n.globalCompositeOperation="copy"),n.fillStyle="rgba(0,0,0,0)",n.fillStyle=a,n.fillRect(0,0,1,1);var f=n.getImageData(0,0,1,1).data;m=i[a]=[f[0]/255,f[1]/255,f[2]/255]}else m=[0,0,0];_=m.slice()}return[u,_]}var s=[[0,3,1],[2,0,1],[1,0,3],[1,2,0],[3,1,0],[0,1,2]],h={"rgb-hsb":function(a,o,u){var _=Math.max(a,o,u),d=Math.min(a,o,u),c=_-d,l=c===0?0:(_==a?(o-u)/c+(o<u?6:0):_==o?(u-a)/c+2:(a-o)/c+4)*60;return[l,_===0?0:c/_,_]},"hsb-rgb":function(a,o,u){a=(a/60%6+6)%6;var d=Math.floor(a),_=a-d,d=s[d],c=[u,u*(1-o),u*(1-o*_),u*(1-o*(1-_))];return[c[d[0]],c[d[1]],c[d[2]]]},"rgb-hsl":function(a,o,u){var _=Math.max(a,o,u),d=Math.min(a,o,u),c=_-d,l=c===0,p=l?0:(_==a?(o-u)/c+(o<u?6:0):_==o?(u-a)/c+2:(a-o)/c+4)*60,y=(_+d)/2,C=l?0:y<.5?c/(_+d):c/(2-_-d);return[p,C,y]},"hsl-rgb":function(a,o,u){if(a=(a/360%1+1)%1,o===0)return[u,u,u];for(var _=[a+1/3,a,a-1/3],d=u<.5?u*(1+o):u+o-u*o,c=2*u-d,l=[],p=0;p<3;p++){var y=_[p];y<0&&(y+=1),y>1&&(y-=1),l[p]=6*y<1?c+(d-c)*6*y:2*y<1?d:3*y<2?c+(d-c)*(2/3-y)*6:c}return l},"rgb-gray":function(a,o,u){return[a*.2989+o*.587+u*.114]},"gray-rgb":function(a){return[a,a,a]},"gray-hsb":function(a){return[0,0,a]},"gray-hsl":function(a){return[0,0,a]},"gradient-rgb":function(){return[]},"rgb-gradient":function(){return[]}};return T.each(t,function(a,o){e[o]=[],T.each(a,function(u,_){var d=T.capitalize(u),c=/^(hue|saturation)$/.test(u),l=e[o][_]=o==="gradient"?u==="gradient"?function(p){var y=this._components[0];return p=ee.read(Array.isArray(p)?p:arguments,0,{readNull:!0}),y!==p&&(y&&y._removeOwner(this),p&&p._addOwner(this)),p}:function(){return L.read(arguments,0,{readNull:u==="highlight",clone:!0})}:function(p){return p==null||isNaN(p)?0:+p};this["get"+d]=function(){return this._type===o||c&&/^hs[bl]$/.test(this._type)?this._components[_]:this._convert(o)[_]},this["set"+d]=function(p){this._type!==o&&!(c&&/^hs[bl]$/.test(this._type))&&(this._components=this._convert(o),this._properties=t[o],this._type=o),this._components[_]=l.call(this,p),this._changed()}},this)},{_class:"Color",_readIndex:!0,initialize:function a(o){var u=arguments,_=this.__read,d=0,c,l,p,y;Array.isArray(o)&&(u=o,o=u[0]);var C=o!=null&&typeof o;if(C==="string"&&o in t&&(c=o,o=u[1],Array.isArray(o)?(l=o,p=u[2]):(_&&(d=1),u=T.slice(u,1),C=typeof o)),!l){if(y=C==="number"?u:C==="object"&&o.length!=null?o:null,y){c||(c=y.length>=3?"rgb":"gray");var v=t[c].length;p=y[v],_&&(d+=y===arguments?v+(p!=null?1:0):1),y.length>v&&(y=T.slice(y,0,v))}else if(C==="string"){var m=r(o);c=m[0],l=m[1],l.length===4&&(p=l[3],l.length--)}else if(C==="object")if(o.constructor===a){if(c=o._type,l=o._components.slice(),p=o._alpha,c==="gradient")for(var f=1,g=l.length;f<g;f++){var w=l[f];w&&(l[f]=w.clone())}}else if(o.constructor===ee)c="gradient",y=u;else{c="hue"in o?"lightness"in o?"hsl":"hsb":"gradient"in o||"stops"in o||"radial"in o?"gradient":"gray"in o?"gray":"rgb";var S=t[c],x=e[c];this._components=l=[];for(var f=0,g=S.length;f<g;f++){var b=o[S[f]];b==null&&!f&&c==="gradient"&&"stops"in o&&(b={stops:o.stops,radial:o.radial}),b=x[f].call(this,b),b!=null&&(l[f]=b)}p=o.alpha}_&&c&&(d=1)}if(this._type=c||"rgb",!l){this._components=l=[];for(var x=e[this._type],f=0,g=x.length;f<g;f++){var b=x[f].call(this,y&&y[f]);b!=null&&(l[f]=b)}}return this._components=l,this._properties=t[this._type],this._alpha=p,_&&(this.__read=d),this},set:"#initialize",_serialize:function(a,o){var u=this.getComponents();return T.serialize(/^(gray|rgb)$/.test(this._type)?u:[this._type].concat(u),a,!0,o)},_changed:function(){this._canvasStyle=null,this._owner&&(this._setter?this._owner[this._setter](this):this._owner._changed(129))},_convert:function(a){var o;return this._type===a?this._components.slice():(o=h[this._type+"-"+a])?o.apply(this,this._components):h["rgb-"+a].apply(this,h[this._type+"-rgb"].apply(this,this._components))},convert:function(a){return new ct(a,this._convert(a),this._alpha)},getType:function(){return this._type},setType:function(a){this._components=this._convert(a),this._properties=t[a],this._type=a},getComponents:function(){var a=this._components.slice();return this._alpha!=null&&a.push(this._alpha),a},getAlpha:function(){return this._alpha!=null?this._alpha:1},setAlpha:function(a){this._alpha=a==null?null:Math.min(Math.max(a,0),1),this._changed()},hasAlpha:function(){return this._alpha!=null},equals:function(a){var o=T.isPlainValue(a,!0)?ct.read(arguments):a;return o===this||o&&this._class===o._class&&this._type===o._type&&this.getAlpha()===o.getAlpha()&&T.equals(this._components,o._components)||!1},toString:function(){for(var a=this._properties,o=[],u=this._type==="gradient",_=bt.instance,d=0,c=a.length;d<c;d++){var l=this._components[d];l!=null&&o.push(a[d]+": "+(u?l:_.number(l)))}return this._alpha!=null&&o.push("alpha: "+_.number(this._alpha)),"{ "+o.join(", ")+" }"},toCSS:function(a){var o=this._convert("rgb"),u=a||this._alpha==null?1:this._alpha;function _(d){return Math.round((d<0?0:d>1?1:d)*255)}return o=[_(o[0]),_(o[1]),_(o[2])],u<1&&o.push(u<0?0:u),a?"#"+((1<<24)+(o[0]<<16)+(o[1]<<8)+o[2]).toString(16).slice(1):(o.length==4?"rgba(":"rgb(")+o.join(",")+")"},toCanvasStyle:function(a,o){if(this._canvasStyle)return this._canvasStyle;if(this._type!=="gradient")return this._canvasStyle=this.toCSS();var u=this._components,_=u[0],d=_._stops,c=u[1],l=u[2],p=u[3],y=o&&o.inverted(),C;if(y&&(c=y._transformPoint(c),l=y._transformPoint(l),p&&(p=y._transformPoint(p))),_._radial){var v=l.getDistance(c);if(p){var m=p.subtract(c);m.getLength()>v&&(p=c.add(m.normalize(v-.1)))}var f=p||c;C=a.createRadialGradient(f.x,f.y,0,c.x,c.y,v)}else C=a.createLinearGradient(c.x,c.y,l.x,l.y);for(var g=0,w=d.length;g<w;g++){var S=d[g],x=S._offset;C.addColorStop(x==null?g/(w-1):x,S._color.toCanvasStyle())}return this._canvasStyle=C},transform:function(a){if(this._type==="gradient"){for(var o=this._components,u=1,_=o.length;u<_;u++){var d=o[u];a._transformPoint(d,d,!0)}this._changed()}},statics:{_types:t,random:function(){var a=Math.random;return new ct(a(),a(),a())},_setOwner:function(a,o,u){return a&&(a._owner&&o&&a._owner!==o&&(a=a.clone()),!a._owner^!o&&(a._owner=o||null,a._setter=u||null)),a}}})},new function(){var t={add:function(e,i){return e+i},subtract:function(e,i){return e-i},multiply:function(e,i){return e*i},divide:function(e,i){return e/i}};return T.each(t,function(e,i){this[i]=function(n){n=ct.read(arguments);for(var r=this._type,s=this._components,h=n._convert(r),a=0,o=s.length;a<o;a++)h[a]=e(s[a],h[a]);return new ct(r,h,this._alpha!=null?e(this._alpha,n.getAlpha()):null)}},{})}),ee=T.extend({_class:"Gradient",initialize:function(e,i){this._id=jt.get(),e&&T.isPlainObject(e)&&(this.set(e),e=i=null),this._stops==null&&this.setStops(e||["white","black"]),this._radial==null&&this.setRadial(typeof i=="string"&&i==="radial"||i||!1)},_serialize:function(t,e){return e.add(this,function(){return T.serialize([this._stops,this._radial],t,!0,e)})},_changed:function(){for(var t=0,e=this._owners&&this._owners.length;t<e;t++)this._owners[t]._changed()},_addOwner:function(t){this._owners||(this._owners=[]),this._owners.push(t)},_removeOwner:function(t){var e=this._owners?this._owners.indexOf(t):-1;e!=-1&&(this._owners.splice(e,1),this._owners.length||(this._owners=D))},clone:function(){for(var t=[],e=0,i=this._stops.length;e<i;e++)t[e]=this._stops[e].clone();return new ee(t,this._radial)},getStops:function(){return this._stops},setStops:function(t){if(t.length<2)throw new Error("Gradient stop list needs to contain at least two stops.");var e=this._stops;if(e)for(var i=0,n=e.length;i<n;i++)e[i]._owner=D;e=this._stops=ae.readList(t,0,{clone:!0});for(var i=0,n=e.length;i<n;i++)e[i]._owner=this;this._changed()},getRadial:function(){return this._radial},setRadial:function(t){this._radial=t,this._changed()},equals:function(t){if(t===this)return!0;if(t&&this._class===t._class){var e=this._stops,i=t._stops,n=e.length;if(n===i.length){for(var r=0;r<n;r++)if(!e[r].equals(i[r]))return!1;return!0}}return!1}}),ae=T.extend({_class:"GradientStop",initialize:function(e,i){var n=e,r=i;typeof e=="object"&&i===D&&(Array.isArray(e)&&typeof e[0]!="number"?(n=e[0],r=e[1]):("color"in e||"offset"in e||"rampPoint"in e)&&(n=e.color,r=e.offset||e.rampPoint||0)),this.setColor(n),this.setOffset(r)},clone:function(){return new ae(this._color.clone(),this._offset)},_serialize:function(t,e){var i=this._color,n=this._offset;return T.serialize(n==null?[i]:[i,n],t,!0,e)},_changed:function(){this._owner&&this._owner._changed(129)},getOffset:function(){return this._offset},setOffset:function(t){this._offset=t,this._changed()},getRampPoint:"#getOffset",setRampPoint:"#setOffset",getColor:function(){return this._color},setColor:function(){ct._setOwner(this._color,null),this._color=ct._setOwner(ct.read(arguments,0),this,"setColor"),this._changed()},equals:function(t){return t===this||t&&this._class===t._class&&this._color.equals(t._color)&&this._offset==t._offset||!1}}),he=T.extend(new function(){var t={fillColor:null,fillRule:"nonzero",strokeColor:null,strokeWidth:1,strokeCap:"butt",strokeJoin:"miter",strokeScaling:!0,miterLimit:10,dashOffset:0,dashArray:[],shadowColor:null,shadowBlur:0,shadowOffset:new L,selectedColor:null},e=T.set({},t,{fontFamily:"sans-serif",fontWeight:"normal",fontSize:12,leading:null,justification:"left"}),i=T.set({},e,{fillColor:new ct}),n={strokeWidth:193,strokeCap:193,strokeJoin:193,strokeScaling:201,miterLimit:193,fontFamily:9,fontWeight:9,fontSize:9,font:9,leading:9,justification:9},r={beans:!0},s={_class:"Style",beans:!0,initialize:function(a,o,u){this._values={},this._owner=o,this._project=o&&o._project||u||Q.project,this._defaults=!o||o instanceof At?e:o instanceof te?i:t,a&&this.set(a)}};return T.each(e,function(h,a){var o=/Color$/.test(a),u=a==="shadowOffset",_=T.capitalize(a),d=n[a],c="set"+_,l="get"+_;s[c]=function(p){var y=this._owner,C=y&&y._children,v=C&&C.length>0&&!(y instanceof Mt);if(v)for(var m=0,f=C.length;m<f;m++)C[m]._style[c](p);if((a==="selectedColor"||!v)&&a in this._defaults){var g=this._values[a];g!==p&&(o&&(g&&(ct._setOwner(g,null),g._canvasStyle=null),p&&p.constructor===ct&&(p=ct._setOwner(p,y,v&&c))),this._values[a]=p,y&&y._changed(d||129))}},s[l]=function(p){var y=this._owner,C=y&&y._children,v=C&&C.length>0&&!(y instanceof Mt),m;if(v&&!p)for(var f=0,g=C.length;f<g;f++){var w=C[f]._style[l]();if(!f)m=w;else if(!T.equals(m,w))return D}else if(a in this._defaults){var m=this._values[a];if(m===D)m=this._defaults[a],m&&m.clone&&(m=m.clone());else{var S=o?ct:u?L:null;S&&!(m&&m.constructor===S)&&(this._values[a]=m=S.read([m],0,{readNull:!0,clone:!0}))}}return m&&o&&(m=ct._setOwner(m,y,v&&c)),m},r[l]=function(p){return this._style[l](p)},r[c]=function(p){this._style[c](p)}}),T.each({Font:"FontFamily",WindingRule:"FillRule"},function(h,a){var o="get"+a,u="set"+a;s[o]=r[o]="#get"+h,s[u]=r[u]="#set"+h}),X.inject(r),s},{set:function(t){var e=t instanceof he,i=e?t._values:t;if(i){for(var n in i)if(n in this._defaults){var r=i[n];this[n]=r&&e&&r.clone?r.clone():r}}},equals:function(t){function e(i,n,r){var s=i._values,h=n._values,a=n._defaults;for(var o in s){var u=s[o],_=h[o];if(!(r&&o in h)&&!T.equals(u,_===D?a[o]:_))return!1}return!0}return t===this||t&&this._class===t._class&&e(this,t)&&e(t,this,!0)||!1},_dispose:function(){var t;t=this.getFillColor(),t&&(t._canvasStyle=null),t=this.getStrokeColor(),t&&(t._canvasStyle=null),t=this.getShadowColor(),t&&(t._canvasStyle=null)},hasFill:function(){var t=this.getFillColor();return!!t&&t.alpha>0},hasStroke:function(){var t=this.getStrokeColor();return!!t&&t.alpha>0&&this.getStrokeWidth()>0},hasShadow:function(){var t=this.getShadowColor();return!!t&&t.alpha>0&&(this.getShadowBlur()>0||!this.getShadowOffset().isZero())},getView:function(){return this._project._view},getFontStyle:function(){var t=this.getFontSize();return this.getFontWeight()+" "+t+(/[a-z]/i.test(t+"")?" ":"px ")+this.getFontFamily()},getFont:"#getFontFamily",setFont:"#setFontFamily",getLeading:function t(){var e=t.base.call(this),i=this.getFontSize();return/pt|em|%|px/.test(i)&&(i=this.getView().getPixelSize(i)),e!=null?e:i*1.2}}),ht=new function(){function t(e,i,n,r){for(var s=["","webkit","moz","Moz","ms","o"],h=i[0].toUpperCase()+i.substring(1),a=0;a<6;a++){var o=s[a],u=o?o+h:i;if(u in e){if(n)e[u]=r;else return e[u];break}}}return{getStyles:function(e){var i=e&&e.nodeType!==9?e.ownerDocument:e,n=i&&i.defaultView;return n&&n.getComputedStyle(e,"")},getBounds:function(e,i){var n=e.ownerDocument,r=n.body,s=n.documentElement,h;try{h=e.getBoundingClientRect()}catch(_){h={left:0,top:0,width:0,height:0}}var a=h.left-(s.clientLeft||r.clientLeft||0),o=h.top-(s.clientTop||r.clientTop||0);if(!i){var u=n.defaultView;a+=u.pageXOffset||s.scrollLeft||r.scrollLeft,o+=u.pageYOffset||s.scrollTop||r.scrollTop}return new K(a,o,h.width,h.height)},getViewportBounds:function(e){var i=e.ownerDocument,n=i.defaultView,r=i.documentElement;return new K(0,0,n.innerWidth||r.clientWidth,n.innerHeight||r.clientHeight)},getOffset:function(e,i){return ht.getBounds(e,i).getPoint()},getSize:function(e){return ht.getBounds(e,!0).getSize()},isInvisible:function(e){return ht.getSize(e).equals(new Z(0,0))},isInView:function(e){return!ht.isInvisible(e)&&ht.getViewportBounds(e).intersects(ht.getBounds(e,!0))},isInserted:function(e){return vt.body.contains(e)},getPrefixed:function(e,i){return e&&t(e,i)},setPrefixed:function(e,i,n){if(typeof i=="object")for(var r in i)t(e,r,!0,i[r]);else t(e,i,!0,n)}}},_t={add:function(t,e){if(t)for(var i in e)for(var n=e[i],r=i.split(/[\s,]+/g),s=0,h=r.length;s<h;s++){var a=r[s],o=t===vt&&(a==="touchstart"||a==="touchmove")?{passive:!1}:!1;t.addEventListener(a,n,o)}},remove:function(t,e){if(t)for(var i in e)for(var n=e[i],r=i.split(/[\s,]+/g),s=0,h=r.length;s<h;s++)t.removeEventListener(r[s],n,!1)},getPoint:function(t){var e=t.targetTouches?t.targetTouches.length?t.targetTouches[0]:t.changedTouches[0]:t;return new L(e.pageX||e.clientX+vt.documentElement.scrollLeft,e.pageY||e.clientY+vt.documentElement.scrollTop)},getTarget:function(t){return t.target||t.srcElement},getRelatedTarget:function(t){return t.relatedTarget||t.toElement},getOffset:function(t,e){return _t.getPoint(t).subtract(ht.getOffset(e||_t.getTarget(t)))}};_t.requestAnimationFrame=new function(){var t=ht.getPrefixed(lt,"requestAnimationFrame"),e=!1,i=[],n;function r(){var s=i;i=[];for(var h=0,a=s.length;h<a;h++)s[h]();e=t&&i.length,e&&t(r)}return function(s){i.push(s),t?e||(t(r),e=!0):n||(n=setInterval(r,16.666666666666668))}};var ot=T.extend(Xt,{_class:"View",initialize:function t(e,i){function n(d){return i[d]||parseInt(i.getAttribute(d),10)}function r(){var d=ht.getSize(i);return d.isNaN()||d.isZero()?new Z(n("width"),n("height")):d}var s;if(lt&&i){this._id=i.getAttribute("id"),this._id==null&&i.setAttribute("id",this._id="paper-view-"+t._id++),_t.add(i,this._viewEvents);var h="none";if(ht.setPrefixed(i.style,{userDrag:h,userSelect:h,touchCallout:h,contentZooming:h,tapHighlightColor:"rgba(0,0,0,0)"}),Rt.hasAttribute(i,"resize")){var a=this;_t.add(lt,this._windowEvents={resize:function(){a.setViewSize(r())}})}if(s=r(),Rt.hasAttribute(i,"stats")&&typeof Stats<"u"){this._stats=new Stats;var o=this._stats.domElement,u=o.style,_=ht.getOffset(i);u.position="absolute",u.left=_.x+"px",u.top=_.y+"px",vt.body.appendChild(o)}}else s=new Z(i),i=null;this._project=e,this._scope=e._scope,this._element=i,this._pixelRatio||(this._pixelRatio=lt&&lt.devicePixelRatio||1),this._setElementSize(s.width,s.height),this._viewSize=s,t._views.push(this),t._viewsById[this._id]=this,(this._matrix=new st)._owner=this,t._focused||(t._focused=this),this._frameItems={},this._frameItemCount=0,this._itemEvents={native:{},virtual:{}},this._autoUpdate=!Q.agent.node,this._needsUpdate=!1},remove:function(){if(!this._project)return!1;ot._focused===this&&(ot._focused=null),ot._views.splice(ot._views.indexOf(this),1),delete ot._viewsById[this._id];var t=this._project;return t._view===this&&(t._view=null),_t.remove(this._element,this._viewEvents),_t.remove(lt,this._windowEvents),this._element=this._project=null,this.off("frame"),this._animate=!1,this._frameItems={},!0},_events:T.each(X._itemHandlers.concat(["onResize","onKeyDown","onKeyUp"]),function(t){this[t]={}},{onFrame:{install:function(){this.play()},uninstall:function(){this.pause()}}}),_animate:!1,_time:0,_count:0,getAutoUpdate:function(){return this._autoUpdate},setAutoUpdate:function(t){this._autoUpdate=t,t&&this.requestUpdate()},update:function(){},draw:function(){this.update()},requestUpdate:function(){if(!this._requested){var t=this;_t.requestAnimationFrame(function(){if(t._requested=!1,t._animate){t.requestUpdate();var e=t._element;(!ht.getPrefixed(vt,"hidden")||Rt.getAttribute(e,"keepalive")==="true")&&ht.isInView(e)&&t._handleFrame()}t._autoUpdate&&t.update()}),this._requested=!0}},play:function(){this._animate=!0,this.requestUpdate()},pause:function(){this._animate=!1},_handleFrame:function(){Q=this._scope;var t=Date.now()/1e3,e=this._last?t-this._last:0;this._last=t,this.emit("frame",new T({delta:e,time:this._time+=e,count:this._count++})),this._stats&&this._stats.update()},_animateItem:function(t,e){var i=this._frameItems;e?(i[t._id]={item:t,time:0,count:0},++this._frameItemCount===1&&this.on("frame",this._handleFrameItems)):(delete i[t._id],--this._frameItemCount===0&&this.off("frame",this._handleFrameItems))},_handleFrameItems:function(t){for(var e in this._frameItems){var i=this._frameItems[e];i.item.emit("frame",new T(t,{time:i.time+=t.delta,count:i.count++}))}},_changed:function(){this._project._changed(4097),this._bounds=this._decomposed=D},getElement:function(){return this._element},getPixelRatio:function(){return this._pixelRatio},getResolution:function(){return this._pixelRatio*72},getViewSize:function(){var t=this._viewSize;return new Wt(t.width,t.height,this,"setViewSize")},setViewSize:function(){var t=Z.read(arguments),e=t.subtract(this._viewSize);e.isZero()||(this._setElementSize(t.width,t.height),this._viewSize.set(t),this._changed(),this.emit("resize",{size:t,delta:e}),this._autoUpdate&&this.update())},_setElementSize:function(t,e){var i=this._element;i&&(i.width!==t&&(i.width=t),i.height!==e&&(i.height=e))},getBounds:function(){return this._bounds||(this._bounds=this._matrix.inverted()._transformBounds(new K(new L,this._viewSize))),this._bounds},getSize:function(){return this.getBounds().getSize()},isVisible:function(){return ht.isInView(this._element)},isInserted:function(){return ht.isInserted(this._element)},getPixelSize:function(t){var e=this._element,i;if(e){var n=e.parentNode,r=vt.createElement("div");r.style.fontSize=t,n.appendChild(r),i=parseFloat(ht.getStyles(r).fontSize),n.removeChild(r)}else i=parseFloat(i);return i},getTextWidth:function(t,e){return 0}},T.each(["rotate","scale","shear","skew"],function(t){var e=t==="rotate";this[t]=function(){var i=arguments,n=(e?T:L).read(i),r=L.read(i,0,{readNull:!0});return this.transform(new st()[t](n,r||this.getCenter(!0)))}},{_decompose:function(){return this._decomposed||(this._decomposed=this._matrix.decompose())},translate:function(){var t=new st;return this.transform(t.translate.apply(t,arguments))},getCenter:function(){return this.getBounds().getCenter()},setCenter:function(){var t=L.read(arguments);this.translate(this.getCenter().subtract(t))},getZoom:function(){var t=this._decompose().scaling;return(t.x+t.y)/2},setZoom:function(t){this.transform(new st().scale(t/this.getZoom(),this.getCenter()))},getRotation:function(){return this._decompose().rotation},setRotation:function(t){var e=this.getRotation();e!=null&&t!=null&&this.rotate(t-e)},getScaling:function(){var t=this._decompose().scaling;return new Nt(t.x,t.y,this,"setScaling")},setScaling:function(){var t=this.getScaling(),e=L.read(arguments,0,{clone:!0,readNull:!0});t&&e&&this.scale(e.x/t.x,e.y/t.y)},getMatrix:function(){return this._matrix},setMatrix:function(){var t=this._matrix;t.set.apply(t,arguments)},transform:function(t){this._matrix.append(t)},scrollBy:function(){this.translate(L.read(arguments).negate())}}),{projectToView:function(){return this._matrix._transformPoint(L.read(arguments))},viewToProject:function(){return this._matrix._inverseTransform(L.read(arguments))},getEventPoint:function(t){return this.viewToProject(_t.getOffset(t,this._element))}},{statics:{_views:[],_viewsById:{},_id:0,create:function(t,e){vt&&typeof e=="string"&&(e=vt.getElementById(e));var i=lt?ge:ot;return new i(t,e)}}},new function(){if(!lt)return;var t,e,i=!1,n=!1;function r(z){var M=_t.getTarget(z);return M.getAttribute&&ot._viewsById[M.getAttribute("id")]}function s(){var z=ot._focused;if(!z||!z.isVisible()){for(var M=0,k=ot._views.length;M<k;M++)if((z=ot._views[M]).isVisible()){ot._focused=e=z;break}}}function h(z,M,k){z._handleMouseEvent("mousemove",M,k)}var a=lt.navigator,o,u,_;a.pointerEnabled||a.msPointerEnabled?(o="pointerdown MSPointerDown",u="pointermove MSPointerMove",_="pointerup pointercancel MSPointerUp MSPointerCancel"):(o="touchstart",u="touchmove",_="touchend touchcancel","ontouchstart"in lt&&a.userAgent.match(/mobile|tablet|ip(ad|hone|od)|android|silk/i)||(o+=" mousedown",u+=" mousemove",_+=" mouseup"));var d={},c={mouseout:function(z){var M=ot._focused,k=_t.getRelatedTarget(z);if(M&&(!k||k.nodeName==="HTML")){var E=_t.getOffset(z,M._element),N=E.x,B=Math.abs,F=B(N),V=1<<25,W=F-V;E.x=B(W)<F?W*(N<0?-1:1):N,h(M,z,M.viewToProject(E))}},scroll:s};d[o]=function(z){var M=ot._focused=r(z);i||(i=!0,M._handleMouseEvent("mousedown",z))},c[u]=function(z){var M=ot._focused;if(!n){var k=r(z);k?M!==k&&(M&&h(M,z),t||(t=M),M=ot._focused=e=k):e&&e===M&&(t&&!t.isInserted()&&(t=null),M=ot._focused=t,t=null,s())}M&&h(M,z)},c[o]=function(){n=!0},c[_]=function(z){var M=ot._focused;M&&i&&M._handleMouseEvent("mouseup",z),n=i=!1},_t.add(vt,c),_t.add(lt,{load:s});var l=!1,p=!1,y={doubleclick:"click",mousedrag:"mousemove"},C=!1,v,m,f,g,w,S,x,b,P;function I(z,M,k,E,N,B,F){var V=!1,W;function H(J,q){if(J.responds(q)){if(W||(W=new pe(q,E,N,M||J,B?N.subtract(B):null)),J.emit(q,W)&&(l=!0,W.prevented&&(p=!0),W.stopped))return V=!0}else{var $=y[q];if($)return H(J,$)}}for(;z&&z!==F&&!H(z,k);)z=z._parent;return V}function O(z,M,k,E,N,B){return z._project.removeOn(k),p=l=!1,S&&I(S,null,k,E,N,B)||M&&M!==S&&!M.isDescendant(S)&&I(M,null,k==="mousedrag"?"mousemove":k,E,N,B,S)||I(z,S||M||z,k,E,N,B)}var A={mousedown:{mousedown:1,mousedrag:1,click:1,doubleclick:1},mouseup:{mouseup:1,mousedrag:1,click:1,doubleclick:1},mousemove:{mousedrag:1,mousemove:1,mouseenter:1,mouseleave:1}};return{_viewEvents:d,_handleMouseEvent:function(z,M,k){var E=this._itemEvents,N=E.native[z],B=z==="mousemove",F=this._scope.tool,V=this;function W(Y){return E.virtual[Y]||V.responds(Y)||F&&F.responds(Y)}B&&i&&W("mousedrag")&&(z="mousedrag"),k||(k=this.getEventPoint(M));var H=this.getBounds().contains(k),J=N&&H&&V._project.hitTest(k,{tolerance:0,fill:!0,stroke:!0}),q=J&&J.item||null,$=!1,U={};if(U[z.substr(5)]=!0,N&&q!==w&&(w&&I(w,null,"mouseleave",M,k),q&&I(q,null,"mouseenter",M,k),w=q),C^H&&(I(this,null,H?"mouseenter":"mouseleave",M,k),v=H?this:null,$=!0),(H||U.drag)&&!k.equals(f)&&(O(this,q,B?z:"mousemove",M,k,f),$=!0),C=H,U.down&&H||U.up&&m){if(O(this,q,z,M,k,m),U.down){if(P=q===x&&Date.now()-b<300,g=x=q,!p&&q){for(var rt=q;rt&&!rt.responds("mousedrag");)rt=rt._parent;rt&&(S=q)}m=k}else U.up&&(!p&&q===g&&(b=Date.now(),O(this,q,P?"doubleclick":"click",M,k,m),P=!1),g=S=null);C=!1,$=!0}f=k,$&&F&&(l=F._handleMouseEvent(z,M,k,U)||l),M.cancelable!==!1&&(l&&!U.move||U.down&&W("mouseup"))&&M.preventDefault()},_handleKeyEvent:function(z,M,k,E){var N=this._scope,B=N.tool,F;function V(W){W.responds(z)&&(Q=N,W.emit(z,F=F||new ve(z,M,k,E)))}this.isVisible()&&(V(this),B&&B.responds(z)&&V(B))},_countItemEvent:function(z,M){var k=this._itemEvents,E=k.native,N=k.virtual;for(var B in A)E[B]=(E[B]||0)+(A[B][z]||0)*M;N[z]=(N[z]||0)+M},statics:{updateFocus:s,_resetState:function(){i=n=l=C=!1,t=e=v=m=f=g=w=S=x=b=P=null}}}}),ge=ot.extend({_class:"CanvasView",initialize:function(e,i){if(!(i instanceof lt.HTMLCanvasElement)){var n=Z.read(arguments,1);if(n.isZero())throw new Error("Cannot create CanvasView with the provided argument: "+T.slice(arguments,1));i=ft.getCanvas(n)}var r=this._context=i.getContext("2d");if(r.save(),this._pixelRatio=1,!/^off|false$/.test(Rt.getAttribute(i,"hidpi"))){var s=lt.devicePixelRatio||1,h=ht.getPrefixed(r,"backingStorePixelRatio")||1;this._pixelRatio=s/h}ot.call(this,e,i),this._needsUpdate=!0},remove:function t(){return this._context.restore(),t.base.call(this)},_setElementSize:function t(e,i){var n=this._pixelRatio;if(t.base.call(this,e*n,i*n),n!==1){var r=this._element,s=this._context;if(!Rt.hasAttribute(r,"resize")){var h=r.style;h.width=e+"px",h.height=i+"px"}s.restore(),s.save(),s.scale(n,n)}},getContext:function(){return this._context},getPixelSize:function t(e){var i=Q.agent,n;if(i&&i.firefox)n=t.base.call(this,e);else{var r=this._context,s=r.font;r.font=e+" serif",n=parseFloat(r.font),r.font=s}return n},getTextWidth:function(t,e){var i=this._context,n=i.font,r=0;i.font=t;for(var s=0,h=e.length;s<h;s++)r=Math.max(r,i.measureText(e[s]).width);return i.font=n,r},update:function(){if(!this._needsUpdate)return!1;var t=this._project,e=this._context,i=this._viewSize;return e.clearRect(0,0,i.width+1,i.height+1),t&&t.draw(e,this._matrix,this._pixelRatio),this._needsUpdate=!1,!0}}),ie=T.extend({_class:"Event",initialize:function(e){this.event=e,this.type=e&&e.type},prevented:!1,stopped:!1,preventDefault:function(){this.prevented=!0,this.event.preventDefault()},stopPropagation:function(){this.stopped=!0,this.event.stopPropagation()},stop:function(){this.stopPropagation(),this.preventDefault()},getTimeStamp:function(){return this.event.timeStamp},getModifiers:function(){return le.modifiers}}),ve=ie.extend({_class:"KeyEvent",initialize:function(e,i,n,r){this.type=e,this.event=i,this.key=n,this.character=r},toString:function(){return"{ type: '"+this.type+"', key: '"+this.key+"', character: '"+this.character+"', modifiers: "+this.getModifiers()+" }"}}),le=new function(){var t={"	":"tab"," ":"space","\b":"backspace","\x7F":"delete",Spacebar:"space",Del:"delete",Win:"meta",Esc:"escape"},e={tab:"	",space:" ",enter:"\r"},i={},n={},r,s,h=new T({shift:!1,control:!1,alt:!1,meta:!1,capsLock:!1,space:!1}).inject({option:{get:function(){return this.alt}},command:{get:function(){var u=Q&&Q.agent;return u&&u.mac?this.meta:this.control}}});function a(u){var _=u.key||u.keyIdentifier;return _=/^U\+/.test(_)?String.fromCharCode(parseInt(_.substr(2),16)):/^Arrow[A-Z]/.test(_)?_.substr(5):_==="Unidentified"||_===D?String.fromCharCode(u.keyCode):_,t[_]||(_.length>1?T.hyphenate(_):_.toLowerCase())}function o(u,_,d,c){var l=u?"keydown":"keyup",p=ot._focused,y;if(i[_]=u,u?n[_]=d:delete n[_],_.length>1&&(y=T.camelize(_))in h){h[y]=u;var C=Q&&Q.agent;if(y==="meta"&&C&&C.mac)if(u)r={};else{for(var v in r)v in n&&o(!1,v,r[v],c);r=null}}else u&&r&&(r[_]=d);p&&p._handleKeyEvent(u?"keydown":"keyup",c,_,d)}return _t.add(vt,{keydown:function(u){var _=a(u),d=Q&&Q.agent;_.length>1||d&&d.chrome&&(u.altKey||d.mac&&u.metaKey||!d.mac&&u.ctrlKey)?o(!0,_,e[_]||(_.length>1?"":_),u):s=_},keypress:function(u){if(s){var _=a(u),d=u.charCode,c=d>=32?String.fromCharCode(d):_.length>1?"":_;_!==s&&(_=c.toLowerCase()),o(!0,_,c,u),s=null}},keyup:function(u){var _=a(u);_ in n&&o(!1,_,n[_],u)}}),_t.add(lt,{blur:function(u){for(var _ in n)o(!1,_,n[_],u)}}),{modifiers:h,isDown:function(u){return!!i[u]}}},pe=ie.extend({_class:"MouseEvent",initialize:function(e,i,n,r,s){this.type=e,this.event=i,this.point=n,this.target=r,this.delta=s},toString:function(){return"{ type: '"+this.type+"', point: "+this.point+", target: "+this.target+(this.delta?", delta: "+this.delta:"")+", modifiers: "+this.getModifiers()+" }"}}),me=ie.extend({_class:"ToolEvent",_item:null,initialize:function(e,i,n){this.tool=e,this.type=i,this.event=n},_choosePoint:function(t,e){return t||(e?e.clone():null)},getPoint:function(){return this._choosePoint(this._point,this.tool._point)},setPoint:function(t){this._point=t},getLastPoint:function(){return this._choosePoint(this._lastPoint,this.tool._lastPoint)},setLastPoint:function(t){this._lastPoint=t},getDownPoint:function(){return this._choosePoint(this._downPoint,this.tool._downPoint)},setDownPoint:function(t){this._downPoint=t},getMiddlePoint:function(){return!this._middlePoint&&this.tool._lastPoint?this.tool._point.add(this.tool._lastPoint).divide(2):this._middlePoint},setMiddlePoint:function(t){this._middlePoint=t},getDelta:function(){return!this._delta&&this.tool._lastPoint?this.tool._point.subtract(this.tool._lastPoint):this._delta},setDelta:function(t){this._delta=t},getCount:function(){return this.tool[/^mouse(down|up)$/.test(this.type)?"_downCount":"_moveCount"]},setCount:function(t){this.tool[/^mouse(down|up)$/.test(this.type)?"downCount":"count"]=t},getItem:function(){if(!this._item){var t=this.tool._scope.project.hitTest(this.getPoint());if(t){for(var e=t.item,i=e._parent;/^(Group|CompoundPath)$/.test(i._class);)e=i,i=i._parent;this._item=e}}return this._item},setItem:function(t){this._item=t},toString:function(){return"{ type: "+this.type+", point: "+this.getPoint()+", count: "+this.getCount()+", modifiers: "+this.getModifiers()+" }"}}),xe=$t.extend({_class:"Tool",_list:"tools",_reference:"tool",_events:["onMouseDown","onMouseUp","onMouseDrag","onMouseMove","onActivate","onDeactivate","onEditOptions","onKeyDown","onKeyUp"],initialize:function(e){$t.call(this),this._moveCount=-1,this._downCount=-1,this.set(e)},getMinDistance:function(){return this._minDistance},setMinDistance:function(t){this._minDistance=t,t!=null&&this._maxDistance!=null&&t>this._maxDistance&&(this._maxDistance=t)},getMaxDistance:function(){return this._maxDistance},setMaxDistance:function(t){this._maxDistance=t,this._minDistance!=null&&t!=null&&t<this._minDistance&&(this._minDistance=t)},getFixedDistance:function(){return this._minDistance==this._maxDistance?this._minDistance:null},setFixedDistance:function(t){this._minDistance=this._maxDistance=t},_handleMouseEvent:function(t,e,i,n){Q=this._scope,n.drag&&!this.responds(t)&&(t="mousemove");var r=n.move||n.drag,s=this.responds(t),h=!1,a=this;function o(_,d){var c=i,l=r?a._point:a._downPoint||c;if(r){if(a._moveCount>=0&&c.equals(l))return!1;if(l&&(_!=null||d!=null)){var p=c.subtract(l),y=p.getLength();if(y<(_||0))return!1;d&&(c=l.add(p.normalize(Math.min(y,d))))}a._moveCount++}return a._point=c,a._lastPoint=l||c,n.down&&(a._moveCount=-1,a._downPoint=c,a._downCount++),!0}function u(){s&&(h=a.emit(t,new me(a,t,e))||h)}if(n.down)o(),u();else if(n.up)o(null,this._maxDistance),u();else if(s)for(;o(this._minDistance,this._maxDistance);)u();return h}}),we=T.extend(Xt,{_class:"Tween",statics:{easings:new T({linear:function(t){return t},easeInQuad:function(t){return t*t},easeOutQuad:function(t){return t*(2-t)},easeInOutQuad:function(t){return t<.5?2*t*t:-1+2*(2-t)*t},easeInCubic:function(t){return t*t*t},easeOutCubic:function(t){return--t*t*t+1},easeInOutCubic:function(t){return t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1},easeInQuart:function(t){return t*t*t*t},easeOutQuart:function(t){return 1- --t*t*t*t},easeInOutQuart:function(t){return t<.5?8*t*t*t*t:1-8*--t*t*t*t},easeInQuint:function(t){return t*t*t*t*t},easeOutQuint:function(t){return 1+--t*t*t*t*t},easeInOutQuint:function(t){return t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t}})},initialize:function t(e,i,n,r,s,h){this.object=e;var a=typeof s,o=a==="function";this.type=o?a:a==="string"?s:"linear",this.easing=o?s:t.easings[this.type],this.duration=r,this.running=!1,this._then=null,this._startTime=null;var u=i||n;this._keys=u?Object.keys(u):[],this._parsedKeys=this._parseKeys(this._keys),this._from=u&&this._getState(i),this._to=u&&this._getState(n),h!==!1&&this.start()},then:function(t){return this._then=t,this},start:function(){return this._startTime=null,this.running=!0,this},stop:function(){return this.running=!1,this},update:function(t){if(this.running){t>=1&&(t=1,this.running=!1);for(var e=this.easing(t),i=this._keys,n=function(_){return typeof _=="function"?_(e,t):_},r=0,s=i&&i.length;r<s;r++){var h=i[r],a=n(this._from[h]),o=n(this._to[h]),u=a&&o&&a.__add&&o.__add?o.__subtract(a).__multiply(e).__add(a):(o-a)*e+a;this._setProperty(this._parsedKeys[h],u)}this.responds("update")&&this.emit("update",new T({progress:t,factor:e})),!this.running&&this._then&&this._then(this.object)}return this},_events:{onUpdate:{}},_handleFrame:function(t){var e=this._startTime,i=e?(t-e)/this.duration:0;e||(this._startTime=t),this.update(i)},_getState:function(t){for(var e=this._keys,i={},n=0,r=e.length;n<r;n++){var s=e[n],h=this._parsedKeys[s],a=this._getProperty(h),o;if(t){var u=this._resolveValue(a,t[s]);this._setProperty(h,u),o=this._getProperty(h),o=o&&o.clone?o.clone():o,this._setProperty(h,a)}else o=a&&a.clone?a.clone():a;i[s]=o}return i},_resolveValue:function(t,e){if(e){if(Array.isArray(e)&&e.length===2){var i=e[0];return i&&i.match&&i.match(/^[+\-\*\/]=/)?this._calculate(t,i[0],e[1]):e}else if(typeof e=="string"){var n=e.match(/^[+\-*/]=(.*)/);if(n){var r=JSON.parse(n[1].replace(/(['"])?([a-zA-Z0-9_]+)(['"])?:/g,'"$2": '));return this._calculate(t,e[0],r)}}}return e},_calculate:function(t,e,i){return Q.PaperScript.calculateBinary(t,e,i)},_parseKeys:function(t){for(var e={},i=0,n=t.length;i<n;i++){var r=t[i],s=r.replace(/\.([^.]*)/g,"/$1").replace(/\[['"]?([^'"\]]*)['"]?\]/g,"/$1");e[r]=s.split("/")}return e},_getProperty:function(t,e){for(var i=this.object,n=0,r=t.length-(e||0);n<r&&i;n++)i=i[t[n]];return i},_setProperty:function(t,e){var i=this._getProperty(t,1);i&&(i[t[t.length-1]]=e)}}),ye={request:function(t){var e=new zt.XMLHttpRequest;return e.open((t.method||"get").toUpperCase(),t.url,T.pick(t.async,!0)),t.mimeType&&e.overrideMimeType(t.mimeType),e.onload=function(){var i=e.status;i===0||i===200?t.onLoad&&t.onLoad.call(e,e.responseText):e.onerror()},e.onerror=function(){var i=e.status,n='Could not load "'+t.url+'" (Status: '+i+")";if(t.onError)t.onError(n,i);else throw new Error(n)},e.send(null)}},ft=T.exports.CanvasProvider={canvases:[],getCanvas:function(t,e,i){if(!lt)return null;var n,r=!0;typeof t=="object"&&(e=t.height,t=t.width),this.canvases.length?n=this.canvases.pop():(n=vt.createElement("canvas"),r=!1);var s=n.getContext("2d",i||{});if(!s)throw new Error("Canvas "+n+" is unable to provide a 2D context.");return n.width===t&&n.height===e?r&&s.clearRect(0,0,t+1,e+1):(n.width=t,n.height=e),s.save(),n},getContext:function(t,e,i){var n=this.getCanvas(t,e,i);return n?n.getContext("2d",i||{}):null},release:function(t){var e=t&&t.canvas?t.canvas:t;e&&e.getContext&&(e.getContext("2d").restore(),this.canvases.push(e))}},oe=new function(){var t=Math.min,e=Math.max,i=Math.abs,n,r,s,h,a,o,u,_,d,c,l;function p(w,S,x){return .2989*w+.587*S+.114*x}function y(w,S,x,I){var P=I-p(w,S,x);d=w+P,c=S+P,l=x+P;var I=p(d,c,l),O=t(d,c,l),A=e(d,c,l);if(O<0){var z=I-O;d=I+(d-I)*I/z,c=I+(c-I)*I/z,l=I+(l-I)*I/z}if(A>255){var M=255-I,k=A-I;d=I+(d-I)*M/k,c=I+(c-I)*M/k,l=I+(l-I)*M/k}}function C(w,S,x){return e(w,S,x)-t(w,S,x)}function v(w,S,x,b){var P=[w,S,x],I=e(w,S,x),O=t(w,S,x),A;O=O===w?0:O===S?1:2,I=I===w?0:I===S?1:2,A=t(O,I)===0?e(O,I)===1?2:1:0,P[I]>P[O]?(P[A]=(P[A]-P[O])*b/(P[I]-P[O]),P[I]=b):P[A]=P[I]=0,P[O]=0,d=P[0],c=P[1],l=P[2]}var m={multiply:function(){d=a*n/255,c=o*r/255,l=u*s/255},screen:function(){d=a+n-a*n/255,c=o+r-o*r/255,l=u+s-u*s/255},overlay:function(){d=a<128?2*a*n/255:255-2*(255-a)*(255-n)/255,c=o<128?2*o*r/255:255-2*(255-o)*(255-r)/255,l=u<128?2*u*s/255:255-2*(255-u)*(255-s)/255},"soft-light":function(){var w=n*a/255;d=w+a*(255-(255-a)*(255-n)/255-w)/255,w=r*o/255,c=w+o*(255-(255-o)*(255-r)/255-w)/255,w=s*u/255,l=w+u*(255-(255-u)*(255-s)/255-w)/255},"hard-light":function(){d=n<128?2*n*a/255:255-2*(255-n)*(255-a)/255,c=r<128?2*r*o/255:255-2*(255-r)*(255-o)/255,l=s<128?2*s*u/255:255-2*(255-s)*(255-u)/255},"color-dodge":function(){d=a===0?0:n===255?255:t(255,255*a/(255-n)),c=o===0?0:r===255?255:t(255,255*o/(255-r)),l=u===0?0:s===255?255:t(255,255*u/(255-s))},"color-burn":function(){d=a===255?255:n===0?0:e(0,255-(255-a)*255/n),c=o===255?255:r===0?0:e(0,255-(255-o)*255/r),l=u===255?255:s===0?0:e(0,255-(255-u)*255/s)},darken:function(){d=a<n?a:n,c=o<r?o:r,l=u<s?u:s},lighten:function(){d=a>n?a:n,c=o>r?o:r,l=u>s?u:s},difference:function(){d=a-n,d<0&&(d=-d),c=o-r,c<0&&(c=-c),l=u-s,l<0&&(l=-l)},exclusion:function(){d=a+n*(255-a-a)/255,c=o+r*(255-o-o)/255,l=u+s*(255-u-u)/255},hue:function(){v(n,r,s,C(a,o,u)),y(d,c,l,p(a,o,u))},saturation:function(){v(a,o,u,C(n,r,s)),y(d,c,l,p(a,o,u))},luminosity:function(){y(a,o,u,p(n,r,s))},color:function(){y(n,r,s,p(a,o,u))},add:function(){d=t(a+n,255),c=t(o+r,255),l=t(u+s,255)},subtract:function(){d=e(a-n,0),c=e(o-r,0),l=e(u-s,0)},average:function(){d=(a+n)/2,c=(o+r)/2,l=(u+s)/2},negation:function(){d=255-i(255-n-a),c=255-i(255-r-o),l=255-i(255-s-u)}},f=this.nativeModes=T.each(["source-over","source-in","source-out","source-atop","destination-over","destination-in","destination-out","destination-atop","lighter","darker","copy","xor"],function(w){this[w]=!0},{}),g=ft.getContext(1,1,{willReadFrequently:!0});g&&(T.each(m,function(w,S){var x=S==="darken",b=!1;g.save();try{g.fillStyle=x?"#300":"#a00",g.fillRect(0,0,1,1),g.globalCompositeOperation=S,g.globalCompositeOperation===S&&(g.fillStyle=x?"#a00":"#300",g.fillRect(0,0,1,1),b=g.getImageData(0,0,1,1).data[0]!==x?170:51)}catch(P){}g.restore(),f[S]=b}),ft.release(g)),this.process=function(w,S,x,b,P){var I=S.canvas,O=w==="normal";if(O||f[w])x.save(),x.setTransform(1,0,0,1,0,0),x.globalAlpha=b,O||(x.globalCompositeOperation=w),x.drawImage(I,P.x,P.y),x.restore();else{var A=m[w];if(!A)return;for(var z=x.getImageData(P.x,P.y,I.width,I.height),M=z.data,k=S.getImageData(0,0,I.width,I.height).data,E=0,N=M.length;E<N;E+=4){n=k[E],a=M[E],r=k[E+1],o=M[E+1],s=k[E+2],u=M[E+2],h=k[E+3],_=M[E+3],A();var B=h*b/255,F=1-B;M[E]=B*d+F*a,M[E+1]=B*c+F*o,M[E+2]=B*l+F*u,M[E+3]=h*b+F*_}x.putImageData(z,P.x,P.y)}}},ut=new function(){var t="http://www.w3.org/2000/svg",e="http://www.w3.org/2000/xmlns",i="http://www.w3.org/1999/xlink",n={href:i,xlink:e,xmlns:e+"/","xmlns:xlink":e+"/"};function r(a,o,u){return h(vt.createElementNS(t,a),o,u)}function s(a,o){var u=n[o],_=u?a.getAttributeNS(u,o):a.getAttribute(o);return _==="null"?null:_}function h(a,o,u){for(var _ in o){var d=o[_],c=n[_];typeof d=="number"&&u&&(d=u.number(d)),c?a.setAttributeNS(c,_,d):a.setAttribute(_,d)}return a}return{svg:t,xmlns:e,xlink:i,create:r,get:s,set:h}},fe=T.each({fillColor:["fill","color"],fillRule:["fill-rule","string"],strokeColor:["stroke","color"],strokeWidth:["stroke-width","number"],strokeCap:["stroke-linecap","string"],strokeJoin:["stroke-linejoin","string"],strokeScaling:["vector-effect","lookup",{true:"none",false:"non-scaling-stroke"},function(t,e){return!e&&(t instanceof Zt||t instanceof xt||t instanceof te)}],miterLimit:["stroke-miterlimit","number"],dashArray:["stroke-dasharray","array"],dashOffset:["stroke-dashoffset","number"],fontFamily:["font-family","string"],fontWeight:["font-weight","string"],fontSize:["font-size","number"],justification:["text-anchor","lookup",{left:"start",center:"middle",right:"end"}],opacity:["opacity","number"],blendMode:["mix-blend-mode","style"]},function(t,e){var i=T.capitalize(e),n=t[2];this[e]={type:t[1],property:e,attribute:t[0],toSVG:n,fromSVG:n&&T.each(n,function(r,s){this[r]=s},{}),exportFilter:t[3],get:"get"+i,set:"set"+i}},{});new function(){var t;function e(m,f,g){var w=new T,S=m.getTranslation();if(f){var x;m.isInvertible()?(m=m._shiftless(),x=m._inverseTransform(S),S=null):x=new L,w[g?"cx":"x"]=x.x,w[g?"cy":"y"]=x.y}if(!m.isIdentity()){var b=m.decompose();if(b){var P=[],I=b.rotation,O=b.scaling,A=b.skewing;S&&!S.isZero()&&P.push("translate("+t.point(S)+")"),I&&P.push("rotate("+t.number(I)+")"),(!et.isZero(O.x-1)||!et.isZero(O.y-1))&&P.push("scale("+t.point(O)+")"),A.x&&P.push("skewX("+t.number(A.x)+")"),A.y&&P.push("skewY("+t.number(A.y)+")"),w.transform=P.join(" ")}else w.transform="matrix("+m.getValues().join(",")+")"}return w}function i(m,f){for(var g=e(m._matrix),w=m._children,S=ut.create("g",g,t),x=0,b=w.length;x<b;x++){var P=w[x],I=C(P,f);if(I)if(P.isClipMask()){var O=ut.create("clipPath");O.appendChild(I),p(P,O,"clip"),ut.set(S,{"clip-path":"url(#"+O.id+")"})}else S.appendChild(I)}return S}function n(m,f){var g=e(m._matrix,!0),w=m.getSize(),S=m.getImage();return g.x-=w.width/2,g.y-=w.height/2,g.width=w.width,g.height=w.height,g.href=f.embedImages==!1&&S&&S.src||m.toDataURL(),ut.create("image",g,t)}function r(m,f){var g=f.matchShapes;if(g){var w=m.toShape(!1);if(w)return s(w,f)}var S=m._segments,x=S.length,b,P=e(m._matrix);if(g&&x>=2&&!m.hasHandles())if(x>2){b=m._closed?"polygon":"polyline";for(var I=[],O=0;O<x;O++)I.push(t.point(S[O]._point));P.points=I.join(" ")}else{b="line";var A=S[0]._point,z=S[1]._point;P.set({x1:A.x,y1:A.y,x2:z.x,y2:z.y})}else b="path",P.d=m.getPathData(null,f.precision);return ut.create(b,P,t)}function s(m){var f=m._type,g=m._radius,w=e(m._matrix,!0,f!=="rectangle");if(f==="rectangle"){f="rect";var S=m._size,x=S.width,b=S.height;w.x-=x/2,w.y-=b/2,w.width=x,w.height=b,g.isZero()&&(g=null)}return g&&(f==="circle"?w.r=g:(w.rx=g.width,w.ry=g.height)),ut.create(f,w,t)}function h(m,f){var g=e(m._matrix),w=m.getPathData(null,f.precision);return w&&(g.d=w),ut.create("path",g,t)}function a(m,f){var g=e(m._matrix,!0),w=m._definition,S=l(w,"symbol"),x=w._item,b=x.getStrokeBounds();return S||(S=ut.create("symbol",{viewBox:t.rectangle(b)}),S.appendChild(C(x,f)),p(w,S,"symbol")),g.href="#"+S.id,g.x+=b.x,g.y+=b.y,g.width=b.width,g.height=b.height,g.overflow="visible",ut.create("use",g,t)}function o(m){var f=l(m,"color");if(!f){var g=m.getGradient(),w=g._radial,S=m.getOrigin(),x=m.getDestination(),b;if(w){b={cx:S.x,cy:S.y,r:S.getDistance(x)};var P=m.getHighlight();P&&(b.fx=P.x,b.fy=P.y)}else b={x1:S.x,y1:S.y,x2:x.x,y2:x.y};b.gradientUnits="userSpaceOnUse",f=ut.create((w?"radial":"linear")+"Gradient",b,t);for(var I=g._stops,O=0,A=I.length;O<A;O++){var z=I[O],M=z._color,k=M.getAlpha(),E=z._offset;b={offset:E==null?O/(A-1):E},M&&(b["stop-color"]=M.toCSS(!0)),k<1&&(b["stop-opacity"]=k),f.appendChild(ut.create("stop",b,t))}p(m,f,"color")}return"url(#"+f.id+")"}function u(m){var f=ut.create("text",e(m._matrix,!0),t);return f.textContent=m._content,f}var _={Group:i,Layer:i,Raster:n,Path:r,Shape:s,CompoundPath:h,SymbolItem:a,PointText:u};function d(m,f,g,w){var S={},x=!w&&m.getParent(),b=[];return m._name!=null&&(S.id=m._name),T.each(fe,function(P){var I=P.get,O=P.type,A=m[I]();if(P.exportFilter?P.exportFilter(m,A):g.reduceAttributes==!1||!x||!T.equals(x[I](),A)){if(O==="color"&&A!=null){var z=A.getAlpha();z<1&&(S[P.attribute+"-opacity"]=z)}O==="style"?b.push(P.attribute+": "+A):S[P.attribute]=A==null?"none":O==="color"?A.gradient?o(A,m):A.toCSS(!0):O==="array"?A.join(","):O==="lookup"?P.toSVG[A]:A}}),b.length&&(S.style=b.join(";")),S.opacity===1&&delete S.opacity,m._visible||(S.visibility="hidden"),ut.set(f,S,t)}var c;function l(m,f){return c||(c={ids:{},svgs:{}}),m&&c.svgs[f+"-"+(m._id||m.__id||(m.__id=jt.get("svg")))]}function p(m,f,g){c||l();var w=c.ids[g]=(c.ids[g]||0)+1;f.id=g+"-"+w,c.svgs[g+"-"+(m._id||m.__id)]=f}function y(m,f){var g=m,w=null;if(c){g=m.nodeName.toLowerCase()==="svg"&&m;for(var S in c.svgs)w||(g||(g=ut.create("svg"),g.appendChild(m)),w=g.insertBefore(ut.create("defs"),g.firstChild)),w.appendChild(c.svgs[S]);c=null}return f.asString?new zt.XMLSerializer().serializeToString(g):g}function C(m,f,g){var w=_[m._class],S=w&&w(m,f);if(S){var x=f.onExport;x&&(S=x(m,S,f)||S);var b=JSON.stringify(m._data);b&&b!=="{}"&&b!=="null"&&S.setAttribute("data-paper-data",b)}return S&&d(m,S,f,g)}function v(m){return m||(m={}),t=new bt(m.precision),m}X.inject({exportSVG:function(m){return m=v(m),y(C(this,m,!0),m)}}),Qt.inject({exportSVG:function(m){m=v(m);var f=this._children,g=this.getView(),w=T.pick(m.bounds,"view"),S=m.matrix||w==="view"&&g._matrix,x=S&&st.read([S]),b=w==="view"?new K([0,0],g.getViewSize()):w==="content"?X._getBounds(f,x,{stroke:!0}).rect:K.read([w],0,{readNull:!0}),P={version:"1.1",xmlns:ut.svg,"xmlns:xlink":ut.xlink};b&&(P.width=b.width,P.height=b.height,(b.x||b.x===0||b.y||b.y===0)&&(P.viewBox=t.rectangle(b)));var I=ut.create("svg",P,t),O=I;x&&!x.isIdentity()&&(O=I.appendChild(ut.create("g",e(x),t)));for(var A=0,z=f.length;A<z;A++)O.appendChild(C(f[A],m,!0));return y(I,m)}})},new function(){var t={},e;function i(f,g,w,S,x,b){var P=ut.get(f,g)||b,I=P==null?S?null:w?"":0:w?P:parseFloat(P);return/%\s*$/.test(P)?I/100*(x?1:e[/x|^width/.test(g)?"width":"height"]):I}function n(f,g,w,S,x,b,P){return g=i(f,g||"x",!1,S,x,b),w=i(f,w||"y",!1,S,x,P),S&&(g==null||w==null)?null:new L(g,w)}function r(f,g,w,S,x){return g=i(f,g||"width",!1,S,x),w=i(f,w||"height",!1,S,x),S&&(g==null||w==null)?null:new Z(g,w)}function s(f,g,w){return f==="none"?null:g==="number"?parseFloat(f):g==="array"?f?f.split(/[\s,]+/g).map(parseFloat):[]:g==="color"?C(f)||f:g==="lookup"?w[f]:f}function h(f,g,w,S){var x=f.childNodes,b=g==="clippath",P=g==="defs",I=new At,O=I._project,A=O._currentStyle,z=[];if(!b&&!P&&(I=y(I,f,S),O._currentStyle=I._style.clone()),S)for(var M=f.querySelectorAll("defs"),k=0,E=M.length;k<E;k++)v(M[k],w,!1);for(var k=0,E=x.length;k<E;k++){var N=x[k],B;N.nodeType===1&&!/^defs$/i.test(N.nodeName)&&(B=v(N,w,!1))&&!(B instanceof Bt)&&z.push(B)}return I.addChildren(z),b&&(I=y(I.reduce(),f,S)),O._currentStyle=A,(b||P)&&(I.remove(),I=null),I}function a(f,g){for(var w=f.getAttribute("points").match(/[+-]?(?:\d*\.\d+|\d+\.?)(?:[eE][+-]?\d+)?/g),S=[],x=0,b=w.length;x<b;x+=2)S.push(new L(parseFloat(w[x]),parseFloat(w[x+1])));var P=new nt(S);return g==="polygon"&&P.closePath(),P}function o(f){return Zt.create(f.getAttribute("d"))}function u(f,g){var w=(i(f,"href",!0)||"").substring(1),S=g==="radialgradient",x;if(w)x=t[w].getGradient(),x._radial^S&&(x=x.clone(),x._radial=S);else{for(var b=f.childNodes,P=[],I=0,O=b.length;I<O;I++){var A=b[I];A.nodeType===1&&P.push(y(new ae,A))}x=new ee(P,S)}var z,M,k,E=i(f,"gradientUnits",!0)!=="userSpaceOnUse";S?(z=n(f,"cx","cy",!1,E,"50%","50%"),M=z.add(i(f,"r",!1,!1,E,"50%"),0),k=n(f,"fx","fy",!0,E)):(z=n(f,"x1","y1",!1,E,"0%","0%"),M=n(f,"x2","y2",!1,E,"100%","0%"));var N=y(new ct(x,z,M,k),f);return N._scaleToBounds=E,null}var _={"#document":function(f,g,w,S){for(var x=f.childNodes,b=0,P=x.length;b<P;b++){var I=x[b];if(I.nodeType===1)return v(I,w,S)}},g:h,svg:h,clippath:h,polygon:a,polyline:a,path:o,lineargradient:u,radialgradient:u,image:function(f){var g=new Kt(i(f,"href",!0));return g.on("load",function(){var w=r(f);this.setSize(w);var S=n(f).add(w.divide(2));this._matrix.append(new st().translate(S))}),g},symbol:function(f,g,w,S){return new Bt(h(f,g,w,S),!0)},defs:h,use:function(f){var g=(i(f,"href",!0)||"").substring(1),w=t[g],S=n(f);return w?w instanceof Bt?w.place(S):w.clone().translate(S):null},circle:function(f){return new xt.Circle(n(f,"cx","cy"),i(f,"r"))},ellipse:function(f){return new xt.Ellipse({center:n(f,"cx","cy"),radius:r(f,"rx","ry")})},rect:function(f){return new xt.Rectangle(new K(n(f),r(f)),r(f,"rx","ry"))},line:function(f){return new nt.Line(n(f,"x1","y1"),n(f,"x2","y2"))},text:function(f){var g=new de(n(f).add(n(f,"dx","dy")));return g.setContent(f.textContent.trim()||""),g},switch:h};function d(f,g,w,S){if(f.transform){for(var x=(S.getAttribute(w)||"").split(/\)\s*/g),b=new st,P=0,I=x.length;P<I;P++){var O=x[P];if(!O)break;for(var A=O.split(/\(\s*/),z=A[0].trim(),M=A[1].split(/[\s,]+/g),k=0,E=M.length;k<E;k++)M[k]=parseFloat(M[k]);switch(z){case"matrix":b.append(new st(M[0],M[1],M[2],M[3],M[4],M[5]));break;case"rotate":b.rotate(M[0],M[1]||0,M[2]||0);break;case"translate":b.translate(M[0],M[1]||0);break;case"scale":b.scale(M);break;case"skewX":b.skew(M[0],0);break;case"skewY":b.skew(0,M[0]);break}}f.transform(b)}}function c(f,g,w){var S=w==="fill-opacity"?"getFillColor":"getStrokeColor",x=f[S]&&f[S]();x&&x.setAlpha(parseFloat(g))}var l=T.set(T.each(fe,function(f){this[f.attribute]=function(g,w){if(g[f.set]&&(g[f.set](s(w,f.type,f.fromSVG)),f.type==="color")){var S=g[f.get]();if(S&&S._scaleToBounds){var x=g.getBounds();S.transform(new st().translate(x.getPoint()).scale(x.getSize()))}}}},{}),{id:function(f,g){t[g]=f,f.setName&&f.setName(g)},"clip-path":function(f,g){var w=C(g);if(w)if(w=w.clone(),w.setClipMask(!0),f instanceof At)f.insertChild(0,w);else return new At(w,f)},gradientTransform:d,transform:d,"fill-opacity":c,"stroke-opacity":c,visibility:function(f,g){f.setVisible&&f.setVisible(g==="visible")},display:function(f,g){f.setVisible&&f.setVisible(g!==null)},"stop-color":function(f,g){f.setColor&&f.setColor(g)},"stop-opacity":function(f,g){f._color&&f._color.setAlpha(parseFloat(g))},offset:function(f,g){if(f.setOffset){var w=g.match(/(.*)%$/);f.setOffset(w?w[1]/100:parseFloat(g))}},viewBox:function(f,g,w,S,x){var b=new K(s(g,"array")),P=r(S,null,null,!0),I,O;if(f instanceof At){var A=P?P.divide(b.getSize()):1,O=new st().scale(A).translate(b.getPoint().negate());I=f}else f instanceof Bt&&(P&&b.setSize(P),I=f._item);if(I){if(p(S,"overflow",x)!=="visible"){var z=new xt.Rectangle(b);z.setClipMask(!0),I.addChild(z)}O&&I.transform(O)}}});function p(f,g,w){var S=f.attributes[g],x=S&&S.value;if(!x&&f.style){var b=T.camelize(g);x=f.style[b],!x&&w.node[b]!==w.parent[b]&&(x=w.node[b])}return x?x==="none"?null:x:D}function y(f,g,w){var S=g.parentNode,x={node:ht.getStyles(g)||{},parent:!w&&!/^defs$/i.test(S.tagName)&&ht.getStyles(S)||{}};return T.each(l,function(b,P){var I=p(g,P,x);f=I!==D&&b(f,I,P,g,x)||f}),f}function C(f){var g=f&&f.match(/\((?:["'#]*)([^"')]+)/),w=g&&g[1],S=w&&t[lt?w.replace(lt.location.href.split("#")[0]+"#",""):w];return S&&S._scaleToBounds&&(S=S.clone(),S._scaleToBounds=!0),S}function v(f,g,w){var S=f.nodeName.toLowerCase(),x=S!=="#document",b=vt.body,P,I,O;w&&x&&(e=Q.getView().getSize(),e=r(f,null,null,!0)||e,P=ut.create("svg",{style:"stroke-width: 1px; stroke-miterlimit: 10"}),I=f.parentNode,O=f.nextSibling,P.appendChild(f),b.appendChild(P));var A=Q.settings,z=A.applyMatrix,M=A.insertItems;A.applyMatrix=!1,A.insertItems=!1;var k=_[S],E=k&&k(f,S,g,w)||null;if(A.insertItems=M,A.applyMatrix=z,E){x&&!(E instanceof At)&&(E=y(E,f,w));var N=g.onImport,B=x&&f.getAttribute("data-paper-data");N&&(E=N(f,E,g)||E),g.expandShapes&&E instanceof xt&&(E.remove(),E=E.toPath()),B&&(E._data=JSON.parse(B))}return P&&(b.removeChild(P),I&&(O?I.insertBefore(f,O):I.appendChild(f))),w&&(t={},E&&T.pick(g.applyMatrix,z)&&E.matrix.apply(!0,!0)),E}function m(f,g,w){if(!f)return null;g=typeof g=="function"?{onLoad:g}:g||{};var S=Q,x=null;function b(A){try{var z=typeof A=="object"?A:new zt.DOMParser().parseFromString(A.trim(),"image/svg+xml");if(!z.nodeName)throw z=null,new Error("Unsupported SVG source: "+f);Q=S,x=v(z,g,!0),(!g||g.insert!==!1)&&w._insertItem(D,x);var M=g.onLoad;M&&M(x,A)}catch(k){P(k)}}function P(A,z){var M=g.onError;if(M)M(A,z);else throw new Error(A)}if(typeof f=="string"&&!/^[\s\S]*</.test(f)){var I=vt.getElementById(f);I?b(I):ye.request({url:f,async:!0,onLoad:b,onError:P})}else if(typeof File<"u"&&f instanceof File){var O=new FileReader;return O.onload=function(){b(O.result)},O.onerror=function(){P(O.error)},O.readAsText(f)}else b(f);return x}X.inject({importSVG:function(f,g){return m(f,g,this)}}),Qt.inject({importSVG:function(f,g){return this.activate(),m(f,g,this)}})};var Q=new(Rt.inject(T.exports,{Base:T,Numerical:et,Key:le,DomEvent:_t,DomElement:ht,document:vt,window:lt,Symbol:Bt,PlacedSymbol:re}));return Q.agent.node&&Et(66999)(Q),Ht=Q,Ut=typeof Ht=="function"?Ht.call(kt,Et,kt,Ct):Ht,Ut!==D&&(Ct.exports=Ut),Q}).call(this,typeof self=="object"?self:null)},93017:(Ct,kt,Et)=>{"use strict";Et.r(kt),Et.d(kt,{paper:()=>Ut.a});var Ht=Et(74556),Ut=Et.n(Ht)}}]);

//# sourceMappingURL=lazy-lib-paper-5a9908939d850d4cae67.js.map