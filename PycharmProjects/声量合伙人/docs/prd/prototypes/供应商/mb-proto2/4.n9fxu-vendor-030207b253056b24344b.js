try{let Se=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},ce=new Se.Error().stack;ce&&(Se._sentryDebugIds=Se._sentryDebugIds||{},Se._sentryDebugIds[ce]="a53746ab-a560-4b49-8693-65ce8e10e651",Se._sentryDebugIdIdentifier="sentry-dbid-a53746ab-a560-4b49-8693-65ce8e10e651")}catch(Se){}{let Se=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};Se.SENTRY_RELEASE={id:"22.0.4"}}(self.webpackChunk_mb2024_mb_proto=self.webpackChunk_mb2024_mb_proto||[]).push([[908],{1076:(Se,ce,n)=>{"use strict";n.d(ce,{Ay:()=>u,X2:()=>p});const s={user:{id:0,user_id:"",user_cid:"",email:"",is_viewer:!1,name:"",mobile:null,avatar:""},org:{name:"",cid:"",logo:null,spaces:[],plan:"",trial_end_on:null,next_billing_on:"",settings:{},permissions:[],limitation:{is_export_watermark:!1,is_org_quit_transfer:!0,is_org_recent:!0,max_mkt_count:0,max_org_log_days:0,max_pic_bytes:0,max_project_count:0,max_project_share_count:0,max_project_template_count:0,max_project_version_count:0,max_recycler_days:0,max_screen_count:0,storage:0},members:[]},space:{cid:"",created_at:"",deleted:!1,name:"",org_cid:"",permissions:[],space_type:"",updated_at:"",user_id:0,is_disable_banned_viewer:!0},folderListMap:new Map,projectListMap:new Map,lang:"zh-CN"};let p=function(B){return B.UpdateState="reducer:update:user",B.UpdateUser="reducer:update:user",B.UpdateOrg="reducer:update:org",B.UpdateSpace="reducer:update:space",B.UpdateFolderListMap="reducer:update:folderListMap",B.UpdateProjectListMap="reducer:update:projectListMap",B}({});const u=function(B,q){B===void 0&&(B=s);const{type:V,payload:R}=q;switch(V){case p.UpdateState:return{...B,...R};case p.UpdateUser:return{...B,user:{...B.user,...R}};case p.UpdateOrg:return{...B,org:{...B.org,...R}};case p.UpdateSpace:return{...B,space:{...B.space,...R}};case p.UpdateFolderListMap:return{...B,folderListMap:R};case p.UpdateProjectListMap:return{...B,projectListMap:R};default:return B}}},5649:(Se,ce,n)=>{"use strict";n.d(ce,{s:()=>j,A:()=>K});var s=n(95549),p=n(38502),N=n(53732),u=n.n(N),B=n(69173),q=n.n(B),V=n(91752),R=n(39e3),J=n(23089),M=n(17307),F=n(83199),ie=n(93257),D=n(57479),pe=n(69815),H=n(67787),G=n(69594);const I=(0,H.DU)([".no-pointer-events{pointer-events:none;user-select:none;&.col-resize{cursor:",";}&.row-resize{cursor:",";}}"],G.M.ewResizeActive,G.M.nsResizeActive),v=H.Ay.label.withConfig({displayName:"styles__StyledInputNumber",componentId:"sc-1hqayf3-0"})(['position:relative;display:flex;justify-content:space-between;align-items:center;min-width:2em;border:1px solid transparent;font-family:-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;height:28px;font-size:12px;border-radius:6px;overflow:hidden;.title{display:flex;align-items:center;justify-content:center;padding:0 3px 0 5px;width:max-content;height:100%;user-select:none;color:',";&.title-cursor{&.col-resize{cursor:",";}&.row-resize{cursor:",";}}}.left-title{padding:0 13px 0 0;}.normal-right{margin-left:","px;}input{color:",";width:100%;height:100%;margin-left:8px;cursor:default;}.text-align-right{text-align:right;padding-right:2px;}.suffix{position:absolute;top:0;display:flex;align-items:center;height:100%;pointer-events:none;user-select:none;width:100%;color:",";&::before{content:attr(data-value);display:inline-block;margin-right:0.125em;width:fit-content;overflow:hidden;opacity:0;}}.action{position:absolute;top:0;right:0;width:14px;height:100%;text-align:center;visibility:hidden;svg{height:4px;fill:currentColor;fill-rule:evenodd;}button{display:block;margin:0;padding:0;width:100%;height:50%;line-height:0;color:",";&.no-transition{transition:none;}&:hover{color:",";}&:active{color:",";}&[data-action=up] svg{margin:5px 0 2px;}&[data-action=down] svg{margin:2px 0 5px;}}&.caret{width:26px;svg{width:26px;height:26px;margin-right:0;color:",";}button{height:100%;}&.active{visibility:visible;}}}&.is-disabled{> *{color:",";cursor:not-allowed;}}&.is-readOnly{pointer-events:none;.action{visibility:hidden;}}&:hover:not(.has-menu):not(.is-disabled){border:1px solid ",";.action{visibility:visible;color:#c8cdd0;}}&.is-active:not(.has-menu):not(.is-disabled){border:1px solid ",";outline:1px solid ",";outline-offset:-2px;.action{&:not(.caret){visibility:visible;}}}&.is-active-title{border:1px solid ",";outline:1px solid ",";outline-offset:-2px;cursor:pointer;}&.has-menu{border:1px solid transparent;input{width:calc(100% - 26px);border:1px solid transparent;border-radius:4px 0 0 4px;margin-left:0;padding-left:5px;}&:hover:not(.is-active):not(.is-disabled){border:1px solid ",";.action.caret{visibility:visible;background:",";svg.icon{color:",";}}}&:active:not(.is-active):not(.is-disabled){border:1px solid ",";.action.caret{visibility:visible;background:",";svg.icon{color:",";}}}&.is-active{overflow:visible;input{border:1px solid ",";outline:1px solid ",";position:relative;z-index:2;}.action.caret{visibility:visible;color:#c8cdd0;position:relative;z-index:1;svg.icon{color:",";}&:hover{background:",";border-radius:0 4px 4px 0;outline:1px solid ",";}}}}"],T=>T.theme.color_text_L3,G.M.ewResize,G.M.nsResize,T=>31-T.valueLength*6,T=>T.theme.color_text_L1,T=>T.theme.color_text_L2,T=>T.theme.color_text_L3,T=>T.theme.color_text_L2,T=>T.theme.color_text_L2,T=>T.theme.color_text_L3,T=>T.theme.color_text_disabled01,T=>T.theme.color_bg_border_02,T=>T.theme.color_proto,T=>T.theme.color_proto,T=>T.theme.color_proto,T=>T.theme.color_proto,T=>T.theme.color_bg_border_02,T=>T.theme.color_btn_secondary_hover,T=>T.theme.color_text_L1,T=>T.theme.color_bg_border_02,T=>T.theme.color_btn_secondary_active,T=>T.theme.color_text_L1,T=>T.theme.color_proto,T=>T.theme.color_proto,T=>T.theme.color_text_L1,T=>T.theme.color_btn_secondary_hover,T=>T.theme.color_bg_border_02);var C=n(72214);const f=500,y=30,h=(T,X)=>Number(Number(T).toFixed(X));function b(T,X,g){return g===void 0&&(g=0),X?T:h(T!==""&&q()(Number(T))?Number(T):"",g)}const S=function(T,X){let{shiftKey:g,metaKey:L}=T;return X===void 0&&(X=1),g?X*10:L?X*100:X},A=T=>{let{currentTarget:X}=T;return setTimeout(()=>X.select(),50)};class K extends p.PureComponent{constructor(X){super(X),(0,s.A)(this,"setElemRef",g=>this.$title=g),(0,s.A)(this,"setLabelRef",g=>this.$label=g),(0,s.A)(this,"setInputRef",g=>this.$input=g),(0,s.A)(this,"onChange",g=>{const{target:{value:L}}=g,{isEditing:ne}=this.state;!ne&&this.setState({isEditing:!0});const ee=L.trim();this.setValue(ee,g)}),(0,s.A)(this,"correctNumber",g=>{const{value:L,min:ne,max:ee,precision:se,suffix:te,parser:fe}=this.props,De=fe(g,te),Ye=h(Math.min(Math.max(De,ne),ee),se);return isNaN(Ye)?L:Ye}),(0,s.A)(this,"checkValidity",g=>/^\+$/.test(g)?this.canBePositive:/^-$/.test(g)?this.canBeNegative:g===""||isFinite(g)&&this.correctNumber(g)===Number(g)),(0,s.A)(this,"setValue",(g,L,ne)=>{L.persist&&L.persist(),this.setState({value:g},ne)}),(0,s.A)(this,"setConfirmedValue",(g,L)=>this.setValue(g,L,()=>this.onConfirm(L))),(0,s.A)(this,"getConfirmValue",g=>{const{value:L,precision:ne,shouldCorrectOnConfirm:ee,onlyPreview:se,fontSize:te,fontFamily:fe,isWRichText:De}=this.props,Ye=this.checkValidity(g);if(this.props.isDisabled||this.props.disabled)return L;const Re=this.correctNumber(g),mt=isNaN(Re)?L:Re;let Pe=g===""?L:Ye?/^[+-]$/.test(g)?0:se?Math.min(Math.max(Number(g),20),400):Number(g):Re===h(g,ne)?Re:ee?mt:L||mt;return(!Ye||g==="")&&De&&(Pe=(0,pe.qp)(fe,te)),Pe}),(0,s.A)(this,"onConfirm",g=>{const{attr:L,onConfirm:ne}=this.props,{value:ee}=this.state,se=this.getConfirmValue(ee);return this.setState({value:se,isEditing:!1},()=>ne(se,L,g,ee))}),(0,s.A)(this,"focusOnInput",g=>{try{const L=g.currentTarget.closest("label").querySelector("input");setTimeout(()=>{L.focus()})}catch(L){console.error(L)}}),(0,s.A)(this,"onStep",async g=>{g.persist(),g.nativeEvent.stopPropagation(),this.handleBeforeChangeValue();const{parser:L,suffix:ne}=this.props,{value:ee}=this.state,se=L(ee,ne),{action:te}=g.currentTarget.dataset,fe=S(g,this.props.step)*(te==="up"?1:-1);await this.focusOnInput(g),this.setConfirmedValue(this.correctNumber(se+fe),g),Object.assign(this,{longPressedTimeout:setTimeout(()=>Object.assign(this,{steppingInterval:setInterval(()=>{const{parser:De,suffix:Ye}=this.props,{value:ke}=this.state,Re=De(ke,Ye);this.setConfirmedValue(this.correctNumber(Re+fe),g)},y)}),f)})}),(0,s.A)(this,"onRelease",()=>{clearTimeout(this.longPressedTimeout),clearInterval(this.steppingInterval),this.handleAfterChangeValue()}),(0,s.A)(this,"onFocus",g=>{const{attr:L,dontSelectOnFocus:ne,onFocus:ee=ne?void 0:A}=this.props;this.state.isActive||this.setActive(),ee(g,L)}),(0,s.A)(this,"onKeyDown",g=>{const{key:L,currentTarget:ne}=g,{disableKeyboardEvents:ee}=this.props;if(ee&&(L===D._.ArrowUp||L===D._.ArrowDown)){g.preventDefault();return}const se=L===D._.ArrowUp?"up":L===D._.ArrowDown?"down":L===D._.Enter?"enter":L===D._.Tab?"tab":null,te=ne instanceof Element&&ne.matches("input");if(se){if(g.persist&&g.persist(),se!=="tab"&&g.preventDefault(),te&&se==="tab")return this.setInactive(),this.setState({isEditing:!1}),this.onConfirm(g);if(te&&se==="enter"){g.stopPropagation(),this.onConfirm(g),this.setInactive(),this.setState({isEditing:!1}),this.$input.blur();return}if(te){const{parser:fe,suffix:De}=this.props,{value:Ye}=this.state,ke=fe(Ye,De),Re=S(g,this.props.step)*(se==="up"?1:-1);this.handleBeforeChangeValue(),this.setState({isEditing:!1}),this.setConfirmedValue(this.correctNumber(ke+Re),g)}}}),(0,s.A)(this,"setActive",()=>{this.setState({isActive:!0}),MB&&MB.f&&(MB.f.isInSetting=!0);const{withToolTip:g,onChangeTooltipsHover:L}=this.props;g&&(this.setState({showTooltip:!1}),L&&L(!1))}),(0,s.A)(this,"setInactive",()=>{this.setState({isActive:!1,isEditing:!1}),MB&&MB.f&&(MB.f.isInSetting=!1)}),(0,s.A)(this,"toggleMenu",()=>this.setState({isMenuOpen:!this.state.isMenuOpen})),(0,s.A)(this,"closeMenu",()=>this.setState({isMenuOpen:!1})),(0,s.A)(this,"onSelect",g=>{g.persist();const{attr:L,onConfirm:ne}=this.props,{currentTarget:ee}=g,{value:se}=ee.dataset;this.setState({value:se,isEditing:!1},()=>ne(se,L,g)),this.setInactive(),this.closeMenu()}),(0,s.A)(this,"onClickOutside",g=>{const{target:L}=g,{onClickOutside:ne}=this.props;ne&&!(ne!=null&&ne(g))||L.closest("label")&&this.$label.contains(L)||L.closest(".SelectOption")||(this.onConfirm(g),this.setInactive(),this.setState({isEditing:!1}))}),(0,s.A)(this,"onBlur",async g=>{await this.onConfirm(g),this.setState({isEditing:!1}),this.props.onBlur(g)}),(0,s.A)(this,"handleRectResize",g=>{g.stopPropagation();const{isDisabled:L,disabled:ne,cursorSize:ee,cursorDirection:se,onResizeMove:te,onResizeEnd:fe,attr:De}=this.props;if(L||ne)return;const{left:Ye,right:ke,top:Re,bottom:mt}=this.$title.getBoundingClientRect();this.lastRecordedClientX=ee==="col-resize"?(Ye+ke)/2:(Re+mt)/2,this.lastRecordedValue=Number(this.state.value),this.setState({isTitleActive:!0}),document.querySelector("html").classList.add("no-pointer-events",ee),sdkStore.combineMergeMark("input-number-"+De),this.handleBeforeChangeValue();const Pe=Y=>{te&&te();const me=(ee==="col-resize"?Y.pageX:Y.pageY)-this.lastRecordedClientX,Ne=this.correctNumber(this.lastRecordedValue+(se?Number(me):-Number(me)));this.setConfirmedValue(Ne,Y)},Q=()=>{fe&&fe(),this.setState({isTitleActive:!1}),document.querySelector("html").classList.remove("no-pointer-events",ee),this.handleAfterChangeValue(),sdkStore.combineMerge("input-number-"+De),document.removeEventListener("mousemove",Pe),document.removeEventListener("mouseup",Q)};document.addEventListener("mousemove",Pe),document.addEventListener("mouseup",Q)}),(0,s.A)(this,"handleChangeCursor",()=>{const{isDisabled:g,disabled:L}=this.props;g||L||this.setState({isCursor:!this.state.isCursor})}),(0,s.A)(this,"handleAfterChangeValue",()=>{const{onAfterChangeValue:g}=this.props;g()}),(0,s.A)(this,"handleBeforeChangeValue",()=>{const{onBeforeChangeValue:g}=this.props;g()}),(0,s.A)(this,"onInputNumberMouseEnter",g=>{const{onMouseEnter:L,withToolTip:ne,onChangeTooltipsHover:ee}=this.props;L(g),this.setState({mouseEntered:!0});const se=()=>this.setState({showTooltip:!0});ne&&(ee?ee(!0,se):se())}),(0,s.A)(this,"onInputNumberMouseLeave",g=>{const{onMouseLeave:L,withToolTip:ne,onChangeTooltipsHover:ee}=this.props;L(g),this.setState({mouseEntered:!1}),ne&&(this.setState({showTooltip:!1}),ee&&ee(!1))}),this.state={prevProps:this.props,value:b(this.props.value,this.props.isNotVerify,this.props.precision),isActive:!1,isMenuOpen:!1,isTitleActive:!1,isCursor:!1,isEditing:!1,showTooltip:!1,mouseEntered:!1},this.lastRecordedClientX=null,this.lastRecordedValue=b(this.props.value,this.props.isNotVerify,this.props.precision),this.toolTipNode=document.getElementById("IBOT_TOOLTIP_ROOT")}static getDerivedStateFromProps(X,g){let{prevProps:L,value:ne}=g;return L.value!==X.value||L.isNotVerify!==X.isNotVerify||L.precision!==X.precision?{prevProps:X,value:b(X.value,X.isNotVerify,X.precision)}:null}componentDidMount(){this.props.isSelect&&(this.$input.select(),this.setState({isActive:!0}))}componentDidUpdate(X){!X.isSelect&&this.props.isSelect&&this.$input.select()}componentWillUnmount(){if(!this.props.unMountCallback||!this.state.isActive)return;const{value:X,attr:g,unMountCallback:L}=this.props,{value:ne}=this.state,ee=this.getConfirmValue(ne);String(X)!==String(ee)&&L&&L(ee,g,new Event("dummy"),ne)}get canBePositive(){return this.props.max>0}get canBeNegative(){return this.props.min<0}render(){const{inputClassName:X,menuClassName:g,className:L="",placeholder:ne,title:ee,rightTitle:se,cursorSize:te,suffix:fe,actionButton:De,formatter:Ye,optionList:ke,canSlidingAdjustment:Re,readOnly:mt,toolTipContent:Pe,showCaretIcon:Q,normalRight:Y,isLongTimeHoverForInput:me=!1}=this.props,{value:Ne,isActive:je,isMenuOpen:Ge,isTitleActive:Me,isCursor:Ie,isEditing:Je,showTooltip:nt,mouseEntered:yt}=this.state,xt=Ne,bt=this.props.isDisabled||this.props.disabled,wt=ke&&ke.length>0,r=u()(L,"WorkspaceInputNumber",{"is-disabled":bt,"is-active":je&&!bt,"is-menu-open":Ge,"is-active-title":Me,"can-sliding-adjustment":Re,"has-menu":wt,"is-readOnly":mt}),ue=ne,ve=String(Ne).split(".").join("").length-1,Te=me?F.A:ie.A;return(0,C.jsx)(Te,{content:Pe,direction:"down",children:(0,C.jsxs)(v,{className:r,ref:this.setLabelRef,onMouseDown:wt?void 0:this.setActive,onMouseEnter:this.onInputNumberMouseEnter,onMouseLeave:this.onInputNumberMouseLeave,valueLength:ve,children:[ee&&(0,C.jsx)("span",{className:u()("title",{"title-cursor":Ie},te),ref:this.setElemRef,onMouseDown:this.handleRectResize,onMouseEnter:this.handleChangeCursor,onMouseLeave:this.handleChangeCursor,children:ee}),(0,C.jsx)("input",{className:u()(X,{"text-align-right":Y&&!yt&&!je}),type:"text",value:Je?xt:Ye(xt,fe),placeholder:ue,ref:this.setInputRef,disabled:bt,onChange:this.onChange,onKeyDown:this.onKeyDown,onKeyUp:this.handleAfterChangeValue,onMouseDown:wt?this.setActive:void 0,onFocus:this.onFocus,onBlur:this.onBlur}),se&&(0,C.jsx)("span",{className:u()("title","left-title",{"title-cursor":Ie},te),ref:this.setElemRef,onMouseDown:this.handleRectResize,onMouseEnter:this.handleChangeCursor,onMouseLeave:this.handleChangeCursor,children:se}),De&&(0,C.jsx)(d,{hasMenu:wt,onToggleMenu:this.toggleMenu,onStep:this.onStep,onRelease:this.onRelease,showCaretIcon:Q}),wt&&Ge&&(0,C.jsx)(J.V0,{isOpen:Ge,menuClassName:u()("SelectNumberMenu",g),$select:this.$label,optionList:ke,value:xt,onChange:this.onSelect,onClose:this.closeMenu}),je&&(0,C.jsx)(R.A,{target:document,onMouseDown:(0,R.t)(this.onClickOutside,{capture:!0})})]})})}}(0,s.A)(K,"defaultProps",{unstyled:!1,value:"",placeholder:"",cursorSize:"col-resize",inputClassName:"",menuClassName:"",cursorDirection:!0,step:1,isNotVerify:!1,formatter:(T,X)=>""+T+(X||""),parser:(T,X)=>{const g=T.toString();return X&&g.endsWith(X)?Number(g.slice(0,g.length-X.length)):Number(g)},min:0,max:1/0,isDisabled:!1,disabled:!1,readOnly:!1,actionButton:!0,isSelect:!1,onConfirm:()=>null,onMouseEnter:()=>null,onMouseLeave:()=>null,onAfterChangeValue:()=>null,onBeforeChangeValue:()=>null,onBlur:()=>null,shouldCorrectOnConfirm:!0,precision:0,dontSelectOnFocus:!1,canSlidingAdjustment:!0,isLineHeight:!1,disableKeyboardEvents:!1});const d=(0,p.memo)(T=>{let{hasMenu:X,onToggleMenu:g,onStep:L,onRelease:ne,showCaretIcon:ee}=T;return(0,C.jsxs)(p.Fragment,{children:[X?(0,C.jsx)("div",{className:u()("action caret",{active:ee}),children:(0,C.jsx)(V.Ay,{className:"no-transition",type:"text",tabIndex:"-1",onClick:g,children:(0,C.jsx)(M.C,{name:"common/expand@26"})})}):(0,C.jsxs)("div",{className:"action",children:[(0,C.jsx)(V.Ay,{className:"no-transition",type:"text",tabIndex:"-1","data-action":"up",onMouseDown:L,onMouseLeave:ne,onMouseUp:ne,children:(0,C.jsx)(M.C,{name:"general/triangle_up"})}),(0,C.jsx)(V.Ay,{className:"no-transition",type:"text",tabIndex:"-1","data-action":"down",onMouseDown:L,onMouseLeave:ne,onMouseUp:ne,children:(0,C.jsx)(M.C,{name:"general/triangle_down"})})]}),(0,C.jsx)(I,{})]})}),j=T=>(0,C.jsx)(K,{...T,isLongTimeHoverForInput:!0})},5743:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>p});var s=n(67787);const p=s.Ay.div.withConfig({displayName:"styles__StyledMainPage",componentId:"sc-8fjrks-0"})(["width:414px;background-color:",";color:",";position:relative;height:212px;&.loading{height:212px;}&.dark{.personal-project-access{.title{span{color:rgba(255,255,255,0.9);}}.content{span{color:rgba(255,255,255,0.9);}}}.access-operation{.open-access-switch > span{color:rgba(255,255,255,0.9);}.item-center > button{background-color:#252626;&:hover{background-color:#666666;}span{color:rgba(255,255,255,0.9);}}}.sharing-footer{background-color:#151515;.sharing-type{span{color:rgba(255,255,255,0.9);}.toolbar-icon-item > svg > path{color:rgba(255,255,255,0.9);}}.sharingV2-click-visible{&:hover{background-color:#666666;}span{color:rgba(255,255,255,0.9);}}}}.access-operation{margin:20px 20px 14px;.open-access-switch{display:flex;align-items:center;margin-bottom:20px;span{font-weight:500;font-size:14px;line-height:20px;color:#333333;margin-right:6px;}}}"],N=>N.theme.color_bg_white,N=>N.theme.color_text_L2)},5995:Se=>{var ce=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function n(s){return s.match(ce)||[]}Se.exports=n},12246:(Se,ce,n)=>{"use strict";n.d(ce,{l:()=>s});const s=function(p,N,u,B){u===void 0&&(u=""),B===void 0&&(B=!1);let q;return p?q={mode:"org",orgCid:p.cid,payEntrance:N,checkoutArea:"proto",checkoutPlace:u||"org_use_vip_mkt"}:q={mode:B?"org":"solo",isSelectOrg:!!B,payEntrance:N,checkoutArea:"proto",checkoutPlace:u||"solo_use_vip_mkt"},q}},12603:(Se,ce,n)=>{"use strict";n.d(ce,{W:()=>s,p:()=>p});let s=function(N){return N.Org="org",N.OrgSpace="space",N.Folder="folder",N.Project="project",N}({}),p=function(N){return N.OrgSpace="orgspace-permission-tip",N.OrgSpaceLimiter="orgspace-limiter-permission-tip",N.RootFolder="root-folder-permission-tip",N.SubFolder="sub-folder-permission-tip",N.Project="project-permission-tip",N}({})},13076:function(Se){(function(ce,n){Se.exports=n()})(this,function(){"use strict";return function(ce,n){n.prototype.isSameOrBefore=function(s,p){return this.isSame(s,p)||this.isBefore(s,p)}}})},16335:(Se,ce,n)=>{"use strict";if(n.d(ce,{$r:()=>F,$z:()=>V,Aq:()=>ie,Cp:()=>H,Jv:()=>v,PJ:()=>f,Uy:()=>M,Wu:()=>B,X:()=>J,Yk:()=>R,ZF:()=>I,a3:()=>q,je:()=>C,rd:()=>G}),n.j!=15)var s=n(19249);var p=n(18833),N=n(47946);const u=300,B=async function(y){y===void 0&&(y="");const h=1;let b=[];try{const S=await(0,s.Yo)("/api/community/v1/workspace/my_star?page="+h+(y&&"&category="+y)+"&page_size="+u);S&&(b=S==null?void 0:S.market_templates.filter(A=>(A==null?void 0:A.version)!=="v1"))}catch(S){(0,p.$r)()}return b.map(S=>({...S,is_star:!0}))},q=async y=>{await(0,s.Ds)("/api/community/v1/market_template/star/"+y)},V=async y=>{await(0,s.DW)("/api/community/v1/market_template/star/"+y)},R=async function(y,h){h===void 0&&(h=!0);let b;try{b=await(0,s.DE)("/api/community/v1/market_template/"+y)}catch(S){h&&(0,p.ai)(S)}return b},J=function(y){return y===void 0&&(y=""),(0,s.rh)("/api/community/v1/market_template?is_from_workspace=true&page="+1+y+"&page_size="+u)},M=async()=>{let y;try{y=await(0,s.DE)("/flpak/ww-p2recent")}catch(h){(0,p.$r)()}return y},F=async()=>{try{return await(0,s.DE)("/api/community/v1/hot_icons")}catch(y){(0,p.$r)()}},ie=async()=>{try{return await(0,s.DE)("/api/community/v1/hot_color_icons")}catch(y){(0,p.$r)()}},D=y=>{let{category:h="combo_group",keyword:b=""}=y;return fetchGetJSON("/api/community/v1/es/basic_resource/search_"+h+"?q="+encodeURIComponent(b))},pe={screen_list:"screen",combo_group:"combo",icon_group:"icon",user_combo:"user_combo"},H=y=>{let{projectUpperType:h="",projectUpperCid:b="",itemCid:S=""}=y;return(0,s.Ds)("/api/community/v1/recent_template",{project_upper_type:pe[h]||h,project_upper_cid:b,item_cid:S})},G=async y=>{const{sdkStore:h}=await(0,N.O3)({flpakKey:y});return h},I=async function(y){y===void 0&&(y="mobile");try{return(await(0,s.DE)("/api/market_template/v4/workspace/new_page_types?platform="+y)).types}catch(h){return console.error(h),[]}},v=async function(y){y===void 0&&(y="mobile");try{return await(0,s.DE)("/api/community/v1/workspace/recommend_page?platform="+y)}catch(h){return(0,p.$r)({onClick:b=>b({type:"entry:resources:set:pagePanel:find:update:data",payload:{platformType:y,isReRequest:!0}})}),{}}},C=async y=>{let{platform:h,firstTypeCid:b,secondTypeCid:S=null}=y;const A={first_type_cid:b,second_type_cid:S,platform:h};let K=new URLSearchParams;K=Object.assign(K,A);for(const[d,j]of Object.entries(K))j?j==="default"?K.delete(d):K.set(d,j):K.delete(d);try{return await(0,s.DE)("/flpak/ww-p2pagemkt?"+K.toString())}catch(d){return(0,p.$r)({onClick:j=>j({type:"entry:resources:set:pagePanel:find:update:data",payload:{platformType:h,isReRequest:!0}})}),{}}},f=async()=>{try{const{color_icon_groups:y}=await(0,s.DE)("/api/community/v1/color_icon_groups");return y&&y.length>0?y.reduce((h,b)=>(h[b.source_upper_cid]=b,h),{}):{}}catch(y){return(0,p.$r)(),{}}}},16615:(Se,ce,n)=>{"use strict";n.d(ce,{qk:()=>G,I2:()=>D,ZI:()=>pe});const s={set:function(I,v,C,f){f===void 0&&(f="/");let y="";if(y+=I+"="+encodeURIComponent(v),f&&(y+=";path="+f),C){const h=new Date;h&&(h.setTime(h.getTime()+C*1e3),y+=";expires="+h.toUTCString())}document.cookie=y},get:function(I){const v=document.cookie.split(";");let C="";for(let f=0;f<v.length;f++){const y=v[f].indexOf(I);if(y!==-1){C=v[f].substring(y+I.length+1,v[f].length);break}}return C}},p=function(I,v){if(I!=="visit")return;const C="visit_id",f=60*24*3600;return s.set(C,v,f,"/"),v},N=function(){const I=s.get("visit_id");if(I&&I!="")return I;const v=new Date,C=v.getFullYear().toString().substr(2,2),f=v.getMonth()+1,y=v.getDate(),h=v.getHours(),b=v.getMinutes(),S=v.getSeconds(),A=v.getMilliseconds();let K=Math.round(Math.random()*Math.random()*1e4).toString();const d=K.length;d===1?K=K+"000":d===2?K=K+"00":d===3&&(K=K+"0");let j="2_"+C;return j+=f<10?"0"+f:f,j+=y<10?"0"+y:y,j+=h<10?"0"+h:h,j+=b<10?"0"+b:b,j+=S<10?"0"+S:S,A<10?j+="00"+A:A<100?j+="0"+A:j+=A,j+="_"+K,p("visit",j),j};var u=function(I){return I.WWW="www",I.WWW_TEST="www-test",I.OVERSEA="oversea",I.OVERSEA_TEST="oversea-test",I}(u||{});const B={www:"https://sc.bosyun.cn","www-test":"https://sc.bosyun.cn",oversea:"https://sc.bosyun.net","oversea-test":"https://sc.bosyun.net"},q="MD_",V="modao",R=()=>{let I=u.WWW;return/^([a-z0-9]+\.)*mockitt\.com$/.test(location.host)&&(I=u.OVERSEA),I},J=()=>{const I=window.sensorsDataAnalytic201505;if(!I)return;const v=R();I.init({name:"sensors",server_url:B[v]+"/sa.gif?project=production",is_track_single_page:!1,use_client_time:!0,show_log:!1,send_type:"beacon",heatmap:{clickmap:"not_collect",scroll_notice_map:"not_collect"}}),I.identify(N(),!0),I.registerPage({product:V}),window.sensors=I},M=function(I){var v;if(window.sensors){I=""+q+I;for(var C=arguments.length,f=new Array(C>1?C-1:0),y=1;y<C;y++)f[y-1]=arguments[y];(v=window.sensors)==null||v.track(I,...f)}},F=function(){return window.sensors&&window.sensors.setOnceProfile(...arguments)},ie=function(){return window.sensors&&window.sensors.setProfile(...arguments)},D=ENV.NO_TRACK?()=>{}:J,pe=ENV.NO_TRACK?()=>{}:M,H=ENV.NO_TRACK?()=>{}:F,G=ENV.IS_MO?I=>{if(!window.gtag)return console.error("gtag is not a function");window.gtag("config","UA-4839360-64",{user_id:I,transport_type:"beacon"})}:()=>{}},18833:(Se,ce,n)=>{"use strict";n.d(ce,{$r:()=>J,AG:()=>B,FY:()=>u,K7:()=>R,ai:()=>q,m0:()=>V});var s=n(19249),p=n(87612),N=n(88856);const u=async(M,F)=>{if((0,p.p)("[alertAsyncBlocked]",M,{url:M.url,status:M.status}),M.status===403||/403/.test(M.message)){var ie;(ie=MB)==null||(ie=ie.messageBucket)==null||ie.send("mobileLoadProjectError",{errMsg:I18N.dPages.cant_edit_desc}),await MB.global.popupHelper.alertAsyncBlocked({title:I18N.dModule.cant_edit,confirmText:I18N.dModule.exit_editing,desc:I18N.dModule.cant_edit_desc}).then(()=>F?F():MB.global.onBackButtonClick())}else if(M.status===401||/401/.test(M.message))R(),await MB.global.popupHelper.alertAsyncBlocked({title:I18N.dModule.err_info_modal.INVALID_COOKIE.title,desc:I18N.dModule.err_info_modal.INVALID_COOKIE.desc,confirmText:I18N.dModule.confirm}).then(()=>F?F():B());else{var D;const pe=String(M.status||M.message||"UNKNOWN").replace(/\s/g,"").slice(0,16);(D=MB)==null||(D=D.messageBucket)==null||D.send("mobileLoadProjectError",{errMsg:window.I18N.dModule.unknown_error.replace(/%s/i,pe)}),await MB.global.popupHelper.alertAsyncBlocked({desc:window.I18N.dModule.unknown_error.replace(/%s/i,pe),isHTML:!0})}},B=async()=>{try{await(0,s.DE)("/api/web/v3/initial"),location.reload()}catch(M){const F=location.origin+"/signin?next="+location.pathname;location.href=F}},q=M=>{M.status===404&&M.error_type==="MT_NOT_FOUND"&&MB.notice({text:I18N.dModals.template_no_available,type:"error"})},V=async(M,F)=>{M.status===403||/403/.test(M.message)?F():(M.status===401||/401/.test(M.message))&&await MB.global.popupHelper.alertAsyncBlocked({title:I18N.dModule.err_info_modal.INVALID_COOKIE.title,desc:I18N.dModule.err_info_modal.INVALID_COOKIE.desc,confirmText:I18N.dModule.confirm}).then(()=>B())},R=()=>{window.top.postMessage(JSON.stringify({sharingMessage:"sharing:loginInvalid"}),"*")},J=function(M){let{onClick:F}=M===void 0?{}:M;F&&!ENV.IS_MO?MB.notice({text:window.I18N.dModule.network_api_error_1,CustomChildComponent:(0,N.DV)({onClick:F}),type:"error",duration:5e3}):MB.notice({text:window.I18N.dModule.network_api_error,type:"error"})}},18941:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>p});var s=n(22756);function p(N,u){N.prototype=Object.create(u.prototype),N.prototype.constructor=N,(0,s.A)(N,u)}},19481:(Se,ce,n)=>{"use strict";n.d(ce,{FU:()=>u,OO:()=>V,Z8:()=>p,au:()=>q,lY:()=>B,p_:()=>N,zB:()=>s});const s={OWNER:1e4,SUPERMANAGER:9999,MANAGER:999,MEMBER:99,LIMITER:10,VIEWER:9,BANNED:1,UNKNOWN:0,UNJOINED:-1},p={space_owner:16,space_manager:15,space_member:14,space_viewer:13,space_limiter:12,team_owner:11,team_manager:10,team_member:9,team_viewer:8,project_owner:6,project_manager:5,project_member:4,project_viewer:3,project_ban_viewer:2,project_team_owner:1,team_ban_viewer:1},N={org_owner:s.OWNER,org_manager:s.SUPERMANAGER,org_admin:s.MANAGER,org_member:s.MEMBER,org_viewer:s.VIEWER},u={space_owner:s.MANAGER,space_manager:s.MANAGER,space_member:s.MEMBER,space_viewer:s.VIEWER,space_limiter:s.LIMITER},B={team_owner:s.MANAGER,team_manager:s.MANAGER,team_member:s.MEMBER,team_viewer:s.VIEWER,team_none:s.VIEWER,team_ban_viewer:s.BANNED},q=Object.assign({project_owner:s.MANAGER,project_team_owner:s.MANAGER,project_manager:s.MANAGER,project_member:s.MEMBER,project_viewer:s.VIEWER,project_none:s.VIEWER,project_ban_viewer:s.BANNED},B),V={space_owner:"project_manager",space_manager:"project_manager",space_member:"project_member",space_viewer:"project_viewer",team_owner:"project_manager",team_manager:"project_manager",team_member:"project_member",team_viewer:"project_viewer",team_ban_viewer:"project_ban_viewer",team_none:"project_viewer",project_owner:"project_manager"},R={space_owner:"team_manager",space_manager:"team_manager",space_member:"team_member",space_viewer:"team_viewer",team_none:"team_viewer",team_owner:"team_manager"}},19631:(Se,ce,n)=>{var s=n(5995),p=n(60820),N=n(59696),u=n(96411);function B(q,V,R){return q=N(q),V=R?void 0:V,V===void 0?p(q)?u(q):s(q):q.match(V)||[]}Se.exports=B},21066:(Se,ce,n)=>{"use strict";n.d(ce,{m:()=>De,cG:()=>mt,Dr:()=>Ye,aM:()=>ke,Ay:()=>fe});var s=n(25582),p=n.n(s),N=n(38502),u=n(69623),B=n(67787),q=n(53732),V=n.n(q),R=n(51582);class J{constructor(Q,Y){Q===void 0&&(Q=0),Y===void 0&&(Y=0),this.x=Q,this.y=Y}inside(Q){const{left:Y,right:me,top:Ne,bottom:je}=Q;return this.x>=Y&&this.x<=me&&this.y>=Ne&&this.y<=je}}const M=(0,N.createContext)(""),F=Pe=>{if(!Pe)return;const Q=Pe.getBoundingClientRect(),Y=Pe.classList.contains("sub-menu"),me=1/.8,Ne=1/(.8*.9);if(Y){const Ge=document.getElementById("root-menu").getBoundingClientRect();Ge.top+(Q.top-Ge.top)*me+Q.height*Ne>window.innerHeight-28&&Pe.classList.add("top"),Ge.left+(Q.left-Ge.left)*me+Q.width*Ne>window.innerWidth&&Pe.classList.add("left");return}if(Q.left+Q.width*me>window.innerWidth-28&&Pe.classList.add("left"),Q.height>window.innerHeight){Pe.style.height=window.innerHeight+"px",Pe.style.overflow="auto",Pe.style.overflowX="hidden",Pe.style.marginTop=-Q.top+"px";return}Q.top+Q.height<window.innerHeight||(Pe.style.marginTop=window.innerHeight-(Q.top+Q.height)+"px")},ie=function(Pe,Q,Y,me,Ne,je){let{subMenuLeftOffset:Ge,subMenuTopOffset:Me}=Ne;if(je===void 0&&(je=!1),!Pe||!Q)return;const Ie=Q.getBoundingClientRect(),Je=Q.getBoundingClientRect(),nt=1/.9,{left:yt,top:xt}=Y;Ie.top+Ie.height*nt>window.innerHeight-28?Ie.top-Ie.height*nt>0?(Pe.style.bottom="-4px",Pe.style.top="unset"):Pe.style.marginTop=window.innerHeight-28-(Ie.top+Ie.height*nt)+"px":Pe.style.top=Ie.top-xt-11+"px",Ie.left+Ie.width*nt>window.innerWidth?(Pe.style.right=Ie.left+"px",Pe.style.left="unset"):Pe.style.left=Ie.right-yt-1+12*je+"px",me==="left"&&(Pe.style.left=-2*Ie.width-12-22*je+"px"),Ge&&(Pe.style.left=parseInt(Pe.style.left)+Ge+4*je+"px"),Me&&(Pe.style.top=parseInt(Pe.style.top)+Me+"px"),Pe.style.maxHeight=window.innerHeight-Je.top+"px"},D=function(Pe,Q,Y,me,Ne,je,Ge){let{subMenuLeftOffset:Me,subMenuTopOffset:Ie}=Ne;if(je===void 0&&(je=!1),!Pe||!Q)return;const Je=Ge.getBoundingClientRect(),nt=Q.getBoundingClientRect(),yt=Q.getBoundingClientRect(),{left:xt,top:bt}=Y,{top:wt}=Je;Pe.style.top=nt.top-wt-11+"px",Pe.style.left=nt.right-xt-1+12*je+"px",me==="left"&&(Pe.style.left=-2*nt.width-12-22*je+"px"),Me&&(Pe.style.left=parseInt(Pe.style.left)+Me+4*je+"px"),Pe.style.maxHeight=window.innerHeight-yt.top+"px"},pe=(Pe,Q)=>{let{x:Y,y:me}=Pe,Ne=!1;for(let je=0,Ge=Q.length-1;je<Q.length;Ge=je++){let{x:Me,y:Ie}=Q[je],{x:Je,y:nt}=Q[Ge];Ie>me!=nt>me&&Y<(Je-Me)*(me-Ie)/(nt-Ie)+Me&&(Ne=!Ne)}return Ne};var H=n(60719);const G=(0,B.AH)(["background:#333;box-shadow:0 2px 8px rgba(0,0,0,0.3);border-radius:4px;color:#fff;border:1px solid #454647;"]),I=B.Ay.div.withConfig({displayName:"styles__StyledMask",componentId:"sc-h83js-0"})(["position:fixed;top:0;left:0;right:0;width:100vw;height:100vh;z-index:199;"]),v=B.Ay.div.withConfig({displayName:"styles__StyledContextMenu",componentId:"sc-h83js-1"})(["position:fixed;z-index:200;&.size-small > ul{min-width:120px;}ul{list-style:none;}& > ul{position:absolute;top:0;left:0;padding:8px 0;min-width:200px;margin:0;",";&.top{top:initial !important;bottom:-8px;}&.left{left:initial;right:100%;}&.top.left{}&::-webkit-scrollbar{display:block;width:6px;height:6px;}&::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,0.25);border-radius:4px;&:hover{background-color:rgba(255,255,255,0.30);}}}"],G),C=B.Ay.ul.withConfig({displayName:"styles__StyledSubMenu",componentId:"sc-h83js-2"})(["position:absolute;min-width:200px;max-height:500px;padding:4px 0;",";opacity:0;pointer-events:none;overflow-x:hidden;margin:0;&:lang(ja){min-width:260px;}&.is-show{opacity:1;pointer-events:auto;}&.inner-sub{overflow-x:unset;}&.is-normal{li.MenuItem{& > a{padding-left:32px;}&.hasIcon{> a{padding-left:15px;}}}}&.is-airy{li.MenuItem{& > a{padding-left:29px;}&.hasIcon{> a{padding-left:12px;}}}}"],G),f=B.Ay.li.withConfig({displayName:"styles__StyledMenuItem",componentId:"sc-h83js-3"})(["position:relative;a{height:28px;padding-left:16px;display:flex;align-items:center;color:#fff;padding-right:10px;& > span{margin-right:0;display:flex;justifyContent:flex-end;flex:1;alignItems:center;.text{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100%;line-height:20px;}}& > span{white-space:nowrap;margin-right:16px;.invisible{width:20px;height:20px;display:inline-block;color:",";margin-top:-3px;}}.selected-icon{width:8px;margin-right:9px;}.right-arrow{margin-left:auto;margin-right:-4px;display:flex;align-items:center;}img{width:14px;height:14px;opacity:0.8;margin-right:7px;}.fa-caret-right,.shortcut{margin-left:auto;width:12px;text-align:center;margin-right:-2px;}.shortcut{display:flex;align-items:center;color:#999;.mac{font-size:16px;margin-right:2px;}}.shortfont{display:flex;margin-left:auto;kbd{display:inline-block;text-align:center;min-width:12px;color:rgba(255,255,255,0.7);font-family:Inter;&.key-cmd{width:13px;text-align:right;}&:first-child{margin-left:0;}&:last-child{margin-right:0;}}}}&.hasDivider{a{margin-bottom:4px;}}&:not(.disabled).active{a{background:#666;}}&.disabled{*{cursor:not-allowed;}a{color:rgba(255,255,255,0.22);.right-arrow svg > path{fill:rgba(255,255,255,0.22);}.shortfont kbd{color:rgba(255,255,255,0.22);}}}"],H.f.color_text_L2.value_dark),y=B.Ay.li.withConfig({displayName:"styles__StyledMenuItemFont",componentId:"sc-h83js-4"})(["position:relative;a{height:28px;display:flex;align-items:center;color:#fff;& > span{margin-right:0;display:flex;justifyContent:flex-end;flex:1;alignItems:center;&.text{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100%;line-height:20px;}&.is-svg{height:14px;}}& > span{white-space:nowrap;margin-left:20px;}.selected-icon{width:16px;position:absolute;}.right-arrow{margin-right:8px;display:flex;align-items:center;}img{width:14px;height:14px;opacity:0.8;margin-right:7px;}.fa-caret-right,.shortcut{margin-left:auto;width:12px;text-align:center;margin-right:-2px;}}&:not(.disabled).active{a{background:#666;}}"]);var h=n(72214);function b(Pe,Q,Y){return(Q=S(Q))in Pe?Object.defineProperty(Pe,Q,{value:Y,enumerable:!0,configurable:!0,writable:!0}):Pe[Q]=Y,Pe}function S(Pe){var Q=A(Pe,"string");return typeof Q=="symbol"?Q:Q+""}function A(Pe,Q){if(typeof Pe!="object"||!Pe)return Pe;var Y=Pe[Symbol.toPrimitive];if(Y!==void 0){var me=Y.call(Pe,Q||"default");if(typeof me!="object")return me;throw new TypeError("@@toPrimitive must return a primitive value.")}return(Q==="string"?String:Number)(Pe)}const K=(0,h.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M5.5 5.04031L5.5 10.9597C5.5 11.3789 5.98497 11.612 6.31235 11.3501L10.012 8.39043C10.2622 8.19027 10.2622 7.80973 10.012 7.60957L6.31235 4.64988C5.98497 4.38797 5.5 4.62106 5.5 5.04031Z",fill:"white",fillOpacity:"0.9"})}),d="M1016 416L1016 -1856L328 -1856L328 416ZM512 -320L832 -320L832 -64L512 -64ZM512 -704L576 -704L576 -512L768 -512L768 -640L704 -640L704 -576L640 -576L640 -704L832 -704L832 -448L512 -448ZM512 192L728 64L512 64L512 0L832 0L832 65L617 192L832 192L832 256L512 256ZM512 -1408L704 -1408L704 -1248L832 -1248L832 -1184L512 -1184ZM512 -1536L624 -1536L624 -1632L512 -1632L512 -1696L832 -1696L832 -1632L688 -1632L688 -1536L832 -1536L832 -1472L512 -1472ZM512 -960L640 -960L640 -1056L512 -1056L512 -1120L832 -1120L832 -1056L704 -1056L704 -896L512 -896ZM512 -832L768 -832L768 -992L832 -992L832 -768L512 -768ZM768 -128L768 -256L576 -256L576 -128ZM640 -1248L640 -1344L576 -1344L576 -1248Z",j="M800 -800L800 -1240L1240 -1240L1240 -800Z",T="M860 416L172 416L548 -1856L1236 -1856ZM478 -320L436 -64L756 -64L798 -320ZM542 -704L499 -448L819 -448L862 -704L670 -704L648 -576L712 -576L723 -640L787 -640L766 -512L574 -512L606 -704ZM393 192L383 256L703 256L713 192L498 192L734 65L745 0L425 0L414 64L630 64ZM658 -1408L621 -1184L941 -1184L952 -1248L824 -1248L850 -1408ZM679 -1536L669 -1472L989 -1472L999 -1536L855 -1536L871 -1632L1015 -1632L1026 -1696L706 -1696L695 -1632L807 -1632L791 -1536ZM584 -960L573 -896L765 -896L792 -1056L920 -1056L930 -1120L610 -1120L600 -1056L728 -1056L712 -960ZM563 -832L552 -768L872 -768L909 -992L845 -992L819 -832ZM702 -128L510 -128L531 -256L723 -256ZM760 -1248L696 -1248L711 -1344L775 -1344Z",X="M1016 416L328 416L328 -1856L1016 -1856ZM512 -320L512 -64L832 -64L832 -320ZM512 -704L512 -448L832 -448L832 -704L640 -704L640 -576L704 -576L704 -640L768 -640L768 -512L576 -512L576 -704ZM512 192L512 256L832 256L832 192L617 192L832 65L832 0L512 0L512 64L728 64ZM512 -1408L512 -1184L832 -1184L832 -1248L704 -1248L704 -1408ZM512 -1536L512 -1472L832 -1472L832 -1536L688 -1536L688 -1632L832 -1632L832 -1696L512 -1696L512 -1632L624 -1632L624 -1536ZM512 -960L512 -896L704 -896L704 -1056L832 -1056L832 -1120L512 -1120L512 -1056L640 -1056L640 -960ZM512 -832L512 -768L832 -768L832 -992L768 -992L768 -832ZM768 -128L576 -128L576 -256L768 -256ZM640 -1248L576 -1248L576 -1344L640 -1344Z",g=3;let L=[],ne=!1;const ee=new Map,se=Pe=>{L.push(Pe),L.length>g&&L.shift()},te=Pe=>{Pe.ctrlKey===!0&&Pe.preventDefault()};class fe extends N.PureComponent{constructor(Q){super(Q),b(this,"setMenuRef",Y=>this.$menu=Y),b(this,"createContextMenuRef",Y=>this.$mountRoot=Y),b(this,"mousewheelHandle",Y=>{const me=Y.deltaX||Y.wheelDeltaX;if(!me||!this.$elem)return;const Ne=this.$elem.scrollLeft+this.$elem.offsetWidth===this.$elem.scrollWidth&&me>0,je=this.$elem.scrollLeft===0&&me<0;(Ne||je)&&Y.preventDefault()}),b(this,"handleClose",Y=>{const{onClose:me}=this.props;me&&me(Y)}),b(this,"handleCloseMouse",Y=>{Y.button===2&&this.handleClose(Y)}),b(this,"handleClickMenu",Y=>{const{alwaysOpen:me}=this.props;me||this.handleClose(Y)}),b(this,"handleUpdateState",Y=>this.setState({...Y})),this.$elem=document.createElement("div"),this.state={activePath:[],showSubMenu:!1}}componentDidMount(){document.body.appendChild(this.$elem),!this.props.style&&F(this.$menu),this.$elem&&this.$elem.addEventListener("mousewheel",this.mousewheelHandle),this.$elem&&this.$elem.addEventListener("wheel",te)}componentWillUnmount(){this.$elem&&this.$elem.removeEventListener("mousewheel",this.mousewheelHandle,{capture:!0}),this.$elem&&this.$elem.removeEventListener("wheel",te,{capture:!0}),L=[],ne=!1,this.$elem&&document.body.contains(this.$elem)&&document.body.removeChild(this.$elem)}preventDefault(Q){Q.preventDefault()}render(){const{position:Q,children:Y,className:me,style:Ne={}}=this.props,{top:je,left:Ge}=Q,{activePath:Me,showSubMenu:Ie}=this.state;return(0,u.createPortal)((0,h.jsx)(I,{className:"context-menu-mask",onMouseDown:this.handleClose,onClick:this.handleClickMenu,children:(0,h.jsx)(v,{ref:this.createContextMenuRef,style:{top:je+2,left:Ge+2,...Ne},className:me,onContextMenu:this.preventDefault,children:(0,h.jsx)("ul",{ref:this.setMenuRef,children:(0,h.jsx)(M.Provider,{value:{mountRoot:this.$mountRoot,contextMenuPosition:Q,activePath:Me,showSubMenu:Ie,onUpdateState:this.handleUpdateState},children:Y})})})}),this.$elem)}}b(fe,"propTypes",{className:p().string,position:p().object,children:p().oneOfType([p().element,p().array]),style:p().object,onClose:p().func,alwaysOpen:p().bool});class De extends N.PureComponent{constructor(Q){super(Q),b(this,"setMenuRef",Y=>this.$menu=Y),b(this,"createContextMenuRef",Y=>this.$mountRoot=Y),b(this,"mousewheelHandle",Y=>{const me=Y.deltaX||Y.wheelDeltaX;if(!me||!this.$elem)return;const Ne=this.$elem.scrollLeft+this.$elem.offsetWidth===this.$elem.scrollWidth&&me>0,je=this.$elem.scrollLeft===0&&me<0;(Ne||je)&&Y.preventDefault()}),b(this,"handleClose",Y=>{const{onClose:me}=this.props;me&&me(Y)}),b(this,"handleCloseMouse",Y=>{Y.button===2&&this.handleClose(Y)}),b(this,"handleClickMenu",Y=>{const{alwaysOpen:me}=this.props;me||this.handleClose(Y)}),b(this,"handleUpdateState",Y=>this.setState({...Y})),this.$elem=document.createElement("div"),this.state={activePath:[],showSubMenu:!1}}componentDidMount(){document.body.appendChild(this.$elem),this.$elem&&this.$elem.addEventListener("mousewheel",this.mousewheelHandle)}componentWillUnmount(){this.$elem&&this.$elem.removeEventListener("mousewheel",this.mousewheelHandle,{capture:!0}),L=[],ne=!1,this.$elem&&document.body.contains(this.$elem)&&document.body.removeChild(this.$elem)}preventDefault(Q){Q.preventDefault()}render(){const{position:Q,children:Y,className:me,style:Ne={}}=this.props,{top:je,left:Ge}=Q,{activePath:Me,showSubMenu:Ie}=this.state;return(0,h.jsx)(v,{ref:this.createContextMenuRef,style:{top:je+2,left:Ge+2,...Ne},className:me,onContextMenu:this.preventDefault,children:(0,h.jsx)("ul",{ref:this.setMenuRef,children:(0,h.jsx)(M.Provider,{value:{mountRoot:this.$mountRoot,contextMenuPosition:Q,activePath:Me,showSubMenu:Ie,onUpdateState:this.handleUpdateState,containerRef:this.$elem,isEmbed:!0},children:Y})})})}}b(De,"propTypes",{className:p().string,position:p().object,children:p().oneOfType([p().element,p().array]),style:p().object,onClose:p().func,alwaysOpen:p().bool});class Ye extends N.PureComponent{constructor(){super(...arguments),b(this,"createMenuItemRef",Q=>this.$menuItemRef=Q),b(this,"createSubMenuRef",Q=>this.$subMenuRef=Q),b(this,"handleActiveItem",(Q,Y)=>{const{children:me}=this.props;Y({activePath:Q,showSubMenu:!!me})}),b(this,"handleMouseEnter",(Q,Y,me)=>{const{onMouseEnter:Ne}=this.props;Ne&&Ne(Q),ne?setTimeout(()=>{if(this.$menuItemRef&&L.length>0){const je=this.$menuItemRef.getBoundingClientRect();L.slice(-1)[0].inside(je)&&this.handleActiveItem(Y,me)}},200):this.handleActiveItem(Y,me)}),b(this,"handleMouseLeave",async(Q,Y,me,Ne)=>{const{onMouseLeave:je}=this.props;if(je&&je(Q),me&&this.$subMenuRef){const Ge=this.$subMenuRef.getBoundingClientRect();if(Ge&&L.length>0){const{left:Me,top:Ie,bottom:Je}=Ge,nt=new J(Me,Ie),yt=new J(Me,Je),xt=[L[0],nt,yt],bt=new J(Q.clientX,Q.clientY);if(pe(bt,xt)){ne=!0;return}}}if(Y){Ne({activePath:Y.dataset.index.split(","),showSubMenu:!0}),ne=!1;return}Ne({activePath:[],showSubMenu:!1}),ne=!1}),b(this,"handleMouseMove",Q=>{const Y=new J(Q.clientX,Q.clientY);se(Y)}),b(this,"handleMouseDown",Q=>Q.stopPropagation()),b(this,"handleGetCanActiveSubMenu",(Q,Y,me)=>{let Ne=!1;return Y&&(Q&&Y.join("")===me.join("")||Y.slice(0,me.length).join("")===me.join(""))&&(Ne=!0),Ne}),b(this,"handleGetPath",Q=>{const{dataIndex:Y,text:me}=this.props,Ne=Y!==void 0?Y:me;return Q?Q.dataset.index.split(",").concat(Ne):[Ne]}),b(this,"handleClick",Q=>{const{disabled:Y,onClick:me,canClick:Ne,children:je}=this.props;(Ne||!Y)&&me&&me(Q),(je||Y)&&Q.stopPropagation()})}render(){const{icon:Q,text:Y,children:me,disabled:Ne,hotKeyText:je,className:Ge,dataType:Me,subMenuClassName:Ie,subMenuDirection:Je,subMenuLeftOffset:nt,subMenuTopOffset:yt,isVisible:xt,isAiry:bt=!1}=this.props;return(0,h.jsx)(M.Consumer,{children:wt=>{const{contextMenuPosition:r,mountRoot:ue,activePath:ve,showSubMenu:Te,parentRef:He,onUpdateState:qe,isEmbed:dt=!1}=wt,It=this.handleGetPath(He),Bt=this.handleGetCanActiveSubMenu(Te,ve,It);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(f,{ref:this.createMenuItemRef,className:V()("MenuItem",{disabled:Ne,hasIcon:Q},Ge,{active:Bt}),"data-type":Me,"data-index":It,onMouseEnter:Vt=>this.handleMouseEnter(Vt,It,qe),onMouseLeave:Vt=>this.handleMouseLeave(Vt,He,Te,qe),onMouseMove:this.handleMouseMove,onMouseDown:this.handleMouseDown,onClick:this.handleClick,children:(0,h.jsxs)("a",{children:[Q,(0,h.jsxs)("span",{children:[(0,h.jsx)("span",{className:"text",children:Y}),xt?(0,h.jsx)(R.A,{className:"invisible",name:"eyeinvisible"}):null]}),me&&(0,h.jsx)("div",{className:"right-arrow",children:K}),je&&je.length>0&&(0,h.jsx)("div",{className:"shortfont",children:je.map((Vt,$t)=>(0,h.jsx)("kbd",{children:Vt},$t))})]})}),me&&Bt&&!Ne&&(0,u.createPortal)((0,h.jsx)(Re,{parentRef:this.$menuItemRef,setRef:this.createSubMenuRef,contextMenuPosition:r,subMenuDirection:Je,subMenuLeftOffset:nt,subMenuTopOffset:yt,className:Ie,mountRoot:ue,isEmbed:dt,isAiry:bt,children:(0,h.jsx)(M.Provider,{value:{parentRef:this.$menuItemRef,mountRoot:ue,contextMenuPosition:r,activePath:ve,showSubMenu:Te,onUpdateState:qe},children:me})}),ue)]})}})}}b(Ye,"propTypes",{icon:p().object,text:p().string.isRequired,className:p().string,disabled:p().bool,canClick:p().bool,children:p().oneOfType([p().element,p().array]),hotKeyText:p().array,onClick:p().func,onMouseEnter:p().func,onMouseLeave:p().func,dataType:p().number,dataIndex:p().oneOfType([p().number,p().string]),subMenuClassName:p().string,subMenuDirection:p().string,subMenuLeftOffset:p().number,subMenuTopOffset:p().number,isVisible:p().bool,isAiry:p().bool}),b(Ye,"defaultProps",{subMenuClassName:""});class ke extends N.PureComponent{constructor(Q){super(Q),b(this,"createMenuItemRef",Me=>this.$menuItemRef=Me),b(this,"createSubMenuRef",Me=>this.$subMenuRef=Me),b(this,"spanRef",Me=>this.$spanRef=Me),b(this,"handleActiveItem",(Me,Ie)=>{const{children:Je}=this.props;Ie({activePath:Me,showSubMenu:!!Je})}),b(this,"handleMouseEnter",(Me,Ie,Je)=>{const{onMouseEnter:nt}=this.props;nt&&nt(Me),ne?setTimeout(()=>{if(this.$menuItemRef&&L.length>0){const yt=this.$menuItemRef.getBoundingClientRect();L.slice(-1)[0].inside(yt)&&this.handleActiveItem(Ie,Je)}},200):this.handleActiveItem(Ie,Je)}),b(this,"handleMouseLeave",async(Me,Ie,Je,nt)=>{const{onMouseLeave:yt}=this.props;if(yt&&yt(Me),Je&&this.$subMenuRef){const xt=this.$subMenuRef.getBoundingClientRect();if(xt&&L.length>0){const{left:bt,top:wt,bottom:r}=xt,ue=new J(bt,wt),ve=new J(bt,r),Te=[L[0],ue,ve],He=new J(Me.clientX,Me.clientY);if(pe(He,Te)){ne=!0;return}}}if(Ie){nt({activePath:Ie.dataset.index.split(","),showSubMenu:!0}),ne=!1;return}nt({activePath:[],showSubMenu:!1}),ne=!1}),b(this,"handleMouseMove",Me=>{const Ie=new J(Me.clientX,Me.clientY);se(Ie)}),b(this,"handleMouseDown",Me=>Me.stopPropagation()),b(this,"handleGetCanActiveSubMenu",(Me,Ie,Je)=>{let nt=!1;return Ie&&(Me&&Ie.join("")===Je.join("")||Ie.slice(0,Je.length).join("")===Je.join(""))&&(nt=!0),nt}),b(this,"handleGetPath",Me=>{const{dataIndex:Ie,text:Je}=this.props,nt=Ie!==void 0?Ie:Je;return Me?Me.dataset.index.split(",").concat(nt):[nt]}),b(this,"handleClick",Me=>{const{disabled:Ie,onClick:Je,canClick:nt,children:yt}=this.props;(nt||!Ie)&&Je&&Je(Me),(yt||Ie)&&Me.stopPropagation()});const{text:Y,fontUrl:me,usePlainText:Ne}=this.props,je="U="+me+"&T="+Y,Ge=ee.get(je);Ne?this.state={renderContent:Q.text,contentType:"text"}:this.state={renderContent:(Ge==null?void 0:Ge.content)||Q.text,contentType:(Ge==null?void 0:Ge.type)||"text"}}async componentDidMount(){const{text:Q,fontUrl:Y,usePlainText:me}=this.props;if(me)return;const Ne="U="+Y+"&T="+Q;if(!ee.get(Ne)){if(!Y){ee.set(Ne,{type:"text",content:Q});return}try{const je=await(await fetch("/flatkiq/fontsvg/sync.svg?"+Ne)).text();if(je!==""){const Ge=[...new DOMParser().parseFromString(je,"text/html").querySelectorAll("path")].map(Me=>Me.getAttribute("d"));if(Ge.find(Me=>Me===d||Me===j||Me===T||Me===X)){ee.set(Ne,{type:"text",content:Q});return}Ge.filter(Boolean).length!==Q.split("").filter(Me=>Me!==" ").length?ee.set(Ne,{type:"text",content:Q}):(ee.set(Ne,{type:"svg",content:je}),this.setState({renderContent:je,contentType:"svg"}))}else ee.set(Ne,{type:"text",content:Q})}catch(je){console.error(je),ee.set(Ne,{type:"text",content:Q})}}}render(){const{icon:Q,children:Y,disabled:me,className:Ne,dataType:je,subMenuClassName:Ge,subMenuDirection:Me,subMenuLeftOffset:Ie,subMenuTopOffset:Je,tooltipComponent:nt=null,tooltipWrapper:yt=null}=this.props,{renderContent:xt,contentType:bt}=this.state;return(0,h.jsx)(M.Consumer,{children:wt=>{const{contextMenuPosition:r,mountRoot:ue,activePath:ve,showSubMenu:Te,parentRef:He,onUpdateState:qe,isEmbed:dt=!1}=wt,It=this.handleGetPath(He),Bt=this.handleGetCanActiveSubMenu(Te,ve,It),Vt=(0,h.jsxs)("a",{children:[Q,(0,h.jsx)("span",{dangerouslySetInnerHTML:{__html:xt},className:V()("text",{"is-svg":bt==="svg"})}),nt,Y&&(0,h.jsx)("div",{className:"right-arrow",children:K})]});return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(y,{ref:this.createMenuItemRef,className:V()("MenuItem",{disabled:me,hasIcon:Q},Ne,{active:Bt}),"data-type":je,"data-index":It,onMouseEnter:$t=>this.handleMouseEnter($t,It,qe),onMouseLeave:$t=>this.handleMouseLeave($t,He,Te,qe),onMouseMove:this.handleMouseMove,onMouseDown:this.handleMouseDown,onClick:this.handleClick,children:yt?yt(Vt):Vt}),Y&&Bt&&!me&&(0,u.createPortal)((0,h.jsx)(Re,{parentRef:this.$menuItemRef,setRef:this.createSubMenuRef,contextMenuPosition:r,subMenuDirection:Me,subMenuLeftOffset:Ie,subMenuTopOffset:Je,className:Ge,mountRoot:ue,isEmbed:dt,isNoraml:!1,isAiry:!1,children:(0,h.jsx)(M.Provider,{value:{parentRef:this.$menuItemRef,mountRoot:ue,contextMenuPosition:r,activePath:ve,showSubMenu:Te,onUpdateState:qe},children:Y})}),ue)]})}})}}b(ke,"defaultProps",{subMenuClassName:""});class Re extends N.PureComponent{constructor(Q){super(Q),b(this,"createRef",Y=>{const{setRef:me}=this.props;me(Y),this.subMenu=Y}),this.state={isTransform:!1}}componentDidMount(){this.setState({isTransform:!0});const{parentRef:Q,contextMenuPosition:Y,subMenuDirection:me,subMenuLeftOffset:Ne,subMenuTopOffset:je,isAiry:Ge,isEmbed:Me,mountRoot:Ie}=this.props;Me?D(this.subMenu,Q,Y,me,{subMenuLeftOffset:Ne,subMenuTopOffset:je},Ge,Ie):ie(this.subMenu,Q,Y,me,{subMenuLeftOffset:Ne,subMenuTopOffset:je},Ge)}render(){const{children:Q,isAiry:Y,className:me,isNoraml:Ne=!0}=this.props,{isTransform:je}=this.state;return(0,h.jsx)(C,{className:V()("SubMenu",{"is-show":je},{"is-airy":Y},{"is-normal":Ne},me),ref:this.createRef,children:Q})}}b(Re,"propTypes",{children:p().oneOfType([p().element,p().array]),className:p().string,parentRef:p().node,contextMenuPosition:p().object,setRef:p().func,subMenuDirection:p().string,subMenuLeftOffset:p().number,subMenuTopOffset:p().number,isAiry:p().bool});const mt=B.Ay.div.withConfig({displayName:"ContextMenu__Divider",componentId:"sc-1802crt-0"})(["margin:8px 0;border-top:1px solid #454546;"])},22460:(Se,ce,n)=>{"use strict";n.d(ce,{O:()=>N});var s=n(67787),p=n(47507);const N=s.Ay.div.withConfig({displayName:"styles__StyledButton",componentId:"sc-166yiuz-0"})(["border-radius:6px;display:inline-block;justify-content:center;align-items:center;font-family:inherit;font-size:14px;font-weight:500;line-height:18px;user-select:none;cursor:pointer;.btn-icon-text-container{display:flex;justify-content:center;align-items:center;}.btn-icon{display:flex;justify-content:center;align-items:center;.btn-icon-container{margin-right:4px;width:16px;height:16px;overflow:hidden;}}&[class*='is-disabled']{cursor:not-allowed;opacity:0.4;}&[class*='type-linear']{color:",";background:",";border:1px solid ",";&:hover:not([class*='is-disabled']){background:",";}&:active:not([class*='is-disabled']){background:",";}}&[class*='type-primary']{color:",";background:",";border:1px solid transparent;&:hover:not([class*='is-disabled']){background:",";}&:active:not([class*='is-disabled']){background:",";}}&[class*='type-secondary']{color:",";background:",";border:1px solid ",";&:hover:not([class*='is-disabled']){background:",";border:1px solid ",";}&:active:not([class*='is-disabled']){background:",";border:1px solid transparent;}}&[class*='type-link']{border:1px solid transparent;padding:0;color:",";&:hover:not([class*='is-disabled']){color:",";}&:active:not([class*='is-disabled']){color:",";}&.link-gray{color:",";&:hover{color:",";}&:active{color:",";}}}&[class*='type-danger']{background:",";color:",";border:1px solid transparent;&:hover:not([class*='is-disabled']){background:",";}&:active:not([class*='is-disabled']){background:",";}}&[class*='size-tiny']{padding:4px 8px;max-height:24px;font-size:12px;line-height:14px;.btn-icon-text-container{min-width:46px;}&[class*='type-link']{padding:0;}}&[class*='size-common']{padding:6px 12px;max-height:32px;.btn-icon-text-container{min-width:54px;}&[class*='type-link']{padding:0;}}&[class*='size-medium']{padding:6px 16px;max-height:32px;.btn-icon-text-container{min-width:60px;}&[class*='type-link']{padding:0;}}&[class*='size-large']{padding:8px 16px;max-height:36px;.btn-icon-text-container{min-width:76px;}&[class*='type-link']{padding:0;}.btn-icon-container{margin-right:6px;}}&[class*='corner-smooth']{border-radius:6px;}&[class*='corner-soft']{border-radius:4px;}&[class*='corner-round']{border-radius:calc(infinity * 1px);}&[class*='only-icon']{padding:0;border:none;background:transparent;.btn-icon-text-container{min-width:0;}.btn-icon-container{margin-right:0;svg > *{fill:",";}}&:hover:not([class*='is-disabled']){background:none;.btn-icon-container{svg > *{fill:",";}}}&:active:not([class*='is-disabled']){background:none;.btn-icon-container{svg > *{fill:",";}}}}"],p.q.color_text_L2,p.q.color_bg_normal,p.q.color_split_line,p.q.color_bg_btn_normal_hover,p.q.color_bg_btn_normal_active,p.q.color_text_white,p.q.color_btn_primary_normal,p.q.color_btn_primary_hover,p.q.color_btn_primary_click,p.q.color_text_L2,p.q.color_bg_secondary_btn,p.q.color_btn_secondary_border_normal,p.q.color_btn_secondary_hover,p.q.color_btn_secondary_border_hover,p.q.color_btn_secondary_click,p.q.color_btn_primary_normal,p.q.color_btn_primary_hover,p.q.color_btn_primary_click,p.q.color_text_L2,p.q.color_text_L3,p.q.color_text_L1,p.q.color_btn_danger_normal,p.q.color_text_white,p.q.color_btn_danger_hover,p.q.color_btn_danger_click,p.q.color_text_L2,p.q.color_text_L3,p.q.color_text_L1)},23089:(Se,ce,n)=>{"use strict";n.d(ce,{it:()=>B,V0:()=>M,mq:()=>J});var s=n(38502),p=n(48986),N=n(67787);const u=(0,N.Ay)(p.Ay).withConfig({displayName:"styled__StyledWorkspaceSelectProto",componentId:"sc-32xdp1-0"})(["&.WorkspaceSelect{padding:0px;border-radius:4px;button{width:auto;display:flex;padding:0 6px;align-items:center;color:",";}.caret{flex-shrink:0;width:26px;display:flex;justify-content:center;align-items:center;padding:0;color:",";svg{width:26px;height:26px;}}&:not(.is-disabled):not(.readonly):hover{border-color:",";button{width:calc(100% - 26px);}.caret{padding:0;color:",";}}&.is-open{border-color:transparent;}}"],F=>F.theme.color_text_L1,F=>F.theme.color_text_L3,F=>F.theme.color_bg_border_02,F=>F.theme.color_text_L1),B="select-menu-width-108",q=(0,N.DU)(["#IBOT_SELECT_MENU_ROOT{.SelectMenuPortal{.menu-animation-select{margin-left:-15px;}.WorkspaceSelectMenu{padding:8px;border-radius:8px;color:",";background:",";box-shadow:",";&.is-open{border:1px solid ",";border-radius:8px;width:176px;max-width:176px;padding:8px;background-color:",";box-shadow:",";.SelectOption{&:not(.empty-msg):not(.is-disabled):hover{background:",";}}}> .divider{border-bottom:1px solid ",";margin:5px;}.SelectOption{border-radius:4px;height:28px;line-height:28px;&.is-active{color:",";}&:not(.empty-msg):not(.is-disabled):not(.is-active):hover{background-color:",";color:",";}}}.menu-animation-select .SelectGroup > .title{color:",";}}.autoFillPanelSelectScreenMenu{margin-left:-138px;margin-top:26px;width:175px;}.bdr-width-input-menu{width:88px;}.arrow-setting-menu{width:78px;}.","{width:108px;}}"],F=>F.theme.color_text_L1,F=>F.theme.color_bg_white,F=>F.theme.shadow_m,F=>F.theme.color_bg_border_01,F=>F.theme.color_bg_white,F=>F.theme.shadow_m,F=>F.theme.color_btn_secondary_hover,F=>F.theme.color_bg_border_01,F=>F.theme.color_text_L1,F=>F.theme.color_btn_secondary_hover,F=>F.theme.color_text_L1,F=>F.theme.color_text_L3,B);var V=n(17307),R=n(72214);class J extends s.PureComponent{render(){return(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)(u,{...this.props,arrowSvg:(0,R.jsx)(V.C,{className:"triangle-icon",name:"common/expand@26"})}),(0,R.jsx)(q,{})]})}}class M extends s.PureComponent{render(){return(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)(p.eB,{...this.props}),(0,R.jsx)(q,{})]})}}},24469:(Se,ce,n)=>{var s=n(35954),p={\u00C0:"A",\u00C1:"A",\u00C2:"A",\u00C3:"A",\u00C4:"A",\u00C5:"A",\u00E0:"a",\u00E1:"a",\u00E2:"a",\u00E3:"a",\u00E4:"a",\u00E5:"a",\u00C7:"C",\u00E7:"c",\u00D0:"D",\u00F0:"d",\u00C8:"E",\u00C9:"E",\u00CA:"E",\u00CB:"E",\u00E8:"e",\u00E9:"e",\u00EA:"e",\u00EB:"e",\u00CC:"I",\u00CD:"I",\u00CE:"I",\u00CF:"I",\u00EC:"i",\u00ED:"i",\u00EE:"i",\u00EF:"i",\u00D1:"N",\u00F1:"n",\u00D2:"O",\u00D3:"O",\u00D4:"O",\u00D5:"O",\u00D6:"O",\u00D8:"O",\u00F2:"o",\u00F3:"o",\u00F4:"o",\u00F5:"o",\u00F6:"o",\u00F8:"o",\u00D9:"U",\u00DA:"U",\u00DB:"U",\u00DC:"U",\u00F9:"u",\u00FA:"u",\u00FB:"u",\u00FC:"u",\u00DD:"Y",\u00FD:"y",\u00FF:"y",\u00C6:"Ae",\u00E6:"ae",\u00DE:"Th",\u00FE:"th",\u00DF:"ss",\u0100:"A",\u0102:"A",\u0104:"A",\u0101:"a",\u0103:"a",\u0105:"a",\u0106:"C",\u0108:"C",\u010A:"C",\u010C:"C",\u0107:"c",\u0109:"c",\u010B:"c",\u010D:"c",\u010E:"D",\u0110:"D",\u010F:"d",\u0111:"d",\u0112:"E",\u0114:"E",\u0116:"E",\u0118:"E",\u011A:"E",\u0113:"e",\u0115:"e",\u0117:"e",\u0119:"e",\u011B:"e",\u011C:"G",\u011E:"G",\u0120:"G",\u0122:"G",\u011D:"g",\u011F:"g",\u0121:"g",\u0123:"g",\u0124:"H",\u0126:"H",\u0125:"h",\u0127:"h",\u0128:"I",\u012A:"I",\u012C:"I",\u012E:"I",\u0130:"I",\u0129:"i",\u012B:"i",\u012D:"i",\u012F:"i",\u0131:"i",\u0134:"J",\u0135:"j",\u0136:"K",\u0137:"k",\u0138:"k",\u0139:"L",\u013B:"L",\u013D:"L",\u013F:"L",\u0141:"L",\u013A:"l",\u013C:"l",\u013E:"l",\u0140:"l",\u0142:"l",\u0143:"N",\u0145:"N",\u0147:"N",\u014A:"N",\u0144:"n",\u0146:"n",\u0148:"n",\u014B:"n",\u014C:"O",\u014E:"O",\u0150:"O",\u014D:"o",\u014F:"o",\u0151:"o",\u0154:"R",\u0156:"R",\u0158:"R",\u0155:"r",\u0157:"r",\u0159:"r",\u015A:"S",\u015C:"S",\u015E:"S",\u0160:"S",\u015B:"s",\u015D:"s",\u015F:"s",\u0161:"s",\u0162:"T",\u0164:"T",\u0166:"T",\u0163:"t",\u0165:"t",\u0167:"t",\u0168:"U",\u016A:"U",\u016C:"U",\u016E:"U",\u0170:"U",\u0172:"U",\u0169:"u",\u016B:"u",\u016D:"u",\u016F:"u",\u0171:"u",\u0173:"u",\u0174:"W",\u0175:"w",\u0176:"Y",\u0177:"y",\u0178:"Y",\u0179:"Z",\u017B:"Z",\u017D:"Z",\u017A:"z",\u017C:"z",\u017E:"z",\u0132:"IJ",\u0133:"ij",\u0152:"Oe",\u0153:"oe",\u0149:"'n",\u017F:"s"},N=s(p);Se.exports=N},25234:(Se,ce,n)=>{"use strict";n.d(ce,{Oc:()=>B,Qi:()=>J,jf:()=>q,jn:()=>V,lV:()=>H,tA:()=>R,z9:()=>F});var s=n(38502),p=n(97974),N=n(17307),u=n(72214);const B=(0,s.memo)(G=>{let{type:I="linear",className:v,disabled:C=!1,size:f="common",corner:y="smooth",forceTheme:h,children:b,onClick:S}=G;return(0,u.jsx)(p.Yr,{type:I,className:v,disabled:C,size:f,corner:y,forceTheme:h,onClick:S,children:b})}),q=n.j!=15?(0,s.memo)(G=>{let{type:I="linear",className:v,disabled:C=!1,size:f="common",corner:y="smooth",icon:h,children:b,forceTheme:S,onClick:A}=G;return(0,u.jsx)(p.hE,{type:I,className:v,disabled:C,size:f,corner:y,icon:h,forceTheme:S,onClick:A,children:b})}):null,V=(0,s.memo)(G=>{let{className:I,children:v,disabled:C=!1,onClick:f}=G;return(0,u.jsx)(p.Yr,{type:"primary",size:"common",className:I,disabled:C,onClick:f,children:v})}),R=G=>{let{className:I,children:v,disabled:C=!1,onClick:f}=G;return(0,u.jsx)(p.Yr,{type:"secondary",size:"common",className:I,disabled:C,onClick:f,children:v})},J=(0,s.memo)(G=>{let{className:I,children:v,disabled:C=!1,onClick:f}=G;return(0,u.jsx)(p.Yr,{type:"danger",size:"common",className:I,disabled:C,onClick:f,children:v})}),M=null,F=n.j!=15?(0,s.memo)(G=>{let{className:I,children:v,disabled:C=!1,onClick:f}=G;return(0,u.jsx)(p.Yr,{type:"link",className:I,disabled:C,onClick:f,children:v})}):null,ie=null,D=null,pe=null,H=n.j!=15?(0,s.memo)(G=>{let{className:I,isLoading:v=!1,children:C,disabled:f=!1,corner:y="soft",onClick:h}=G;return(0,u.jsx)(p.hE,{type:"secondary",size:"tiny",corner:y,className:I,icon:v?(0,u.jsx)(N.C,{name:"general/loading",className:"btn-loading"}):null,disabled:f,onClick:h,children:C})}):null},25942:(Se,ce)=>{"use strict";var n;n={value:!0};var s=Object.assign||function(q){for(var V=1;V<arguments.length;V++){var R=arguments[V];for(var J in R)Object.prototype.hasOwnProperty.call(R,J)&&(q[J]=R[J])}return q},p=function(){var V=null,R={},J=function(pe){var H=R[pe.type];return H&&H(V,pe)},M=function(pe){if(typeof pe.getState!="function"||typeof pe.dispatch!="function")throw new Error("[ReduxEntry][middleware] invalid reduxMiddlewareStore");if(V!==null)throw new Error("[ReduxEntry][middleware] already set reduxMiddlewareStore");return V=pe,function(H){return function(G){return J(G)===!0||H(G)}}},F=function(pe,H){if(typeof pe!="string")throw new Error("[ReduxEntry][setEntry] non-string actionType: "+pe);if(typeof H!="function")throw new Error("[ReduxEntry][setEntry] non-function entryFunction: "+pe+", "+H);R[pe]&&console.warn("[ReduxEntry][setEntry] possible unexpected entry overwrite: "+pe),R[pe]=H},ie=function(pe){return Object.keys(pe).forEach(function(H){return F(H,pe[H])})};return{middleware:M,setEntry:F,setEntryMap:ie}},N=function(V){if(V===void 0)throw new Error("[ReduxEntry][createStateStore] initialState expected");return{getState:function(){return V},setState:function(J){return V=J},wrapEntry:function(J){return function(M,F){return J(V,M,F)}}}},u=function(V,R){var J=R.getState,M=R.setState;return function(F,ie){var D=ie.type,pe=ie.payload;return D===V&&M(pe),J()}},B=function(V,R){var J=R.getState,M=R.setState;return function(F,ie){var D=ie.type,pe=ie.payload;return D===V&&M(s({},J(),pe)),J()}};ce.RZ=p,ce.J$=N,ce.Tf=u,n=B},29152:(Se,ce,n)=>{"use strict";n.d(ce,{IE:()=>q,M0:()=>M,WR:()=>u,XW:()=>ie,_B:()=>D,cb:()=>pe,hG:()=>I,iF:()=>F,il:()=>J,kG:()=>R,oV:()=>G,q7:()=>C,wA:()=>B,yZ:()=>H});var s=n(12211),p=n(76844),N=n(12603);const u=f=>{var y;return(y=f.projectAccess.user)==null?void 0:y.user_id},B=f=>f.projectAccess.org,q=f=>{var y;return((y=f.projectAccess.org)==null?void 0:y.cid)||""},V=f=>{var y;return((y=f.projectAccess.org)==null?void 0:y.members)||[]},R=f=>{var y;return((y=f.projectAccess.org)==null?void 0:y.permissions)||[]},J=f=>f.projectAccess.space,M=f=>f.projectAccess.space.cid,F=f=>f.projectAccess.space.permissions||[],ie=f=>f.projectAccess.folderListMap||new Map,D=f=>f.projectAccess.projectListMap||new Map,pe=(0,s.Mz)(u,R,(f,y)=>(0,p.oE)({userId:f,orgPermissions:y})),H=(0,s.Mz)(u,F,(f,y)=>(0,p.sq)({userId:f,spacePermissions:y})),G=(0,s.Mz)(F,f=>{const y=(0,p.Rc)(f);return f.reduce((h,b)=>{const{user_id:S}=b,A=(0,p.CF)({userId:S,permissionsMap:y,permissionScope:N.W.OrgSpace});return h.set(Number(S),A)},new Map)}),I=(0,s.Mz)(F,f=>f.filter(h=>h.role==="space_limiter").reduce((h,b)=>{const{user_id:S}=b;return h.set(Number(S),b)},new Map)),v=(0,s.Mz)(V,f=>f.reduce((y,h)=>{const{user_id:b,user_cid:S,email:A,mobile:K}=h;return b&&S?y.set(Number(b)||b,{...h}):(A&&y.set(A,{...h}),K&&y.set(K,{...h}),y)},new Map)),C=(0,s.Mz)(R,v,(f,y)=>f.reduce((h,b)=>{const{user_id:S,unsign_remark:A}=b,K=S||A,d=y.get(K)||{},j=(0,p.Sc)(b);return h.set(K,{...d,permission:j})},new Map))},29523:(Se,ce,n)=>{"use strict";n.d(ce,{f:()=>B});const B={...{color_modao:{description:"\u54C1\u724C\u8272-\u58A8\u5200",value_light:"#FF3333",value_dark:"#FF3333"},color_proto:{description:"\u5DE5\u5177\u8272-\u539F\u578B\u5DE5\u5177",value_light:"#266FFF",value_dark:"#266FFF"},color_mindmap:{description:"\u5DE5\u5177\u8272-\u601D\u7EF4\u5BFC\u56FE",value_light:"#39CCBC",value_dark:"#39CCBC"},color_flowchart:{description:"\u5DE5\u5177\u8272-\u6D41\u7A0B\u56FE",value_light:"#FF1D47",value_dark:"#FF1D47"},color_cooperation:{description:"\u5DE5\u5177\u8272-\u8BBE\u8BA1\u534F\u4F5C",value_light:"#FF6915",value_dark:"#FF6915"},color_mockitt:{description:"\u5DE5\u5177\u8272-MOCKITT",value_light:"#4257FF",value_dark:"#4257FF"}},...{color_btn_primary_disabled:{description:"\u5E38\u89C4\u6309\u94AE disabled",value_light:"#A8C6FF",value_dark:"#234381"},color_btn_primary_normal:{description:"\u5E38\u89C4\u6309\u94AE normal",value_light:"#266FFF",value_dark:"#266BF3"},color_btn_primary_hover:{description:"\u5E38\u89C4\u6309\u94AE hover",value_light:"#4080FF",value_dark:"#3C7DFF"},color_btn_primary_clicked:{description:"\u5E38\u89C4\u6309\u94AE clicked",value_light:"#2264E5",value_dark:"#255BC7"},color_btn_danger_disabled:{description:"\u5371\u9669\u6309\u94AE disabled",value_light:"#FFB3B3",value_dark:"#682B2B"},color_btn_danger_normal:{description:"\u5371\u9669\u6309\u94AE normal",value_light:"#FF4040",value_dark:"#D33B3B"},color_btn_danger_hover:{description:"\u5371\u9669\u6309\u94AE hover",value_light:"#FF7575",value_dark:"#D74F4F"},color_btn_danger_clicked:{description:"\u5371\u9669\u6309\u94AE clicked",value_light:"#E53A3A",value_dark:"#A23434"},color_info:{description:"\u53CD\u9988-\u4FE1\u606F",value_light:"#266FFF",value_dark:"#266BF3"},color_info_bg:{description:"\u53CD\u9988\u4FE1\u606F-\u80CC\u666F\u8272",value_light:"#EFF4FF",value_dark:"#222A3A"},color_success:{description:"\u53CD\u9988-\u6210\u529F",value_light:"#39BF50",value_dark:"#61CC73"},color_success_bg:{description:"\u53CD\u9988\u6210\u529F-\u80CC\u666F\u8272",value_light:"#F0FAF2",value_dark:"#232D24"},color_warning:{description:"\u53CD\u9988-\u8B66\u544A",value_light:"#FF8C19",value_dark:"#FFA347"},color_warning_bg:{description:"\u53CD\u9988\u8B66\u544A-\u80CC\u666F\u8272",value_light:"#FFF7EE",value_dark:"#312B24"},color_error:{description:"\u53CD\u9988-\u51FA\u9519",value_light:"#FF4040",value_dark:"#D33B3B"},color_error_bg:{description:"\u53CD\u9988\u51FA\u9519-\u80CC\u666F\u8272",value_light:"#FFF1F1",value_dark:"#2E2323"},color_polyline_normal:{description:"\u6807\u5C3A\u8F85\u52A9\u7EBF-normal",value_light:"rgba(255, 64, 64, 0.5)",value_dark:"rgba(255, 64, 64, 0.5)"},color_polyline_hover:{description:"\u6807\u5C3A\u8F85\u52A9\u7EBF-hover",value_light:"#FF4040",value_dark:"#D33B3B"},color_polyline_select:{description:"\u6807\u5C3A\u8F85\u52A9\u7EBF-\u9009\u4E2D",value_light:"#266FFF",value_dark:"#266FFF"},color_bg_tab:{description:"tab \u9009\u4E2D\u7684\u5E95\u8272",value_light:"#E6EFFF",value_dark:"#223252"},color_bg_select03:{description:"\u5217\u8868\u9009\u4E2D\u5B50\u7EA7",value_light:"#F2F6FF",value_dark:"#222937"},color_bg_select02:{description:"\u5217\u8868\u9009\u4E2D\u7236\u7EA7/\u6846\u9009\u80CC\u666F\u8272",value_light:"#D8E5FF",value_dark:"#223252"},color_bg_select01:{description:"\u9875\u9762\u6807\u8BC6 hover \u8272",value_light:"#A8C6FF",value_dark:"#234381"},color_textarea:{description:"\u6587\u5B57\u533A\u57DF\u80CC\u666F\u8272",value_light:"rgba(5, 88, 255, 0.24)",value_dark:"rgba(6, 92, 255, 0.35)"},color_grid:{description:"\u5E03\u5C40\u6805\u683C\u80CC\u666F\u8272/\u753B\u677F\u9AD8\u5EA6\u62D6\u62FD\u533A\u57DF\u80CC\u666F\u8272",value_light:"rgba(11, 93, 255, 0.16)",value_dark:"rgba(11, 93, 255, 0.16)"},color_smartline:{description:"\u667A\u80FD\u6D4B\u8DDD\u53C2\u8003\u7EBF\u989C\u8272",value_light:"#F23DD1",value_dark:"#F23DD1"},color_spacing:{description:"\u7B49\u95F4\u8DDD\u5757\u80CC\u666F\u53CA\u6570\u5B57\u8272",value_light:"rgba(242, 61, 209, 0.3)",value_dark:"rgba(245, 78, 219, 0.3)"},color_comments:{description:"\u6807\u6CE8\u6A21\u5F0F\u6D4B\u8DDD\u989C\u8272/\u8BC4\u8BBA\u6C14\u6CE1\u8272/\u70ED\u533A\u533A\u57DF\u63CF\u8FB9\u8272 ",value_light:"#FF8C19",value_dark:"#FF8C19"},color_hotspot:{description:"\u6807\u6CE8\u6A21\u5F0F\u70ED\u533A\u533A\u57DF\u586B\u5145\u8272 ",value_light:"rgba(255, 140, 25, 0.15)",value_dark:"rgba(255, 140, 25, 0.15)"},color_master:{description:"\u6BCD\u7248\u8272",value_light:"#7146FE",value_dark:"#7146FE"}},...{color_bg_white:{description:"\u56FA\u5B9A\u5C42\u80CC\u666F\u8272",value_light:"#ffffff",value_dark:"#212121"},color_bg_canvas:{description:"\u753B\u5E03\u80CC\u666F\u8272",value_light:"#EFEFEF",value_dark:"#1A1A1A"},color_btn_secondary_hover:{description:"\u6B21\u8981\u6309\u94AE/icon-hover\u80CC\u666F\u8272",value_light:"#F3F3F4",value_dark:"#333333"},color_btn_secondary_active:{description:"\u6B21\u8981\u6309\u94AE/icon-\u9009\u4E2D\u80CC\u666F\u8272",value_light:"#EEEEF0",value_dark:"#3C3C3C"},color_grid_line:{description:"\u56FE\u6807\u9009\u4E2D\u80CC\u666F\u8272/\u4E8C\u7EA7\u5206\u5272\u7EBF",value_light:"#E5E5E5",value_dark:"#454647"},color_bg_line:{description:"\u63A7\u4EF6\u63CF\u8FB9/\u4E00\u7EA7\u5206\u5272\u7EBF/\u83DC\u5355/default\u6309\u94AE\u63CF\u8FB9/\u5F39\u7A97\u63CF\u8FB9",value_light:"#DBDBDB",value_dark:"#4F5052"},color_bg_border_01:{description:"\u5206\u9694\u7EBF/\u83DC\u5355/\u5F39\u7A97\u5916\u63CF\u8FB9",value_light:"rgba(18, 17, 42, 0.07)",value_dark:"rgba(255, 255, 255, 0.08)"},color_bg_border_02:{description:"\u6B21\u8981\u6309\u94AE\u63CF\u8FB9/\u8F93\u5165\u6846/\u641C\u7D22\u6846",value_light:"rgba(2, 9, 16, 0.13)",value_dark:"rgba(255, 255, 255, 0.19)"},color_bg_split_hover:{description:"\u4E00\u7EA7\u5206\u5272\u7EBF hover \u6548\u679C",value_light:"#CCCCCC",value_dark:"#CCCCCC"},color_bg_card:{description:"\u7D20\u6750\u9762\u677F\u5361\u7247/tab\u80CC\u666F\u8272",value_light:"#F7F7F9",value_dark:"#303030"},color_navigation_hover:{description:"\u5DE6\u4FA7\u5BFC\u822A\u60AC\u505C",value_light:"rgba(18, 17, 42, 0.03)",value_dark:"rgba(255, 255, 255, 0.04)"},color_navigation_active:{description:"\u5DE6\u4FA7\u5BFC\u822A\u9009\u4E2D",value_light:"rgba(18, 17, 42, 0.05)",value_dark:"rgba(255, 255, 255, 0.08)"},color_directory_hover:{description:"\u76EE\u5F55\u9009\u9879\u60AC\u505C",value_light:"#FAFAFC",value_dark:"rgba(255, 255, 255, 0.05)"},color_directory_active:{description:"\u76EE\u5F55\u9009\u9879\u9009\u4E2D",value_light:"#F3F3F5",value_dark:"rgba(255, 255, 255, 0.08)"},color_bg_menu:{description:"\u83DC\u5355\u80CC\u666F\u8272",value_light:"#ffffff",value_dark:"#303030"},color_bg_tips:{description:"\u6C14\u6CE1/\u5168\u5C40\u63D0\u793A\u80CC\u666F\u8272",value_light:"#303030",value_dark:"#303030"},color_bg_mask:{description:"\u5F39\u7A97\u8499\u5C42\u8272",value_light:"rgba(18, 17, 42, 0.4)",value_dark:"rgba(0, 0, 0, 0.5)"},color_slider_normal:{description:"\u6ED1\u6746 normal \u8272",value_light:"rgba(18, 17, 42, 0.15)",value_dark:"rgba(255, 255, 255, 0.25)"},color_slider_hover:{description:"\u6ED1\u6746 hover \u8272",value_light:"rgba(18, 17, 42, 0.3)",value_dark:"rgba(255, 255, 255, 0.35)"}},...{color_text_btn:{description:"\u6309\u94AE\u5185normal\u6001\u6587\u672C\u8272",value_light:"#FFFFFF",value_dark:"rgba(255, 255, 255, 0.91)"},color_text_L0:{description:"\u6807\u9898\u6587\u672C\u8272/\u4E00\u7EA7\u6587\u672C\u8272",value_light:"#19191A",value_dark:"rgba(255, 255, 255, 0.91)"},color_text_L1:{description:"\u6807\u9898\u6587\u672C\u8272/\u4E00\u7EA7\u6587\u672C\u8272",value_light:"#19191A",value_dark:"rgba(255, 255, 255, 0.91)"},color_text_L2:{description:"\u4E8C\u7EA7\u6587\u672C\u8272",value_light:"#555557",value_dark:"rgba(255, 255, 255, 0.64)"},color_text_L3:{description:"\u5360\u4F4D\u7B26\u6587\u672C\u8272",value_light:"#8B8C8F",value_dark:"rgba(255, 255, 255, 0.45)"},color_text_disabled01:{description:"\u9ED1\u8272\u6587\u672C\u7981\u7528\u8272",value_light:"#B7B9BD",value_dark:"rgba(255, 255, 255, 0.19)"},color_text_disabled02:{description:"\u767D\u8272\u6587\u672C\u7981\u7528\u8272",value_light:"rgba(255, 255, 255, 0.82)",value_dark:"rgba(255, 255, 255, 0.19)"},color_text_link_normal:{description:"\u94FE\u63A5/\u6587\u672C\u6309\u94AE\u8272 normal",value_light:"#2264E5",value_dark:"#88B0FF"},color_text_link_hover:{description:"\u94FE\u63A5/\u6587\u672C\u6309\u94AE\u8272 hover",value_light:"#6297FF",value_dark:"#93B7FF"},color_text_tips_normal:{description:"\u63D0\u793A\u5185\u84DD\u8272\u6587\u672C\u8272 normal",value_light:"#88B0FF",value_dark:"#88B0FF"},color_text_tips_hover:{description:"\u63D0\u793A\u5185\u84DD\u8272\u6587\u672C\u8272 hover",value_light:"#A8C6FF",value_dark:"#A8C6FF"},color_text_error:{description:"\u51FA\u9519\u6587\u672C\u8272",value_light:"#D34444",value_dark:"#FF7575"}}}},33236:(Se,ce,n)=>{"use strict";n.d(ce,{l:()=>N,r:()=>p});var s=n(67787);const p=(0,s.DU)(["::-webkit-scrollbar-track{background-color:transparent;}::-webkit-scrollbar{width:4px;height:4px;border-radius:4px;}::-webkit-scrollbar-thumb{background-color:",";border-radius:4px;&:hover{background-color:",";}}::-webkit-scrollbar-corner{background-color:transparent;}.widget.panel{::-webkit-scrollbar-thumb{background-color:#d2d2d8;&:hover{background-color:",";}}}"],u=>u.theme.color_bg_border_02,u=>u.theme.color_background_split_hover,u=>u.theme.color_background_split_hover),N=(0,s.DU)([".ModalPortal,.CoreModalPortal,.OverlayPortal{min-width:initial !important;}"])},35428:(Se,ce,n)=>{"use strict";n.d(ce,{_:()=>N});var s=n(5209);const p=u=>{let{exposure_reason:B}=u;try{saTrack("trial_watermark_exposure",{exposure_reason:B})}catch(q){console.log(q.message)}},N=u=>{let{click_button:B}=u;try{(0,s.kH)("trial_watermark_click",{click_button:B})}catch(q){console.log(q.message)}}},35603:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>S,d:()=>ie});var s=n(18250),p=n.n(s),N=n(63986),u=n.n(N),B=n(38502),q=n(69623),V=n(25582),R=n.n(V),J=n(27992),M=n(69368);const F=9;function ie(K){let{$opener:d,$menuBase:j,menuX:T="left",menuY:X="bottom",menuBaseStyle:g={},inflexible:L=!1,shouldSetMaxHeight:ne=!1}=K;if(!d||!j)return{};const ee=j.querySelector("*"),se={styleFor$menuBase:{},styleFor$menu:{}},te=Je=>Object.assign(se.styleFor$menuBase,Je),fe=Je=>Object.assign(se.styleFor$menu,Je),{offsetHeight:De}=ee,Ye=g.width||d.offsetWidth,ke=g.height||d.offsetHeight,Re=d.getBoundingClientRect(),{top:mt,bottom:Pe,left:Q}=Object.assign({top:Re.top,right:Re.right,bottom:Re.bottom,left:Re.left},g);te({top:mt+"px",left:Q+"px",width:Ye+"px",height:ke+"px"});const{innerHeight:Y}=window,me=10,Ne=Y-10,je=X==="top"?1/3:2/3,Ge=Y*je,Me=mt+ke/2,Ie=mt+ke;return(L&&X==="bottom"||!L&&Ge>=Me)&&Ie+De+F<Y?(se.isDownward=!0,ne&&Pe+De>Ne&&fe({maxHeight:Ne-Pe+"px"})):(se.isDownward=!1,ne&&mt-De<me&&fe({maxHeight:mt-me+"px"})),Object.assign(j.style,se.styleFor$menuBase),Object.assign(ee.style,se.styleFor$menu),se}var D=n(67787);const pe=D.Ay.label.withConfig({displayName:"styled__StyledDropDown",componentId:"sc-665wv9-0"})(["&.is-disabled{opacity:0.5;> button{cursor:not-allowed;}}"]),H=D.Ay.div.withConfig({displayName:"styled__StyledDropDownMenu",componentId:"sc-665wv9-1"})(["position:absolute;margin:0.25em 0;width:-moz-fit-content;width:-webkit-fit-content;width:fit-content;min-width:7em;font-size:12px;filter:drop-shadow(0 2px 10px rgba(39,54,78,0.12)) drop-shadow(4px 12px 40px rgba(39,54,78,0.12));pointer-events:none;opacity:0;transition:0.2s ease-in-out;transition-property:transform,opacity;transform-origin:50% 0;transform:scale(0.9);&.is-open{pointer-events:initial;opacity:1;transform:scale(1);}&.x-left{left:0;}&.x-right{left:initial;right:0;}&.x-center{left:50%;transform:scale(0.9) translateX(-50%);&.is-open{transform:scale(1) translateX(-50%);}}&.is-downward{top:100%;bottom:initial;}&.is-upward{top:initial;bottom:100%;}&.arrowed{margin-top:0.75em;margin-bottom:0.75em;&.x-left.x-arrow-based{left:50%;margin-left:-14px;}&.x-right.x-arrow-based{left:initial;right:50%;margin-right:-14px;}}.arrow{position:absolute;top:0;left:calc(50% - 0.5em);width:1em;height:0.375em;font-size:14px;line-height:0;fill:currentColor;fill-rule:evenodd;color:#fff;svg{position:absolute;width:auto;height:5px;transform:translateY(-100%);}}&.dark .arrow{color:rgba(30,41,46,.9);}&.x-left .arrow{left:0.5em;right:initial;}&.x-right .arrow{left:initial;right:0.5em;}&.is-upward .arrow{transform:rotate(180deg);top:initial;bottom:0;}.content{padding:0.75em 0.5em;min-height:32px;background-color:#fff;border-radius:4px;}&.dark .content{background-color:rgba(30,41,46,.9);color:#fff;}ul.MenuList{margin:0;padding:0;list-style:none;text-align:center;color:#5b6b73;li{line-height:32px;border-radius:2px;cursor:pointer;&:not(.is-disabled):hover{background-color:#f6f7f8;}&.is-active{color:#298df8;}&.is-disabled{cursor:not-allowed;opacity:0.5;}}}"]),G=D.Ay.div.withConfig({displayName:"styled__StyledDropDownBase",componentId:"sc-665wv9-2"})(["position:fixed;z-index:1100;pointer-events:none;"]);var I=n(72214);function v(K,d,j){return(d=C(d))in K?Object.defineProperty(K,d,{value:j,enumerable:!0,configurable:!0,writable:!0}):K[d]=j,K}function C(K){var d=f(K,"string");return typeof d=="symbol"?d:d+""}function f(K,d){if(typeof K!="object"||!K)return K;var j=K[Symbol.toPrimitive];if(j!==void 0){var T=j.call(K,d||"default");if(typeof T!="object")return T;throw new TypeError("@@toPrimitive must return a primitive value.")}return(d==="string"?String:Number)(K)}const y="IBOT_DROPDOWN_MENU_ROOT",h=document.getElementById(y)||Object.assign(document.createElement("div"),{id:y}),b=document.body;b.contains(h)||b.appendChild(h);class S extends B.PureComponent{constructor(){super(...arguments),v(this,"state",{prevProps:this.props,isOpen:this.props.isOpen,$opener:null,currentMenuListItemIdx:this.props.currentMenuListItemIdx}),v(this,"leaveTimeoutList",[]),v(this,"createMenuRef",d=>this.$menuRef=d),v(this,"toggle",d=>this.setState({isOpen:p()(d)?d:!this.state.isOpen})),v(this,"open",()=>this.toggle(!0)),v(this,"close",()=>this.toggle(!1)),v(this,"onMouseEnter",()=>{const{shouldOpenOnHover:d}=this.props;d&&(clearTimeout(this.closeTimeout),Object.assign(this,{hoverTimeout:setTimeout(this.open,this.props.hoverDelay)}))}),v(this,"onMouseLeave",()=>{const{shouldOpenOnHover:d}=this.props;d&&clearTimeout(this.hoverTimeout)}),v(this,"onMouseMove",d=>{var j;let{clientX:T,clientY:X}=d;const{shouldOpenOnHover:g,hoverDelay:L,hoverCloseDelay:ne}=this.props,{$opener:ee}=this.state;if(!g)return;clearTimeout(this.hoverTimeout);const se=document.elementFromPoint(T,X),te=!ee.contains(se),fe=!((j=this.$menuRef)!=null&&(j=j.menuBaseRef)!=null&&(j=j.current)!=null&&j.contains(se));fe?te&&fe&&this.leaveTimeoutList.push(setTimeout(this.close,ne!==void 0?ne:Math.max(L,300))):(this.leaveTimeoutList.map(clearTimeout),Object.assign(this,{leaveTimeoutList:[]}))}),v(this,"set$opener",d=>this.setState({$opener:d})),v(this,"onSelect",d=>{let{currentTarget:j}=d;const{menuList:T,onSelect:X,shouldCloseOnSelect:g}=this.props;if(typeof X!="function")return;const L=j.dataset.idx,ne=T[L],ee=typeof ne=="string"?ne:ne&&ne.value;X(L,ee),this.setState({currentMenuListItemIdx:L}),g&&this.close()})}static getDerivedStateFromProps(d,j){let{prevProps:T,isOpen:X}=j;return u()(d,T)?null:p()(d.isOpen)?{prevProps:d,isOpen:d.isOpen}:{prevProps:d}}componentDidUpdate(d,j){let{isOpen:T}=j;const{onOpen:X,onClose:g,onToggle:L}=this.props,{isOpen:ne}=this.state;T!==ne&&(ne?(X(),L(!0)):(g(),L(!1)))}render(){const{className:d,opener:j,openerType:T,openerClassName:X,shouldCloseOnClickOutside:g}=this.props,{isOpen:L,$opener:ne,currentMenuListItemIdx:ee}=this.state,se=this.props.isDisabled||this.props.disabled,te=(0,M.Hn)(["Dropdown",L&&"is-open",se&&"is-disabled",d]),fe={onClick:this.toggle,onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave,onMouseMove:this.onMouseEnter,disabled:se,className:X};return(0,I.jsxs)(pe,{ref:this.set$opener,className:te,children:[T!=="button"&&(0,B.isValidElement)(j)?(0,B.cloneElement)(j,fe):(0,I.jsx)("button",{type:"button",...fe,children:j}),(0,I.jsx)(A,{ref:this.createMenuRef,...this.props,isOpen:L,$opener:ne,onSelect:this.onSelect,onClose:this.close,shouldCloseOnClickOutside:g,currentMenuListItemIdx:ee}),L&&(0,I.jsx)(J.A,{target:document,onMouseMove:this.onMouseMove})]})}}v(S,"positionMenu",ie),v(S,"propTypes",{isOpen:R().bool,mode:R().oneOf(["light","dark"]),opener:R().node,openerType:R().oneOf(["button","custom"]),className:R().string,portalClassName:R().string,menuBaseClassName:R().string,openerClassName:R().string,menuClassName:R().string,menuBaseStyle:R().shape({top:R().number,right:R().number,bottom:R().number,width:R().number,height:R().number}),menu:R().node,menuList:R().arrayOf(R().oneOfType([R().node,R().shape({label:R().node,value:R().any,isDisabled:R().bool})])),currentMenuListItemIdx:R().oneOfType([R().number,R().string]),shouldPreventScrollingPropagation:R().bool,shouldOpenOnHover:R().bool,shouldCloseOnClickOutside:R().bool,hoverDelay:R().oneOfType([R().number,R().string]),hoverCloseDelay:R().oneOfType([R().number,R().string]),arrowed:R().bool,inflexible:R().bool,menuX:R().oneOf(["left","center","right"]),menuY:R().oneOf(["top","bottom"]),menuBasedX:R().bool,isDisabled:R().bool,disabled:R().bool,onSelect:R().func,shouldCloseOnSelect:R().bool,onOpen:R().func.isRequired,onClose:R().func.isRequired,onToggle:R().func.isRequired}),v(S,"defaultProps",{arrowed:!1,openerType:"button",mode:"light",shouldPreventScrollingPropagation:!0,shouldCloseOnSelect:!0,shouldOpenOnHover:!1,shouldCloseOnClickOutside:!0,hoverDelay:200,menuX:"center",menuY:"bottom",inflexible:!1,menuBasedX:!1,onOpen:()=>null,onClose:()=>null,onToggle:()=>null});class A extends B.PureComponent{constructor(){super(...arguments),v(this,"state",{isDownward:this.props.position==="bottom"}),v(this,"portal",(0,M.ep)(h,(0,M.Hn)(["DropdownMenuPortal",this.props.portalClassName]))),v(this,"menuBaseRef",(0,B.createRef)()),v(this,"onResizeWindow",()=>this.props.isOpen&&this.position()),v(this,"onClickOutside",d=>{let{target:j}=d;const{$opener:T,onClose:X,shouldCloseOnClickOutside:g}=this.props;if(!g)return;const L=!h.contains(j),ne=j.closest("label"),ee=ne&&ne.contains(T),se=!!(0,M.$)(".SelectMenu.is-open");L&&!ee&&!se&&X()}),v(this,"position",()=>{const{$opener:d,menuX:j,menuY:T,menuBaseStyle:X,inflexible:g}=this.props,{menuBaseRef:{current:L}}=this,{isDownward:ne}=ie({$menuBase:L,$opener:d,menuX:j,menuY:T,menuBaseStyle:X,inflexible:g});this.setState({isDownward:ne})})}componentDidMount(){const{isOpen:d,shouldPreventScrollingPropagation:j}=this.props,{menuBaseRef:{current:T}}=this;d&&setTimeout(this.position),j&&(0,M.sA)((0,M.$)(".content",T)),window.addEventListener("resize",this.onResizeWindow)}componentDidUpdate(d){let{isOpen:j}=d;const{isOpen:T}=this.props;!j&&T&&this.position()}componentWillUnmount(){this.portal&&this.portal.remove(),window.removeEventListener("resize",this.onResizeWindow)}render(){const{portal:d,menu:j}=this;return(0,q.createPortal)(j,d)}get menu(){const{isOpen:d,mode:j,menuBaseClassName:T,menuClassName:X,menu:g,menuList:L,arrowed:ne,menuX:ee,menuBasedX:se,currentMenuListItemIdx:te,onSelect:fe}=this.props,{isDownward:De}=this.state,Ye=(0,M.Hn)(["DropdownMenu",j,d&&"is-open",De?"is-downward":"is-upward","x-"+ee,ne&&"arrowed "+(se?"x-menu-based":"x-arrow-based"),X]);return(0,I.jsx)(G,{ref:this.menuBaseRef,className:(0,M.Hn)(["DropdownMenuBase",T]),children:(0,I.jsxs)(H,{className:Ye,children:[ne&&(0,I.jsx)("span",{className:"arrow",dangerouslySetInnerHTML:{__html:M.t4.mr}}),(0,I.jsx)("div",{className:"content",children:L?(0,I.jsx)("ul",{className:"MenuList",children:L.map((ke,Re)=>(0,I.jsx)("li",{role:"option","data-idx":Re,className:(0,M.Hn)([ke.isDisabled&&"is-disabled",Re===Number(te)&&"is-active"]),onClick:ke.isDisabled?void 0:fe,children:ke.label||ke},Re))}):g}),d&&(0,I.jsx)(J.A,{target:document,onClick:(0,J.t)(this.onClickOutside,{capture:!0})}),d&&(0,I.jsx)(J.A,{target:document,onScroll:(0,J.t)(this.position,{capture:!0})})]})})}}v(A,"propTypes",{...S.propTypes,isOpen:R().bool,$opener:R().instanceOf(Element),onSelect:R().func,onClose:R().func})},35954:Se=>{function ce(n){return function(s){return n==null?void 0:n[s]}}Se.exports=ce},37810:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>R});var s=n(95549),p=n(38502),N=n(19952),u=n(25582),B=n.n(u),q=n(72214);class V extends p.PureComponent{constructor(M){super(M),(0,s.A)(this,"freshCanvas",()=>{const F=this.canvasWrapperRef.current,{height:ie,width:D,text:pe,foreground:H,background:G}=this.props,I=(0,N.j)({height:ie,width:D,text:pe,foreground:H,background:G});F.hasChildNodes()?F.replaceChild(I,F.firstChild):F.appendChild(I)}),this.canvasWrapperRef=(0,p.createRef)()}componentDidMount(){this.freshCanvas()}componentDidUpdate(){this.freshCanvas()}render(){const{className:M}=this.props;return(0,q.jsx)("div",{className:M,ref:this.canvasWrapperRef})}}V.propTypes={width:B().number,height:B().number,text:B().string.isRequired,foreground:B().string,background:B().string,className:B().string},V.defaultProps={width:150,height:150};const R=V},37862:(Se,ce,n)=>{var s=n(70168),p=n(43293);function N(u){return s(u)&&u.nodeType===1&&!p(u)}Se.exports=N},39303:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>u});var s=n(79458),p=n(67787);const u=(0,p.Ay)(s.ft).withConfig({displayName:"ThemeLoading",componentId:"sc-1f7bkn3-0"})(["svg g g:first-child g path:last-child{stroke:",";}svg g g:last-child g path:last-child{stroke:",";}"],B=>B.theme.color_btn_secondary_active,B=>B.theme.color_text_disabled01)},41440:(Se,ce,n)=>{var s=n(43293);function p(N){return s(N)?void 0:N}Se.exports=p},41946:(Se,ce,n)=>{var s=n(73597),p=s(function(N,u,B){return N+(B?"_":"")+u.toLowerCase()});Se.exports=p},43293:(Se,ce,n)=>{var s=n(41678),p=n(41413),N=n(70168),u="[object Object]",B=Function.prototype,q=Object.prototype,V=B.toString,R=q.hasOwnProperty,J=V.call(Object);function M(F){if(!N(F)||s(F)!=u)return!1;var ie=p(F);if(ie===null)return!0;var D=R.call(ie,"constructor")&&ie.constructor;return typeof D=="function"&&D instanceof D&&V.call(D)==J}Se.exports=M},45308:(Se,ce,n)=>{var s=n(71602);function p(N){var u=N==null?0:N.length;return u?s(N,1):[]}Se.exports=p},45525:(Se,ce,n)=>{var s=n(32554),p=n(86789),N=n(48605),u=n(68151),B=n(31493),q=n(41440),V=n(85378),R=n(24235),J=1,M=2,F=4,ie=V(function(D,pe){var H={};if(D==null)return H;var G=!1;pe=s(pe,function(v){return v=u(v,D),G||(G=v.length>1),v}),B(D,R(D),H),G&&(H=p(H,J|M|F,q));for(var I=pe.length;I--;)N(H,pe[I]);return H});Se.exports=ie},46710:(Se,ce,n)=>{"use strict";n.d(ce,{HZ:()=>N,rA:()=>B});var s=n(51785),p=n(83123);const N=q=>{const V={pt_total_seats:10,pt_bindings:[],ptlt_total_seats:0,ptlt_bindings:[],bx_total_seats:10,bx_bindings:[],bxlt_total_seats:0,bxlt_bindings:[],aippt_total_seats:0,aippt_bindings:[],total_viewer_seats:100,viewer_seats_taken:0},{pt_total_seats:R=0,pt_bindings:J=[],ptlt_total_seats:M=0,ptlt_bindings:F=[],bxlt_bindings:ie=[],bxlt_total_seats:D=0,bx_total_seats:pe=0,bx_bindings:H=[],aippt_total_seats:G=0,aippt_bindings:I=[]}=q||V;return{plTotalSeats:M,pTotalSeats:R,pBindings:J||[],plBindings:F||[],blTotalSeats:D,bTotalSeats:pe,bBindings:H||[],blBindings:ie||[],aiPptTotalSeats:G,aiPptBindings:I||[]}},u=(q,V)=>{const{pBindings:R,plBindings:J,bBindings:M}=N(q),F=J.includes(V),ie=R.includes(V),D=M.includes(V);return{isPLSeat:F,isPSeat:ie,isBSeat:D}},B=(q,V)=>{if(!q)return{protoSeat:!1,bomxSeat:!1};if(q.otype==="personal")return{protoSeat:!0,bomxSeat:!0};const R=s.tz.InitialOrg(q),J=R.planSdk.getIsDefaultFreeOrg(),M=R.planSdk.getOrgStatus()===p._E.Trial;if(J||M)return{protoSeat:!0,bomxSeat:!0};const{pTotalSeats:F,bTotalSeats:ie,plTotalSeats:D}=N(q),{isPLSeat:pe,isPSeat:H,isBSeat:G}=u(q,V);return{protoSeat:F>0||D>0?pe||H:!0,bomxSeat:ie>0?G:!0}}},47507:(Se,ce,n)=>{"use strict";n.d(ce,{q:()=>p});const s={common_white:"#FFFFFF",common_black:"#000000",primary_blue_01:"#126ACA",primary_blue_02:"#1684FC",primary_blue_03:"#459DFD",primary_blue_04:"#8AC1FD",primary_blue_05:"#B9DAFE",primary_blue_06:"#D0E6FE",primary_blue_07:"#E8F3FF",primary_blue_08:"#F5F8FD",primary_gray_01:"#19191A",primary_gray_02:"#555557",primary_gray_03:"#7D8CA5",primary_gray_04:"#9EA9BC",primary_gray_05:"#BEC5D2",primary_gray_06:"#D2D9E4",primary_gray_07:"#E1E6EF",primary_gray_08:"#F0F2F7",primary_gray_09:"#F3F5F9",primary_red_01:"#DD4747",primary_red_02:"#F85050",primary_red_03:"#F97373",primary_red_04:"#FDC2C2",primary_red_05:"#FEE5E5"},p={color_btn_primary_normal:s.primary_blue_02,color_btn_primary_hover:s.primary_blue_03,color_btn_primary_click:s.primary_blue_01,color_btn_primary_disable:s.primary_blue_05,color_btn_secondary_click:s.primary_blue_06,color_btn_secondary_normal:s.primary_blue_08,color_btn_secondary_hover:s.primary_blue_07,color_btn_outline_click:s.primary_gray_07,color_btn_outline_normal:s.common_white,color_btn_outline_hover:s.primary_gray_08,color_bg_normal:s.common_white,color_bg_card:s.primary_blue_08,color_bg_menu_click:s.primary_blue_06,color_bg_card_hover:s.primary_gray_07,color_bg_menu_hover:s.primary_blue_07,color_bg_input_hover:s.primary_gray_08,color_bg_input_normal:s.primary_gray_09,color_bg_item_gray:s.primary_gray_08,color_bg_item_hover:s.primary_gray_09,color_bg_fill_common_blue:s.primary_blue_02,color_bg_menu_drag_border:s.primary_blue_06,color_bg_card_hover_fusion_gray:s.primary_gray_06,color_bg_card_hover_fusion_blue:s.primary_blue_05,color_bg_card_hover_fusion:s.primary_gray_06,color_bg_secondary_btn:s.primary_blue_08,color_bg_btn_normal_hover:s.primary_gray_08,color_bg_btn_normal_active:s.primary_gray_07,color_bg_icon_gray:s.primary_gray_04,color_bg_icon_hover:s.primary_gray_03,color_text_L1:s.primary_gray_01,color_text_L2:s.primary_gray_02,color_text_L3:s.primary_gray_03,color_text_L4:s.primary_gray_04,color_text_L5:s.primary_blue_02,color_text_white:s.common_white,color_text_blue:s.primary_blue_02,color_text_blue_hover:s.primary_blue_01,color_text_blue_active:s.primary_blue_03,color_text_disable_01:s.primary_gray_05,color_tab_line:s.primary_gray_06,color_split_line:s.primary_gray_07,color_border_blue_common:s.primary_blue_02,color_btn_border_normal:s.primary_blue_05,color_btn_border_disable:s.primary_blue_07,color_btn_secondary_border_normal:s.primary_blue_06,color_btn_secondary_border_hover:s.primary_blue_07,color_btn_danger_normal:s.primary_red_02,color_btn_danger_hover:s.primary_red_03,color_btn_danger_click:s.primary_red_01,color_btn_danger_disable:s.primary_red_04,color_bg_modal_item_header:s.primary_gray_09,color_bg_light_red:s.primary_red_05,color_alarm_common:s.primary_red_02}},47946:(Se,ce,n)=>{"use strict";n.d(ce,{pX:()=>ko,O3:()=>Vs,tr:()=>fs,ur:()=>Lo});var s={};if(n.r(s),n.d(s,{Decoder:()=>Tn,Encoder:()=>es,PacketType:()=>ut,protocol:()=>Xs}),n.j!=15)var p=n(76805);if(n.j!=15)var N=n(60892);const u=Object.create(null);u.open="0",u.close="1",u.ping="2",u.pong="3",u.message="4",u.upgrade="5",u.noop="6";const B=Object.create(null);Object.keys(u).forEach(a=>{B[u[a]]=a});const q={type:"error",data:"parser error"},V=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",R=typeof ArrayBuffer=="function",J=a=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(a):a&&a.buffer instanceof ArrayBuffer,M=(a,t,o)=>{let{type:m,data:z}=a;return V&&z instanceof Blob?t?o(z):F(z,o):R&&(z instanceof ArrayBuffer||J(z))?t?o(z):F(new Blob([z]),o):o(u[m]+(z||""))},F=(a,t)=>{const o=new FileReader;return o.onload=function(){const m=o.result.split(",")[1];t("b"+(m||""))},o.readAsDataURL(a)};function ie(a){return a instanceof Uint8Array?a:a instanceof ArrayBuffer?new Uint8Array(a):new Uint8Array(a.buffer,a.byteOffset,a.byteLength)}let D;function pe(a,t){if(V&&a.data instanceof Blob)return a.data.arrayBuffer().then(ie).then(t);if(R&&(a.data instanceof ArrayBuffer||J(a.data)))return t(ie(a.data));M(a,!1,o=>{D||(D=new TextEncoder),t(D.encode(o))})}const H="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",G=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let a=0;a<H.length;a++)G[H.charCodeAt(a)]=a;const I=a=>{let t=new Uint8Array(a),o,m=t.length,z="";for(o=0;o<m;o+=3)z+=H[t[o]>>2],z+=H[(t[o]&3)<<4|t[o+1]>>4],z+=H[(t[o+1]&15)<<2|t[o+2]>>6],z+=H[t[o+2]&63];return m%3===2?z=z.substring(0,z.length-1)+"=":m%3===1&&(z=z.substring(0,z.length-2)+"=="),z},v=a=>{let t=a.length*.75,o=a.length,m,z=0,de,Ae,Ee,Ue;a[a.length-1]==="="&&(t--,a[a.length-2]==="="&&t--);const Ke=new ArrayBuffer(t),Fe=new Uint8Array(Ke);for(m=0;m<o;m+=4)de=G[a.charCodeAt(m)],Ae=G[a.charCodeAt(m+1)],Ee=G[a.charCodeAt(m+2)],Ue=G[a.charCodeAt(m+3)],Fe[z++]=de<<2|Ae>>4,Fe[z++]=(Ae&15)<<4|Ee>>2,Fe[z++]=(Ee&3)<<6|Ue&63;return Ke},C=typeof ArrayBuffer=="function",f=(a,t)=>{if(typeof a!="string")return{type:"message",data:h(a,t)};const o=a.charAt(0);return o==="b"?{type:"message",data:y(a.substring(1),t)}:B[o]?a.length>1?{type:B[o],data:a.substring(1)}:{type:B[o]}:q},y=(a,t)=>{if(C){const o=v(a);return h(o,t)}else return{base64:!0,data:a}},h=(a,t)=>{switch(t){case"blob":return a instanceof Blob?a:new Blob([a]);case"arraybuffer":default:return a instanceof ArrayBuffer?a:a.buffer}},b="",S=(a,t)=>{const o=a.length,m=new Array(o);let z=0;a.forEach((de,Ae)=>{M(de,!1,Ee=>{m[Ae]=Ee,++z===o&&t(m.join(b))})})},A=(a,t)=>{const o=a.split(b),m=[];for(let z=0;z<o.length;z++){const de=f(o[z],t);if(m.push(de),de.type==="error")break}return m};function K(){return new TransformStream({transform(a,t){pe(a,o=>{const m=o.length;let z;if(m<126)z=new Uint8Array(1),new DataView(z.buffer).setUint8(0,m);else if(m<65536){z=new Uint8Array(3);const de=new DataView(z.buffer);de.setUint8(0,126),de.setUint16(1,m)}else{z=new Uint8Array(9);const de=new DataView(z.buffer);de.setUint8(0,127),de.setBigUint64(1,BigInt(m))}a.data&&typeof a.data!="string"&&(z[0]|=128),t.enqueue(z),t.enqueue(o)})}})}let d;function j(a){return a.reduce((t,o)=>t+o.length,0)}function T(a,t){if(a[0].length===t)return a.shift();const o=new Uint8Array(t);let m=0;for(let z=0;z<t;z++)o[z]=a[0][m++],m===a[0].length&&(a.shift(),m=0);return a.length&&m<a[0].length&&(a[0]=a[0].slice(m)),o}function X(a,t){d||(d=new TextDecoder);const o=[];let m=0,z=-1,de=!1;return new TransformStream({transform(Ae,Ee){for(o.push(Ae);;){if(m===0){if(j(o)<1)break;const Ue=T(o,1);de=(Ue[0]&128)===128,z=Ue[0]&127,z<126?m=3:z===126?m=1:m=2}else if(m===1){if(j(o)<2)break;const Ue=T(o,2);z=new DataView(Ue.buffer,Ue.byteOffset,Ue.length).getUint16(0),m=3}else if(m===2){if(j(o)<8)break;const Ue=T(o,8),Ke=new DataView(Ue.buffer,Ue.byteOffset,Ue.length),Fe=Ke.getUint32(0);if(Fe>Math.pow(2,21)-1){Ee.enqueue(q);break}z=Fe*Math.pow(2,32)+Ke.getUint32(4),m=3}else{if(j(o)<z)break;const Ue=T(o,z);Ee.enqueue(f(de?Ue:d.decode(Ue),t)),m=0}if(z===0||z>a){Ee.enqueue(q);break}}}})}const g=4;function L(a){if(a)return ne(a)}function ne(a){for(var t in L.prototype)a[t]=L.prototype[t];return a}L.prototype.on=L.prototype.addEventListener=function(a,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+a]=this._callbacks["$"+a]||[]).push(t),this},L.prototype.once=function(a,t){function o(){this.off(a,o),t.apply(this,arguments)}return o.fn=t,this.on(a,o),this},L.prototype.off=L.prototype.removeListener=L.prototype.removeAllListeners=L.prototype.removeEventListener=function(a,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var o=this._callbacks["$"+a];if(!o)return this;if(arguments.length==1)return delete this._callbacks["$"+a],this;for(var m,z=0;z<o.length;z++)if(m=o[z],m===t||m.fn===t){o.splice(z,1);break}return o.length===0&&delete this._callbacks["$"+a],this},L.prototype.emit=function(a){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),o=this._callbacks["$"+a],m=1;m<arguments.length;m++)t[m-1]=arguments[m];if(o){o=o.slice(0);for(var m=0,z=o.length;m<z;++m)o[m].apply(this,t)}return this},L.prototype.emitReserved=L.prototype.emit,L.prototype.listeners=function(a){return this._callbacks=this._callbacks||{},this._callbacks["$"+a]||[]},L.prototype.hasListeners=function(a){return!!this.listeners(a).length};const ee=typeof self<"u"?self:typeof window<"u"?window:Function("return this")();function se(a){for(var t=arguments.length,o=new Array(t>1?t-1:0),m=1;m<t;m++)o[m-1]=arguments[m];return o.reduce((z,de)=>(a.hasOwnProperty(de)&&(z[de]=a[de]),z),{})}const te=ee.setTimeout,fe=ee.clearTimeout;function De(a,t){t.useNativeTimers?(a.setTimeoutFn=te.bind(ee),a.clearTimeoutFn=fe.bind(ee)):(a.setTimeoutFn=ee.setTimeout.bind(ee),a.clearTimeoutFn=ee.clearTimeout.bind(ee))}const Ye=1.33;function ke(a){return typeof a=="string"?Re(a):Math.ceil((a.byteLength||a.size)*Ye)}function Re(a){let t=0,o=0;for(let m=0,z=a.length;m<z;m++)t=a.charCodeAt(m),t<128?o+=1:t<2048?o+=2:t<55296||t>=57344?o+=3:(m++,o+=4);return o}function mt(a){let t="";for(let o in a)a.hasOwnProperty(o)&&(t.length&&(t+="&"),t+=encodeURIComponent(o)+"="+encodeURIComponent(a[o]));return t}function Pe(a){let t={},o=a.split("&");for(let m=0,z=o.length;m<z;m++){let de=o[m].split("=");t[decodeURIComponent(de[0])]=decodeURIComponent(de[1])}return t}class Q extends Error{constructor(t,o,m){super(t),this.description=o,this.context=m,this.type="TransportError"}}class Y extends L{constructor(t){super(),this.writable=!1,De(this,t),this.opts=t,this.query=t.query,this.socket=t.socket}onError(t,o,m){return super.emitReserved("error",new Q(t,o,m)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const o=f(t,this.socket.binaryType);this.onPacket(o)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,o){return o===void 0&&(o={}),t+"://"+this._hostname()+this._port()+this.opts.path+this._query(o)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const o=mt(t);return o.length?"?"+o:""}}const me="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),Ne=64,je={};let Ge=0,Me=0,Ie;function Je(a){let t="";do t=me[a%Ne]+t,a=Math.floor(a/Ne);while(a>0);return t}function nt(a){let t=0;for(Me=0;Me<a.length;Me++)t=t*Ne+je[a.charAt(Me)];return t}function yt(){const a=Je(+new Date);return a!==Ie?(Ge=0,Ie=a):a+"."+Je(Ge++)}for(;Me<Ne;Me++)je[me[Me]]=Me;let xt=!1;try{xt=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch(a){}const bt=xt;function wt(a){const t=a.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||bt))return new XMLHttpRequest}catch(o){}if(!t)try{return new ee[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(o){}}function r(){}function ue(){}const ve=function(){return new wt({xdomain:!1}).responseType!=null}();class Te extends Y{constructor(t){if(super(t),this.polling=!1,typeof location<"u"){const m=location.protocol==="https:";let z=location.port;z||(z=m?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||z!==t.port}const o=t&&t.forceBase64;this.supportsBinary=ve&&!o,this.opts.withCredentials&&(this.cookieJar=void 0)}get name(){return"polling"}doOpen(){this.poll()}pause(t){this.readyState="pausing";const o=()=>{this.readyState="paused",t()};if(this.polling||!this.writable){let m=0;this.polling&&(m++,this.once("pollComplete",function(){--m||o()})),this.writable||(m++,this.once("drain",function(){--m||o()}))}else o()}poll(){this.polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const o=m=>{if(this.readyState==="opening"&&m.type==="open"&&this.onOpen(),m.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(m)};A(t,this.socket.binaryType).forEach(o),this.readyState!=="closed"&&(this.polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this.poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,S(t,o=>{this.doWrite(o,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",o=this.query||{};return this.opts.timestampRequests!==!1&&(o[this.opts.timestampParam]=yt()),!this.supportsBinary&&!o.sid&&(o.b64=1),this.createUri(t,o)}request(t){return t===void 0&&(t={}),Object.assign(t,{xd:this.xd,cookieJar:this.cookieJar},this.opts),new He(this.uri(),t)}doWrite(t,o){const m=this.request({method:"POST",data:t});m.on("success",o),m.on("error",(z,de)=>{this.onError("xhr post error",z,de)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(o,m)=>{this.onError("xhr poll error",o,m)}),this.pollXhr=t}}class He extends L{constructor(t,o){super(),De(this,o),this.opts=o,this.method=o.method||"GET",this.uri=t,this.data=o.data!==void 0?o.data:null,this.create()}create(){var t;const o=se(this.opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");o.xdomain=!!this.opts.xd;const m=this.xhr=new wt(o);try{m.open(this.method,this.uri,!0);try{if(this.opts.extraHeaders){m.setDisableHeaderCheck&&m.setDisableHeaderCheck(!0);for(let z in this.opts.extraHeaders)this.opts.extraHeaders.hasOwnProperty(z)&&m.setRequestHeader(z,this.opts.extraHeaders[z])}}catch(z){}if(this.method==="POST")try{m.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(z){}try{m.setRequestHeader("Accept","*/*")}catch(z){}(t=this.opts.cookieJar)===null||t===void 0||t.addCookies(m),"withCredentials"in m&&(m.withCredentials=this.opts.withCredentials),this.opts.requestTimeout&&(m.timeout=this.opts.requestTimeout),m.onreadystatechange=()=>{var z;m.readyState===3&&((z=this.opts.cookieJar)===null||z===void 0||z.parseCookies(m)),m.readyState===4&&(m.status===200||m.status===1223?this.onLoad():this.setTimeoutFn(()=>{this.onError(typeof m.status=="number"?m.status:0)},0))},m.send(this.data)}catch(z){this.setTimeoutFn(()=>{this.onError(z)},0);return}typeof document<"u"&&(this.index=He.requestsCount++,He.requests[this.index]=this)}onError(t){this.emitReserved("error",t,this.xhr),this.cleanup(!0)}cleanup(t){if(!(typeof this.xhr>"u"||this.xhr===null)){if(this.xhr.onreadystatechange=ue,t)try{this.xhr.abort()}catch(o){}typeof document<"u"&&delete He.requests[this.index],this.xhr=null}}onLoad(){const t=this.xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this.cleanup())}abort(){this.cleanup()}}if(He.requestsCount=0,He.requests={},typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",qe);else if(typeof addEventListener=="function"){const a="onpagehide"in ee?"pagehide":"unload";addEventListener(a,qe,!1)}}function qe(){for(let a in He.requests)He.requests.hasOwnProperty(a)&&He.requests[a].abort()}const dt=typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,o)=>o(t,0),It=ee.WebSocket||ee.MozWebSocket,Bt=!0,Vt="arraybuffer",$t=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Rn extends Y{constructor(t){super(t),this.supportsBinary=!t.forceBase64}get name(){return"websocket"}doOpen(){if(!this.check())return;const t=this.uri(),o=this.opts.protocols,m=$t?{}:se(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(m.headers=this.opts.extraHeaders);try{this.ws=Bt&&!$t?o?new It(t,o):new It(t):new It(t,o,m)}catch(z){return this.emitReserved("error",z)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let o=0;o<t.length;o++){const m=t[o],z=o===t.length-1;M(m,this.supportsBinary,de=>{const Ae={};Bt||(m.options&&(Ae.compress=m.options.compress),this.opts.perMessageDeflate&&(typeof de=="string"?Buffer.byteLength(de):de.length)<this.opts.perMessageDeflate.threshold&&(Ae.compress=!1));try{Bt?this.ws.send(de):this.ws.send(de,Ae)}catch(Ee){}z&&dt(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",o=this.query||{};return this.opts.timestampRequests&&(o[this.opts.timestampParam]=yt()),this.supportsBinary||(o.b64=1),this.createUri(t,o)}check(){return!!It}}class un extends Y{get name(){return"webtransport"}doOpen(){typeof WebTransport=="function"&&(this.transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name]),this.transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this.transport.ready.then(()=>{this.transport.createBidirectionalStream().then(t=>{const o=X(Number.MAX_SAFE_INTEGER,this.socket.binaryType),m=t.readable.pipeThrough(o).getReader(),z=K();z.readable.pipeTo(t.writable),this.writer=z.writable.getWriter();const de=()=>{m.read().then(Ee=>{let{done:Ue,value:Ke}=Ee;Ue||(this.onPacket(Ke),de())}).catch(Ee=>{})};de();const Ae={type:"open"};this.query.sid&&(Ae.data='{"sid":"'+this.query.sid+'"}'),this.writer.write(Ae).then(()=>this.onOpen())})}))}write(t){this.writable=!1;for(let o=0;o<t.length;o++){const m=t[o],z=o===t.length-1;this.writer.write(m).then(()=>{z&&dt(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this.transport)===null||t===void 0||t.close()}}const Nn={websocket:Rn,webtransport:un,polling:Te},pn=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,In=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function mn(a){if(a.length>2e3)throw"URI too long";const t=a,o=a.indexOf("["),m=a.indexOf("]");o!=-1&&m!=-1&&(a=a.substring(0,o)+a.substring(o,m).replace(/:/g,";")+a.substring(m,a.length));let z=pn.exec(a||""),de={},Ae=14;for(;Ae--;)de[In[Ae]]=z[Ae]||"";return o!=-1&&m!=-1&&(de.source=t,de.host=de.host.substring(1,de.host.length-1).replace(/;/g,":"),de.authority=de.authority.replace("[","").replace("]","").replace(/;/g,":"),de.ipv6uri=!0),de.pathNames=kn(de,de.path),de.queryKey=tn(de,de.query),de}function kn(a,t){const o=/\/{2,9}/g,m=t.replace(o,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&m.splice(0,1),t.slice(-1)=="/"&&m.splice(m.length-1,1),m}function tn(a,t){const o={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(m,z,de){z&&(o[z]=de)}),o}class hn extends L{constructor(t,o){o===void 0&&(o={}),super(),this.binaryType=Vt,this.writeBuffer=[],t&&typeof t=="object"&&(o=t,t=null),t?(t=mn(t),o.hostname=t.host,o.secure=t.protocol==="https"||t.protocol==="wss",o.port=t.port,t.query&&(o.query=t.query)):o.host&&(o.hostname=mn(o.host).host),De(this,o),this.secure=o.secure!=null?o.secure:typeof location<"u"&&location.protocol==="https:",o.hostname&&!o.port&&(o.port=this.secure?"443":"80"),this.hostname=o.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=o.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=o.transports||["polling","websocket","webtransport"],this.writeBuffer=[],this.prevBufferLen=0,this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},o),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=Pe(this.opts.query)),this.id=null,this.upgrades=null,this.pingInterval=null,this.pingTimeout=null,this.pingTimeoutTimer=null,typeof addEventListener=="function"&&(this.opts.closeOnBeforeunload&&(this.beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this.beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this.offlineEventListener=()=>{this.onClose("transport close",{description:"network connection lost"})},addEventListener("offline",this.offlineEventListener,!1))),this.open()}createTransport(t){const o=Object.assign({},this.opts.query);o.EIO=g,o.transport=t,this.id&&(o.sid=this.id);const m=Object.assign({},this.opts,{query:o,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new Nn[t](m)}open(){let t;if(this.opts.rememberUpgrade&&hn.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1)t="websocket";else if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}else t=this.transports[0];this.readyState="opening";try{t=this.createTransport(t)}catch(o){this.transports.shift(),this.open();return}t.open(),this.setTransport(t)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this.onDrain.bind(this)).on("packet",this.onPacket.bind(this)).on("error",this.onError.bind(this)).on("close",o=>this.onClose("transport close",o))}probe(t){let o=this.createTransport(t),m=!1;hn.priorWebsocketSuccess=!1;const z=()=>{m||(o.send([{type:"ping",data:"probe"}]),o.once("packet",Xe=>{if(!m)if(Xe.type==="pong"&&Xe.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",o),!o)return;hn.priorWebsocketSuccess=o.name==="websocket",this.transport.pause(()=>{m||this.readyState!=="closed"&&(Fe(),this.setTransport(o),o.send([{type:"upgrade"}]),this.emitReserved("upgrade",o),o=null,this.upgrading=!1,this.flush())})}else{const we=new Error("probe error");we.transport=o.name,this.emitReserved("upgradeError",we)}}))};function de(){m||(m=!0,Fe(),o.close(),o=null)}const Ae=Xe=>{const we=new Error("probe error: "+Xe);we.transport=o.name,de(),this.emitReserved("upgradeError",we)};function Ee(){Ae("transport closed")}function Ue(){Ae("socket closed")}function Ke(Xe){o&&Xe.name!==o.name&&de()}const Fe=()=>{o.removeListener("open",z),o.removeListener("error",Ae),o.removeListener("close",Ee),this.off("close",Ue),this.off("upgrading",Ke)};o.once("open",z),o.once("error",Ae),o.once("close",Ee),this.once("close",Ue),this.once("upgrading",Ke),this.upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{m||o.open()},200):o.open()}onOpen(){if(this.readyState="open",hn.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush(),this.readyState==="open"&&this.opts.upgrade){let t=0;const o=this.upgrades.length;for(;t<o;t++)this.probe(this.upgrades[t])}}onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),this.resetPingTimeout(),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this.sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong");break;case"error":const o=new Error("server error");o.code=t.data,this.onError(o);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.upgrades=this.filterUpgrades(t.upgrades),this.pingInterval=t.pingInterval,this.pingTimeout=t.pingTimeout,this.maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this.resetPingTimeout()}resetPingTimeout(){this.clearTimeoutFn(this.pingTimeoutTimer),this.pingTimeoutTimer=this.setTimeoutFn(()=>{this.onClose("ping timeout")},this.pingInterval+this.pingTimeout),this.opts.autoUnref&&this.pingTimeoutTimer.unref()}onDrain(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this.getWritablePackets();this.transport.send(t),this.prevBufferLen=t.length,this.emitReserved("flush")}}getWritablePackets(){if(!(this.maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let o=1;for(let m=0;m<this.writeBuffer.length;m++){const z=this.writeBuffer[m].data;if(z&&(o+=ke(z)),m>0&&o>this.maxPayload)return this.writeBuffer.slice(0,m);o+=2}return this.writeBuffer}write(t,o,m){return this.sendPacket("message",t,o,m),this}send(t,o,m){return this.sendPacket("message",t,o,m),this}sendPacket(t,o,m,z){if(typeof o=="function"&&(z=o,o=void 0),typeof m=="function"&&(z=m,m=null),this.readyState==="closing"||this.readyState==="closed")return;m=m||{},m.compress=m.compress!==!1;const de={type:t,data:o,options:m};this.emitReserved("packetCreate",de),this.writeBuffer.push(de),z&&this.once("flush",z),this.flush()}close(){const t=()=>{this.onClose("forced close"),this.transport.close()},o=()=>{this.off("upgrade",o),this.off("upgradeError",o),t()},m=()=>{this.once("upgrade",o),this.once("upgradeError",o)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?m():t()}):this.upgrading?m():t()),this}onError(t){hn.priorWebsocketSuccess=!1,this.emitReserved("error",t),this.onClose("transport error",t)}onClose(t,o){(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")&&(this.clearTimeoutFn(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),typeof removeEventListener=="function"&&(removeEventListener("beforeunload",this.beforeunloadEventListener,!1),removeEventListener("offline",this.offlineEventListener,!1)),this.readyState="closed",this.id=null,this.emitReserved("close",t,o),this.writeBuffer=[],this.prevBufferLen=0)}filterUpgrades(t){const o=[];let m=0;const z=t.length;for(;m<z;m++)~this.transports.indexOf(t[m])&&o.push(t[m]);return o}}hn.protocol=g;const Gs=hn.protocol;function Dt(a,t,o){t===void 0&&(t="");let m=a;o=o||typeof location<"u"&&location,a==null&&(a=o.protocol+"//"+o.host),typeof a=="string"&&(a.charAt(0)==="/"&&(a.charAt(1)==="/"?a=o.protocol+a:a=o.host+a),/^(https?|wss?):\/\//.test(a)||(typeof o<"u"?a=o.protocol+"//"+a:a="https://"+a),m=mn(a)),m.port||(/^(http|ws)$/.test(m.protocol)?m.port="80":/^(http|ws)s$/.test(m.protocol)&&(m.port="443")),m.path=m.path||"/";const de=m.host.indexOf(":")!==-1?"["+m.host+"]":m.host;return m.id=m.protocol+"://"+de+":"+m.port+t,m.href=m.protocol+"://"+de+(o&&o.port===m.port?"":":"+m.port),m}const Ys=typeof ArrayBuffer=="function",_n=a=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(a):a.buffer instanceof ArrayBuffer,ys=Object.prototype.toString,$s=typeof Blob=="function"||typeof Blob<"u"&&ys.call(Blob)==="[object BlobConstructor]",Qs=typeof File=="function"||typeof File<"u"&&ys.call(File)==="[object FileConstructor]";function Fn(a){return Ys&&(a instanceof ArrayBuffer||_n(a))||$s&&a instanceof Blob||Qs&&a instanceof File}function Bn(a,t){if(!a||typeof a!="object")return!1;if(Array.isArray(a)){for(let o=0,m=a.length;o<m;o++)if(Bn(a[o]))return!0;return!1}if(Fn(a))return!0;if(a.toJSON&&typeof a.toJSON=="function"&&arguments.length===1)return Bn(a.toJSON(),!0);for(const o in a)if(Object.prototype.hasOwnProperty.call(a,o)&&Bn(a[o]))return!0;return!1}function qn(a){const t=[],o=a.data,m=a;return m.data=En(o,t),m.attachments=t.length,{packet:m,buffers:t}}function En(a,t){if(!a)return a;if(Fn(a)){const o={_placeholder:!0,num:t.length};return t.push(a),o}else if(Array.isArray(a)){const o=new Array(a.length);for(let m=0;m<a.length;m++)o[m]=En(a[m],t);return o}else if(typeof a=="object"&&!(a instanceof Date)){const o={};for(const m in a)Object.prototype.hasOwnProperty.call(a,m)&&(o[m]=En(a[m],t));return o}return a}function an(a,t){return a.data=jn(a.data,t),delete a.attachments,a}function jn(a,t){if(!a)return a;if(a&&a._placeholder===!0){if(typeof a.num=="number"&&a.num>=0&&a.num<t.length)return t[a.num];throw new Error("illegal attachments")}else if(Array.isArray(a))for(let o=0;o<a.length;o++)a[o]=jn(a[o],t);else if(typeof a=="object")for(const o in a)Object.prototype.hasOwnProperty.call(a,o)&&(a[o]=jn(a[o],t));return a}const Js=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Xs=5;var ut;(function(a){a[a.CONNECT=0]="CONNECT",a[a.DISCONNECT=1]="DISCONNECT",a[a.EVENT=2]="EVENT",a[a.ACK=3]="ACK",a[a.CONNECT_ERROR=4]="CONNECT_ERROR",a[a.BINARY_EVENT=5]="BINARY_EVENT",a[a.BINARY_ACK=6]="BINARY_ACK"})(ut||(ut={}));class es{constructor(t){this.replacer=t}encode(t){return(t.type===ut.EVENT||t.type===ut.ACK)&&Bn(t)?this.encodeAsBinary({type:t.type===ut.EVENT?ut.BINARY_EVENT:ut.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let o=""+t.type;return(t.type===ut.BINARY_EVENT||t.type===ut.BINARY_ACK)&&(o+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(o+=t.nsp+","),t.id!=null&&(o+=t.id),t.data!=null&&(o+=JSON.stringify(t.data,this.replacer)),o}encodeAsBinary(t){const o=qn(t),m=this.encodeAsString(o.packet),z=o.buffers;return z.unshift(m),z}}function wn(a){return Object.prototype.toString.call(a)==="[object Object]"}class Tn extends L{constructor(t){super(),this.reviver=t}add(t){let o;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");o=this.decodeString(t);const m=o.type===ut.BINARY_EVENT;m||o.type===ut.BINARY_ACK?(o.type=m?ut.EVENT:ut.ACK,this.reconstructor=new ts(o),o.attachments===0&&super.emitReserved("decoded",o)):super.emitReserved("decoded",o)}else if(Fn(t)||t.base64)if(this.reconstructor)o=this.reconstructor.takeBinaryData(t),o&&(this.reconstructor=null,super.emitReserved("decoded",o));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let o=0;const m={type:Number(t.charAt(0))};if(ut[m.type]===void 0)throw new Error("unknown packet type "+m.type);if(m.type===ut.BINARY_EVENT||m.type===ut.BINARY_ACK){const de=o+1;for(;t.charAt(++o)!=="-"&&o!=t.length;);const Ae=t.substring(de,o);if(Ae!=Number(Ae)||t.charAt(o)!=="-")throw new Error("Illegal attachments");m.attachments=Number(Ae)}if(t.charAt(o+1)==="/"){const de=o+1;for(;++o&&!(t.charAt(o)===","||o===t.length););m.nsp=t.substring(de,o)}else m.nsp="/";const z=t.charAt(o+1);if(z!==""&&Number(z)==z){const de=o+1;for(;++o;){const Ae=t.charAt(o);if(Ae==null||Number(Ae)!=Ae){--o;break}if(o===t.length)break}m.id=Number(t.substring(de,o+1))}if(t.charAt(++o)){const de=this.tryParse(t.substr(o));if(Tn.isPayloadValid(m.type,de))m.data=de;else throw new Error("invalid payload")}return m}tryParse(t){try{return JSON.parse(t,this.reviver)}catch(o){return!1}}static isPayloadValid(t,o){switch(t){case ut.CONNECT:return wn(o);case ut.DISCONNECT:return o===void 0;case ut.CONNECT_ERROR:return typeof o=="string"||wn(o);case ut.EVENT:case ut.BINARY_EVENT:return Array.isArray(o)&&(typeof o[0]=="number"||typeof o[0]=="string"&&Js.indexOf(o[0])===-1);case ut.ACK:case ut.BINARY_ACK:return Array.isArray(o)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class ts{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const o=an(this.reconPack,this.buffers);return this.finishedReconstruction(),o}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function nn(a,t,o){return a.on(t,o),function(){a.off(t,o)}}const qs=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class xs extends L{constructor(t,o,m){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=o,m&&m.auth&&(this.auth=m.auth),this._opts=Object.assign({},m),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[nn(t,"open",this.onopen.bind(this)),nn(t,"packet",this.onpacket.bind(this)),nn(t,"error",this.onerror.bind(this)),nn(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(){for(var t=arguments.length,o=new Array(t),m=0;m<t;m++)o[m]=arguments[m];return o.unshift("message"),this.emit.apply(this,o),this}emit(t){if(qs.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');for(var o=arguments.length,m=new Array(o>1?o-1:0),z=1;z<o;z++)m[z-1]=arguments[z];if(m.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(m),this;const de={type:ut.EVENT,data:m};if(de.options={},de.options.compress=this.flags.compress!==!1,typeof m[m.length-1]=="function"){const Ue=this.ids++,Ke=m.pop();this._registerAckCallback(Ue,Ke),de.id=Ue}const Ae=this.io.engine&&this.io.engine.transport&&this.io.engine.transport.writable;return this.flags.volatile&&(!Ae||!this.connected)||(this.connected?(this.notifyOutgoingListeners(de),this.packet(de)):this.sendBuffer.push(de)),this.flags={},this}_registerAckCallback(t,o){var m=this,z;const de=(z=this.flags.timeout)!==null&&z!==void 0?z:this._opts.ackTimeout;if(de===void 0){this.acks[t]=o;return}const Ae=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let Ue=0;Ue<this.sendBuffer.length;Ue++)this.sendBuffer[Ue].id===t&&this.sendBuffer.splice(Ue,1);o.call(this,new Error("operation has timed out"))},de),Ee=function(){m.io.clearTimeoutFn(Ae);for(var Ue=arguments.length,Ke=new Array(Ue),Fe=0;Fe<Ue;Fe++)Ke[Fe]=arguments[Fe];o.apply(m,Ke)};Ee.withError=!0,this.acks[t]=Ee}emitWithAck(t){for(var o=arguments.length,m=new Array(o>1?o-1:0),z=1;z<o;z++)m[z-1]=arguments[z];return new Promise((de,Ae)=>{const Ee=(Ue,Ke)=>Ue?Ae(Ue):de(Ke);Ee.withError=!0,m.push(Ee),this.emit(t,...m)})}_addToQueue(t){var o=this;let m;typeof t[t.length-1]=="function"&&(m=t.pop());const z={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push(function(de){if(z!==o._queue[0])return;if(de!==null)z.tryCount>o._opts.retries&&(o._queue.shift(),m&&m(de));else if(o._queue.shift(),m){for(var Ee=arguments.length,Ue=new Array(Ee>1?Ee-1:0),Ke=1;Ke<Ee;Ke++)Ue[Ke-1]=arguments[Ke];m(null,...Ue)}return z.pending=!1,o._drainQueue()}),this._queue.push(z),this._drainQueue()}_drainQueue(t){if(t===void 0&&(t=!1),!this.connected||this._queue.length===0)return;const o=this._queue[0];o.pending&&!t||(o.pending=!0,o.tryCount++,this.flags=o.flags,this.emit.apply(this,o.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:ut.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,o){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,o),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(m=>String(m.id)===t)){const m=this.acks[t];delete this.acks[t],m.withError&&m.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case ut.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case ut.EVENT:case ut.BINARY_EVENT:this.onevent(t);break;case ut.ACK:case ut.BINARY_ACK:this.onack(t);break;case ut.DISCONNECT:this.ondisconnect();break;case ut.CONNECT_ERROR:this.destroy();const m=new Error(t.data.message);m.data=t.data.data,this.emitReserved("connect_error",m);break}}onevent(t){const o=t.data||[];t.id!=null&&o.push(this.ack(t.id)),this.connected?this.emitEvent(o):this.receiveBuffer.push(Object.freeze(o))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const o=this._anyListeners.slice();for(const m of o)m.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const o=this;let m=!1;return function(){if(!m){m=!0;for(var z=arguments.length,de=new Array(z),Ae=0;Ae<z;Ae++)de[Ae]=arguments[Ae];o.packet({type:ut.ACK,id:t,data:de})}}}onack(t){const o=this.acks[t.id];typeof o=="function"&&(delete this.acks[t.id],o.withError&&t.data.unshift(null),o.apply(this,t.data))}onconnect(t,o){this.id=t,this.recovered=o&&this._pid===o,this._pid=o,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:ut.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const o=this._anyListeners;for(let m=0;m<o.length;m++)if(t===o[m])return o.splice(m,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const o=this._anyOutgoingListeners;for(let m=0;m<o.length;m++)if(t===o[m])return o.splice(m,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const o=this._anyOutgoingListeners.slice();for(const m of o)m.apply(this,t.data)}}}function Cn(a){a=a||{},this.ms=a.min||100,this.max=a.max||1e4,this.factor=a.factor||2,this.jitter=a.jitter>0&&a.jitter<=1?a.jitter:0,this.attempts=0}Cn.prototype.duration=function(){var a=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),o=Math.floor(t*this.jitter*a);a=(Math.floor(t*10)&1)==0?a-o:a+o}return Math.min(a,this.max)|0},Cn.prototype.reset=function(){this.attempts=0},Cn.prototype.setMin=function(a){this.ms=a},Cn.prototype.setMax=function(a){this.max=a},Cn.prototype.setJitter=function(a){this.jitter=a};class zn extends L{constructor(t,o){var m;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(o=t,t=void 0),o=o||{},o.path=o.path||"/socket.io",this.opts=o,De(this,o),this.reconnection(o.reconnection!==!1),this.reconnectionAttempts(o.reconnectionAttempts||1/0),this.reconnectionDelay(o.reconnectionDelay||1e3),this.reconnectionDelayMax(o.reconnectionDelayMax||5e3),this.randomizationFactor((m=o.randomizationFactor)!==null&&m!==void 0?m:.5),this.backoff=new Cn({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(o.timeout==null?2e4:o.timeout),this._readyState="closed",this.uri=t;const z=o.parser||s;this.encoder=new z.Encoder,this.decoder=new z.Decoder,this._autoConnect=o.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var o;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(o=this.backoff)===null||o===void 0||o.setMin(t),this)}randomizationFactor(t){var o;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(o=this.backoff)===null||o===void 0||o.setJitter(t),this)}reconnectionDelayMax(t){var o;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(o=this.backoff)===null||o===void 0||o.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new hn(this.uri,this.opts);const o=this.engine,m=this;this._readyState="opening",this.skipReconnect=!1;const z=nn(o,"open",function(){m.onopen(),t&&t()}),de=Ee=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",Ee),t?t(Ee):this.maybeReconnectOnOpen()},Ae=nn(o,"error",de);if(this._timeout!==!1){const Ee=this._timeout,Ue=this.setTimeoutFn(()=>{z(),de(new Error("timeout")),o.close()},Ee);this.opts.autoUnref&&Ue.unref(),this.subs.push(()=>{this.clearTimeoutFn(Ue)})}return this.subs.push(z),this.subs.push(Ae),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(nn(t,"ping",this.onping.bind(this)),nn(t,"data",this.ondata.bind(this)),nn(t,"error",this.onerror.bind(this)),nn(t,"close",this.onclose.bind(this)),nn(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(o){this.onclose("parse error",o)}}ondecoded(t){dt(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,o){let m=this.nsps[t];return m?this._autoConnect&&!m.active&&m.connect():(m=new xs(this,t,o),this.nsps[t]=m),m}_destroy(t){const o=Object.keys(this.nsps);for(const m of o)if(this.nsps[m].active)return;this._close()}_packet(t){const o=this.encoder.encode(t);for(let m=0;m<o.length;m++)this.engine.write(o[m],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close"),this.engine&&this.engine.close()}disconnect(){return this._close()}onclose(t,o){this.cleanup(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,o),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const o=this.backoff.duration();this._reconnecting=!0;const m=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(z=>{z?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",z)):t.onreconnect()}))},o);this.opts.autoUnref&&m.unref(),this.subs.push(()=>{this.clearTimeoutFn(m)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const An={};function ns(a,t){typeof a=="object"&&(t=a,a=void 0),t=t||{};const o=Dt(a,t.path||"/socket.io"),m=o.source,z=o.id,de=o.path,Ae=An[z]&&de in An[z].nsps,Ee=t.forceNew||t["force new connection"]||t.multiplex===!1||Ae;let Ue;return Ee?Ue=new zn(m,t):(An[z]||(An[z]=new zn(m,t)),Ue=An[z]),o.query&&!t.query&&(t.query=o.queryKey),Ue.socket(o.path,t)}if(Object.assign(ns,{Manager:zn,Socket:xs,io:ns,connect:ns}),n.j!=15)var ss=n(51326);if(n.j!=15)var Ct=n(47437);if(n.j!=15)var sn=n(87612);if(n.j!=15)var bs=n(27814);const os=n.j!=15?2*1e3:null,rs=n.j!=15?24*1e3:null,Un=n.j!=15?48*1e3:null,Wn=()=>{},eo=()=>({destroy:Wn,reset:Wn,emitAsync:to,emitLossy:Wn,getIsConnected:()=>!0,getSocketId:()=>"",getReadyPromise:()=>Promise.resolve(),getOffsetMsec:()=>0,DUMMY:!0}),to=async(a,t)=>({type:a,payload:t,isDummy:!0}),no=a=>{let{query:{uId:t,fURL:o=globalThis.location.href.slice(0,128),uTag:m=Ct.CH,vTag:z=bs.r,rKey:de},lossyListenerMap:Ae={},queueListenerMap:Ee={},socketUrl:Ue=globalThis.location.origin,socketPath:Ke=Ct.$r,usePolling:Fe=!1}=a;if(!t||!de)throw new Error("SKT bad query");const Xe=(0,ss.I6)();let we,Ce=(0,p.wT)(),ze=0,$e=null;const pt=()=>{we!==void 0&&(we.close(),we=void 0,Ce=(0,p.wT)(),Mt=!1,$e&&clearTimeout($e),$e=null)},Tt=()=>{pt();const it=new zn(Ue,{path:Ke,query:{uId:t,fURL:o,uTag:m,vTag:z,rKey:de},transports:Fe?["polling","websocket"]:["websocket"],timeout:Un,reconnectionDelay:Math.round((os+rs)/2),reconnectionDelayMax:Un,randomizationFactor:(rs-os)/(rs+os),closeOnBeforeunload:!1}).socket(Ct.I4);it.on("connect_error",Ve=>{console.log("[socket] connect_error",Ve),Ve.message.includes(Ct.uB)&&(pt(),Xe.dispatchEvent({type:Ct.VU,payload:{error:Ve}})),Xe.dispatchEvent({type:Ct.Ei,payload:{error:Ve}})}),it.on("connect",async()=>{console.log("[socket] connect");{const Ve=Date.now(),st=await(0,p.hq)(Kt(Ct.Ip),10*1e3,Ct.Ip).catch(async rt=>{throw(0,N.et)(Tt,2*1e3),console.warn(Ct.Ip,rt),Ce.reject(rt),rt}),et=Date.now();ze=st-(Ve+et)*.5}$e=(0,N.et)(()=>{!we||!we.connected||Mt||Tt()},(1+Math.random())*30*60*1e3),console.log("[socket] ready"),Xe.dispatchEvent({type:Ct.zS}),Ce.resolve()});{let Ve=Promise.resolve();const st=(et,rt)=>{const ht=Ve.then(et);return Ve=ht.then(Wn,Ft=>{(0,sn.p)("SKT-QA-"+rt,Ft)}),ht};Xe.clear();for(const[et,rt]of Object.entries(Ae))it.on(et,ht=>rt({type:et,payload:ht})),Xe.addEventListener(et,ht=>{let{payload:Ft}=ht;return rt({type:et,payload:Ft})});for(const[et,rt]of Object.entries(Ee))it.on(et,ht=>st(()=>rt({type:et,payload:ht}),et)),Xe.addEventListener(et,ht=>{let{payload:Ft}=ht;return st(()=>rt({type:et,payload:Ft}),et)})}we=it};let Mt=!1;const Kt=async(Zt,it)=>{Mt=!0;const{error:Ve,result:st}=await(0,p.hq)(new Promise(et=>we.emit(Zt,it,et)),42*1e3,"SKT-USR "+Zt).finally(()=>{Mt=!1});if(Ve!==void 0)throw Object.assign(new Error(typeof Ve=="object"&&Ve.message||"SKT-USR "+Zt),Ve);return st},Ut=(Zt,it)=>{we.volatile.emit(Zt,it)};return Tt(),{destroy:pt,reset:Tt,emitAsync:Kt,emitLossy:Ut,getIsConnected:()=>we&&we.connected||!1,getSocketId:()=>we&&we.id||"",getReadyPromise:()=>Ce.promise,getOffsetMsec:()=>ze}};var yn=n(41011),Qt=n(29158),Sn=n(26812),Rt=n(196);if(n.j!=15)var kt=n(69415);if(n.j!=15)var zt=n(86033);var xn=n(8984);const Ln=0,vs={cDebugMin:Ln,cCmtPack:Ln+1,cCmtThread:Ln+2,cCmtContent:Ln+3,cDebugMax:35},Mn=(0,xn.W)("cmt-type",vs),_s=Mn.pack("cCmtPack"),Hn=Mn.pack("cCmtThread"),ws=Mn.pack("cCmtContent"),Cs=new Set(["cCmtPack","cCmtThread","cCmtContent"]),so=new Set([_s,Hn,ws]);if(n.j!=15)var Ss=n(40093);const oo={unstyled:0,"header-one":1,"header-two":2,"header-three":3,"ordered-list-item":4,"unordered-list-item":5,atomic:6},Ms=(0,xn.W)("DraftBlockType",oo),Ns={left:0,center:1,right:2,justify:3},Vn=(0,xn.W)("DraftAlignment",Ns),ro={LINK:0,IMAGE:1},Is=(0,xn.W)("DraftEntityMapType",ro),io={MUTABLE:0,IMMUTABLE:1,SEGMENTED:2},is=(0,xn.W)("DraftEntityMapMutability",io),ks={_blank:0,_self:1},Es=(0,xn.W)("DraftLinkTargetOption",ks),ao=a=>{const t={};return Object.keys(a).forEach(o=>{const{type:m,mutability:z,data:de}=a[o],Ae=[Is.pack(m),is.pack(z)],Ee=[];m==="LINK"?(Ae.push(Es.pack(de.targetOption)),Ee.push(de.url||"")):m==="IMAGE"&&(Ae.push((0,kt.c5)(de.width),Vn.pack(de.alignment)),Ee.push(de.src||"",de.alt||"")),t["e/"+o]=[Ae.join(zt.xi),...Ee]}),t},lo=a=>{const t={};for(const o of Object.keys(a)){if(!o.startsWith("e/"))continue;const m=o.split("e/")[1],[z,...de]=a[o],[Ae,Ee,Ue,Ke]=z.split(zt.xi),Fe=Is.parse(Ae),Xe=is.parse(Ee);t[m]=Fe==="LINK"?{type:Fe,mutability:Xe,data:{url:de[0],targetOption:Es.parse(Ue)}}:{type:Fe,mutability:Xe,data:{src:de[0],alt:de[1],width:(0,kt._3)(Ue),alignment:Vn.parse(Ke)}}}return t},js=a=>{const t={};return Object.keys(a).forEach(o=>{const{text:m,type:z,depth:de,inlineStyleRanges:Ae,entityRanges:Ee,data:{textAlign:Ue,textIndent:Ke,paraSpacing:Fe}}=a[o],Xe=[Ms.packDef(z,"unstyled"),(0,kt.c5)(de),Ue?Vn.pack(Ue):"",Ke!==void 0?(0,kt.c5)(Ke):"",Fe!==void 0?(0,Rt.ZI)(Fe):""];t["b/"+o]=[Xe.join(zt.xi),m,Ae.map(we=>{let{offset:Ce,length:ze,style:$e}=we;return ln(Ce,ze,$e)}),Ee.map(we=>{let{offset:Ce,length:ze,key:$e}=we;return co(Ce,ze,$e)})]}),t},co=(a,t,o)=>""+(0,kt.c5)(a)+zt.xi+(0,kt.c5)(t)+zt.xi+(0,kt.c5)(o),uo=a=>{const t=a.indexOf(zt.xi),o=a.indexOf(zt.xi,t+1);return[(0,kt._3)(a.slice(0,t)),(0,kt._3)(a.slice(t+1,o)),(0,kt._3)(a.slice(o+1))]},ln=(a,t,o)=>""+(0,kt.c5)(a)+zt.xi+(0,kt.c5)(t)+zt.xi+o,po=a=>{const t=a.indexOf(zt.xi),o=a.indexOf(zt.xi,t+1);return[(0,kt._3)(a.slice(0,t)),(0,kt._3)(a.slice(t+1,o)),a.slice(o+1)]},_t=a=>{const t={};for(const o of Object.keys(a)){if(!o.startsWith("b/"))continue;const m=o.split("b/")[1],[z,de,Ae,Ee]=a[o],[Ue,Ke,Fe,Xe,we]=z.split(zt.xi),Ce={};Fe!==""&&(Ce.textAlign=Vn.parse(Fe)),Xe!==""&&(Ce.textIndent=(0,kt._3)(Xe)),we!==""&&we!==void 0&&(Ce.paraSpacing=(0,Rt.bp)(we)),t[m]={text:de,type:Ms.parse(Ue),depth:(0,kt._3)(Ke),inlineStyleRanges:Ae.map(ze=>{const[$e,pt,Tt]=po(ze);return{offset:$e,length:pt,style:Tt}}),entityRanges:Ee.map(ze=>{const[$e,pt,Tt]=uo(ze);return{offset:$e,length:pt,key:Tt}}),data:Ce}}return t},Ts=a=>{let{message:t}=a;const{blocks:o={},entityMap:m={}}=t;return{...js(o),...ao(m)}},Kn=a=>({message:{blocks:_t(a),entityMap:lo(a)}}),as=a=>({T:Mn.pack(a.type),IC:[(0,kt.c5)(a.index),(0,kt.vH)(a.ctime)].join(zt.xi)}),ls=a=>{const[t,o]=a.IC.split(zt.xi);return{type:Mn.parse(a.T),index:(0,kt._3)(t),ctime:(0,kt.TT)(o)}},As=a=>({...as(a),R:a.refKey}),ho=a=>({...ls(a),refKey:a.R}),Ls=a=>({...as(a),...Ts(a),xy:(0,Rt.x3)(a.x,a.y),wh:(0,Rt.x3)(a.w,a.h),z:(0,Rt.ZI)(a.z),userInfo:a.userInfo,userId:(0,Rt.ZI)(a.userId),mtime:(0,kt.vH)(a.mtime),isCompleted:(0,Ss.rK)(a.isCompleted),canvasVec2:a.canvasVec2?{x:(0,Rt.ZI)(a.canvasVec2.x),y:(0,Rt.ZI)(a.canvasVec2.y)}:a.canvasVec2,canvasCid:a.canvasCid,threadType:a.threadType}),Os=a=>{const[t,o]=(0,Rt.fm)(a.xy),[m,z]=(0,Rt.fm)(a.wh),de=a.canvasVec2?(0,Rt.bp)(a.canvasVec2.x):void 0,Ae=a.canvasVec2?(0,Rt.bp)(a.canvasVec2.y):void 0,Ee=(0,Rt.bp)(a.z);return{...ls(a),...Kn(a),x:t,y:o,w:m,h:z,z:Ee,mtime:(0,kt.TT)(a.mtime),userInfo:a.userInfo,userId:(0,Rt.bp)(a.userId),isCompleted:(0,Ss.Vf)(a.isCompleted),canvasCid:a.canvasCid,threadType:a.threadType,canvasVec2:de&&Ae?{x:de,y:Ae}:void 0}},Zn=a=>({...as(a),...Ts(a),mtime:(0,kt.vH)(a.mtime),userInfo:a.userInfo}),Ps=a=>({...ls(a),...Kn(a),mtime:(0,kt.TT)(a.mtime),userInfo:a.userInfo}),mo=a=>{try{switch(a.T){case void 0:return a;case _s:return ho(a);case Hn:return Os(a);case ws:return Ps(a);default:throw new Error("invalid attr: "+a.T+" -> "+Mn.parse(a.T))}}catch(t){throw(0,sn.p)("toP2CHA badDat",t,a),t}},Ds=a=>{try{switch(a.type){case void 0:return a;case"cCmtPack":return As(a);case"cCmtThread":return Ls(a);case"cCmtContent":return Zn(a);default:throw new Error("invalid attr: "+a.type)}}catch(t){throw(0,sn.p)("toP2CCA badDat",t,a),t}};if(n.j!=15)var go=n(43972);const Rs=(a,t)=>{const o=Ee=>{const Ue=a(Ee);return Ue===void 0?[]:Ue.sub.map(Ke=>a(Ke))},m=(0,go.MX)(Ee=>Ee.sub.map(Ue=>a(Ue))),z=(Ee,Ue)=>{const Ke=a(Ee);Ke!==void 0&&(Ue(Ke)||Ke.sub.length!==0&&m(Ke,Ue))};return{getHotItem:a,getSubHotItemList:o,walkHotItemSubtree:z,isTrashed:Ee=>(0,Qt.DM)(Ee,t.getFlatTree()),__devHotTree:function(Ee){Ee===void 0&&(Ee=Qt.gT);const Ue=new Map,Ke=Fe=>{let Xe=Ue.get(Fe);return Xe===void 0&&Ue.set(Fe,Xe={}),Xe};return z(Ee,Fe=>{Ke(Fe.sup)[Fe.key]=Fe.hotAttr,Fe.sub.length!==0&&(Ke(Fe.sup)[Fe.key+" SUB"]=Ke(Fe.key))}),Ke(a(Ee).sup)}}};if(n.j!=15)var Fs=n(97529);if(n.j!=15)var cs=n(78235);var fo=n(62124),qt=n(25037);if(n.j!=15)var yo=n(89989);const xo=(a,t,o)=>{const m=we=>{if(we===void 0||we.length===0)return;const Ce=new Set;for(const $e of we)Ce.add($e.key),$e.type==="I"&&Ce.add($e.keySup);const ze=(0,Qt.CF)([...Ce].filter(Qt.xe),o.getFlatTree());a({type:yo.a,payload:ze})},z=we=>{const Ce=[],ze=we(Ce);return m(o.applyDiff(Ce)),ze},de=we=>z(Ce=>{const ze=[],$e=we(ze);for(const pt of ze)pt.type==="update"?Ke(pt.hotItem,Ce):pt.type==="delete"&&Fe(pt.key,Ce);return $e}),Ae=function(){const we=[],Ce=[];let ze,$e=!1;for(var pt=arguments.length,Tt=new Array(pt),Mt=0;Mt<pt;Mt++)Tt[Mt]=arguments[Mt];for(const Kt of Tt){ze=Kt(we);const Ut=o.applyDiff(we);Ut!==void 0&&(Ce.push(...Ut),$e?o.combineLastUndo():$e=!0)}return m(Ce),ze},Ee=function(){for(var we=arguments.length,Ce=new Array(we),ze=0;ze<we;ze++)Ce[ze]=arguments[ze];const $e=Ce.map(pt=>Tt=>{const Mt=[],Kt=pt(Mt);for(const Ut of Mt)Ut.type==="update"?Ke(Ut.hotItem,Tt):Ut.type==="delete"&&Fe(Ut.key,Tt);return Kt});return Ae(...$e)},Ue=(we,Ce)=>{for(const ze of Object.keys(we)){const $e=we[ze],pt=Ce[ze];$e!==pt&&(0,Fs.sw)($e)&&(0,Fs.sw)(pt)&&JSON.stringify($e)===JSON.stringify(pt)&&(we[ze]=pt)}},Ke=(we,Ce)=>{const ze=(0,Qt.PV)(we.key,o.getFlatTree());if(ze===void 0)Ce.push({type:qt.UF,key:we.key,keySup:we.sup}),Ce.push({type:qt.qC,key:we.key,attrDiff:Ds(we.hotAttr)});else{ze.sup!==we.sup&&Ce.push({type:qt.UF,key:we.key,keySup:we.sup});const $e=Ds(we.hotAttr);Ue($e,ze.attr);const pt=(0,fo.m)($e,ze.attr);pt!==void 0&&Ce.push({type:qt.qC,key:we.key,attrDiff:pt})}},Fe=(we,Ce)=>{o.getFlatItem(we)&&Ce.push({type:qt.UF,key:we,keySup:Qt.Ri})},Xe=(we,Ce,ze)=>{o.getFlatItem(we)&&o.getFlatItem(Ce)&&ze.push({type:qt.UF,key:we,keySup:Ce})};return{_edit:z,_editAction:de,_editBatch:Ae,_editActionBatch:Ee,onRemotePatchList:we=>{const Ce=[];for(const ze of we){const $e=o.applyDiffRemote(ze.diffList,ze.clock);$e!==void 0&&Ce.push(...$e)}m(Ce)},updateHotItem:we=>z(Ce=>{Ke(we,Ce)}),updateHotItemBatch:we=>z(Ce=>{for(const ze of we)Ke(ze,Ce)}),deleteHotItem:we=>z(Ce=>{Fe(we,Ce)}),deleteHotItemBatch:we=>z(Ce=>{for(const ze of we)Fe(ze,Ce)}),moveHotItem:(we,Ce)=>z(ze=>{Xe(we,Ce,ze)}),moveHotItemBatch:we=>z(Ce=>{for(const[ze,$e]of we)Xe(ze,$e,Ce)}),editHotItemBatch:we=>z(Ce=>{for(const ze of we)ze.type==="update"?Ke(ze.hotItem,Ce):ze.type==="delete"&&Fe(ze.key,Ce)}),updateHotAttrMerge:(we,Ce)=>z(ze=>{const $e=t(we);if($e===void 0)throw new Error("no item: "+we);Ke((0,cs.MK)($e,"hotAttr",{...$e.hotAttr,...Ce}),ze)}),updateHotAttrKV:(we,Ce,ze)=>z($e=>{const pt=t(we);if(pt===void 0)throw new Error("no item: "+we);Ke((0,cs.MK)(pt,"hotAttr",(0,cs.MK)(pt.hotAttr,Ce,ze)),$e)}),canUndo:o.canUndo,canRedo:o.canRedo,combineMergeMark:o.combineMergeMark,combineMerge:o.combineMerge,undo:()=>{m(o.undo())},redo:()=>{m(o.redo())}}};var Gn=n(96737);const bo=a=>{const t=(0,ss.wz)(),o=t.subscribe,m=t.unsubscribe,z=Bs(a),de=Rs(z,a),Ae=()=>vo(a.getFlatTree),Ee=xo(t.send,z,a),{_edit:Ue,_editAction:Ke,_editBatch:Fe,_editActionBatch:Xe,...we}=Ee;return{subscribe:o,unsubscribe:m,...de,createRoCmtStore:Ae,...we}},Bs=a=>t=>{const o=a.getFlatTree().get(t);if(o===void 0)return;const m=a.getOCI(o);return m.hotItem===void 0&&(m.hotItem={key:o.key,sup:o.sup,sub:o.sub,hotAttr:mo(o.attr)}),m.hotItem},vo=a=>{const t=(0,Gn.b$)(a(),(0,yn.aF)({})),o=()=>t.getFlatTree()!==a(),m=()=>t.sync(a()),z=Bs(t),de=Rs(z,t);return{hasUpdate:o,syncUpdate:m,...de}};var _o=n(83557),ds=n(8445);if(n.j!=15)var wo=n(80730);if(n.j!=15)var Co=n(32182);var bn=n(51933),Yn=n(62732),On=n(55338);const us=a=>{const[t,o,m]=a,z=async()=>(0,Yn.BT)(On.r,t.tre),de=async function(Fe){Fe===void 0&&(Fe=-1);const Xe=[];for(const{pbx:we,t10s:Ce}of o){if(Ce<Fe)continue;const ze=(0,Co.CE)(await On.r.gunzipU8AAsync(new Uint8Array(we)));for(const $e of(0,bn.Pb)(ze))Xe.push($e)}for(const{pch:we,t10s:Ce}of m)Ce<Fe||Xe.push(we);return Xe},Ae=async function(Fe){Fe===void 0&&(Fe=-1);const Xe=[];for(const we of await de(Fe))Xe.push((0,qt.iz)(we));return Xe},Ee=async function(Fe){Fe===void 0&&(Fe=-1);const Xe=[];for(const we of await de(Fe)){const{clock:Ce,diffList:ze}=(0,qt.iz)(we);Xe.push([(0,yn._r)(Ce).slice(5),ze,we])}return Xe};return{getParsedTre:z,getPchList:de,getPatchList:Ae,getReadablePatchList:Ee,filterReadablePatchList:async function(Fe,Xe){return Xe===void 0&&(Xe=-1),(await Ee(Xe)).filter(we=>{let[,,Ce]=we;return Ce.includes(Fe)})},getMergedReadablePatchList:async function(Fe,Xe){Fe===void 0&&(Fe=-1),Xe===void 0&&(Xe=16);const we=[];let Ce={timePrefix:"",countDI:0,countDA:0,userIdSet:new Set};const ze=$e=>{Ce.timePrefix!==$e&&(Ce.timePrefix!==""&&we.push(Ce.timePrefix.slice(5)+"..Z I*"+(Ce.countDI||"-")+" A*"+(Ce.countDA||"-")+" ["+[...Ce.userIdSet].join(",")+"]"),Ce={timePrefix:$e,countDI:0,countDA:0,userIdSet:new Set})};for(const{clock:[$e,pt],diffList:Tt}of await Ae(Fe)){const Mt=new Date($e).toISOString().slice(0,Xe);ze(Mt),Ce.userIdSet.add(pt);for(const{type:Kt}of Tt)Kt===qt.UF?Ce.countDI++:Ce.countDA++}return ze(""),we},flpak:a}},ps=a=>Math.floor(a/10/1e3),hs=a=>{const[t]=(0,qt.cx)(a);return ps(t)},So=a=>{let[t,o,m]=a;return m.length!==0?m[m.length-1].t10s:o.length!==0?o[o.length-1].t10s:t.t10s};if(n.j!=15)var ms=n(8113);const gn=(a,t,o)=>{let m=!1,z=0,de=0;const Ae=new Map;let Ee=[],Ue,Ke,Fe,Xe;const we=Ve=>{let[st,,et]=Ve;if(st!==a)return!1;const rt=(0,bn.vZ)(et);for(const ht of rt)Ee.push([ht,hs(ht),!1]);return Fe!==void 0&&Fe(rt.map(ht=>(0,qt.iz)(ht))),!0},Ce=async(Ve,st)=>{if(Ke!==void 0)throw new Error("no re-init flatStore");Ue=Ve,Xe=st;const et=await Xe(a);{const At="INSP-INIT-FLPAK";location.hash.includes(At)&&Object.assign(globalThis,{[At+"/"+a]:{...us(et),__flpakKey:a,__name:t}})}z=So(et);const rt=(0,Gn.OR)(),ht=await(0,Yn.TN)(On.r,et,(At,Be)=>(0,bn.qG)(At,Be,rt.mutateTFAAFNB));let Ft=Qt.xL;{const At="BYPASS-FLPAK";location.hash.includes(At)&&(Ft=Be=>console.warn("["+At+"|vFT]",Be))}for(const[At,Be,ct]of Ee)!ct&&z>=Be||(0,bn.qG)(ht,At,rt.mutateTFAAFNB);Ke=(0,Gn.b$)(ht,Ve,(At,Be,ct)=>{if(m&&console.log("flat|onPatch",{diffList:At,clock:Be,isLocal:ct}),Ee.push([(0,qt.AO)({diffList:At,clock:Be}),ps(Be[0]),ct]),ct)return;const[Gt,en,Pn,fn]=Be;de=Math.max(de,Gt);const cn=(0,yn.au)(en,Pn);if(Ue.selfCCK===cn)throw new Error("unexpected selfCck: "+cn);const Dn=Ae.get(cn)||-1;Dn>fn&&console.warn("bad editId for cck: "+cn+", last: "+Dn+", new: "+fn),Ae.set(cn,fn)},(At,Be)=>{console.warn("flat|requestResync",{error:At,message:Be})}),Fe=At=>{for(const Be of At)Ke.applyDiffRemote(Be.diffList,Be.clock)};const Wt=rt.getFixDiffList(ht);Wt.length&&(Ke.applyDiff(Wt),Ke.resetUndo()),o===!0&&Ft(ht)},ze=Ve=>{Fe=Ve},$e=()=>Ue,pt=()=>Ke,Tt=async function(Ve){if(Ve===void 0&&(Ve=""),o===!0)try{if(!Ke||!z||!Ut())return;const st=Ke.getFlatTree(),[et,rt]=await(0,Yn.Vz)(On.r,st);await(0,ms.hp)("//FSS/"+t+"/"+a,new Blob([rt],{type:"application/octet-stream"}),{message:Ve,treRawSize:et,treSize:rt.byteLength})}catch(st){console.warn("saveSOS",Ve,st)}},Mt=async()=>{if(o===!0)try{const Ve=await(0,ms.Ey)("//FSS/"+t+"/"+a);if(Ve.length){console.warn("[loadSOS] found "+Ve.length+" record");const{data:st,extra:et}=Ve[0];console.warn("[loadSOS] load record from "+new Date(et.time).toISOString());const rt=await(0,wo.WL)(st),ht=await(0,Yn.BT)(On.r,rt);return console.warn("[loadSOS] record tree: "+ht.slowSize()),{tree:ht,...et}}}catch(Ve){console.warn("loadSOS",Ve)}},Kt=async()=>{if(o===!0)try{return await(0,ms.EP)("//FSS/"+t+"/"+a)}catch(Ve){console.warn("freeSOS",Ve)}},Ut=()=>{if(o!==!0)return!1;for(const[,,Ve]of Ee)if(Ve)return!0;return!1};return{flpakKey:a,name:t,__setIsDev:Ve=>{m=Ve},onRemotePayloadSave:we,bind:Ce,useExtORPL:ze,getClockStore:$e,getFlatStore:pt,saveSOS:Tt,loadSOS:Mt,freeSOS:Kt,hasPatch:Ut,preSettlePatch:()=>{if(o!==!0)return[];const Ve=[];for(const[st,,et]of Ee)et&&Ve.push(st);return Ve},postSettlePatch:Ve=>{o===!0&&(Ee=Ee.filter(st=>{let[et,rt,ht]=st;return!ht||!Ve.includes(et)}))}}},zo=(a,t,o)=>{let m,z;return{flpakKey:a,name:"[DUMMY]"+t,__setIsDev:de=>{},onRemotePayloadSave:de=>{let[Ae,,Ee]=de;return!1},bind:async(de,Ae)=>{if(z!==void 0)throw new Error("no re-init flatStore");m=de,z=(0,Gn.b$)((0,Qt.Id)(),de)},useExtORPL:de=>{},getClockStore:()=>m,getFlatStore:()=>z,saveSOS:async de=>{},loadSOS:async()=>{},freeSOS:async()=>0,hasPatch:()=>!1,preSettlePatch:()=>[],postSettlePatch:de=>{}}};if(n.j!=15)var Mo=n(45246);const zs=n.j!=15?Rt.x3:null,No=n.j!=15?Rt.fm:null,Us=a=>{const t=new Map;let o=!1,m=!1,z,de,Ae=()=>"";const Ee=()=>{t.clear(),Fe()};let Ue=0;const Ke=it=>{const Ve={socketId:it,index:Ue++,lastCurAt:0,cur:void 0,lastStatAt:0,stat:void 0};return t.set(it,Ve),o=!0,Ve},Fe=(0,p.Ds)(async()=>{if(await(0,N.Y_)(2*1e3),z===void 0)return;const it=[];for(const{socketId:rt,lastStatAt:ht}of t.values())ht!==0&&it.push(rt);for(const[rt,ht,Ft]of await z([a,it])){const Wt=t.get(rt)||Ke(rt);Wt.lastStatAt=Date.now(),Wt.stat=[ht,Ft]}const[Ve,st,et]=Ce;Ve&&de&&de([a,Ve,zs(st,et)])},it=>{console.warn("_tPS",it)}).trigger,Xe=it=>{let[Ve,st,et]=it;const[rt,ht]=No(et),Ft=[rt,ht,st],Wt=t.get(Ve)||Ke(Ve);Wt.lastCurAt=Date.now(),Wt.cur=Ft,Wt.lastStatAt===0?Fe():m=!0},we=it=>{let[Ve,st]=it;const et=t.has(st);Ve===0&&et&&(t.delete(st),o=!0)};let Ce=["",NaN,NaN],ze=0;const $e=function(it){it===void 0&&(it=Ce[0]);const Ve=Ae();if(Ve==="")return!1;for(const{socketId:st,cur:et}of t.values())if(et&&et[2]===it&&st!==Ve)return!0;return!1},pt=function(it,Ve,st){Ve===void 0&&(Ve=Rt.Ni),st===void 0&&(st=Rt.Ni),!(Ce[0]===it&&Ce[1]===Ve&&Ce[2]===st)&&(Ce[0]!==it&&(ze=1),!(!ze&&(t.size<2||!$e(it)))&&(Ce=[it,Ve,st],ze&&ze--,de&&de([a,it,zs(Ve,st)])))},Tt=async(it,Ve,st)=>{if(z!==void 0)throw new Error("no re-init fetchCurStat");z=it,de=Ve,Ae=st},Mt=()=>{const it=Ae(),Ve=[];if(it==="")return Ve;for(const{socketId:st,stat:et}of t.values()){if(et===void 0)continue;const[rt,ht]=et;it===st?Ve.unshift([st,rt,ht]):Ve.push([st,rt,ht])}return Ve},Kt=it=>{const Ve=Ae(),st=[];if(Ve==="")return st;for(const{socketId:et,index:rt,cur:ht,stat:Ft}of t.values()){if(Ve===et||ht===void 0||Ft===void 0)continue;const[Wt,At,Be]=ht;if(Be!==it||Wt===Rt.Ni)continue;const[ct,Gt]=Ft;st.push([rt,Wt,At,ct,Gt])}return st},Ut=()=>o?(o=!1,!0):!1,Zt=()=>m?(m=!1,!0):!1;return{flpakKey:a,__curStatMap:t,onSocketConnect:Ee,onRemoteCur:Xe,onRemoteSocket:we,hasOtherUser:$e,tryPackCur:pt,bind:Tt,getRoomStat:Mt,getSocketStat:Mt,getCurStat:Kt,pollRoomChg:Ut,pollCurChg:Zt,pollChange:Zt}},Io=a=>{const t=()=>{},o=Fe=>{let[Xe,we,Ce]=Fe},m=Fe=>{let[Xe,we]=Fe},z=Fe=>!1,de=(Fe,Xe,we)=>{},Ae=async(Fe,Xe,we)=>{},Ee=()=>[],Ue=Fe=>[],Ke=()=>!1;return{flpakKey:a,onSocketConnect:t,onRemoteCur:o,onRemoteSocket:m,hasOtherUser:z,tryPackCur:de,bind:Ae,getRoomStat:Ee,getSocketStat:Ee,getCurStat:Ue,pollRoomChg:Ke,pollCurChg:Ke,pollChange:Ke}},Ws=()=>{};let Hs=!1;const ko=a=>{Hs=a},Eo="dat",jo="cmt",To="mkt:dat",Ao="sclib:dat",Lo=async a=>{let{flpakKey:t,userId:o,uTag:m,token:z,passwd:de,isDummyCmt:Ae=!1,isAllowDat:Ee=!1,isAllowCmt:Ue=!1,isAllowCur:Ke=!1,afterStoreCreate:Fe=Ws,__fetchFlpakAsync:Xe=ds.TP,onTransferError:we=Ws}=a;const Ce=gn(t,Eo,Ee),ze=(Ae?zo:gn)((0,Mo.J6)(t),jo,Ue),$e=(Ke?Us:Io)(t);await Fe({fssDat:Ce});const pt=async(Be,ct)=>{await Ce.saveSOS("OTE|"+Be+"|"+ct),await we(Be,ct)},Tt=(0,Ct.LZ)({flpakKey:t,needDat:!!Ee,needCmt:!!Ue,needCur:!!Ke}),Mt=!o||!Tt?eo():no({query:{uId:String(o),uTag:m,rKey:Tt},usePolling:Hs,lossyListenerMap:{[Ct.NX]:Be=>{const{payload:ct}=Be;$e.onRemoteCur(ct)}},queueListenerMap:{[Ct.VU]:Be=>{const{error:ct}=Be;console.log("flat-ws drop-client",ct),pt("4XX",ct)},[Ct.Ei]:Be=>{const{error:ct}=Be;console.log("flat-ws error",ct),pt("5XX",ct)},[Ct.zS]:()=>{console.log("flat-ws (re)connected"),$e.onSocketConnect()},[Ct.sP]:Be=>{const{payload:ct}=Be;Ce.onRemotePayloadSave(ct)||ze.onRemotePayloadSave(ct)||console.warn("drop save payload:",ct)},[Ct.$7]:Be=>{const{payload:ct}=Be;console.log("flat-ws room cursor:",ct),$e.onRemoteSocket(ct)}}});await Mt.getReadyPromise();const Kt=async(Be,ct,Gt)=>{try{return await Xe(Be,{token:z,passwd:de,skipTTre:String(ct||""),skipTPbxList:(Gt||[]).join("")})}catch(en){const Pn=gs(en);throw $n(en)!=="Fch"&&(0,sn.p)("fFS "+$n(en),en),await pt(Pn,en),en}};await Promise.all([Ce.bind((0,yn.aF)({userId:o,mtimeOffset:Mt.getOffsetMsec()}),Kt).catch(Be=>{throw(0,sn.p)("bindDat",Be),Be}),ze.bind((0,yn.aF)({userId:o,mtimeOffset:Mt.getOffsetMsec()}),Kt).catch(Be=>{throw(0,sn.p)("bindCmt",Be),Be}),$e.bind(async Be=>{try{if(Mt.getIsConnected())return await Mt.emitAsync(Ct.Am,Be)}catch(ct){await(0,N.Y_)(42*1e3)}return[]},Be=>Mt.getIsConnected()&&Mt.emitLossy(Ct.pL,Be),Mt.getSocketId).catch(Be=>{throw(0,sn.p)("bindCur",Be),Be})]);const Ut=new Error("[tracked-error]"),Zt=async Be=>{if(!Be.hasPatch())return;const ct=Be.preSettlePatch(),Gt=(0,bn.ok)((0,bn.oG)(ct));for(let en=0,Pn=Gt.length;en<Pn;en++){const fn=Gt[en];try{await Mt.emitAsync(Ct.s5,[Be.flpakKey,fn.length,(0,bn.iM)(fn)])}catch(cn){const Dn=gs(cn);throw Dn==="5XX"&&(0,sn.p)("saveFss "+$n(cn),cn,Be.name,Be.flpakKey,en+1+"/"+Pn,fn),await pt(Dn,cn),Ut}}Be.postSettlePatch(ct)},it=(0,p.Ds)(async()=>(0,p.g7)(async()=>{await Zt(Ce),await Zt(ze),await Ce.freeSOS()},5*60*1e3,"lFFss.lA"),Be=>{if(Be===Ut)return;const ct=gs(Be);(0,sn.p)("lFFss "+$n(Be),Be),Promise.resolve(pt(ct,Be)).catch(Gt=>{(0,sn.p)("lFFss "+ct+" Err",Gt)})}),Ve=(0,p.Ds)(async()=>{it.trigger(),await it.getRunningPromise();for(let Be=0;Be++<16;)!$e.hasOtherUser()&&await(0,N.Y_)(1e3)});setInterval(Ve.trigger,2*1e3),Ke&&setInterval(()=>$e.onSocketConnect(),20*60*1e3);const st=Ce.getFlatStore(),et=(0,Sn.uq)(Ce.getFlatStore());Ce.useExtORPL(et.onRemotePatchList);const rt=bo(ze.getFlatStore());ze.useExtORPL(rt.onRemotePatchList);const ht=()=>Ce.hasPatch()||ze.hasPatch(),Ft=(0,p.B1)(async()=>(0,p.JN)(async()=>{let Be=0;for(;ht();)await(0,N.Y_)(Math.min(Be,5)*1e3),it.trigger(),await it.getRunningPromise(),Be++})),Wt=()=>globalThis.MB.getSelectionItems()[0].key,At=(0,p.J9)($e.tryPackCur,125);return{fssDat:Ce,fssCmt:ze,cssCur:$e,flatSocket:Mt,flatStore:st,sdkStore:et,cmtStore:rt,sendCur:At,hasSave:ht,saveAsync:Ft,theTree:function(Be){return Be===void 0&&(Be=Qt.gT),console.log((0,Qt.Wr)(st.getFlatTree(),Be))},hotTree:function(Be){return Be===void 0&&(Be=Qt.gT),console.log(et.__devHotTree(Be))},slctTree:function(Be){return Be===void 0&&(Be=Wt()),console.log(et.__devHotTree(Be))},upTree:function(Be,ct){return Be===void 0&&(Be=Wt()),ct===void 0&&(ct=Qt.gT),console.log(et.findUpHotItemList(Be,ct))},newFFT:async function(Be,ct,Gt){return Gt===void 0&&(Gt=ct.name+"-\u4FEE\u590D\u6570\u636E"),(0,_o.Nc)(Be,ct.team_cid,{...ct,name:Gt})},cmtTree:function(Be){return Be===void 0&&(Be=Qt.gT),console.log(rt.__devHotTree(Be))}}},gs=a=>{const t=a&&a.status;return t>=400&&t<500?"4XX":String(a).toLowerCase().includes("timeout")?"Tot":"5XX"},$n=a=>{const t=a&&a.status,o=[];t>=400&&o.push(String(t));const m=String(a).toLowerCase();return m.includes("timeout")&&o.push("Tot"),m.includes("fetch")&&o.push("Fch"),o.join("")||"Err"},Vs=async a=>{let{flpakKey:t}=a;const o=gn(t,To,!1),m=async(de,Ae,Ee)=>(0,ds.TP)(de,{skipTTre:String(Ae||""),skipTPbxList:(Ee||[]).join("")});return await o.bind((0,yn.aF)({}),m),{sdkStore:(0,Sn.uq)(o.getFlatStore())}},fs=async a=>{let{flpakKey:t}=a;const o=gn(t,Ao,!1),m=async(de,Ae,Ee)=>(0,ds.O0)(de,{skipTTre:String(Ae||""),skipTPbxList:(Ee||[]).join("")});return await o.bind((0,yn.aF)({}),m),{sdkStore:(0,Sn.uq)(o.getFlatStore())}}},48986:(Se,ce,n)=>{"use strict";n.d(ce,{eB:()=>Re,Ay:()=>ke});var s=n(38502),p=n(69623),N=n(25582),u=n.n(N),B=n(27992),q=n(28322),V=n.n(q),R=n(89775),J=n.n(R),M=n(63986),F=n.n(M),ie=n(37862),D=n.n(ie),pe=n(68677),H=n(69368),G=n(69173),I=n.n(G),v=n(74937),C=n.n(v);const f=9;function y(Q){let{$opener:Y,$menuBase:me,menuBaseStyle:Ne={},inflexible:je=!1,shouldSetMaxHeight:Ge=!1,$menuContainer:Me,$fontTip:Ie}=Q;if(!Y||!me)return;const Je=me.querySelector("*"),yt=((0,H.$)("li[role=option].is-active",Je)||(0,H.$)("li[role=option]",Je)||(0,H.$)("li[role=empty-msg]",Je)).getBoundingClientRect(),xt=Je.getBoundingClientRect(),bt={styleFor$menuBase:{},styleFor$menu:{},styleFor$menuContainer:{},styleFor$fontTip:{}},wt=tn=>Object.assign(bt.styleFor$menuBase,tn),r=tn=>Object.assign(bt.styleFor$menu,tn),ue=tn=>Object.assign(bt.styleFor$menuContainer,tn),ve=tn=>Object.assign(bt.styleFor$fontTip,tn),{offsetHeight:Te}=Je,He=Ne.width||Y.offsetWidth,qe=Ne.height||Y.offsetHeight,dt=Y.getBoundingClientRect(),{top:It,bottom:Bt,left:Vt}=Object.assign({top:dt.top,right:dt.right,bottom:dt.bottom,left:dt.left},Ne);wt({top:It+"px",left:Vt+"px",width:He+"px",height:qe+"px"});const{innerHeight:$t}=window,Rn=10,un=$t-10,Nn=It+qe;let pn=0,In=0;const mn=(yt.top-xt.top)/.8;j(xt,yt)?pn=-Math.min(mn,It):(In=yt.bottom/.8-xt.bottom/.8,pn=-mn+In);let kn=0;return Ie&&(kn=Ie.getBoundingClientRect().height),Nn+Te+f+pn+kn<$t?(bt.isDownward=!0,j(xt,yt)?(ue({top:pn+"px"}),Ie&&ve({top:pn+xt.height/.8+"px",width:xt.width/.8+"px"})):(ue({top:pn+"px"}),Me.scrollTop=In,Ie&&ve({top:pn+xt.height/.8+"px",width:xt.width/.8+"px"})),Ge&&Bt+Te>un&&r({maxHeight:un-Bt+"px"})):(bt.isDownward=!1,Ge&&It-Te<Rn&&r({maxHeight:It-Rn+"px"})),Object.assign(me.style,bt.styleFor$menuBase),Object.assign(Je.style,bt.styleFor$menu),Object.assign(Me.style,bt.styleFor$menuContainer),Ie&&Object.assign(Ie.style,bt.styleFor$fontTip),bt}function h(Q,Y){return C()(Q)||I()(Q)||(0,s.isValidElement)(Q)?Q:Q[Y||"label"]?Q[Y||"label"]:void 0}function b(Q){return C()(Q)||I()(Q)?String(Q):Q.value||Q.label?String(Q.value||Q.label):void 0}function S(Q){return C()(Q)||I()(Q)?String(Q):Q.ItemIcon?String(Q.ItemIcon):void 0}function A(Q){return C()(Q)||I()(Q)?String(Q):Q.shortcutKey?String(Q.shortcutKey):void 0}function K(Q,Y){return!!Y&&b(Q)===String(Y)}const d='<svg width="10" height="10" ><path d="M5 5.255l1.87-2.043a.623.623 0 0 1 .936 0 .77.77 0 0 1 0 1.022L5.468 6.788a.623.623 0 0 1-.936 0L2.194 4.234a.77.77 0 0 1 0-1.022.623.623 0 0 1 .935 0L5 5.255z" /></svg>';function j(Q,Y){return Y.top/.8>=Q.top/.8&&Y.bottom/.8<=Q.bottom/.8}var T=n(67787);const X=T.Ay.label.withConfig({displayName:"styles__StyledSelect",componentId:"sc-tgxfzg-0"})(["position:relative;display:inline-flex;align-items:center;max-width:100%;min-width:3em;height:28px;font-size:12px;color:#5b6b73;cursor:pointer;button{display:block;height:100%;color:#1F292E;text-align:start;cursor:inherit;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;padding-left:8px;line-height:26px;}.caret{padding:0 11px;color:#7D8694;height:100%;display:inline-flex;align-items:center;svg{width:10px;height:10px;fill:currentColor;transition:transform 0.2s ease-out;}}&{padding-left:5px;border:1px solid transparent;border-radius:2px;}&.is-open{border-color:transparent;.caret{margin-left:auto;visibility:visible;svg{transform:rotate(180deg);}}}&:not(.is-disabled):not(.readonly):hover{border:1px solid transparent;.caret{padding:0 11px;}}&.is-disabled{cursor:not-allowed;opacity:0.6;}&.readonly{cursor:default;}"]),g=T.Ay.div.withConfig({displayName:"styles__StyledSelectMenu",componentId:"sc-tgxfzg-1"})(["position:fixed;pointer-events:none;z-index:1100;.WorkspaceSelectMenu{position:absolute;padding:8px 0;display:block;max-width:20em;min-width:100%;min-height:30px;max-height:300px;overflow-x:hidden;overflow-y:auto;list-style:none;box-shadow:0 2px 8px 0 rgba(0,0,0,0.1);color:#fff;border-radius:2px;background-color:#1f292e;pointer-events:all;transition-property:transform,opacity;transform-origin:50% 0;margin:0;&::-webkit-scrollbar{display:block;width:6px;height:6px;}&::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,0.25);border-radius:4px;&:hover{background-color:rgba(255,255,255,0.30);}}&::-webkit-scrollbar-track{background-color:rgba(#fff,0.5);}&.is-empty{width:fit-content;}&:not(.is-open){opacity:0;transform:scale(0.8);}&.is-downward{top:100%;bottom:initial;}&.is-upward{transform-origin:50% 100%;top:initial;bottom:100%;}&.is-open{opacity:1;pointer-events:initial;border-radius:4px;background-color:#333;width:160px;padding:8px 0px;}> .divider{border-bottom:1px solid rgba(255,255,255,.1);margin:5px;}.SelectOption{display:flex;align-items:center;height:24px;line-height:24px;cursor:pointer;font-size:12px;font-weight:400;& > .Ellipsis{padding:0 16px 0 32px;min-width:160px;}&.is-disabled{cursor:not-allowed;opacity:0.5;}&.is-active{color:#fff;mix-blend-mode:normal;position:relative;}.shortcutkey{color:#999;float:right;}.svg-icon3 svg{color:#f2f4f5;width:12px;height:12px;position:relative;top:2px;}&.is-active .svg-icon2 svg{width:8px;height:6px;position:absolute;left:12px;top:9px;}&:not(.empty-msg):not(.is-disabled):not(.is-active):hover{background-color:#666;color:#fff;}&.empty-msg{padding:0 0.5em;color:#8d9ea6;cursor:not-allowed;}}&.cant-select .SelectOption{cursor:default;}.SelectGroup{.divider{height:1px;margin:4px 16px;background:#7d8694;}& > .title{font-size:12px;padding:0 8px 0 16px;width:100%;height:30px;line-height:30px;color:rgba(255,255,255,0.7);span{display:block;}}& > ul{margin:0;padding:0;}}}.font-select-menu-tip{position:absolute;margin-top:-1px;padding:8px 16px;font-size:10px;box-shadow:0 2px 10px 0 rgba(39,54,78,0.08),4px 12px 40px 0 rgba(39,54,78,0.1);color:#fff;background-color:#1f292e;border-radius:0 0 2px 2px;pointer-events:auto;transition:opacity 0.2s ease-in;.font-link{color:#fff;margin-left:10px;text-decoration:underline;}&:not(.is-show){opacity:0;}&.is-show{opacity:1;color:rgba(255,255,255,1);}}"]);var L=n(51582),ne=n(72214);function ee(Q,Y,me){return(Y=se(Y))in Q?Object.defineProperty(Q,Y,{value:me,enumerable:!0,configurable:!0,writable:!0}):Q[Y]=me,Q}function se(Q){var Y=te(Q,"string");return typeof Y=="symbol"?Y:Y+""}function te(Q,Y){if(typeof Q!="object"||!Q)return Q;var me=Q[Symbol.toPrimitive];if(me!==void 0){var Ne=me.call(Q,Y||"default");if(typeof Ne!="object")return Ne;throw new TypeError("@@toPrimitive must return a primitive value.")}return(Y==="string"?String:Number)(Q)}const fe="IBOT_SELECT_MENU_ROOT",De=document.getElementById(fe)||Object.assign(document.createElement("div"),{id:fe}),Ye=document.body;Ye.contains(De)||Ye.appendChild(De);class ke extends s.PureComponent{constructor(){super(...arguments),ee(this,"state",{isOpen:!1,prevProps:this.props,value:this.props.value}),ee(this,"set$select",Y=>this.setState({$select:Y})),ee(this,"open",()=>this.setState({isOpen:!0})),ee(this,"close",()=>this.setState({isOpen:!1})),ee(this,"toggle",()=>{const Y=!this.state.isOpen;this.setState({isOpen:Y}),Y?this.props.onOpen():this.props.onClose()}),ee(this,"onResizeWindow",()=>this.state.isOpen&&this.close()),ee(this,"onChange",Y=>this.setState({value:Y},()=>{this.close(),this.props.onChange(Y,this.props.attr)})),ee(this,"onSelect",Y=>{let{currentTarget:me}=Y;const{value:Ne}=this.props,{canSelect:je}=this;return this.onChange(je?me.dataset.value:Ne)})}static getDerivedStateFromProps(Y,me){let{prevProps:Ne,value:je}=me;return F()(Ne,Y)?null:{prevProps:Y,value:Y.value}}componentDidMount(){window.addEventListener("resize",this.onResizeWindow)}componentWillUnmount(){window.removeEventListener("resize",this.onResizeWindow)}get isDisabled(){const{isDisabled:Y,disabled:me}=this.props;return Y||me}get readOnly(){return this.props.readOnly}get canSelect(){const{isDisabled:Y,readOnly:me}=this;return!Y&&!me}get displayText(){const{optionList:Y,placeholder:me,optionLabelProp:Ne}=this.props,{value:je}=this.state,Me=(Y.find(Ie=>J()(Ie)&&Ie.slice(0).some(Je=>K(Je,je)))||Y).find(Ie=>!J()(Ie)&&K(Ie,je));return Me?h(Me,Ne):me}render(){const{size:Y,unstyled:me,className:Ne,onMouseEnter:je,onMouseLeave:Ge,arrowSvg:Me}=this.props,{isOpen:Ie,$select:Je,value:nt}=this.state,{isDisabled:yt,readOnly:xt,canSelect:bt}=this,wt=(0,H.Hn)(["WorkspaceSelect",Y,me&&"unstyled",Ne,Ie&&"is-open",yt&&"is-disabled",xt&&"readonly"]);return(0,ne.jsxs)(X,{className:wt,role:"listbox",ref:this.set$select,children:[(0,ne.jsx)("button",{onClick:this.toggle,disabled:yt,children:this.displayText}),typeof Me=="string"?(0,ne.jsx)("span",{className:"caret",dangerouslySetInnerHTML:{__html:Me}}):(0,ne.jsx)("span",{className:"caret",children:Me}),Ie&&(0,ne.jsx)(Re,{isOpen:Ie,...this.props,value:nt,$select:Je,canSelect:bt,onChange:this.onSelect,onMouseEnter:je,onMouseLeave:Ge,onClose:this.close})]})}}ee(ke,"propTypes",{size:u().oneOf(["regular","small"]),theme:u().oneOf(["core","plain"]),menuTheme:u().oneOf(["core","plain","check"]),unstyled:u().bool,className:u().string,menuClassName:u().string,placeholder:u().string,optionList:u().arrayOf(u().oneOfType([u().node,u().shape({label:u().node,shortcutKey:u().node,tooltipWrapper:u().func,ItemIcon:u().node,value:u().any,isDisabled:u().bool}),u().arrayOf(u().oneOfType([u().node,u().shape({label:u().node,ItemIcon:u().node,value:u().any,shortcutKey:u().node,tooltipWrapper:u().func,isDisabled:u().bool})]))])).isRequired,value:u().oneOfType([u().number,u().string]),isDisabled:u().bool,disabled:u().bool,readOnly:u().bool,onChange:u().func,onMouseEnter:u().func,onMouseLeave:u().func,onOpen:u().func,onClose:u().func,optionLabelProp:u().string,arrowSvg:u().oneOfType([u().string,u().node])}),ee(ke,"defaultProps",{size:"regular",theme:"plain",menuTheme:"plain",className:"",menuClassName:"",placeholder:"Choose one\u2026",emptyMsg:"Nothing to display\u2026",optionList:[],isDisabled:!1,arrowSvg:d,onChange:()=>null,onMouseEnter:()=>null,onMouseLeave:()=>null,onOpen:()=>null,onClose:()=>null});class Re extends s.PureComponent{constructor(){super(...arguments),ee(this,"state",{isDownward:!0}),ee(this,"portal",(0,H.ep)(De,"SelectMenuPortal")),ee(this,"menuBaseRef",(0,s.createRef)()),ee(this,"menuContainerRef",(0,s.createRef)()),ee(this,"position",Y=>{const{$select:me}=this.props,{menuBaseRef:{current:Ne},menuContainerRef:{current:je}}=this;if(Y){const Me=V()(Y,"target");if(Me&&D()(Me)&&Me.matches(".WorkspaceSelectMenu"))return}const{isDownward:Ge}=y({$menuBase:Ne,$opener:me,shouldSetMaxHeight:!1,$menuContainer:je});this.setState({isDownward:Ge,isTransform:!0})}),ee(this,"onChange",Y=>{const{onChange:me}=this.props,{isDownward:Ne}=this.state,je=Y.currentTarget,Ge=je.closest(".WorkspaceSelectMenu");if(!je||!Ge)return this.onlose();const{top:Me,bottom:Ie}=je.getBoundingClientRect(),{top:Je,bottom:nt}=Ge.getBoundingClientRect();return Ne&&Me>=Je||!Ne&&Ie<=nt?je.classList.contains("title")?void 0:me(Y):this.onClose()}),ee(this,"onClose",()=>{const{onClose:Y}=this.props;Y()}),ee(this,"onClickOutside",Y=>{let{target:me}=Y;const{$select:Ne}=this.props,je=!De.contains(me),Ge=me.closest("label"),Me=Ge&&Ge.contains(Ne);je&&!Me&&this.onClose()})}componentDidMount(){const{menuBaseRef:{current:Y}}=this;(0,H.sA)((0,H.$)(".WorkspaceSelectMenu",Y)),this.position()}componentWillUnmount(){this.portal&&this.portal.remove()}render(){return(0,p.createPortal)(this.menu,this.portal)}get menu(){const{isOpen:Y,isDisabled:me,menuTheme:Ne,menuClassName:je,optionList:Ge,emptyMsg:Me,value:Ie,canSelect:Je,onMouseEnter:nt,onMouseLeave:yt}=this.props,{isDownward:xt,isTransform:bt}=this.state,wt=Ge.length===0,r=(0,H.Hn)(["WorkspaceSelectMenu",je,bt&&"is-open",xt?"is-downward":"is-upward",me&&"is-disabled",wt&&"is-empty",Je?"can-select":"cant-select"]);return(0,ne.jsxs)(g,{ref:this.menuBaseRef,children:[(0,ne.jsx)("ul",{className:r,ref:this.menuContainerRef,children:wt?(0,ne.jsx)("li",{className:"SelectOption empty-msg",role:"empty-msg",children:Me}):Ge.map((ue,ve)=>J()(ue)?(0,ne.jsx)(mt,{menuTheme:Ne,optionList:ue,value:Ie,onChange:this.onChange,onMouseEnter:nt,onMouseLeave:yt},ve):(0,ne.jsx)(Pe,{menuTheme:Ne,isActive:K(ue,Ie),option:ue,isDisabled:ue.isDisabled,onChange:this.onChange,onMouseEnter:nt,onMouseLeave:yt},ve))}),Y&&(0,ne.jsx)(B.A,{target:document,onClick:this.onClickOutside})]})}}ee(Re,"propTypes",{...ke.propTypes,isOpen:u().bool,canSelect:u().bool,onChange:u().func,onClose:u().func,$select:u().instanceOf(Element)}),ee(Re,"defaultProps",{isOpen:!1,isTransform:!1});function mt(Q){let{value:Y,optionList:[me,...Ne],menuTheme:je,onChange:Ge,onMouseEnter:Me,onMouseLeave:Ie}=Q;return(0,ne.jsxs)("li",{className:"SelectGroup",children:[me==="HIDDELINE"?null:me==="DIVIDER"?(0,ne.jsx)("div",{className:"divider"}):(0,ne.jsx)(pe.A,{className:"title",onClick:Ge,children:me}),(0,ne.jsx)("ul",{children:Ne.map((Je,nt)=>(0,ne.jsx)(Pe,{menuTheme:je,option:Je,isActive:K(Je,Y),isDisabled:Je.isDisabled,onChange:Ge,onMouseEnter:Me,onMouseLeave:Ie},nt))})]})}mt.propTypes={value:u().oneOfType([u().string,u().number]),optionList:u().array,onChange:u().func,menuTheme:u().string,onMouseEnter:u().func,onMouseLeave:u().func};function Pe(Q){let{option:Y,isActive:me,isDisabled:Ne,menuTheme:je,onChange:Ge,onMouseEnter:Me,onMouseLeave:Ie}=Q;const Je=(0,H.Hn)(["SelectOption",me&&"is-active",Ne&&"is-disabled"]),nt=h(Y),yt=b(Y),xt=S(Y),bt=A(Y),{tooltipWrapper:wt}=Y;if(Y.type==="divider")return(0,ne.jsx)("li",{className:"divider"});const r=(0,ne.jsxs)("li",{role:"option","data-value":yt,className:Je,onClick:Ne?void 0:Ge,onMouseEnter:Me,onMouseLeave:Ie,children:[me&&(0,ne.jsx)("span",{className:"svg-icon2",children:(0,ne.jsx)(L.A,{name:"select_right"})}),(0,ne.jsxs)(pe.A,{children:[(0,ne.jsxs)("span",{className:"svg-icon3",children:[(0,ne.jsx)(L.A,{name:xt})," "]})," ",nt,"  ",(0,ne.jsxs)("span",{className:"shortcutkey",children:[bt," "]})]})]});return wt?wt(r):r}Pe.propTypes={isActive:u().bool,option:u().oneOfType([u().node,u().object]),isDisabled:u().bool,menuTheme:u().string,onChange:u().func,onMouseEnter:u().func,onMouseLeave:u().func}},49612:(Se,ce,n)=>{"use strict";n.d(ce,{$I:()=>q,Rj:()=>J,SR:()=>p,Tq:()=>B,UP:()=>R});var s=n(27660);let p=function(M){return M.AIAssistant="AIAssistant",M.Page="Page",M.Flow="flow",M.Mind="mind",M.Table="table",M.Chart="chart",M.AutoFill="autofill",M.Semantic="semantic",M.Component="Component",M.PageV9="PageV9",M.Chat="Chat",M}({}),N=function(M){return M.page="page",M.component="component",M.other="other",M.semantic="semantic ",M}({}),u=function(M){return M.hot="hot",M.other="other",M.semantic="semantic ",M}({});const B="GENERATE_AI_COMPONENT",q=(0,s.R)(()=>[{label:I18N.ChatGPT.ai_prototype,title:I18N.ChatGPT.ai_prototype_title,type:p.PageV9,points:400,recommends:[I18N.ChatGPT.ai_prototype_label_1,I18N.ChatGPT.ai_prototype_label_2,I18N.ChatGPT.ai_prototype_label_3],aiType:"\u751F\u6210\u539F\u578B\u9875\u9762",source:"",menu:N.page,hotMenu:u.hot,icon:"chatGPT/ai-component-page",desc:I18N.ChatGPT.ai_prototype_desc,className:"ai-page",componentTitle:I18N.ChatGPT.ai_prototype_component_title},{label:B,title:I18N.ChatGPT.ai_component_title,type:p.Component,points:200,aiType:"\u751F\u6210AI\u7EC4\u4EF6",source:"",menu:N.component,hotMenu:u.hot,icon:"chatGPT/ai-component-component",desc:I18N.ChatGPT.ai_component_desc,className:"ai-component",componentTitle:I18N.ChatGPT.ai_component_component_title},{label:I18N.ArtBoard.flow,title:I18N.ChatGPT.ai_flow_title,type:p.Flow,points:200,recommends:[I18N.ChatGPT.ai_flow_label_1,I18N.ChatGPT.ai_flow_label_2,I18N.ChatGPT.ai_flow_label_3],aiType:"\u751F\u6210\u6D41\u7A0B\u56FE",source:"",menu:N.component,hotMenu:u.other,icon:"chatGPT/ai-component-flow",desc:I18N.ChatGPT.ai_flow_desc,className:"ai-flow",componentTitle:I18N.ChatGPT.ai_flow_component_title},{label:I18N.ArtBoard.mind,title:I18N.ChatGPT.ai_mind_title,type:p.Mind,points:200,recommends:[I18N.ChatGPT.ai_mind_label_1,I18N.ChatGPT.ai_mind_label_2,I18N.ChatGPT.ai_mind_label_3],aiType:"\u751F\u6210\u601D\u7EF4\u5BFC\u56FE",source:"",menu:N.component,hotMenu:u.other,icon:"chatGPT/ai-component-mind",desc:I18N.ChatGPT.ai_mind_desc,className:"ai-mind",componentTitle:I18N.ChatGPT.ai_mind_component_title},{label:I18N.w.table,title:I18N.ChatGPT.ai_table_title,type:p.Table,points:200,recommends:[I18N.ChatGPT.ai_table_label_1,I18N.ChatGPT.ai_table_label_2,I18N.ChatGPT.ai_table_label_3],aiType:"\u751F\u6210\u8868\u683C",source:"",menu:N.component,hotMenu:u.other,icon:"chatGPT/ai-component-table",desc:I18N.ChatGPT.ai_table_desc,className:"ai-table",componentTitle:I18N.ChatGPT.ai_table_component_title},{label:I18N.w.wChart,title:I18N.ChatGPT.ai_Chart_title,type:p.Chart,points:200,recommends:[I18N.ChatGPT.ai_wChart_label_1,I18N.ChatGPT.ai_wChart_label_2,I18N.ChatGPT.ai_wChart_label_3],aiType:"\u751F\u6210\u56FE\u8868",source:"",menu:N.component,hotMenu:u.other,icon:"chatGPT/ai-component-chart",desc:I18N.ChatGPT.ai_Chart_desc,className:"ai-chart",componentTitle:I18N.ChatGPT.ai_Chart_component_title},{label:I18N.ChatGPT.magic_fill,title:I18N.ChatGPT.ai_magicFill_title,type:p.AutoFill,points:200,recommends:[I18N.ChatGPT.ai_smart_fill_label_2,I18N.ChatGPT.ai_smart_fill_label_1,I18N.ChatGPT.ai_smart_fill_label_3],aiType:"\u667A\u80FD\u586B\u5145",source:"",menu:N.other,hotMenu:u.other,icon:"chatGPT/ai-component-autofill",desc:I18N.ChatGPT.ai_magicFill_desc,className:"ai-magic-fill"},{label:"\u8BED\u4E49\u5316",title:"AI \u8BED\u4E49\u5316",type:p.Semantic,points:200,recommends:["\u5E2E\u6211\u751F\u6210\u4E00\u4E2A\u7535\u5546\u7C7BAPP\uFF0C\u5E76\u4E14\u8F93\u5165\u4E00\u4EFDprd\u6587\u6863","`\u6211\u7231\u52A0\u73ED`\u3002\u8BF7\u5E2E\u6211\u628A\u4E0A\u9762\u8FD9\u6BB5\u8BDD\u8C03\u6574\u7684\u66F4\u52A0\u4E30\u5BCC\u751F\u52A8\u4E00\u70B9","\u5E2E\u6211\u751F\u6210\u4E00\u4EFD2022\u5E74\u56FD\u5BB6\u7ECF\u6D4E\u66F2\u7EBF\u56FE\u30012022\u5E74\u5404\u7701\u7ECF\u6D4E\u6536\u5165\u8868\u683C"],aiType:"MD\u8BED\u4E49\u5316",source:"AI\u8BED\u4E49\u5316\u9762\u677F",menu:N.semantic,hotMenu:u.semantic,icon:"chatGPT/ai-component-autofill",desc:I18N.ChatGPT.ai_magicFill_desc,className:"ai-magic-fill"}]),V=(0,s.R)(()=>[{title:I18N.ChatGPT.ai_prototype,items:q().filter(F=>F.menu===N.page)},{title:I18N.ChatGPT.ai_advanced_components,items:q().filter(F=>F.menu===N.component)},{title:I18N.ChatGPT.ai_magic_fill,items:q().filter(F=>F.menu===N.other)}]),R=(0,s.R)(()=>[{title:I18N.ChatGPT.ai_hot_menu,items:q().filter(F=>F.hotMenu===u.hot)},{title:I18N.ChatGPT.ai_other_menu,items:q().filter(F=>F.hotMenu===u.other)}]),J=(0,s.R)(()=>q().reduce((M,F)=>(M[F.type]=F,M),{}))},50159:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>u,u:()=>N});var s=n(41946),p=n.n(s);const N=(B,q)=>{if(!(B!=null&&B.cid))return-1;let V=0,R="",J=B;for(;J&&!J.root_project&&(V=V+1,R=J.parent_cid,!!R);)J=q.get(R);return V};function u(B){return B&&Object.entries(B).reduce((q,V)=>{let[R,J]=V;return Object.assign(q,{[p()(R)]:J})},{})}},51090:(Se,ce,n)=>{"use strict";n.d(ce,{Aw:()=>u,L9:()=>p,ZJ:()=>J,eP:()=>V,n9:()=>q,nw:()=>R});var s=n(67787);const p=(0,s.AH)(["white-space:nowrap;overflow:hidden;text-overflow:ellipsis;"]),N=(0,s.AH)(['position:relative;pointer-events:none;&::after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;cursor:not-allowed;pointer-events:auto;}']),u=(0,s.AH)(["position:relative;height:32px;padding-left:14px;padding-right:4px;display:flex;align-items:center;border:1px solid transparent;color:",";font-size:12px;cursor:pointer;.expander{width:12px;margin-left:-12px;color:",";}.editable-span,.editable-name{margin-right:14px;line-height:24px;border-radius:2px;&.is-editing{border-bottom-color:transparent;}}.actions{display:none;height:100%;padding-left:6px;.Dropdown > button{display:flex;padding:8px 10px 8px 0;}.action{margin-right:6px;font-size:12px;color:",";&:hover{color:",";}}&.is-active{display:flex;align-items:center;color:",";}}&:hover{.actions{display:flex;align-items:center;}}&:hover,&.hover{color:",";background:",";}&.active,&.is-active{color:",";background:",";}"],M=>M.theme.color_text_L2,M=>M.theme.color_text_disabled01,M=>M.theme.color_text_L2,M=>M.theme.color_text_L2,M=>M.theme.color_text_L1,M=>M.theme.color_text_L1,M=>M.theme.color_btn_secondary_hover,M=>M.theme.color_text_L1,M=>M.theme.color_btn_secondary_active),B=s.Ay.div.withConfig({displayName:"variables__StyledCustomTooltipInner",componentId:"sc-1k65wxr-0"})(["display:flex;align-items:center;height:22px;margin:-3px -7px;.tipsLeft{padding:0 8px;}.tipsRight{width:22px;line-height:22px;background-color:",";border-radius:0 2px 2px 0;color:#f2f4f5;}"],M=>M.theme.color_primary_brand01),q=(0,s.AH)(["width:24px;height:24px;display:flex;justify-content:center;align-items:center;color:",";cursor:pointer;.fore{fill:",";}&:hover{color:",";.fore{fill:",";}}.svg-icon{width:24px;height:24px;}"],M=>M.theme.color_text_L2,M=>M.theme.color_text_L3,M=>M.theme.color_proto,M=>M.theme.color_proto),V=(0,s.AH)(["ul{.active,.select,.hover,li:hover,.rn-list-item:hover{& + li > .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}+ ul > li:first-child > .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}.rn-list-item:has(+ ul > li.active:first-child,+ ul > li.select:first-child,+ ul > li.hover:first-child,+ ul > li:first-child:hover){border-bottom-left-radius:0;border-bottom-right-radius:0;}li{&:has(+ li.active,+ li.select,+ li.hover,+ li:hover){&.active,&.select,&.hover,&:hover{> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}}.rn-list-item{border-radius:6px;&.context-menu-select{border-radius:6px;}}}li.active,li.select,li.dummy-select,li:hover{&:has(> ul > li.dummy-select){> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}&:has(+ li.active,+ li.select,+li:hover){> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}ul{li.dummy-select{&:last-child{> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}}}}ul{li.dummy-select{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}&:has(+ li.dummy-select){> .rn-list-item{border-radius:0;}ul{li.dummy-select{&:last-child{> .rn-list-item{border-radius:0;}}}}}&:last-child,&:first-child{&:has(> ul){> .rn-list-item{border-radius:0;}}}}}}li.active,li.select,li.dummy-select,li:hover{& + li{&.active,&.select,&.dummy-select,&:hover{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}li{> .rn-list-item:hover{&:has(+ ul > li.active:first-child,+ ul > li.select:first-child,+ ul > li.hover:first-child,+ ul > li:first-child:hover){border-bottom-left-radius:0;border-bottom-right-radius:0;}& + ul > li.active:first-child,& + ul > li.select:first-child,& + ul > li.hover:first-child,& + ul > li:first-child:hover{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}li:has(+ li:hover,+ li.select,+ li.active){ul > li:last-child{&.active,&.select,&:hover{> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}}}li:has(ul > li:last-child.active,ul > li:last-child.select,ul > li:last-child:hover){& + li:hover,& + li.select,& + li.active{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}"]),R=(0,s.AH)(["ul{li{.rn-list-item{border-radius:6px;&.context-menu-select{border-radius:6px;}}}li.select,li.dummy-select,li:hover{&:has(> ul > li.dummy-select){> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}&:has(+ li.select,+li:hover){> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}ul{li.dummy-select{&:last-child{> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}}}}ul{li.dummy-select{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}&:has(+ li.dummy-select){> .rn-list-item{border-radius:0;}ul{li.dummy-select{&:last-child{> .rn-list-item{border-radius:0;}}}}}&:last-child,&:first-child{&:has(> ul){> .rn-list-item{border-radius:0;}}}}}}li.select,li.dummy-select,li:hover{& + li{&.select,&.dummy-select,&:hover{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}li{> .rn-list-item:hover{&:has(+ ul > li.select:first-child,+ ul > li.hover:first-child,+ ul > li:first-child:hover){border-bottom-left-radius:0;border-bottom-right-radius:0;}& + ul > li.select:first-child,& + ul > li.hover:first-child,& + ul > li:first-child:hover{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}li:has(+ li:hover,+ li.select){ul > li:last-child{&.select,&:hover{> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}}}li:has(ul > li:last-child.select,ul > li:last-child:hover){& + li:hover,& + li.select{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}"]),J=(0,s.AH)(["ul{padding:8px;border-radius:8px;border:1px solid ",";color:",";background:",";box-shadow:",";li.MenuItem{a{height:28px;border-radius:4px;background:",";color:",";svg:not(.pure-svg-icon) path{fill:",";}.right-arrow svg > path{fill:",";}.shortfont kbd{color:",";}}&:not(.disabled).active{a{background:",";}}&.disabled{a{color:",";svg path{fill:",";}.shortfont kbd{color:",";}}}}}"],M=>M.theme.color_bg_border_01,M=>M.theme.color_text_L1,M=>M.theme.color_bg_white,M=>M.theme.shadow_m,M=>M.theme.color_bg_white,M=>M.theme.color_text_L1,M=>M.theme.color_text_L1,M=>M.theme.color_text_L3,M=>M.theme.color_text_L3,M=>M.theme.color_btn_secondary_hover,M=>M.theme.color_text_disabled01,M=>M.theme.color_text_disabled01,M=>M.theme.color_text_disabled01)},53008:(Se,ce,n)=>{"use strict";n.d(ce,{CV:()=>F,EL:()=>G,Mb:()=>f,Mj:()=>v,O8:()=>A,R0:()=>M,RF:()=>I,R_:()=>D,Sy:()=>C,Uh:()=>ie,a2:()=>pe,eH:()=>b,it:()=>S,jc:()=>K,kv:()=>H,vt:()=>y,zM:()=>h});var s=n(81717),p=n(78401),N=n(79287),u=n.n(N),B=n(18183),q=n(18833),V=n(74222),R=n(15515),J=n(51785);const M=d=>d?[{value:"public",label:I18N.imockSharing.share_anyone_view},{value:"restricted",label:I18N.imockSharing.org_members_only}]:[{value:"public",label:I18N.imockSharing.share_anyone_view}],F=d=>{let{accessToken:j,targetKey:T,pageKey:X,view_mode:g,selection:L,page_begin:ne,canvas_begin:ee}=d;const se=(fe,De)=>De?"&"+fe+"="+De:"";let te=""+location.origin+B.o$+"/"+j+"/sharing?view_mode="+(g||"read_only");return g==="read_only"?te=""+te+se("screen",X)+se("canvasId",T)+se("selection",L):g==="device"?te=""+te+se("screen",ne!=null?ne:X)+se("canvasId",ee!=null?ee:T)+se("selection",L):te=""+te+se("screen",X)+se("selection",L),te},ie=d=>d&&d.length>0?d[0]:"",D=(d,j,T,X)=>{const g=X?"  \u5BC6\u7801\uFF1A"+X:"";return d+" #"+(0,p.qk)(j)+"-"+(0,p.qk)(T||"\u5206\u4EAB")+g},pe=d=>""+(["v6.modao.cc"].includes(location.host)?"https://modao.cc":location.origin)+B.o$+"/"+d,H=d=>{const{project:j,action:T,linkName:X,isFromScreenList:g}=d;s.Y4.trackShareProjectAction({project_type:"\u539F\u578B V8",source:g?"\u9875\u9762\u5217\u8868\u83DC\u5355":(0,s.IQ)(),project_name:j.name,project_cid:j.cid,share_link_action:T,share_link_name:X})},G=d=>{if(!d)return!1;const{created_at:j}=d;return u()(j).isBetween(u()().subtract(3,"days"),u()())},I=d=>Object.entries(d).filter(T=>{let[X,g]=T;return!g.hotAttr.asFolder&&X!==V.$k}).map(T=>T[0]),v=(d,j,T)=>{if(d)return T.length;let X=0;return j==null||j.forEach(g=>{T.find(ne=>ne===g)&&(X+=1)}),X},C=(d,j)=>{if(j.screen_visible_switch){const{selectedSize:T,pageSize:X}=f(d,j);return T+"/"+X}else return I18N.imockSharing.all},f=(d,j)=>{const T=I(d.pageAttrMap),X=v(!j.screen_visible_switch,j.screen_visible_list,T);return{pageSize:T.length,selectedSize:X}},y=d=>{const j={},T=[],X=new Map;for(const[ne,ee,se]of d)if(se.type==="rResBunch"){ne!==V.$k&&T.push(ne),j[ne]={hotAttr:se},X.get(ne)||X.set(ne,[]);const te=X.get(ee);Array.isArray(te)&&X.set(ee,[...te,ne])}const g=ne=>{let ee=[];for(const te of X.get(ne))ee.push(g(te));ee=ee.sort((te,fe)=>te.data.zIndex-fe.data.zIndex);const{hotAttr:se}=j[ne];return{cid:ne,data:{cid:ne,...se},children:ee}},L=g("B@main");return{pageAttrMap:j,pageKeyList:T,treeData:L}};function h(d,j,T){let X=I18N.imockSharing.share;return j&&(X=X+" "+(j.length+1)),{project_cid:d,view_access:"public",screen_visible_switch:!1,screen_visible_list:[],wechat:!1,highlight:!0,view_sticky:!0,comment_permission:"org_member",is_default_link:!1,password:"",device_model:"read_only",view_prd:!1,expire_type:"forever",sticky:!1,...T,link_name:X,type:"advanced"}}const b=async d=>{let{updateType:j,org:T,updateFn:X,updatedKV:g,isCustom:L}=d;const ne=()=>{const se=(T==null?void 0:T.plan)==="org_full"?"MaxOrgSize":"NormalOrgSize";window.SharingEventEmitter.emit("sharing:count",{whichModal:se}),window.top&&window.top.postMessage(JSON.stringify({sharingMessage:"sharing:count",payload:{whichModal:se}}),"*")},ee=()=>{if(!L&&j==="update"){const se={sharingMessage:"sharing:notice",payload:{type:"settingSuccess"}};try{g!=null&&g.expire_type?MB.notice({text:I18N.imockSharing.sharing_hasRest,type:"success"}):MB.notice({text:I18N.imockSharing.setting_valid})}catch(te){window.top&&window.top.postMessage(JSON.stringify(se),"*")}}};try{const se=await X();if(se)return ee(),se}catch(se){await(0,q.m0)(se,ne)}},S=d=>{let{value:j,currentSharing:T,sharingList:X}=d,g=!1;for(const L of X)if(L.cid!==T.cid&&j===L.link_name){g=!0;break}return g},A=(d,j,T,X)=>{let g;if(j.type==="default"){const ne=new URLSearchParams(location.search),ee=ne.get("view_mode")||(0,R.Yt)(d.cid+"_default_sharing_view_mode","read_only",R.qW.String),se=ne.get("selection"),te=ne.get("screen"),fe=ne.get("canvasId");g=F({accessToken:j.access_token,view_mode:ee,page_begin:j.is_first_canvas_open?"":te,canvas_begin:j.is_first_canvas_open?"":fe,selection:se,pageKey:j.is_first_canvas_open?"":T})}else j.screen_visible_switch?T&&j.screen_visible_list.includes(T)?g=F({accessToken:j.access_token,view_mode:j.device_model,pageKey:j.is_first_canvas_open?"":T}):g=F({accessToken:j.access_token,view_mode:j.device_model}):g=F({accessToken:j.access_token,view_mode:j.device_model,pageKey:j.is_first_canvas_open?"":T});return X?g:D(g,d==null?void 0:d.name,j.link_name||"",j.password)},K=(d,j)=>j&&(j==null?void 0:j.otype)!=="personal"?J.tz.InitialOrg(j).limitationSdk.prototypeLimit.pLimitation.max_project_share_count:d?J.tz.InitialUser(d).limitationSdk.prototypeLimit.pLimitation.max_project_share_count:1},54190:(Se,ce,n)=>{"use strict";n.d(ce,{A1:()=>K,A9:()=>S,CH:()=>Je,Cp:()=>s.Cp,E8:()=>f,F7:()=>je,G8:()=>Me,Gx:()=>D,Ip:()=>xt,Jm:()=>j,Kc:()=>R,OJ:()=>v,PK:()=>r,Qs:()=>Q,Qy:()=>fe,SD:()=>Pe,Tf:()=>F,V7:()=>b,Wr:()=>h,XG:()=>me,Xw:()=>L,Yu:()=>M,bX:()=>G,cn:()=>J,dC:()=>wt,hY:()=>g,kZ:()=>se,l9:()=>C,ob:()=>ee,qi:()=>pe,r3:()=>T,s_:()=>ie,sw:()=>yt,ty:()=>ke,up:()=>nt,vS:()=>Ye,vs:()=>Ge,wR:()=>d,wZ:()=>bt,yV:()=>mt,yY:()=>Ne,yk:()=>Ie,yr:()=>I});var s=n(16335),p=n(19249),N=n(49903),u=n(18833);if(n.j!=15)var B=n(98033);if(n.j!=15)var q=n(37253);const V=(ue,ve)=>{const Te=new Error("[NO SENTRY] "+ue);return Object.assign(Te,ve),Te},R=(ue,ve)=>(0,p.DE)("/api/upper/web_v1/design/readonly_initial?access_token="+ue+"&password="+ve),J=async(ue,ve)=>{const Te=ve==="inspect"?"read_only":ve,He="/api/flat/web_v1/preview/initial?access_token="+ue+"&view_mode="+Te,qe=await fetch(He,{credentials:"same-origin"}),dt=await qe.json();if(qe.status!==200)throw V("[fetchGetHead] failed with "+qe.status+": "+He,{status:qe.status,error_type:dt==null?void 0:dt.error_type});return dt},M=(ue,ve)=>(0,p.SN)("/api/upper/web_v1/basics/"+ue,{...ve}),F=async()=>{let ue=[];try{const ve=await(0,p.DE)("/api/library/v4/recent_keywords");ve!=null&&ve.keywords&&(ue=ve==null?void 0:ve.keywords)}catch(ve){(0,u.$r)()}return ue},ie=()=>(0,p.sM)("/api/library/v4/recent_keywords"),D=ue=>(0,p.Ds)("/api/library/v4/recent_keywords",{keyword:ue}),pe=async()=>{if((0,B.Z)())return H();{var ue;let ve;try{ve=await(0,p.DE)("/api/library/v3/project_upper/user_combo_groups")}catch(Te){return await(0,u.FY)(Te),{}}return{userComboGroupList:(ue=ve)==null?void 0:ue.user_combo_groups}}},H=async()=>{var ue;let ve;try{ve=await(0,p.DE)("/api/library/v3/project_upper/org_combo_groups?org_cid="+MB.user.solo_org.cid)}catch(Te){return await(0,u.FY)(Te),{}}return{userComboGroupList:(ue=ve)==null?void 0:ue.org_combo_groups}},G=async ue=>{var ve;let{orgCid:Te}=ue,He;try{He=await(0,p.DE)("/api/library/v3/project_upper/org_combo_groups?org_cid="+Te)}catch(qe){return await(0,u.FY)(qe),{}}return{orgComboGroupList:(ve=He)==null?void 0:ve.org_combo_groups}},I=async ue=>{const{icon_group:ve,market_template:Te}=await(0,s.Yk)(ue.cid),{source:He,title:qe}=ve||{},dt=await(0,p.DE)(He),It=N.I1[qe]||qe||"svg",{iconGroupData:Bt}=(0,N.u2)(dt,It);return{iconGroupData:Bt,marketTemplate:Te}},v=async()=>{const ue=MB.user.solo_org.cid;try{const{asset_groups:ve=[],assets:Te}=await(0,p.Yo)("/api/library/v6/asset_groups?org_cid="+ue);return{userAssetGroupList:ve,userAssets:Te}}catch(ve){return(0,u.$r)(),{userAssetGroupList:[],userAssets:[]}}},C=async function(ue){ue===void 0&&(ue="");const ve=ue||MB.currentOrg.cid;try{const{asset_groups:Te,assets:He}=await(0,p.Yo)("/api/library/v6/asset_groups?org_cid="+ve);return{orgAssetGroupList:Te,orgAssets:He}}catch(Te){return(0,u.$r)(),{orgAssetGroupList:[],orgAssets:[]}}},f=async ue=>{if((0,B.Z)())return y(ue);try{const{asset:ve,user_asset:Te}=await(0,p.B7)("/api/library/v3/asset_data/me/search_by_url",{image_url:ue});return{asset:ve,user_asset:Te}}catch(ve){await(0,u.FY)(ve)}},y=async ue=>{const ve=MB.user.solo_org.cid;try{const{asset:Te,flat_asset:He}=await(0,p.B7)("/api/library/v6/assets/search_by_url",{image_url:ue,org_cid:ve});return{asset:Te,user_asset:He}}catch(Te){await(0,u.FY)(Te)}},h=async(ue,ve)=>{try{const{asset:Te,flat_asset:He}=await(0,p.B7)("/api/library/v6/assets/search_by_url",{image_url:ue,org_cid:ve});return{asset:Te,org_asset:He}}catch(Te){await(0,u.FY)(Te)}},b=async ue=>{if((0,B.Z)())return A(ue);try{const{user_asset_group:ve}=await(0,p.uP)("/api/library/v4/users/asset_groups/"+ue.cid,{...ue});return{user_asset_group:ve}}catch(ve){await(0,u.FY)(ve)}},S=async(ue,ve)=>{const{name:Te,parentCid:He=null,position:qe}=ve,{asset_group:dt}=await(0,p.zi)("/api/library/v6/asset_groups",{org_cid:ue,name:Te,parent_cid:He,position:qe});return{asset_group:dt}},A=async ue=>{const{cid:ve,name:Te,parent_cid:He,position:qe}=ue;try{const{asset_group:dt}=await(0,p.uP)("/api/library/v6/asset_groups/"+ve,{name:Te,parent_cid:He,position:qe});return{user_asset_group:dt}}catch(dt){await(0,u.FY)(dt)}},K=async ue=>{const{cid:ve,name:Te,parent_cid:He,position:qe}=ue;try{const{asset_group:dt}=await(0,p.uP)("/api/library/v6/asset_groups/"+ve,{name:Te,parent_cid:He,position:qe});return{org_asset_group:dt}}catch(dt){await(0,u.FY)(dt)}},d=async ue=>{if((0,B.Z)())return X(ue);try{return await(0,p.uP)("/api/library/v3/asset_data/me/assets/"+ue.asset_cid,{...ue})}catch(ve){await(0,u.FY)(ve)}},j=async ue=>(await(0,p.SN)("/api/library/v6/asset_groups/",{asset_groups:ue})).asset_groups,T=async(ue,ve)=>{const{name:Te,groupCid:He}=ve;return await(0,p.SN)("/api/library/v6/assets",{cids:ue,name:Te,group_cid:He})},X=async ue=>{try{return await(0,p.uP)("/api/library/v6/assets",{...ue})}catch(ve){await(0,u.FY)(ve)}},g=async ue=>{let{cids:ve,...Te}=ue;try{return await(0,p.uP)("/api/library/v6/assets",{cids:ve,...Te})}catch(He){await(0,u.FY)(He)}},L=async ue=>{let{assetCid:ve,name:Te,groupCid:He}=ue;if((0,B.Z)())return ne({assetCid:ve,name:Te,groupCid:He});try{await(0,p.B7)("/api/library/v3/asset_data/me/assets",{cid,name:Te,group_cid:He})}catch(qe){await(0,u.FY)(qe)}},ne=async ue=>{let{assetCid:ve,name:Te,groupCid:He}=ue;try{await(0,p.B7)("/api/library/v6/assets",{asset_cid:[ve],name:Te,group_cid:He})}catch(qe){await(0,u.FY)(qe)}},ee=async ue=>{let{assetCid:ve,name:Te,groupCid:He}=ue;try{const qe=await(0,p.B7)("/api/library/v6/assets",{asset_cid:[ve],name:Te,group_cid:He});return qe==null?void 0:qe.cid}catch(qe){await(0,u.FY)(qe)}},se=async ue=>{let{org_cid:ve,name:Te,group_cid:He}=ue;if((0,B.Z)())return te({org_cid:ve,name:Te,group_cid:He});try{return(0,p.B7)("/api/library/v3/asset_data/org/asset_group_from_user",{user_asset_group_cid,org_cid:ve,org_asset_group_cid})}catch(qe){await(0,u.FY)(qe)}},te=async ue=>{let{org_cid:ve,name:Te,group_cid:He}=ue;try{return(0,p.B7)("/api/library/v6/asset_groups",{org_cid:ve,name:Te,parent_cid:null,group_cid:He,position:-99})}catch(qe){await(0,u.FY)(qe)}},fe=async ue=>{(0,B.Z)()?await De(ue):await(0,p.sM)("/api/library/v3/asset_data/me/assets/"+ue.cid)},De=async ue=>{await(0,q.uz)("/api/library/v6/assets",{cids:[ue.cid]})},Ye=async ue=>{await(0,q.uz)("/api/library/v6/assets",{cids:[ue.cid]})},ke=async ue=>{(0,B.Z)()?await Re(ue):await(0,p.sM)("/api/library/v3/asset_data/me/asset_groups/"+ue)},Re=async ue=>{await(0,p.sM)("/api/library/v6/asset_groups/"+ue)},mt=async ue=>{await(0,p.sM)("/api/library/v6/asset_groups/"+ue)},Pe=ue=>(0,p.iv)("/preferences/update",{...ue}),Q=async(ue,ve)=>{if((0,B.Z)())return Y(ue,ve);try{return await(0,p.uP)("/api/library/v4/users/asset_groups/"+ue,{...ve})}catch(Te){await(0,u.FY)(Te)}},Y=async(ue,ve)=>{const Te=MB.user.solo_org.cid;try{return await(0,p.uP)("/api/library/v6/asset_groups/"+ue,{...ve,org_cid:Te})}catch(He){await(0,u.FY)(He)}},me=ue=>async(ve,Te)=>{try{const He={...Te,org_cid:ue};return await(0,p.uP)("/api/library/v6/asset_groups/"+ve,{...He})}catch(He){await(0,u.FY)(He)}},Ne=async(ue,ve)=>{let Te=null;try{const He=await(0,p.Yo)("/api/community/v1/workspace?category="+ue+"&plabel="+ve);He!=null&&He.market_templates&&(Te=He==null?void 0:He.market_templates)}catch(He){(0,u.$r)()}return Te},je=async(ue,ve)=>{let Te=null,He=null;try{const qe=await(0,p.DE)("/flpak/ww-p2wsearch?"+ue);if(qe!=null&&qe.data){const dt=qe.data,{user_flat_assets:It,user_flat_asset_groups:Bt,org_flat_assets:Vt,org_flat_asset_groups:$t}=dt.asset||{};return Te={...dt,asset:{user_assets:It,user_asset_groups:Bt,org_assets:Vt,org_asset_groups:$t}},He=qe.p2mMMap,{data:Te,p2mMMap:He}}}catch(qe){(0,u.$r)({onClick:dt=>dt({type:"entry:search-panel:search:from:keyword",payload:{from:ve}})})}},Ge=async ue=>{let ve=null;try{const Te=await(0,p.DE)("/flpak/ww-p2meta/"+ue);Te!=null&&Te.length&&(ve=Te)}catch(Te){(0,u.$r)()}return ve},Me=async ue=>{let{currentComboPanel:ve,projectMetaCid:Te}=ue;try{await(0,p.zi)("/flpak/ww-migrate-"+ve+"-combo-group/"+Te+"?mode=normal")}catch(He){await(0,u.FY)(He)}},Ie=async ue=>{let{projectBasicCid:ve}=ue;try{const{userId:Te,flatKey:He}=await(0,p.DE)("/api/flat/web_v1/proj2-edit-info?prj2Cid="+ve);return{userId:Te,flatKey:He}}catch(Te){await(0,u.FY)(Te)}},Je=async ue=>{let{projectBasicCid:ve,accessToken:Te,password:He}=ue;try{const{userId:qe,flatKey:dt}=await(0,p.DE)("/api/flat/web_v1/proj2-preview-info?prj2Cid="+ve+"&access_token="+Te+"&password="+He);return{userId:qe,flatKey:dt}}catch(qe){await(0,u.FY)(qe)}},nt=ue=>(0,p.DE)("/api/dsh2/web_v1/org_entry/"+ue),yt=async ue=>{const{project:ve}=await(0,p.DE)("/api/dashboard/v5/projects/"+ue);return ve},xt=async(ue,ve)=>{await(0,p.SN)("/api/dashboard/v5/projects/"+ue+"/cover",{project:ve})},bt=async ue=>(0,p.DE)("/api/upper/web_v1/basics/statstics/"+ue),wt=async()=>{try{const{result:ue}=await(0,p.DE)("/api/community/v1/community/scene_tag_labels?label_type=combo_group");return ue}catch(ue){console.log(ue)}},r=async ue=>{try{const{stickers:ve}=await(0,p.zi)("/api/community/v1/sticker_search_es",{q:ue,page:1,page_size:200});return ve}catch(ve){return console.log(ve),[]}}},54844:(Se,ce,n)=>{"use strict";n.d(ce,{ER:()=>v,J_:()=>pe,Or:()=>J,QC:()=>h,X0:()=>G,Y5:()=>H,aX:()=>y,dJ:()=>ie,g:()=>M,l1:()=>f,wi:()=>R,yq:()=>F});var s=n(70768),p=n(56193),N=n(18833),u=n(19249),B=n(47163),q=n(15515),V=n(72214);const R=async b=>{let{projectCid:S}=b;return(0,u.DE)("/api/upper/web_v1/sharing/initial?cid="+S)},J=async b=>{let{projectCid:S,sharing:A}=b;return(await(0,u.zi)("/api/flat/web_v1/project_share/"+S,{...A})).project_share},M=async b=>{let{projectCid:S,sharingCid:A}=b;try{return await(0,u.OD)("/api/flat/web_v1/project_share/"+A+"?project_cid="+S)}catch(K){await(0,N.FY)(K)}},F=async b=>{let{sharing:S}=b;try{return await(0,u.SN)("/api/flat/web_v1/project_share/"+S.cid,{project_share:S})}catch(A){await(0,N.FY)(A)}},ie=async b=>{try{return await(0,u.DE)("/api/flat/web_v1/preview/visit_count/"+b)}catch(S){console.error(S)}},D=()=>{const b=document.getElementById("workspace");(0,s.createRoot)(b).render((0,V.jsx)(p.A,{locale:(0,B.w)(),errorType:404,customDes:I18N.ToolBar.ReEnter,isShowPrimaryBtn:!1}))},pe=async b=>{try{if(!b.accessToken||b.project.access_token===b.accessToken)return H(b.project);{const{project:S,accessToken:A}=b,{project_share:K}=await(0,u.DE)("/api/flat/web_v1/project_share/"+S.cid+"?access_token="+A);return{sharing:{...K,type:"advanced",view_mode:K.device_model}}}}catch(S){await D()}},H=b=>{const{cid:S,access_token:A,access:K,building:d,comment_permission:j,password:T,visibility:X,wechat:g,is_first_canvas_open:L}=b,ne=new URLSearchParams(location.search),ee=ne.get("screen"),se=ne.get("canvasId"),te=ne.get("view_mode")||(0,q.Yt)(S+"_default_sharing_view_mode","read_only",q.qW.String),fe=ne.get("view_mode")||(0,q.Yt)(S+"_default_sharing_view_mode","read_only",q.qW.String),De=!!ne.get("selection"),Ye=De?ne.get("selection").split(","):[];return{sharing:{type:"default",project_cid:S,access_token:A,view_access:K,view_sticky:d,comment_permission:j,password:T,visibility:X,wechat:g,view_mode:te,screen_visible_switch:De,screen_visible_list:Ye,page_begin:ee,canvas_begin:se,device_model:fe,is_first_canvas_open:L}}},G=async b=>{let{projectCid:S}=b;try{const{project_shares:A}=await(0,u.DE)("/api/flat/web_v1/project_share/"+S+"/all");return A.sort((K,d)=>new Date(d.created_at).getTime()-new Date(K.created_at).getTime())}catch(A){return await(0,N.FY)(A),[]}},I=async b=>{try{const{result:S}=await fetchGetJSON("/api/accesses/v4/project/"+b+"/permission_check?permission=P:update");return S}catch(S){await apiErrorModals(S)}},v=async b=>{let S=null;try{S=await(0,u.DE)("/flpak/ww-p2meta/"+b)}catch(A){await(0,N.FY)(A)}return S},C={access_token:"",comment_permission:"",device_model:"",highlight:!0,password:"",project_cid:"",screen_visible_switch:!1,screen_visible_list:[],shell_type:"device",simulator_type:"device",sticky:!0,view_access:"private",view_count:0,view_prd:!1,wechat:!1,enable_version_record:!1,view_sticky:!0,is_first_canvas_open:!1},f=()=>({sharing:C}),y=async b=>{let S,A;try{S=await(0,u.DE)("/api/upper/web_v1/basics/"+b+"/get_details"),S&&JSON.stringify(S)!=="{}"&&(A=!0)}catch(K){S=K,A=!1}return{result:S,statusOk:A}},h=async b=>{let S,A;try{S=await(0,u.DE)("/api/dsh2/web_v1/permissions/can_edit_check?cid="+b),S&&JSON.stringify(S)!=="{}"&&(A=!0)}catch(K){S=K,A=!1}return{result:S,statusOk:A}}},55244:(Se,ce,n)=>{"use strict";n.d(ce,{JQ:()=>ie,kZ:()=>H,tg:()=>G,zW:()=>D});var s=n(79287),p=n.n(s),N=n(89022),u=n.n(N),B=n(13076),q=n.n(B);if(n.j!=15)var V=n(47163);function R(v){return v<10?"0"+v:v}function J(v,C,f){const y=new Date(v),h=y.getFullYear(),b=R(y.getMonth()+1),S=R(y.getDate()),A=R(y.getHours()),K=R(y.getMinutes());return C==="time"?A+":"+K:C==="datetime"?""+b+f+S+" "+A+":"+K:""+h+f+b+f+S+" "+A+":"+K}const M=n.j!=15?["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Spt","Oct","Nov","Dec"]:null;function F(v,C){const f=new Date(v),y=f.getFullYear(),h=M[f.getMonth()],b=R(f.getDate()),S=R(f.getHours()),A=R(f.getMinutes());return C==="time"?S+":"+A:C==="datetime"?h+" "+b+" "+S+":"+A:h+" "+b+", "+y+" "+S+":"+A}function ie(v,C,f){if(C===void 0&&(C="datetime"),f===void 0&&(f="."),!v)return"";let y=v;typeof v!="number"&&(y=parseInt(v,10)),y.length===10&&(y=y*1e3);let h="";const b=(0,V.w)();return b==="zh-CN"?h=J(y,C,f):(b==="en-US"||b==="en")&&(h=F(y,C,f)),h}function D(v,C){C===void 0&&(C=pe());const f=C-v;let y="";if(f<=3*1e3)y=I18N.Common.just_now||"\u521A\u521A";else if(f>3*1e3&&f<60*1e3)y=Math.floor(f/1e3)+I18N.Common.num_seconds_ago||"\u79D2\u524D";else if(f>=60*1e3&&f<60*60*1e3)y=Math.floor(f/(60*1e3))+I18N.Common.num_minutes_ago||"\u5206\u949F\u524D";else{y=ie(v,"full");const h=new Date(new Date().setHours(0,0,0,0)).getTime(),b=new Date(new Date().getFullYear(),0,1).getTime();v>b&&(y=ie(v,"datetime")),v>h&&(y=ie(v,"time"))}return y}function pe(){return new Date().getTime()}const H=v=>{p().locale(v==="zh-CN"?"zh-cn":v),p().extend(u()),p().extend(q())},G=()=>p()().isBefore("2024-10-06"),I=v=>new Promise(C=>setTimeout(C,v))},55769:()=>{"use strict";const Se={},ce={},n={},s={}},56193:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>b});var s=n(25582),p=n.n(s),N=n(38502),u=n(67787),B=n(60719);const q=u.Ay.div.withConfig({displayName:"styled__StyledCrashedPage",componentId:"sc-8goufi-0"})(["position:fixed;top:50%;left:50%;transform:translateX(-50%) translateY(-55%);text-align:center;color:#666;.logo{margin:0 auto 40px;height:320px;.logo-clickable{cursor:pointer;}.svg-icon{width:600px;fill:none;}}.mobile &{.logo{width:100%;transform:scale(0.8);}.content{.title{font-size:20px;line-height:24px;}.proposal{width:290px;font-size:14px;line-height:20px;}}}p{margin:0;}.content{margin:40px auto;.title{font-weight:600;font-size:24px;line-height:24px;color:#1f292e;}.proposal{width:max-content;margin:12px auto 40px;font-style:normal;font-weight:normal;font-size:16px;line-height:20px;color:#415058;.count-detail{color:#1684fc;}}.text-left{text-align:left;margin-bottom:-12px;}.text-list{margin:0;padding-left:24px;}}.btn-list{display:grid;grid-auto-flow:column;gap:20px;justify-content:center;.btn{width:200px;height:48px;display:inline-block;border-radius:6px;font-size:16px;font-weight:500;line-height:46px;cursor:pointer;transition:all 0.2s ease-out;}.primary{color:",";background:",";border:1px solid transparent;&:hover{background:",";border:1px solid transparent;}&:active{background:",";border:1px solid transparent;}}.regular{color:",";background:",";border:1px solid ",";&:hover{background:",";border:1px solid ",";}&:active{background:",";border:1px solid ",";}}}"],B.f.color_text_btn.value_light,B.f.color_btn_primary_normal.value_light,B.f.color_btn_primary_hover.value_light,B.f.color_btn_primary_clicked.value_light,B.f.color_text_L1.value_light,B.f.color_bg_white.value_light,B.f.color_bg_border_02.value_light,B.f.color_btn_secondary_hover.value_light,B.f.color_bg_border_02.value_light,B.f.color_btn_secondary_active.value_light,B.f.color_bg_border_02.value_light);var V=n(72214);const R="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='320' fill='none'%3E%3Cpath fill='url(%23a)' d='M200 158.5c-54.4-5.2-59.3 54.2-55 84.5h308c22.8-46-17.7-69.2-44-73.5 49.3-15.5 19.5-88-28.5-116C258.2-17.8 268 165 200 158.5Z' opacity='.6'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-width='2' d='M145 243h66a2 2 0 0 0 2-2V122.6a2 2 0 0 1 1.6-2L333 94l-101.6 32.6a2 2 0 0 0-1.4 1.9v139a2 2 0 0 0 2.5 2l54-13'/%3E%3Cpath fill='%231F292E' d='M213 121.5v122l17 26V127l100.5-32.5-117.5 27Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M455 243h-38'/%3E%3Ccircle cx='251' cy='198' r='4' fill='%231F292E'/%3E%3Cpath fill='%23FF6161' fill-rule='evenodd' d='M250.4 182.2Zm2.3-3.2a24.2 24.2 0 0 1 2.7-1c.9-.3 3 .2 4.4 1.1.6.6-.5.2-2 .4l-1.9.4a22.7 22.7 0 0 0-5 2.4 5.2 5.2 0 0 1-.7.3 1.8 1.8 0 0 1-.3.1 1.2 1.2 0 0 1-.3 0 1 1 0 0 1-.6-.2 1 1 0 0 1-.4-.8 1 1 0 0 1 0-.4l.3-.4.5-.5 1-1a2.2 2.2 0 0 0 .2-.3.7.7 0 0 0 0-.1.9.9 0 0 0-.1-.1c-.4-.2-1-.4-2.3-.6a3.2 3.2 0 0 1-.3 0 1.6 1.6 0 0 1-.5-.2 1 1 0 0 1-.5-.9c0-.4.2-.7.3-.8l.6-.4.6-.3.5-.2a13 13 0 0 0 2.7-1.2l.9-.7v-.4c-.1-.2-.4-.6-1.3-1-.8-.6-2-1.1-3.7-1.7a2.6 2.6 0 0 1-.8-.5 1 1 0 0 1-.5-.9c-.1-.5.2-1 .4-1.1.3-.2.6-.4.9-.4 1.6-.4 4-1 5.9-2.8 1.7-1.5 3-4.2 1.8-9.2a2.3 2.3 0 0 1 0-1.2l.2-.4a1 1 0 0 1 1.6-.2c.*******.3.4l.5 1c.8 2 3.5 4.9 6.7 6.7 1.2.7 1.2.8 0 .4-2.2-.6-5.6-2-7.3-4.6 1.3 4.3-.8 7-2.4 8.6a13.1 13.1 0 0 1-5.3 2.8c1.2.4 2 .9 2.8 1.3a5 5 0 0 1 2 2c.4.8.3 1.6-.1 2.2-.4.6-1 1-1.6 1.4l-*******.2c.7.4 1.2 1 1.2 1.9Zm-2 0Z' clip-rule='evenodd'/%3E%3Cpath fill='%23fff' d='M292 163.5c61.2 5.2 103.5 55.5 117 80l-115 14-4-12.5 2-8-3-11 3-10 3.5-4v-13l7.5-7c-25-2-35-4-36.5-14.5.5-3.6 3.1-15.9 25.5-14Z'/%3E%3Cpath fill='%231F292E' fill-rule='evenodd' d='M363 191.1a219 219 0 0 1 46.8 51.6 1 1 0 1 1-1.6 1 217 217 0 0 0-46.5-51c-21.7-17-48.6-29.9-78.3-27.4-10.1.8-15 6.3-15.4 11.4-.4 5 3.6 10.6 12.8 11.8l5.2.6c5.4.7 11.2 1.4 16.5 2.4a70 70 0 0 1 12.1 3.2c3.5 1.3 6.5 3 8.5 5.4a1 1 0 0 1-1.6 1.3c-1.6-2-4.2-3.5-7.6-4.8a66.7 66.7 0 0 0-11.3-3c-3.5 2.3-7 6.7-7 12.9-.1 6.3 3.7 14.7 16 24.7 4.7 3.7 9.5 5 13.4 4.5a1 1 0 0 1 1.3-.3 11.1 11.1 0 0 0 1.2-.3c2-.7 3.6-2 4.6-3.7.8-1.4 1-3.2.7-5.3-4.2.6-7.4.3-9.7-.7-2-.8-3.2-2-4-3.6a8.5 8.5 0 0 1-.6-5.2c.3-1.5.8-3 1.6-4.5a26.7 26.7 0 0 1-2.2-2.8 7 7 0 0 1-1.2-2c-.4-.8-.6-1.5-.6-2.2a1 1 0 1 1 2 0c0 .******* *******.6 1.2 1 1.8a24.1 24.1 0 0 0 1.7 2.1 29.4 29.4 0 0 1 19.1-11.4 1 1 0 0 1 .4 2c-5.7.8-10.5 3.2-14 6.3a22.1 22.1 0 0 0-4.3 5 1 1 0 0 1-.4.6 13 13 0 0 0-1.5 4.1c-.3 1.6-.1 3 .4 4 .5 1.1 1.5 2 3 2.7 3 1.3 8.2 1.3 16.3-1.3 3.7-1.5 6.9-2.2 9.3-2.1 2.5 0 4.5.8 5.6 2.1a5 5 0 0 1 .7 5.1 12 12 0 0 1-4.3 5.3c-8 6-7.5 11.8-6.6 13.5a1 1 0 1 1-1.8 1c-1.4-3-1.4-9.6 7.2-16 2-1.5 3.1-3.1 3.6-4.4.5-1.4.3-2.4-.3-3.2-.7-.7-2-1.4-4.1-1.4-2.1 0-5 .5-8.6 2l-6.1 1.6c.4 2.5 0 4.7-1 6.6a10.2 10.2 0 0 1-5.6 4.6 12.3 12.3 0 0 1-1.1.3c.6 2.9.4 6-.7 8.6a9.4 9.4 0 0 1-6.1 5.4c5 4.5 6.4 10 5.1 14.4a8.7 8.7 0 0 1-3 4.6c3.2 2.3 9 5 15.8 5a1 1 0 0 1 1 0 28 28 0 0 0 12.1-3.5 1 1 0 1 1 1 1.8 30 30 0 0 1-11.8 3.6 51.8 51.8 0 0 1 3.2 8c1.2 3.9 2 8.2 1.3 12a1 1 0 1 1-2-.4c.6-3.3 0-7.3-1.2-11-1-3.4-2.4-6.5-3.5-8.5-8 0-14.4-3.3-17.8-6-2 .8-4.6.8-7.2-.3-13.4-5.2-19.9-12-22.4-18.3-2.4-6-1.2-11.6.7-15.1a21.2 21.2 0 0 1-2.8-15.1c.8-4.5 3-8.3 5.6-10.2A17.5 17.5 0 0 1 300 193l-14.1-1.9a765.9 765.9 0 0 1-5.2-.7c-10-1.2-15-7.4-14.6-13.9.5-6.4 6.5-12.3 17.2-13.2 30.4-2.5 57.8 10.7 79.7 27.8Zm-68 23.2c2.3 5.5 7 11.7 15.3 18.4 5 4 10.3 5.5 14.7 5 .5 2.5.4 5.2-.5 7.4a7.7 7.7 0 0 1-6.7 4.6c-3 .3-7.1-.5-12.5-3a28.2 28.2 0 0 1-11.7-9.7 19.2 19.2 0 0 1-3-14.2 15 15 0 0 1 4.4-8.5Zm-2.2 25.2c-1.4 3-2.1 7.6-.1 12.5 2.2 5.7 8.2 12 21.2 17.2 2.3.9 4.3.8 5.8.2a1 1 0 0 1 .7-.3 6.5 6.5 0 0 0 3-4c1-3.7-.3-9.1-5.8-13.4-3.5.3-7.8-.7-13.2-3.3-5.2-2.5-9-5.6-11.6-9Zm75.4 17.1a1 1 0 0 1-.5 1.4 7.7 7.7 0 0 0-3.8 4.5 1 1 0 1 1-2-.5 9.7 9.7 0 0 1 5-5.8 1 1 0 0 1 1.3.4Z' clip-rule='evenodd'/%3E%3Cdefs%3E%3ClinearGradient id='a' x1='426' x2='259' y1='125' y2='289' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23EDF8FF'/%3E%3Cstop offset='1' stop-color='%23FAEFFF'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E",J="data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",M="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='320' fill='none'%3E%3Cg clip-path='url(%23a)'%3E%3Cpath fill='url(%23b)' d='M200 158.5c-54.4-5.2-59.3 54.2-55 84.5h308c22.8-46-17.7-69.2-44-73.5 49.3-15.5 19.5-88-28.5-116C258.2-17.8 268 165 200 158.5Z' opacity='.6'/%3E%3Cpath stroke='%231F292E' stroke-width='2' d='M145 242h310'/%3E%3Cpath fill='%23fff' d='M195.3 213c-7.5-17.5-7.5-20-7.5-20l38.8-39.8c15.9 23.6 45.6 71.5 37.6 74.6a46 46 0 0 0-17.5 13.4h-63.1s19.2-10.5 11.7-28.1Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='10' d='M378 221.6c-29.5-66-56.2-32.1-109.5-78.5-52.2-34.8-99.8-36-117.2-16-38.6 44.2 90.6 76.6 34.4 112.9'/%3E%3Cpath fill='%23fff' d='M306 149.4c-12.4 6.4-15.5 16.6-15.5 21 0 16.7 14 13.4 21 9.6l14.1 15.5a67.3 67.3 0 0 1-37.1 22.6 97.9 97.9 0 0 0-42.2 23l-26-23 12.2-35.2-26-9.2c15.5-15 27.6-23.9 40.2-54.9 2.9-11.3 10.7-33 20-32.6 9.4.3 5.3 20.5 2.1 30.5 10.5-4.1 13.4 4.7 15.5 3.8 7-3 13.9 2.5 15.8 5.9 5-3 14.8-4.3 18.8 0 10.4 11 11.2 29.8 3.8 35.2 0 0-13.7-5.8-14.6-6.7-.4-.4-1.1-.4-2.1-5.5Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M306 149c1.5 3.3 4.5 10.5 8.6 13 5.3 3.4 17.6-1.5 12.1-22.5-5.5-21-22.7-23.4-49.4 4.5'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M300.4 125.4c-2.7-2.3-6.8-7.8-16-5m-23.2 17.2c10.2-10.7 17.7-15.5 23.3-17.2m0 0c-2.3-3.2-7.7-6.4-15.7-3.9m-20 15.3c7.8-9 14.5-13.5 20-15.3m0 0c2.8-7 8.3-26-2.3-30.4-15.1-6.3-13.2 35.3-36 62.4a561.6 561.6 0 0 1-43.1 45'/%3E%3Cpath fill='%23fff' d='m330.3 165.8-5.9.5-5.8 6-7.5 7.4-11.5 6.9-11.5 6.9 28.7 11.6 11-11.5 6.8-13.5-.2-10.7-4-3.6Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M310.8 146.3c-7.5 3.2-24.2 14.2-19 29 3.8 10.8 16.5 10.6 26.5-3.1 10-13.8 20.8-3.9 15.5 10.2-12 31.4-47 34.1-60 40.4-9.7 4.7-16.6 8.7-27.8 18.7'/%3E%3Ccircle cx='383.5' cy='234.5' r='17.5' fill='%23fff' stroke='%231F292E' stroke-width='2'/%3E%3Ccircle cx='381.5' cy='229.5' r='18.5' fill='%231684FC'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linejoin='round' stroke-width='2' d='M329.7 261.3a1 1 0 0 1 .7-.3h139a1 1 0 0 1 .7 1.7l-17.8 16a1 1 0 0 1-.7.3h-139a1 1 0 0 1-.7-1.7l17.8-16Z'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linejoin='round' stroke-width='2' d='M313.7 285.5a1 1 0 0 0 .9.5h139.7a1 1 0 0 0 .8-1.5l-2.8-5a1 1 0 0 0-.9-.5H311.7a1 1 0 0 0-.8 1.5l2.8 5ZM472 269.1a1 1 0 0 1-.3.7L457 285a1 1 0 0 1-1.6-.2l-2.9-5.1a1 1 0 0 1 .2-1.2l17.7-16a1 1 0 0 1 1.7.7v5.9Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='square' stroke-linejoin='round' stroke-width='4' d='M466 277.5c16.3 6.3 39.2 21.6 0 32-39.2 10.4-45 32.7-43 42.5'/%3E%3Crect width='2' height='6' x='342' y='266.9' fill='%231F292E' rx='1' transform='rotate(-27 342 267)'/%3E%3Crect width='2' height='6' x='392' y='266.9' fill='%231F292E' rx='1' transform='rotate(-27 392 267)'/%3E%3Crect width='2' height='6' x='429.9' y='267.1' fill='%231F292E' rx='1' transform='rotate(-31 429.9 267)'/%3E%3Crect width='2' height='6' x='351.9' y='266.1' fill='%231F292E' rx='1' transform='rotate(31 352 266.1)'/%3E%3Crect width='2' height='6' x='402.2' y='266.2' fill='%231F292E' rx='1' transform='rotate(36 402.2 266.2)'/%3E%3Crect width='2' height='6' x='440.6' y='266.3' fill='%231F292E' rx='1' transform='rotate(43 440.6 266.3)'/%3E%3Crect width='4' height='10' x='374' y='225' fill='%231F292E' rx='2'/%3E%3Crect width='4' height='10' x='385' y='225' fill='%231F292E' rx='2'/%3E%3Cpath fill='%23FF6161' fill-rule='evenodd' d='M406 190.4Zm1.6 3.5a24 24 0 0 1-.5 2.9c-.2.9-1.7 2.5-3.2 3.2-.8.3 0-.5.6-1.8.2-.5.5-1.1.6-1.9a22.7 22.7 0 0 0 .7-5.6 5 5 0 0 1 0-1l.2-.3c0-.1.2-.3.5-.4.3-.2.6 0 .9 0l.3.3.2.4.2.7a8.4 8.4 0 0 0 .5 1.8 1 1 0 0 0 .3 0 9 9 0 0 0 1.6-1.7 3.3 3.3 0 0 1 .2-.3l.4-.3a1 1 0 0 1 1 0c.*******.6.7v.7l-.1.7v.5c-.2 1-.4 2-.4 3l.2 1c0 .*******.2.2 0 .7 0 1.6-.5.8-.4 2-1.2 3.3-2.4l.8-.4c.2-.1.6-.2 1 0 .*******.8 1l-.1 1c-.5 1.5-1.2 4-.7 6.4.5 2.3 2.2 4.7 7 *******.9.4 1.1.7l.2.4c0 .1.2.4 0 .8a1 1 0 0 1-.6.6h-.5a12.7 12.7 0 0 1-1.1 0c-2.1-.3-6 .6-9.2 2.4-1.2.6-1.3.6-.3-.4 1.6-1.5 4.5-3.7 7.6-3.8a7.9 7.9 0 0 1-6.1-6.5c-.5-2.2-.1-4.3.3-5.9l-2.6 1.7a5 5 0 0 1-2.7.7c-.9 0-1.5-.5-1.9-1.2-.3-.6-.4-1.4-.4-2l.1-2a4 4 0 0 1-.4.4c-.8.4-1.6.5-2.3 0Zm1-1.7Z' clip-rule='evenodd'/%3E%3C/g%3E%3Cdefs%3E%3ClinearGradient id='b' x1='426' x2='259' y1='125' y2='289' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23EDF8FF'/%3E%3Cstop offset='1' stop-color='%23FAEFFF'/%3E%3C/linearGradient%3E%3CclipPath id='a'%3E%3Cpath fill='%23fff' d='M0 0h600v320H0z'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E",F="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='320' fill='none'%3E%3Cpath fill='url(%23a)' d='M332.3 78.2a71 71 0 0 0-110-27.1c-35.6 37.9-27 57.2-37.5 90.3-13 41.3-45.8 28-45.8 101 0 57.5 111.8 79 162.3 27.2 31-31.7 72.1-13.8 118-27.2 66.1-19 52.2-130-19.4-130-53.9 0-61.5-17-67.6-34.2Z' opacity='.6'/%3E%3Cpath stroke='%231F292E' stroke-width='2' d='M80 242h440'/%3E%3Cpath fill='%231F292E' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M156.8 260.3V134h140.5l18.5 120.7-159 5.6Z'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m139 134.4 17.8-.4v126.3L124 241l15-106.5Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m150.7 134.9-2.6 115m-3.8-115.5-4.8 65.1m-3 20.7-3 25.9m23.3-96.6 142.2-.8'/%3E%3Ccircle cx='165' cy='141.3' r='2.2' fill='%23FF6161'/%3E%3Ccircle cx='172.7' cy='141.3' r='2.2' fill='%23FFD361'/%3E%3Ccircle cx='180.5' cy='141.3' r='2.2' fill='%237BECB6'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m301.8 244.2-107 25.4a2 2 0 0 1-1.7-.3c-38.9-29-36-119.8-36-119.8l142.4-.8s7 37.9 23.5 49.4c1 .7.5 3.8-.6 3.9l-5 .4a1 1 0 0 0-.8 1.5l3.2 5.9a1 1 0 0 1-1.2 1.4l-11.7-4a1 1 0 0 0-1.3 1.4l1.8 3.6a1 1 0 0 1-1 1.4l-19-1.3c-1 0-1.5 1.2-.7 1.8l10.7 7.8a1 1 0 0 1 0 1.7l-3.4 2a1 1 0 0 0-.3 1.5l3.8 4.7a1 1 0 0 1-.6 1.7l-7.2 1.2a1 1 0 0 0-.2 2l12.5 5.6a1 1 0 0 1-.2 1.9Z'/%3E%3Cpath fill='%23F33' d='m226.1 198.2-1-13.2a.3.3 0 0 1 .2-.3l7-.5a.3.3 0 0 1 .3.2l1 13.2a.3.3 0 0 1-.2.3l-7 .6a.3.3 0 0 1-.3-.3Zm12.3-1-1-13.2a.3.3 0 0 1 .2-.3l7-.5a.3.3 0 0 1 .3.2l1 13.2a.3.3 0 0 1-.2.3l-7 .5a.3.3 0 0 1-.3-.2Zm19.7-15.2a.3.3 0 0 1 .3.3l.9 10.4v3a22.2 22.2 0 0 1-11.2 18.1.3.3 0 0 1-.4-.1l-3.4-6.1a.3.3 0 0 1 .2-.4c4.7-2.8 7.7-8 7.2-13.9l-.8-10.4a.3.3 0 0 1 .2-.3l7-.6Z'/%3E%3Cpath fill='%23fff' d='m378.8 208.7-6.5-1.8c-6.5-3.2-19.5-8.3-19.5-3.5 0 6.1.9 24 12.6 40a121.7 121.7 0 0 1 16.9 35.1l79-28.2v-33.8s17.7-46 17.7-53.5-14-5-20.5 4.5-7 13-7 13c-5.5-9-11.5-8.2-15-6.6-3.8-5.2-13.1-8.2-17.3-9-4.2-9.8-12.2-11-15.6-10.5l-15.7 3-4.3 30.9 17.4-7.9c1.7 5.4 3.9 18-1.3 25.2-5.2 7.3-11.2 8.3-13.5 7.8l-7.4-4.7Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M400.7 181.2c-3.5 1.6-10.7 5.1-15.7 4.3-6.3-1-12.7-13.1 5.9-25.9s33.7-2.9 35.7 37.2'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M421 166.5c3.7 0 10.7-1.5 16 6.8m6.5 29.4c-.7-15.3-3.3-24.2-6.5-29.4m0 0c4-1 10.3.2 14.9 7.6m5.3 25.7c0-12.5-2.2-20.6-5.3-25.7m0 0c2.6-7.4 11.2-26 22.4-22.2 16.2 5.3-13.7 36.6-14.1 73.3a571 571 0 0 0 4.4 73.8'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m383.9 206-51.3-27.4c-.9-.5-.6-1.9.5-2l18.9-.6a1 1 0 0 0 .7-1.6l-6.9-9.9a1 1 0 0 1 1-1.5l8.3 2a1 1 0 0 0 1.2-1.1l-1.4-7.6a1 1 0 0 1 1.6-1l10.6 8.4a1 1 0 0 0 1.6-1l-5.1-18c-.3-1.1 1.1-1.8 1.8-1l8 11a1 1 0 0 0 1.8-.4l3.8-12a1 1 0 0 1 1.7-.4l5 4.9a1 1 0 0 0 1.4-.1l9.4-11.2c.6-.8 2-.2 1.7.8l-12.9 69a1 1 0 0 1-1.4.8Z'/%3E%3Cpath fill='%23fff' d='m370.1 177.8 4.4 4.4.4 8.7.9 10.8L380 215l4.4 13.2-30.4-10.4-.9-16.5 4-15.2 7.3-8.3h5.6Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M398.7 175.8c3.7 7.6 9.4 27.5-4.9 35.6-10.3 6-20-2.9-18.6-20.5 1.5-17.6-13.6-17-19-2.4-12 32.7 13.7 58.6 19.5 72.3 5.9 13.7 8.8 23 10.8 51.3'/%3E%3Cpath fill='%23FF6161' fill-rule='evenodd' d='M410 106.7Zm.9 3.8a24.1 24.1 0 0 1-1 2.7c-.3.8-2 2.2-3.7 2.7-.8 0 .2-.5 1-1.7l.9-1.8a22.7 22.7 0 0 0 1.5-5.4 5.4 5.4 0 0 1 .3-1l.2-.3.6-.3c.3 0 .6 0 .8.2.*******.3.4l.1.4v.7a8.3 8.3 0 0 0 .3 1.8 1 1 0 0 0 .2 0c.4-.1 1-.5 2-1.3l.2-.3.4-.2a1 1 0 0 1 1 .2c.*******.5.7l-.1.8a10.7 10.7 0 0 1-.4 1.1c-.3.9-.7 2-.8 2.8v1.2l.2.2 1.7-.2c.9-.3 2.1-.9 3.7-1.8.3-.2.5-.3.8-.3.3 0 .7-.1 1 .*******.7.6 1 0 .4 0 .7-.2 1-.7 1.5-1.9 3.8-1.8 6.2.1 2.4 1.4 5 5.9 *******.7.6 1 1v.3a1 1 0 0 1-.8 1.3 1.4 1.4 0 0 1-.5 0l-1.1-.3c-2-.6-6-.4-9.4.8-1.3.5-1.4.4-.3-.4 1.9-1.2 5-2.9 8.2-2.5a7.9 7.9 0 0 1-5-7.4c0-2.2.6-4.3 1.3-5.8-1 .6-2 1-2.8 1.3a5 5 0 0 1-2.8.2 2 2 0 0 1-1.6-1.5c-.2-.7-.2-1.4 0-2 0-.7.2-1.3.4-2a4.3 4.3 0 0 1-.5.3c-.8.3-1.6.3-2.3-.3Zm1.3-1.5Z' clip-rule='evenodd'/%3E%3Cdefs%3E%3ClinearGradient id='a' x1='427.5' x2='219.7' y1='147.8' y2='310.6' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23EDF8FF'/%3E%3Cstop offset='1' stop-color='%23FAEFFF'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E",ie="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='320' fill='none'%3E%3Cpath fill='url(%23a)' d='M332.3 78.2a71 71 0 0 0-110-27.1c-35.6 37.9-27 57.2-37.5 90.3-13 41.3-45.8 28-45.8 101 0 57.5 111.8 79 162.3 27.2 31-31.7 72.1-13.8 118-27.2 66.1-19 52.2-130-19.4-130-53.9 0-61.5-17-67.6-34.2Z' opacity='.6'/%3E%3Cpath stroke='%231F292E' stroke-width='2' d='M80 242h440'/%3E%3Cpath fill='%231F292E' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M156.8 260.3V134h140.5l18.5 120.7-159 5.6Z'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m139 134.4 17.8-.4v126.3L124 241l15-106.5Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m150.7 134.9-2.6 115m-3.8-115.5-4.8 65.1m-3 20.7-3 25.9m23.3-96.6 142.2-.8'/%3E%3Cpath fill='%23FF6161' d='M165 143.5a2.2 2.2 0 1 0 0-4.3 2.2 2.2 0 0 0 0 4.3Z'/%3E%3Cpath fill='%23FFD361' d='M172.7 143.5a2.2 2.2 0 1 0 0-4.3 2.2 2.2 0 0 0 0 4.3Z'/%3E%3Cpath fill='%237BECB6' d='M180.5 143.5a2.2 2.2 0 1 0 0-4.3 2.2 2.2 0 0 0 0 4.3Z'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m301.6 241.5-107 25.4a2 2 0 0 1-1.6-.3c-39-29-36-119.7-36-119.7l142.3-.9s7 37.9 23.6 49.5c1 .6.5 3.7-.7 3.8l-5 .5a1 1 0 0 0-.9 1l.1.4 3.2 6a1 1 0 0 1-1.2 1.3l-11.7-4a1 1 0 0 0-1.2 1.4l1.7 3.7a1 1 0 0 1-1 1.4l-19-1.4a1 1 0 0 0-.6 1.8l10.7 7.9a1 1 0 0 1-.1 1.6l-3.4 2.1a1 1 0 0 0-.4.7 1 1 0 0 0 .2.8l3.8 4.7a1 1 0 0 1-.6 1.6l-7.2 1.3a1 1 0 0 0-.3 1.9l12.5 5.6a1 1 0 0 1-.2 2Z'/%3E%3Cpath fill='%23fff' d='m378.8 208.7-6.5-1.8c-6.5-3.2-19.5-8.3-19.5-3.5 0 6.1.9 24 12.6 40a121.7 121.7 0 0 1 16.9 35.1l79-28.2v-33.8s17.7-46 17.7-53.5-14-5-20.5 4.5-7 13-7 13c-5.5-9-11.5-8.2-15-6.6-3.8-5.2-13.1-8.2-17.3-9-4.2-9.8-12.2-11-15.6-10.5l-15.7 3-4.3 30.9 17.4-7.9c1.7 5.4 3.9 18-1.3 25.2-5.2 7.3-11.2 8.3-13.5 7.8l-7.4-4.7Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M400.7 181.2c-3.5 1.6-10.7 5.1-15.7 4.3-6.3-1-12.7-13.1 5.9-25.9 18.5-12.7 33.7-2.9 35.7 37.2'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M421 166.5c3.7 0 10.7-1.5 16 6.8m0 0c3.2 5.2 5.8 14 6.5 29.4m-6.5-29.4c4-1 10.3.2 14.9 7.6m0 0a49.3 49.3 0 0 1 5.3 25.7m-5.3-25.7c2.6-7.4 11.2-26 22.4-22.2 16.2 5.3-13.7 36.6-14.1 73.3a571 571 0 0 0 4.4 73.8'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m383.9 206-51.3-27.4c-.9-.5-.6-1.9.5-2l18.9-.6a1 1 0 0 0 1-1.1c0-.2-.2-.4-.3-.5l-6.9-9.9a1 1 0 0 1 1-1.5l8.3 2a1 1 0 0 0 1.2-1.1l-1.4-7.6a1 1 0 0 1 1.6-1l10.6 8.4a1 1 0 0 0 1.6-1l-5.1-18c-.3-1.1 1.1-1.8 1.8-1l8 11a1 1 0 0 0 1.8-.4l3.8-12a1 1 0 0 1 1.7-.4l5 4.9a1 1 0 0 0 1.1.1l.3-.2 9.4-11.2c.6-.8 2-.2 1.7.8l-12.9 69a1 1 0 0 1-.5.8 1 1 0 0 1-1 0Z'/%3E%3Cpath fill='%23fff' d='m370.1 177.8 4.4 4.4.4 8.7.9 10.8L380 215l4.4 13.2-30.4-10.4-.9-16.5 4-15.2 7.3-8.3h5.6Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M398.7 175.8c3.7 7.6 9.4 27.5-4.9 35.6-10.3 6-20-2.9-18.6-20.5 1.5-17.6-13.6-17-19-2.4-12 32.7 13.7 58.6 19.5 72.3 5.9 13.7 8.8 23 10.8 51.3'/%3E%3Cpath fill='%23FF6161' fill-rule='evenodd' d='m410.9 110.5-1 2.7c-.3.8-2 2.2-3.7 2.7-.8 0 .2-.5 1-1.7l.9-1.8a22.7 22.7 0 0 0 1.7-6.1l.1-.3.2-.3.6-.3c.3 0 .6 0 .8.2.*******.3.4l.1.4v.7a8.3 8.3 0 0 0 .3 1.8h.2c.4-.1 1-.5 2-1.3l.2-.3.4-.2a1 1 0 0 1 1 .2c.*******.5.7l-.1.8a10.7 10.7 0 0 1-.4 1.1c-.3.9-.7 2-.8 2.8v1.2l.2.2 1.7-.2c.9-.3 2.1-.9 3.7-1.8.3-.2.5-.3.8-.3.3 0 .7-.1 1 .*******.7.6 1 0 .4 0 .7-.2 1-.7 1.5-1.9 3.8-1.8 6.2.1 2.4 1.4 5 5.9 *******.7.6 1 1v.3a1 1 0 0 1-.8 1.3h-.5l-1.1-.3c-2-.6-6-.4-9.4.8-1.3.5-1.4.4-.3-.4 1.9-1.2 5-2.9 8.2-2.5a7.9 7.9 0 0 1-5-7.4c0-2.2.6-4.3 1.3-5.8-1 .6-2 1-2.8 1.3a5 5 0 0 1-2.8.2 2 2 0 0 1-1.6-1.5c-.2-.7-.2-1.4 0-2 0-.7.2-1.3.4-2l-.5.3c-.8.3-1.6.3-2.3-.3Zm1.3-1.5Z' clip-rule='evenodd'/%3E%3Cmask id='b' fill='%23fff'%3E%3Cpath fill-rule='evenodd' d='m228.2 205.7-1.5-22.6-6.6.1 1.5 22.7 6.6-.2Zm-.8-23.4-8.1.2 1.6 24.2 8-.2-1.5-24.2Z' clip-rule='evenodd'/%3E%3C/mask%3E%3Cpath fill='%23fff' fill-rule='evenodd' d='m228.2 205.7-1.5-22.6-6.6.1 1.5 22.7 6.6-.2Zm-.8-23.4-8.1.2 1.6 24.2 8-.2-1.5-24.2Z' clip-rule='evenodd'/%3E%3Cpath fill='%23000' d='M226.7 183h1.1v-1h-1.2v1.2Zm1.5 22.7v1.2h1.2v-1.2h-1.2Zm-8-22.5-.1-1.1h-1.2v1.2h1.2Zm1.4 22.7h-1.1v1.2h1.2v-1.2Zm5.8-23.6h1.2l-.1-1.2h-1.2v1.2Zm-8.1.2v-1.2H218l.1 1.2h1.2Zm1.6 24.2h-1.2l.1 1.2h1.2v-1.2Zm8-.2.2 1.2h1.1v-1.3H229Zm-3.4-23.4 1.5 22.7 2.3-.1-1.5-22.7-2.3.1Zm-5.3 1.3 6.5-.2-.1-2.3-******* 2.3Zm2.6 21.5-1.5-22.7H219l1.5 22.7h2.3Zm5.3-1.3-******* 2.4 6.5-.2-.1-2.3Zm-1.9-22.3 1.6 24.2h2.4l-1.6-24.3-2.4.1Zm-6.8 1.4 8-.3v-2.3l-******* 2.4Zm2.7 23-1.6-24.2H218l1.6 24.2h2.4Zm6.8-1.4-8 .2v2.4l8.2-.2-.2-2.4Z' mask='url(%23b)'/%3E%3Cpath fill='%232E03A0' d='M225.2 210.6a4.2 4.2 0 0 1-4.3-4c-.1-2.1 1.5-4 3.8-4a4.3 4.3 0 0 1 4.3 3.9 4 4 0 0 1-3.8 4.1Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M221.7 206.7a3.4 3.4 0 0 0 3.5 3.1c1.8 0 3.1-1.5 3-3.3a3.4 3.4 0 0 0-3.5-3.2 3.1 3.1 0 0 0-3 3.4Zm-.8 0c.2 2.2 2 4 4.3 4a4 4 0 0 0 3.8-4.2c-.2-2.3-2-4-4.3-4-2.3.1-4 2-3.8 4.2Z' clip-rule='evenodd'/%3E%3Cpath fill='%23fff' d='m226.7 180.2 13.5 20.7-6.7 4.2-13.6-20.6 6.8-4.3Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='m239.1 200.6-12.6-19.3-5.5 3.4 12.7 19.3 5.4-3.4Zm-12.4-20.4-6.8 4.3 13.6 20.6 6.7-4.2-13.5-20.7Z' clip-rule='evenodd'/%3E%3Cpath fill='%23fff' d='M223.6 186.4a4.2 4.2 0 0 1-4.3-4c-.1-2.1 1.5-4 3.8-4a4.2 4.2 0 0 1 4.3 3.9 4 4 0 0 1-3.8 4.1Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M220 182.5a3.4 3.4 0 0 0 3.6 3.2c1.8 0 3.1-1.6 3-3.4a3.4 3.4 0 0 0-3.5-3.2 3.1 3.1 0 0 0-3 3.4Zm-.7 0c.2 2.2 2 4 4.3 4a4 4 0 0 0 3.8-4.2c-.2-2.3-2-4-4.3-4-2.3.1-4 2-3.8 4.2Z' clip-rule='evenodd'/%3E%3Cpath fill='%2313C1FF' d='m243.5 181.9 1.6 24.2 8.1-.3-1.6-24.2-8 .3Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='m245.8 205.3-1.5-22.7 6.6-.2 1.5 22.7-6.6.2Zm-2.3-23.4 8.1-.3 1.6 24.2-8 .3-1.7-24.2Z' clip-rule='evenodd'/%3E%3Cpath fill='%2306CF13' d='M249.4 210c2.3 0 4-2 3.8-4.2-.1-2.2-2-4-4.3-3.9-2.2 0-4 2-3.8 4.2.2 2.2 2 4 4.3 3.9Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M252.4 205.9a3.1 3.1 0 0 1-3 3.3 3.4 3.4 0 0 1-3.5-3.2 3.1 3.1 0 0 1 3-3.3c1.8 0 3.4 1.4 3.5 3.2Zm.8 0c.1 2.2-1.5 4-3.8 4a4.2 4.2 0 0 1-4.3-3.8 4 4 0 0 1 3.8-4.2c2.2 0 4.2 1.7 4.3 4Z' clip-rule='evenodd'/%3E%3Cpath fill='%23FA52D6' d='M244 179.8 233.2 201l7.3 3.8 10.7-21.3-7.2-3.8Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='m234.3 200.8 10-20 5.9 3.1-10 20-6-3.1Zm9.7-21 7.2 3.8-10.7 21.3-7.3-3.8 10.8-21.3Z' clip-rule='evenodd'/%3E%3Cpath fill='red' d='M235 206.5c2 1.1 4.5.4 5.5-1.6s.1-4.4-1.9-5.5a4 4 0 0 0-5.4 1.7 4 4 0 0 0 1.9 5.4Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M239.8 204.5a3.2 3.2 0 0 1-4.4 1.4 3.3 3.3 0 0 1-1.5-4.5 3.2 3.2 0 0 1 4.4-1.3c1.6.9 2.3 2.9 1.5 4.4Zm.7.4a4 4 0 0 1-5.4 1.6c-2-1-2.9-3.5-1.9-5.4a4 4 0 0 1 5.4-1.7c2 1.1 2.9 3.6 1.9 5.5Z' clip-rule='evenodd'/%3E%3Cpath fill='%23FFC400' d='M247.8 185.8c2.3 0 4-2 3.8-4.2-.2-2.2-2-4-4.3-3.9-2.2 0-4 2-3.8 4.2.2 2.2 2 4 4.3 3.9Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M250.8 181.7a3.1 3.1 0 0 1-3 3.3 3.4 3.4 0 0 1-3.5-3.2 3.1 3.1 0 0 1 3-3.3c1.9 0 3.4 1.3 3.5 3.2Zm.8 0c.1 2.2-1.5 4-3.8 4a4.2 4.2 0 0 1-4.3-3.8 4 4 0 0 1 3.8-4.2c2.2 0 4.1 1.7 4.3 4Z' clip-rule='evenodd'/%3E%3Cdefs%3E%3ClinearGradient id='a' x1='427.5' x2='219.7' y1='147.8' y2='310.6' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23EDF8FF'/%3E%3Cstop offset='1' stop-color='%23FAEFFF'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E";function D(S){const{className:A,source:K,rotate:d,width:j,height:T,onClick:X}=S,g={msTransform:"rotate("+d+"deg)",transform:"rotate("+d+"deg)",width:j,height:T};return(0,V.jsx)("img",{className:A,src:K,style:g,onClick:X})}D.propTypes={className:p().string,source:p().string.isRequired,width:p().number,height:p().number,rotate:p().number,onClick:p().func};const pe=D,H=403,G=404,I=[500,501,502,503,504,505],v="link_expired";var C=n(63938);function f(S,A,K){return(A=y(A))in S?Object.defineProperty(S,A,{value:K,enumerable:!0,configurable:!0,writable:!0}):S[A]=K,S}function y(S){var A=h(S,"string");return typeof A=="symbol"?A:A+""}function h(S,A){if(typeof S!="object"||!S)return S;var K=S[Symbol.toPrimitive];if(K!==void 0){var d=K.call(S,A||"default");if(typeof d!="object")return d;throw new TypeError("@@toPrimitive must return a primitive value.")}return(A==="string"?String:Number)(S)}class b extends N.PureComponent{constructor(){super(...arguments),f(this,"typeText",{title_403:{"zh-CN":"\u5BF9\u4E0D\u8D77\uFF0C\u4F60\u6CA1\u6709\u8BBF\u95EE\u6743\u9650",en:"Permission denied",jp:""},des_403_0:{"zh-CN":(0,V.jsx)(V.Fragment,{children:(0,V.jsxs)("p",{className:"proposal",children:["\u5F53\u524D\u8D26\u53F7\u662F ",(0,V.jsx)("span",{className:"count-detail",children:this.props.account}),"\uFF0C\u6CA1\u6709\u8BBF\u95EE\u8BE5\u6587\u6863\u7684\u6743\u9650"]})}),en:(0,V.jsx)(V.Fragment,{children:(0,V.jsxs)("p",{className:"proposal",children:["Current account ",(0,V.jsx)("span",{className:"count-detail",children:this.props.account}),", permission denied."]})}),jp:""},des_403_1:{"zh-CN":(0,V.jsx)(V.Fragment,{children:(0,V.jsxs)("p",{className:"proposal",children:["\u5F53\u524D\u8D26\u53F7\u662F ",(0,V.jsx)("span",{className:"count-detail",children:this.props.account}),"\uFF0C\u6CA1\u6709\u8BBF\u95EE\u8BE5\u6587\u6863\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u5206\u4EAB\u8005\u4FEE\u6539\u8BBF\u95EE\u6743\u9650"]})}),en:(0,V.jsx)(V.Fragment,{children:(0,V.jsxs)("p",{className:"proposal",children:["Current account ",(0,V.jsx)("span",{className:"count-detail",children:this.props.account}),", permission denied. Please contact administrator."]})}),jp:""},des_403_2:{"zh-CN":"\u60A8\u8FD8\u6CA1\u6709\u767B\u5F55\uFF0C\u8BF7\u767B\u5F55\u540E\u5C1D\u8BD5\u8BBF\u95EE",en:"You are not logged in. Please log in and try again.",jp:""},des_403_3:{"zh-CN":"\u5F53\u524D\u8D26\u53F7\u662F "+this.props.account+"\uFF0C\u6587\u4EF6\u6570\u8D85\u9650\uFF0C\u5347\u7EA7\u540E\u5373\u53EF\u7F16\u8F91",en:"Current account "+this.props.account+", permission denied.",jp:""},des_403_4:{"zh-CN":"\u5F53\u524D\u8D26\u53F7\u662F "+this.props.account+"\uFF0C\u4F01\u4E1A\u7248\u8FC7\u671F\uFF0C\u5347\u7EA7\u540E\u5373\u53EF\u7F16\u8F91",en:"Current account "+this.props.account+", permission denied. Please contact administrator. ",jp:""},title_404:{"zh-CN":"\u5F88\u62B1\u6B49\uFF0C\u627E\u4E0D\u5230\u6587\u4EF6",en:"Sorry, there is no visible page.",jp:""},des_404:{"zh-CN":"\u6587\u4EF6\u53EF\u80FD\u5DF2\u88AB\u5220\u9664\uFF0C\u8BF7\u8054\u7CFB\u5206\u4EAB\u8005\u83B7\u53D6\u65B0\u7684\u5206\u4EAB\u94FE\u63A5",en:"The page may have been deleted. Please contact the sharer to inquire about the link status.",jp:""},title_link_expired:{"zh-CN":"\u5F88\u62B1\u6B49\uFF0C\u94FE\u63A5\u5931\u6548",en:"Sorry, the link is invalid",jp:""},des_link_expired:{"zh-CN":"\u94FE\u63A5\u53EF\u80FD\u5DF2\u88AB\u5220\u9664\u6216\u8005\u8BBE\u7F6E\u6709\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u5206\u4EAB\u8005\u83B7\u53D6\u6700\u65B0\u5206\u4EAB\u94FE\u63A5",en:"The link may have been deleted or set incorrectly. Please contact the sharer to obtain the latest link.",jp:""},title_5xx:{"zh-CN":"\u5F88\u62B1\u6B49\uFF0C\u65E0\u6CD5\u8FDE\u63A5\u58A8\u5200\u670D\u52A1\u5668",en:"Sorry, unable to connect to Mockitt server",jp:""},des_5xx:{"zh-CN":(0,V.jsxs)("div",{className:"proposal text-left",children:[(0,V.jsx)("p",{children:"\u8BF7\u5C1D\u8BD5\u4EE5\u4E0B\u65B9\u6CD5\uFF1A"}),(0,V.jsxs)("ul",{className:"text-list",children:[(0,V.jsx)("li",{children:"\u5982\u679C\u60A8\u5F00\u4E86\u7F51\u7EDC\u4EE3\u7406\uFF08VPN\uFF09\uFF0C\u5EFA\u8BAE\u60A8\u5B8C\u5168\u5173\u95ED\u540E\u518D\u5237\u65B0\u58A8\u5200"}),(0,V.jsx)("li",{children:"\u5982\u679C\u4E0D\u662F\u4E0A\u8FF0\u539F\u56E0\u5BFC\u81F4\u7684\uFF0C\u8BF7\u60A8\u8FDE\u63A5\u624B\u673A\u70ED\u70B9\u6216\u5207\u6362\u53E6\u4E00\u4E2A\u7F51\u7EDC"})]}),(0,V.jsx)("p",{children:"\u82E5\u4F9D\u65E7\u65E0\u6CD5\u6B63\u5E38\u8FDB\u5165\u58A8\u5200\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D"})]}),en:(0,V.jsxs)("div",{className:"proposal text-left",children:[(0,V.jsx)("p",{children:"Please try the following methods:"}),(0,V.jsxs)("ul",{className:"text-list",children:[(0,V.jsx)("li",{children:"If your VPN is on, please turn it off then refresh Mockitt."}),(0,V.jsx)("li",{children:"If not, please switch to another network."})]}),(0,V.jsx)("p",{children:"If the problem stays, please contact support."})]}),jp:""},title_0:{"zh-CN":"\u5F88\u62B1\u6B49\uFF0C\u9875\u9762\u5D29\u6E83\u4E86",en:"Sorry, this page is corrupted",jp:""},des_0:{"zh-CN":"\u8BF7\u5C1D\u8BD5\u5237\u65B0\u9875\u9762\u6216\u5173\u95ED\u518D\u91CD\u65B0\u6253\u5F00\u58A8\u5200\uFF0C\u82E5\u65E0\u6CD5\u89E3\u51B3\u8BF7\u8054\u7CFB\u5BA2\u670D",en:"Please refresh this page or close this page and start Mockitt again. If the problem stays, please contact  support.",jp:""},previewFile:{"zh-CN":"\u9884\u89C8\u6587\u4EF6",en:"Preview File",jp:""},goHome:{"zh-CN":"\u8FD4\u56DE\u58A8\u5200\u9996\u9875",en:"Back to Homepage",jp:""},reloadPage:{"zh-CN":"\u5237\u65B0\u9875\u9762",en:"Refresh this page",jp:""},contactUs:{"zh-CN":"\u8054\u7CFB\u5BA2\u670D",en:"Support",jp:""}}),f(this,"clickGoHome",()=>{let A="",{locale:K,homeUrl:d}=this.props;K==="zh-CN"?A=d:K==="jp"?A="https://mockitt.wondershare.jp":A="https://mockitt.wondershare.com",window.top!==window.self?window.top.location.replace(A):location.replace(A)}),f(this,"clickReloadPage",()=>{window.location.reload()}),f(this,"clickContactUs",()=>{let A="",{locale:K}=this.props;if(K==="zh-CN"){document.getElementById("mdOnlineSupport")&&document.getElementById("mdOnlineSupport").click();return}else K==="jp"?A="https://support.wondershare.jp":A="https://support.wondershare.com/product/mockitt.html";window.open(A)}),f(this,"getErrorConfig",()=>{const{errorType:A,primaryBtnText:K,primaryBtnClick:d,secondaryBtnText:j,secondaryBtnClick:T,locale:X}=this.props,{typeText:g,clickGoHome:L,clickContactUs:ne,clickReloadPage:ee}=this;let se,te,fe="",De=null,Ye;const ke={text:g.goHome[X],click:L},Re={text:g.reloadPage[X],click:ee},mt={text:g.contactUs[X],click:ne};switch(!0){case A===H:se=ke.text,te=ke.click,Ye=R;break;case A===G:se=ke.text,te=ke.click,Ye=J;break;case A===v:se=ke.text,te=ke.click,Ye=J;break;case I.includes(A):se=Re.text,te=Re.click,fe=mt.text,De=mt.click,Ye=M;break;default:se=Re.text,te=Re.click,fe=mt.text,De=mt.click,Ye=X==="en"?ie:F;break}return K&&(se=K),d&&(te=d),j&&(fe=j),T&&(De=T),{svgBg:Ye,primaryText:se,primaryClick:te,secondaryText:fe,secondaryClick:De}}),f(this,"getErrorInfo",()=>{const{errorType:A,identity:K,locale:d,customTitle:j,customDes:T,account:X}=this.props,{typeText:g}=this;let L,ne;if(j)L=j;else{const ee=I.includes(A)?"title_5XX":"title_"+A;L=g[ee]?g[ee][d]:g.title_0[d]}if(T)ne=T;else{let ee="des_"+A;A===H?ee=X?"des_"+A+"_"+K:"des_"+A+"_2":I.includes(A)?ee="des_5xx":A===v&&(ee="des_link_expired"),ne=g[ee]?g[ee][d]:g.des_link_expired[d]}return{title:L,desc:ne}}),f(this,"handleClickLogo",()=>{let{isLogoClickable:A,logoUrl:K}=this.props;A&&(window.top!==window.self?window.top.location.href=K:window.location.href=K)})}render(){const{locale:A,isShowPrimaryBtn:K,isShowSecondaryBtn:d,isLogoClickable:j,previewFileBtnClick:T}=this.props,{typeText:X}=this,{svgBg:g,primaryText:L,primaryClick:ne,secondaryText:ee,secondaryClick:se}=this.getErrorConfig(),{title:te,desc:fe}=this.getErrorInfo();return(0,V.jsxs)(q,{children:[(0,V.jsx)("div",{className:"logo",children:(0,V.jsx)("span",{className:j?"logo-clickable":"",onClick:this.handleClickLogo,children:(0,V.jsx)(pe,{source:g})})}),(0,V.jsxs)("div",{className:"content",children:[(0,V.jsx)("p",{className:"title",children:te}),typeof fe=="string"?(0,V.jsx)("p",{className:"proposal",children:fe}):fe]}),(0,V.jsxs)("div",{className:"btn-list",children:[d&&se&&ee&&(0,V.jsx)("a",{className:"btn regular",onClick:se,children:ee}),T&&(0,V.jsx)("a",{className:"btn regular",onClick:T,children:X.previewFile[A]}),K&&ne&&L&&(0,V.jsx)("a",{className:"btn primary",onClick:ne,children:L}),ee===X.contactUs[A]&&(0,V.jsx)(C.A,{})]})]})}}f(b,"propTypes",{locale:p().oneOf(["zh-CN","en","jp"]),identity:p().oneOf([0,1]),customTitle:p().string,customDes:p().string,isShowPrimaryBtn:p().bool,primaryBtnText:p().string,primaryBtnClick:p().func,isShowSecondaryBtn:p().bool,secondaryBtnText:p().string,secondaryBtnClick:p().func,errorType:p().oneOf([0,403,404,500,501,502,503,504,505,v]).isRequired,account:p().string,isLogoClickable:p().bool,logoUrl:p().string,homeUrl:p().string}),f(b,"defaultProps",{locale:"zh-CN",isShowPrimaryBtn:!0,isShowSecondaryBtn:!0,identity:0,errorType:0,isLogoClickable:!0,logoUrl:"/",homeUrl:"/workspace/home"})},58315:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>pe});var s=n(94586),p=n(8509),N=n(18941);function u(H,G){return H.classList?!!G&&H.classList.contains(G):(" "+(H.className.baseVal||H.className)+" ").indexOf(" "+G+" ")!==-1}function B(H,G){H.classList?H.classList.add(G):u(H,G)||(typeof H.className=="string"?H.className=H.className+" "+G:H.setAttribute("class",(H.className&&H.className.baseVal||"")+" "+G))}function q(H,G){return H.replace(new RegExp("(^|\\s)"+G+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function V(H,G){H.classList?H.classList.remove(G):typeof H.className=="string"?H.className=q(H.className,G):H.setAttribute("class",q(H.className&&H.className.baseVal||"",G))}var R=n(38502),J=n(96582),M=n(99129),F=function(G,I){return G&&I&&I.split(" ").forEach(function(v){return B(G,v)})},ie=function(G,I){return G&&I&&I.split(" ").forEach(function(v){return V(G,v)})},D=function(H){(0,N.A)(G,H);function G(){for(var v,C=arguments.length,f=new Array(C),y=0;y<C;y++)f[y]=arguments[y];return v=H.call.apply(H,[this].concat(f))||this,v.appliedClasses={appear:{},enter:{},exit:{}},v.onEnter=function(h,b){var S=v.resolveArguments(h,b),A=S[0],K=S[1];v.removeClasses(A,"exit"),v.addClass(A,K?"appear":"enter","base"),v.props.onEnter&&v.props.onEnter(h,b)},v.onEntering=function(h,b){var S=v.resolveArguments(h,b),A=S[0],K=S[1],d=K?"appear":"enter";v.addClass(A,d,"active"),v.props.onEntering&&v.props.onEntering(h,b)},v.onEntered=function(h,b){var S=v.resolveArguments(h,b),A=S[0],K=S[1],d=K?"appear":"enter";v.removeClasses(A,d),v.addClass(A,d,"done"),v.props.onEntered&&v.props.onEntered(h,b)},v.onExit=function(h){var b=v.resolveArguments(h),S=b[0];v.removeClasses(S,"appear"),v.removeClasses(S,"enter"),v.addClass(S,"exit","base"),v.props.onExit&&v.props.onExit(h)},v.onExiting=function(h){var b=v.resolveArguments(h),S=b[0];v.addClass(S,"exit","active"),v.props.onExiting&&v.props.onExiting(h)},v.onExited=function(h){var b=v.resolveArguments(h),S=b[0];v.removeClasses(S,"exit"),v.addClass(S,"exit","done"),v.props.onExited&&v.props.onExited(h)},v.resolveArguments=function(h,b){return v.props.nodeRef?[v.props.nodeRef.current,h]:[h,b]},v.getClassNames=function(h){var b=v.props.classNames,S=typeof b=="string",A=S&&b?b+"-":"",K=S?""+A+h:b[h],d=S?K+"-active":b[h+"Active"],j=S?K+"-done":b[h+"Done"];return{baseClassName:K,activeClassName:d,doneClassName:j}},v}var I=G.prototype;return I.addClass=function(C,f,y){var h=this.getClassNames(f)[y+"ClassName"],b=this.getClassNames("enter"),S=b.doneClassName;f==="appear"&&y==="done"&&S&&(h+=" "+S),y==="active"&&C&&(0,M.F)(C),h&&(this.appliedClasses[f][y]=h,F(C,h))},I.removeClasses=function(C,f){var y=this.appliedClasses[f],h=y.base,b=y.active,S=y.done;this.appliedClasses[f]={},h&&ie(C,h),b&&ie(C,b),S&&ie(C,S)},I.render=function(){var C=this.props,f=C.classNames,y=(0,p.A)(C,["classNames"]);return R.createElement(J.Ay,(0,s.A)({},y,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},G}(R.Component);D.defaultProps={classNames:""},D.propTypes={};const pe=D},59316:Se=>{function ce(n,s,p,N){var u=-1,B=n==null?0:n.length;for(N&&B&&(p=n[++u]);++u<B;)p=s(p,n[u],u,n);return p}Se.exports=ce},60690:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>I});var s=n(18654),p=n(33233);const N="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQwIDc5LjE2MDQ1MSwgMjAxNy8wNS8wNi0wMTowODoyMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDkzNzZFN0RDODhFMTFFOEExOUJFNkQ0MTNFM0Q2OTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDkzNzZFN0VDODhFMTFFOEExOUJFNkQ0MTNFM0Q2OTYiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpEOTM3NkU3QkM4OEUxMUU4QTE5QkU2RDQxM0UzRDY5NiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpEOTM3NkU3Q0M4OEUxMUU4QTE5QkU2RDQxM0UzRDY5NiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PktroGEAAAAoSURBVHjaYvz06RMDDPDy8sLZTAw4AOkSjP///4dzPn/+TAs7AAIMAG56COJosoI3AAAAAElFTkSuQmCC",u="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAAGzCI4dAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAACKADAAQAAAABAAAACAAAAACVhHtSAAAAVklEQVQYGYWOMQ6AMAwD04ot7wJWyv+/kblgpIsqZcBDGtu1W9uPc9o17tk0IsKaJHe3rpGKvZAgbCxyhS+jBaOLrCiRciM7iNEFLwkMznwDge/BfxsegvIceOfO91wAAAAASUVORK5CYII=",B=Object.entries(p.fm).reduce((v,C)=>{const f=C[0];return{...v,[f]:[p.fm[f].value_light,p.fm[f].value_dark||p.fm[f].value_light]}},{}),q=Object.entries(p.$B).reduce((v,C)=>{const f=C[0];return{...v,[f]:[p.$B[f].value_light,p.$B[f].value_dark||p.$B[f].value_light]}},{}),V=Object.entries(s.af).reduce((v,C)=>{var f;const y=C[0];return{...v,[y]:[s.af[y].value,((f=s.qY[y])==null?void 0:f.value)||s.af[y].value]}},{}),R=v=>{let C;const[f,y,h]=v,b=h!=null?h:f;return y==="light"?C=s.af[b].value:C=s.qY[b].value,{[f]:[C,C]}},M=[["color_tips_black","dark","color_ruler_shadow_color"]].reduce((C,f)=>{const y=R(f);return{...C,...y}},{}),H={...V,...M,...{loading_styles_bg:["linear-gradient(130deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%)","linear-gradient(118.3deg, #474848 1.71%, #363738 100%)"],float_nav_box_shadow:["0 4px 8px 0 rgba(39, 54, 78, 0.08), 0 4px 12px 0 rgba(39, 54, 78, 0.06)","0px 24px 38px rgba(0, 0, 0, 0.14), 0px 9px 46px rgba(0, 0, 0, 0.12), 0px 11px 15px rgba(0, 0, 0, 0.2)"],menu_content_shadow:["0 6px 16px -8px rgba(0,0,0,0.08), 0 9px 28px 0 rgba(0,0,0,0.05)","0px 24px 38px rgba(0, 0, 0, 0.14), 0px 9px 46px rgba(0, 0, 0, 0.12), 0px 11px 15px rgba(0, 0, 0, 0.2)"],shadow_1:["0 2px 8px 0 rgba(0, 0, 0, 0.1)","0 2px 6px 0 rgba(0,0,0,0.30), 0 10px 30px 0 rgba(0,0,0,0.15), inset 0 1px 0 0 #252626"],shadow_2:["0 2px 10px 0 rgba(39, 54, 78, 0.8), 0 12px 40px 0 rgba(39, 54, 78, 0.1)","0 24px 38px 3px rgba(0,0,0,0.14), 0 9px 46px 8px rgba(0,0,0,0.12), 0 11px 15px -7px rgba(0,0,0,0.20)"],shadow_3:["0 2px 8px 0 rgba(0, 0, 0, 0.1)","0 2px 7px 0 rgba(0, 0, 0, 0.26), 0 12px 36px 0 rgba(0, 0, 0, 0.1)"],shadow_4:["0 2px 10px 0 rgba(0, 0, 0, 0.12)","0 11px 15 px rgba(0, 0, 0, 0.2), 0 9 px 46px 0 rgba(0, 0, 0, 0.12)"],shadow_5:["0 2px 10px 0 rgba(0, 0, 0, 0.12)","0 24px 38px 3px rgba(0, 0, 0, 0.14), 0 9px 46px 8px rgba(0, 0, 0, 0.12), 0 11px 15px -7px rgba(0, 0, 0, 0.2)"],shadow_hover:["0 2px 4px rgba(0, 0, 0, 0.14)","0 2px 4px rgba(0, 0, 0, 0.46), 0 8px 16px rgba(0, 0, 0, 0.40)"],miss_font_box_shadow:["0 3px 10px rgba(0, 0, 0, 0.1)","0px 24px 38px rgba(0, 0, 0, 0.14), 0px 9px 46px rgba(0, 0, 0, 0.12), 0px 11px 15px rgba(0, 0, 0, 0.2)"],export_bar_box_shadow:["0 -4px 8px rgba(219, 219, 219, 0.4)","0 -4px 8px rgba(0, 0, 0, 0.35)"],sidebar_box_shadow:["0 2px 10px 0 rgba(39, 54, 78, 0.08), 0 12px 40px 0 rgba(39, 54, 78, 0.1)",""],interation_active_box_shadow:["0 0 4px 0 rgba(34, 162, 237, 0.66)"," 0 0 4px 0 rgba(41,141,248,0.50)"],modal_bg1:["linear-gradient(to bottom,#fafafa,#f5f5f5)","#4f5052"],modal_shadow:["0 2px 10px 0 rgba(0, 0, 0, 0.1), 0 12px 40px 0 rgba(0, 0, 0, 0.1)",""],comment_layer_box_shadow:["0 -2px 10px rgba(26, 58, 109, 0.12)","0 -24px 38px 3px rgba(0,0,0,0.14), 0 -9px 46px 8px rgba(0,0,0,0.12), 0 11px 15px -7px rgba(0,0,0,0.20)"],workspace_dropdown_menu_shadow:["0 2px 8px 0 rgba(0, 0, 0, 0.1)","0 2px 7px 0 rgba(0, 0, 0, 0.26), 0 12px 36px 0 rgba(0, 0, 0, 0.1)"],drag_sort_box_shadow:["0 1px 4px 0 rgba(0, 0, 0, 0.15)","0px 2px 4px rgba(0, 0, 0, 0.15)"],annotate_btn_shadow:["0 2px 6px rgba(39, 54, 78, 0.08)"," 0 6px 10px rgba(0, 0, 0, 0.22), 0px 13px 30px rgba(0, 0, 0, 0.146744)"],color_var_bind_point:["#DEDFE0","#4B4B4B"],bg_base64:[N,u],img_wrap_border_width:[1,2],fontPosition:["73px",0],disabled_opacity:[1,.5],footer_disabled_opacity:[1,.6]},...B,...q,...{color_literal_number:["#015CC5","#218BFF"],color_literal_string:["#002155","#80CCFF"],color_literal_boolean:["#8250DF","#A475F9"],color_literal_operator:["#F23DD1","#E85AAD"]},...{expr_editor_string:["#002155","#80CCFF"],expr_editor_number:["#015CC5","#218BFF"],expr_editor_boolean:["#8250DF","#A475F9"],expr_editor_func:["#23863A","#2DA44E"],expr_editor_builtinVar:["#8250DF","#A475F9"]}},G=(v,C)=>{const f={};return Object.entries(v).forEach(y=>{let[h,b]=y;Array.isArray(b)?f[h]=b[C]:typeof b=="object"?f[h]=G(b,C):f[h]=b}),f},I={light:G(H,0),dark:G(H,1)}},60719:(Se,ce,n)=>{"use strict";n.d(ce,{f:()=>s.f});var s=n(29523);const u={...{border_modal:{description:"\u5F39\u7A97 border",value_light:"none",value_dark:"1px solid #4F5052"}},...{shadow_xs:{description:"\u5149\u6807\u9634\u5F71",value_light:"0px 1px 2px 0px rgba(0, 18, 38, 0.40)",value_dark:"0px 1px 2px 0px rgba(0, 18, 38, 0.40)"},shadow_s:{description:"\u6ED1\u5757\u6309\u94AE\u9634\u5F71",value_light:"0px 3px 6px 0px rgba(0, 18, 38, 0.10)",value_dark:"0px 3px 6px 0px rgba(0, 18, 38, 0.10)"},shadow_m:{description:"\u83DC\u5355/\u60AC\u6D6E\u7F16\u8F91\u680F\u200D\u6295\u5F71",value_light:"0px 4px 10px 0px rgba(0, 18, 38, 0.10)",value_dark:"0px 4px 10px 0px rgba(0, 18, 38, 0.10)"},shadow_l:{description:"\u5F39\u7A97/\u60AC\u6D6E\u9762\u677F/\u5168\u5C40\u63D0\u793A\u6295\u5F71",value_light:"2px 8px 20px 0px rgba(0, 0, 0, 0.10)",value_dark:"2px 8px 20px 0px rgba(0, 0, 0, 0.10)"},shadow_left:{description:"\u5DE6\u4FA7\u9762\u677F\u5206\u9694\u7EBF",value_light:"-1px 0px 0px 0px #E8EAEC inset",value_dark:"-1px 0px 0px 0px #E8EAEC inset"},shadow_right:{description:"\u53F3\u4FA7\u9762\u677F\u5206\u9694\u7EBF",value_light:"1px 0px 0px 0px #E8EAEC inset",value_dark:"1px 0px 0px 0px #E8EAEC inset"},shadow_bottom:{description:"\u5E95\u90E8\u5206\u9694\u7EBF\uFF08\u9876\u680F\u3001\u6807\u5C3A\u3001\u5F39\u7A97\u6807\u9898\u680F\u7B49\uFF09",value_light:"0px -1px 0px 0px #E8EAEC inset",value_dark:"0px -1px 0px 0px #E8EAEC inset"}}}},60820:Se=>{var ce=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function n(s){return ce.test(s)}Se.exports=n},61068:(Se,ce,n)=>{"use strict";n.d(ce,{p:()=>F,A:()=>pe});var s=n(95549),p=n(38502),N=n(25582),u=n.n(N),B=n(63986),q=n.n(B),V=n(17307),R=n(53732),J=n.n(R),M=n(67787);const F="checkbox-has-box",ie=M.Ay.label.withConfig({displayName:"styles__StyledCheck",componentId:"sc-gd8gcf-0"})(["position:relative;display:flex;align-items:center;cursor:pointer;color:",";> input[type=radio],> input[type=checkbox]{position:absolute;opacity:0;width:12px;height:12px;}.Check-state{position:relative;width:12px;height:12px;font-size:12px;display:flex;align-items:center;border:1px solid ",";border-radius:2px;transition:all 0.2s ease-in-out;background-color:",";flex-shrink:0;.icon{position:absolute;top:-1px;left:-1px;}}.Check-label{margin-left:0.33333em;color:",";}&.is-checked .Check-state{background-color:",";border:1px solid ",";color:#fff;.icon{transform:scale(0.833);color:#fff;}}&:not(.is-checked) .Check-state .icon{speak:none;opacity:0;}&.readonly,&.is-disabled{cursor:default;.Check-state{background:",";border:1px solid ",";.icon{color:",";}}}&.is-disabled{cursor:not-allowed;}&.","{input,.Check-state{margin:6px;transition:none;}.Check-label{margin-left:4px;}}"],H=>H.theme.color_text_L2,H=>H.theme.color_text_disabled01,H=>H.theme.color_bg_white,H=>H.theme.color_text_L2,H=>H.theme.color_proto,H=>H.theme.color_proto,H=>H.theme.color_btn_secondary_active,H=>H.theme.color_btn_secondary_active,H=>H.theme.color_text_disabled01,F);var D=n(72214);class pe extends p.PureComponent{constructor(){super(...arguments),(0,s.A)(this,"state",{prevProps:this.props,isChecked:this.props.isChecked}),(0,s.A)(this,"onToggle",G=>{const{name:I,value:v,label:C,onChange:f,onToggle:y,isDisabled:h,readOnly:b,attr:S}=this.props,{isChecked:A}=this.state,K=!h&&!b,d=K?A==="mixed"?!0:!A:A;this.setState({isChecked:d}),y(d,I,v||C,G),f(d,S,G)})}static getDerivedStateFromProps(G,I){let{prevProps:v}=I;return q()(v,G)?null:{prevProps:G,isChecked:G.isChecked}}render(){const{className:G,label:I,name:v,isDisabled:C,readOnly:f}=this.props,{isChecked:y}=this.state,h=J()(G,{"is-checked":!!y,"is-disabled":C,readonly:f});return(0,D.jsxs)(ie,{className:h,children:[(0,D.jsx)("input",{type:"checkbox",defaultChecked:!!y,disabled:C,name:v,onChange:this.onToggle}),(0,D.jsx)("span",{className:"Check-state",children:(0,D.jsx)(V.C,{name:y==="mixed"?"new_replace/box_check_mixed":"new_replace/box_check"})}),I&&(0,D.jsx)("span",{className:"Check-label",children:I})]})}}(0,s.A)(pe,"propTypes",{isChecked:u().bool||"mixed",isDisabled:u().bool,readOnly:u().bool,onChange:u().func.isRequired,onToggle:u().func.isRequired,label:u().any,name:u().string,attr:u().string,value:u().any,className:u().string}),(0,s.A)(pe,"defaultProps",{isChecked:!1,label:"",className:"",onChange:()=>null,onToggle:()=>null})},63172:(Se,ce,n)=>{"use strict";n.d(ce,{s:()=>F});var s=n(19249);const p=async(ie,D)=>{const{project:pe}=await(0,s.zi)("/api/dashboard/v5/projects/"+ie+"/invite",D);return pe},N=async(ie,D)=>(0,s.SN)("/api/dashboard/v5/permissions/projects/"+ie,D),u=async(ie,D)=>{await(0,s.uz)("/api/dashboard/v5/permissions/projects/"+ie,D)};var B=n(50159),q=n(29152),V=n(1076);const R=async(ie,D)=>await(0,s.DE)("/api/dashboard/v5/spaces/"+ie+"?org_cid="+D),J={project_manager:"\u7684\u6743\u9650\u53D8\u4E3A\u300C\u7BA1\u7406\u5458\u300D",project_member:"\u7684\u6743\u9650\u53D8\u4E3A\u300C\u53EF\u7F16\u8F91\u300D",project_viewer:"\u7684\u6743\u9650\u53D8\u4E3A\u300C\u4EC5\u67E5\u770B\u300D",project_ban_viewer:"\u7684\u6743\u9650\u53D8\u66F4\u4E3A\u300C\u7981\u6B62\u67E5\u770B\u300D"};let M=function(ie){return ie.Org="org",ie.OrgSpace="space",ie.Folder="folder",ie.Project="project",ie}({});const F={"entry:projectAccess:initProjectData":async(ie,D)=>{let{getState:pe,dispatch:H}=ie,{payload:G}=D;try{const{initData:I,members:v}=G,C=I==null?void 0:I.org,{user:f,space:y,project:h,team:b,top_parent_team:S,lang:A}=I||{},K=new Map,d=new Map;return K.set(b==null?void 0:b.cid,b),K.set(S==null?void 0:S.cid,S),b!=null&&b.parent_cid&&(b==null?void 0:b.parent_cid)!==S.cid&&K.set(b==null?void 0:b.parent_cid,S),d.set(h==null?void 0:h.cid,h),H({type:V.X2.UpdateState,payload:{user:f,org:{...C,members:v},space:y,folderListMap:K,projectListMap:d,lang:A}}),!0}catch(I){return console.error(I.message),!1}},"entry:projectAccess:inviteOrgMemberToProject":async(ie,D)=>{let{getState:pe,dispatch:H}=ie,{payload:G}=D;const{projectCid:I,userCid:v,roleName:C,userName:f}=G;if(!I||!v)return;const y=pe(),h=(0,q.IE)(y),b=(0,q.M0)(y),S=(0,q._B)(y);try{const A=await p(I,{user_cid:v,role:C}),{space:K}=await R(b,h);S.set(A.cid,A),await H({type:V.X2.UpdateProjectListMap,payload:new Map(S)}),await H({type:V.X2.UpdateSpace,payload:K}),window.top.postMessage({type:"updateApp",data:{newApp:(0,B.A)(A)}},"*"),window.top.postMessage({type:"successMsg",data:{msg:{name:f,other:J[C]}}},"*")}catch(A){window.top.postMessage({type:"errMsg",data:{msg:"\u6743\u9650\u66F4\u6539\u5931\u8D25"}},"*")}},"entry:projectAccess:updateMemberProjectPermission":async(ie,D)=>{let{getState:pe,dispatch:H}=ie,{payload:G}=D;const{projectCid:I,userId:v,roleName:C,userName:f}=G,y=pe();try{const{permission:h}=await N(I,{target_role:C,target_user_id:v}),b=(0,q._B)(y),S=b.get(I),A=(S==null?void 0:S.permissions)||[],K=A.find(T=>Number(T.user_id)===Number(v));let d=[];K?d=A.map(T=>Number(T.user_id)===Number(v)?h:T):d=[...A,h];const j={...S,permissions:d};b.set(I,j),await H({type:V.X2.UpdateProjectListMap,payload:new Map(b)}),window.top.postMessage({type:"updateApp",data:{newApp:(0,B.A)(j)}},"*"),window.top.postMessage({type:"successMsg",data:{msg:{name:f,other:J[C]}}},"*")}catch(h){window.top.postMessage({type:"errMsg",data:{msg:"\u6743\u9650\u66F4\u6539\u5931\u8D25"}},"*")}},"entry:projectAccess:removeProjectMemberPermission":async(ie,D)=>{let{getState:pe,dispatch:H}=ie,{payload:G}=D;const{projectCid:I,userId:v,roleName:C,userName:f}=G,y=pe();try{await u(I,{target_role:C,target_user_id:v});const h=(0,q._B)(y),b=h.get(I),S=(b==null?void 0:b.permissions)||[];if(S.find(K=>Number(K.user_id)===Number(v))){const K=S.filter(j=>Number(j.user_id)!==Number(v)),d={...b,permissions:K};h.set(I,d),await H({type:V.X2.UpdateProjectListMap,payload:new Map(h)}),window.top.postMessage({type:"successMsg",data:{msg:{name:f,other:"\u7684\u6743\u9650\u5DF2\u79FB\u9664"}}},"*"),window.top.postMessage({type:"updateApp",data:{newApp:(0,B.A)(d)}},"*")}}catch(h){window.top.postMessage({type:"errMsg",data:{msg:"\u6743\u9650\u66F4\u6539\u5931\u8D25"}},"*")}}}},63938:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>B});var s=n(38502),p=n(25582),N=n.n(p),u=n(72214);B.propTypes={className:N().string,children:N().oneOfType([N().array,N().string]),isWonderShare:N().bool,id:N().string,onClick:N().func,disabled:N().bool,canClick:N().bool};function B(q){let{className:V,children:R,isWonderShare:J=!1,id:M="mdOnlineSupport",onClick:F,disabled:ie,canClick:D}=q;const pe="qd30090468111af7daaf09a6395b687fc3becd624cf3",H=J?"https://support.wondershare.com/":"";(0,s.useEffect)(()=>{if(!ie)return!J&&G(),()=>{const v=document.getElementById(pe);v&&v.remove()}},[]);const G=()=>{n.g?(n.g.wpaShowItemId=window.wpaShowItemId="123",n.g.qidian_ex1=window.qidian_ex1="12"):(window.wpaShowItemId="123",window.qidian_ex1="12");let v=document.createElement("script");v.id=pe,v.src="https://wp.qiye.qq.com/qidian/3009046811/1af7daaf09a6395b687fc3becd624cf3",v.charset="utf-8",v.async=!0,v.defer=!0,document.body.appendChild(v)},I=v=>{if(ie){D&&F&&F();return}if(F&&F(),J){window.open(H);return}if(M!=="mdOnlineSupport"){v.preventDefault();const C=document.getElementById("mdOnlineSupport");C&&C.click()}};return(0,u.jsx)("div",{style:{cursor:"pointer"},id:M,className:V,onClick:I,children:R})}},65609:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>V});var s=n(95549),p=n(38502),N=n(53732),u=n.n(N),B=n(22460),q=n(72214);class V extends p.PureComponent{constructor(){super(...arguments),(0,s.A)(this,"getClassName",()=>{const{className:J,type:M,size:F,corner:ie,disabled:D}=this.props;return u()(J,"button-root",{["type-"+M]:M,["size-"+F]:F,["corner-"+ie]:ie,"is-disabled":D})}),(0,s.A)(this,"onButtonClick",J=>{if(this.props.disabled){J.preventDefault();return}const{onClick:M}=this.props;M&&M(J)}),(0,s.A)(this,"onButtonMouseDown",J=>{if(this.props.disabled){J.preventDefault();return}const{onMouseDown:M}=this.props;M&&M(J)}),(0,s.A)(this,"getChildrenNode",()=>(0,q.jsx)("span",{className:"btn-text",children:this.props.children}))}render(){const J=this.getClassName(),M=this.getChildrenNode();return(0,q.jsx)(B.O,{className:J,onClick:this.onButtonClick,onMouseDown:this.onButtonMouseDown,children:(0,q.jsx)("div",{className:"btn-icon-text-container",children:M})})}}(0,s.A)(V,"defaultProps",{type:"linear",disabled:!1,size:"common",corner:"smooth"})},65825:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>V});var s=n(38502),p=n(39303),N=n(67787);const u=N.Ay.div.withConfig({displayName:"styles__StyledSharingLoading",componentId:"sc-iidm8l-0"})(["position:absolute;top:0;left:0;right:0;bottom:0;display:flex;align-items:center;justify-content:center;.loading{width:50px;height:50px;}"]);var B=n(72214);const V=(0,s.memo)(()=>(0,B.jsx)(u,{className:"sharing-loading",children:(0,B.jsx)(p.A,{className:"loading"})}))},68677:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>ie});var s=n(38502),p=n(25582),N=n.n(p),u=n(46219),B=n(69368),q=n(67787);const V=q.Ay.span.withConfig({displayName:"styled__StyledEllipsis",componentId:"sc-z4nkzm-0"})(["null"]);var R=n(72214);function J(D,pe,H){return(pe=M(pe))in D?Object.defineProperty(D,pe,{value:H,enumerable:!0,configurable:!0,writable:!0}):D[pe]=H,D}function M(D){var pe=F(D,"string");return typeof pe=="symbol"?pe:pe+""}function F(D,pe){if(typeof D!="object"||!D)return D;var H=D[Symbol.toPrimitive];if(H!==void 0){var G=H.call(D,pe||"default");if(typeof G!="object")return G;throw new TypeError("@@toPrimitive must return a primitive value.")}return(pe==="string"?String:Number)(D)}class ie extends s.PureComponent{constructor(){var pe;super(...arguments),pe=this,J(this,"state",{isTruncated:!1,isDetected:!1}),J(this,"set$ellipsis",H=>Object.assign(this,{$ellipsis:H.current})),J(this,"detectTruncation",function(H){return H===void 0&&(H=pe.$ellipsis),H.offsetWidth<H.scrollWidth})}componentDidMount(){return this.setState({isDetected:!0,isTruncated:this.detectTruncation()})}componentDidUpdate(pe){let{children:H}=pe;const{children:G}=this.props,{isDetected:I}=this.state;if(H!==G)return this.setState({isDetected:!1});if(!I)return this.setState({isDetected:!0,isTruncated:this.detectTruncation()})}render(){const{className:pe,to:H,type:G,max:I,display:v,lang:C,theme:f,noTooltip:y,withTooltip:h,withQuote:b,withPeriod:S,withComma:A,withQuestionMark:K,children:d,...j}=this.props,{isTruncated:T,isDetected:X}=this.state,g={children:d},L=X&&(T?"is-truncated":"isnt-truncated"),ne={type:H?"link":"inline",theme:f,className:(0,B.Hn)(["Ellipsis",L,pe,(b||S||A||K)&&"Punctuation",b&&"with-quote",S&&"with-period",A&&"with-comma",K&&"with-question-mark"]),href:H,"data-type":G,"data-max":I,style:{display:v,maxWidth:isFinite(I)?I+"em":I},content:(h||T)&&!y&&(0,R.jsx)("div",{lang:C,className:"EllipsisTip",...g}),children:d,setRef:this.set$ellipsis,...j},ee=(0,R.jsx)(u.A,{...ne});return b||S||A||K?(0,R.jsx)(V,{className:(0,B.Hn)(["Punctuation",b&&"with-quote",S&&"with-period",A&&"with-comma",K&&"with-question-mark",L]),children:ee}):ee}}J(ie,"propTypes",{className:N().string,theme:N().oneOf(["core","plain"]),type:N().oneOf(["user","id","email","org","team","app","widget"]),max:N().oneOfType([N().string,N().number]),display:N().oneOf(["inline-block","block"]),lang:N().string,to:N().string,children:N().node,noTooltip:N().bool,withTooltip:N().bool,withQuote:N().bool,withComma:N().bool,withPeriod:N().bool,withQuestionMark:N().bool}),J(ie,"defaultProps",{lang:"en",theme:"core"})},70812:(Se,ce,n)=>{"use strict";n.d(ce,{U:()=>R,j:()=>J});var s=n(16615),p=n(49612),N=n(40486),u=n(78257);const B={[p.SR.PageV9]:"\u751F\u6210\u539F\u578B\u9875\u9762",[p.SR.Component]:"\u751F\u6210\u7EC4\u4EF6",[p.SR.Flow]:"\u751F\u6210\u6D41\u7A0B\u56FE",[p.SR.Mind]:"\u751F\u6210\u601D\u7EF4\u5BFC\u56FE",[p.SR.Table]:"\u751F\u6210\u8868\u683C",[p.SR.Chart]:"\u751F\u6210\u56FE\u8868",[p.SR.Semantic]:"AI\u6307\u4EE4",[p.SR.Chat]:"AI\u5BF9\u8BDD",[p.SR.AutoFill]:"\u667A\u80FD\u586B\u5145",[p.SR.AIHTML]:"\u751F\u6210\u539F\u578B\u9875\u9762v2_\u539F\u578B\u5185"},q={[u.lS.Directory]:"\u76EE\u5F55\u9762\u677F",[u.lS.Builtin]:"\u7EC4\u4EF6\u9762\u677F",[u.lS.Icon]:"\u56FE\u6807\u9762\u677F",[u.lS.Page]:"\u9875\u9762\u9762\u677F",[u.lS.Asset]:"\u56FE\u7247\u9762\u677F",[u.lS.Template]:"\u6BCD\u7248\u9762\u677F",[u.lS.AI]:"AI\u9762\u677F"},V={color:"\u989C\u8272\u6837\u5F0F",appear:"\u5916\u89C2\u6837\u5F0F",text:"\u6587\u672C\u6837\u5F0F"};let R;(function(M){const F=M.AIOpenSourceTrack=j=>{const T=B[j];T&&(0,s.ZI)("proto_ai_entrance",{ai_func_name:T})},ie=M.AIUseTrack=(j,T)=>{const X=B[j];!X||!T||(0,s.ZI)("proto_ai_use",{ai_func:X,ai_input:T})},D=M.addShapeToCanvasTrackSourceMap={quickCreate:"\u5FEB\u6377\u952E",topToolbar:"\u9876\u90E8\u64CD\u4F5C\u680F"},pe=M.addShapeToCanvasTrack=(j,T)=>{j&&T&&(0,s.ZI)("proto_shape_add",{name:j,source:T})},H=M.addDrawIOToCanvasTrack=j=>{j&&(0,s.ZI)("proto_draw_add",{type:j})},G=M.addAnimationTrack=j=>{j&&I18N.dConst.ani[j]&&(0,s.ZI)("proto_animation_add",{type:I18N.dConst.ani[j]})},I=M.updateInteractionTrack=j=>{var T;const{type:X,interactionTrigger:g,interactionType:L,value:ne=""}=j;if(!g||!L)return;const ee=(0,N.Oz)(g);if(!ee)return;const te=(T=(0,N.gu)().find(fe=>fe.value===L))==null?void 0:T.label;te&&(0,s.ZI)("proto_interaction_set",{type:X,trigger:ee,action:te,value:ne})},v=M.searchResultTrack=(j,T,X)=>{const g=q[T];!j||!g||(0,s.ZI)("proto_search_result",{keyword:j,source:g,count:X})},C=M.watermarkExposureTrack=j=>{if(!j)return;const T=!!MB.currentOrg;(0,s.ZI)("proto_watermark_show",{watermark_type:j,workspace_type:T?"org":"solo"})},f=M.protoEditorLoadSuccessTrack=j=>{j&&(0,s.ZI)("proto_load_success",{uid:j})},y=M.protoEditorShareTrack=(j,T)=>{j&&(0,s.ZI)("proto_share",{uid:j,source:T})},h=M.protoEditorPresentTrack=(j,T)=>{j&&(0,s.ZI)("proto_present",{uid:j,source:T})},b=M.protoDownloadTrack=(j,T,X)=>{!j||!T||!X||(0,s.ZI)("proto_download",{uid:j,type:T,source:X})},S=M.protoStyleTrack=(j,T,X)=>{!j||!T||!X||(0,s.ZI)("proto_style_set",{uid:j,type:T,source:V[X]})},A=M.protoMasterClickTrack=j=>{j&&(0,s.ZI)("proto_master_click",{uid:j})},K=M.protoMasterPublishTrack=(j,T)=>{!j||!T||(0,s.ZI)("proto_master_publish",{uid:j,type:T})},d=M.protoMasterUseTrack=(j,T,X)=>{!j||!T||!X||(0,s.ZI)("proto_master_use",{uid:j,source:T,type:X})}})(R||(R={}));const J=M=>{let{source:F,type:ie,status:D}=M;console.log("\u{1F7E6}[blue]->source, type, status: ",F,ie,D),(0,s.ZI)("proto_html2proto",{source:F,type:ie,status:D})}},71272:(Se,ce,n)=>{"use strict";n.d(ce,{Y:()=>p,w:()=>s});const s=400,p=800},72274:(Se,ce,n)=>{"use strict";n.d(ce,{HY:()=>I,Tw:()=>y,Yl:()=>D,Zz:()=>f,y$:()=>ie});var s=n(76241);function p(h){return"Minified Redux error #"+h+"; visit https://redux.js.org/Errors?code="+h+" for the full message or use the non-minified dev environment for full errors. "}var N=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),u=function(){return Math.random().toString(36).substring(7).split("").join(".")},B={INIT:"@@redux/INIT"+u(),REPLACE:"@@redux/REPLACE"+u(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+u()}};function q(h){if(typeof h!="object"||h===null)return!1;for(var b=h;Object.getPrototypeOf(b)!==null;)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(h)===b}function V(h){if(h===void 0)return"undefined";if(h===null)return"null";var b=typeof h;switch(b){case"boolean":case"string":case"number":case"symbol":case"function":return b}if(Array.isArray(h))return"array";if(M(h))return"date";if(J(h))return"error";var S=R(h);switch(S){case"Symbol":case"Promise":case"WeakMap":case"WeakSet":case"Map":case"Set":return S}return b.slice(8,-1).toLowerCase().replace(/\s/g,"")}function R(h){return typeof h.constructor=="function"?h.constructor.name:null}function J(h){return h instanceof Error||typeof h.message=="string"&&h.constructor&&typeof h.constructor.stackTraceLimit=="number"}function M(h){return h instanceof Date?!0:typeof h.toDateString=="function"&&typeof h.getDate=="function"&&typeof h.setDate=="function"}function F(h){var b=typeof h;return b}function ie(h,b,S){var A;if(typeof b=="function"&&typeof S=="function"||typeof S=="function"&&typeof arguments[3]=="function")throw new Error(p(0));if(typeof b=="function"&&typeof S>"u"&&(S=b,b=void 0),typeof S<"u"){if(typeof S!="function")throw new Error(p(1));return S(ie)(h,b)}if(typeof h!="function")throw new Error(p(2));var K=h,d=b,j=[],T=j,X=!1;function g(){T===j&&(T=j.slice())}function L(){if(X)throw new Error(p(3));return d}function ne(fe){if(typeof fe!="function")throw new Error(p(4));if(X)throw new Error(p(5));var De=!0;return g(),T.push(fe),function(){if(De){if(X)throw new Error(p(6));De=!1,g();var ke=T.indexOf(fe);T.splice(ke,1),j=null}}}function ee(fe){if(!q(fe))throw new Error(p(7));if(typeof fe.type>"u")throw new Error(p(8));if(X)throw new Error(p(9));try{X=!0,d=K(d,fe)}finally{X=!1}for(var De=j=T,Ye=0;Ye<De.length;Ye++){var ke=De[Ye];ke()}return fe}function se(fe){if(typeof fe!="function")throw new Error(p(10));K=fe,ee({type:B.REPLACE})}function te(){var fe,De=ne;return fe={subscribe:function(ke){if(typeof ke!="object"||ke===null)throw new Error(p(11));function Re(){ke.next&&ke.next(L())}Re();var mt=De(Re);return{unsubscribe:mt}}},fe[N]=function(){return this},fe}return ee({type:B.INIT}),A={dispatch:ee,subscribe:ne,getState:L,replaceReducer:se},A[N]=te,A}var D=n.j!=15?ie:null;function pe(h){typeof console<"u"&&typeof console.error=="function"&&console.error(h);try{throw new Error(h)}catch(b){}}function H(h,b,S,A){var K=Object.keys(b),d=S&&S.type===B.INIT?"preloadedState argument passed to createStore":"previous state received by the reducer";if(K.length===0)return"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.";if(!q(h))return"The "+d+' has unexpected type of "'+F(h)+'". Expected argument to be an object with the following '+('keys: "'+K.join('", "')+'"');var j=Object.keys(h).filter(function(T){return!b.hasOwnProperty(T)&&!A[T]});if(j.forEach(function(T){A[T]=!0}),!(S&&S.type===B.REPLACE)&&j.length>0)return"Unexpected "+(j.length>1?"keys":"key")+" "+('"'+j.join('", "')+'" found in '+d+". ")+"Expected to find one of the known reducer keys instead: "+('"'+K.join('", "')+'". Unexpected keys will be ignored.')}function G(h){Object.keys(h).forEach(function(b){var S=h[b],A=S(void 0,{type:B.INIT});if(typeof A>"u")throw new Error(p(12));if(typeof S(void 0,{type:B.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(p(13))})}function I(h){for(var b=Object.keys(h),S={},A=0;A<b.length;A++){var K=b[A];typeof h[K]=="function"&&(S[K]=h[K])}var d=Object.keys(S),j,T;try{G(S)}catch(X){T=X}return function(g,L){if(g===void 0&&(g={}),T)throw T;if(0)var ne;for(var ee=!1,se={},te=0;te<d.length;te++){var fe=d[te],De=S[fe],Ye=g[fe],ke=De(Ye,L);if(typeof ke>"u"){var Re=L&&L.type;throw new Error(p(14))}se[fe]=ke,ee=ee||ke!==Ye}return ee=ee||d.length!==Object.keys(g).length,ee?se:g}}function v(h,b){return function(){return b(h.apply(this,arguments))}}function C(h,b){if(typeof h=="function")return v(h,b);if(typeof h!="object"||h===null)throw new Error(p(16));var S={};for(var A in h){var K=h[A];typeof K=="function"&&(S[A]=v(K,b))}return S}function f(){for(var h=arguments.length,b=new Array(h),S=0;S<h;S++)b[S]=arguments[S];return b.length===0?function(A){return A}:b.length===1?b[0]:b.reduce(function(A,K){return function(){return A(K.apply(void 0,arguments))}})}function y(){for(var h=arguments.length,b=new Array(h),S=0;S<h;S++)b[S]=arguments[S];return function(A){return function(){var K=A.apply(void 0,arguments),d=function(){throw new Error(p(15))},j={getState:K.getState,dispatch:function(){return d.apply(void 0,arguments)}},T=b.map(function(X){return X(j)});return d=f.apply(void 0,T)(K.dispatch),(0,s.A)((0,s.A)({},K),{},{dispatch:d})}}}},73597:(Se,ce,n)=>{var s=n(59316),p=n(84934),N=n(19631),u="['\u2019]",B=RegExp(u,"g");function q(V){return function(R){return s(N(p(R).replace(B,"")),V,"")}}Se.exports=q},74755:(Se,ce,n)=>{"use strict";n.d(ce,{n:()=>Pr});var s=n(38502),p=n(72274),N=n(63172),u=n(25942),B=n(1076),q=n(87229);const V={cid:"pslj5lho8lkbh1m4",project_cid:"pblj5lho3sfrgsvp",view_access:"restricted",access_token:"bNDBP6orwlnj5R57xaJ4y",password:"",view_count:0,view_prd:!0,screen_visible_switch:!1,screen_visible_list:[],wechat:!1,highlight:!0,sticky:!0,view_sticky:!0,comment_permission:"org_member",link_name:"\u5206\u4EAB",is_default_link:!1,creator_id:2735130,expire_type:"forever",device_model:"read_only",is_first_canvas_open:!1},R=null,J="123",M={projectBasicToken:"BcHc8Xdrrwlnj5pdDxPloA",projectShares:[{cid:"pslj5lho8lkbh1m4",project_cid:"pblj5lho3sfrgsvp",view_access:"restricted",access_token:"bNDBP6orwlnj5R57xaJ4y",password:"",view_count:0,view_prd:!0,screen_visible_switch:!1,screen_visible_list:[],wechat:!1,highlight:!0,sticky:!0,view_sticky:!0,comment_permission:"org_member",link_name:"\u5206\u4EAB",is_default_link:!1,creator_id:2735130,expire_type:"forever",device_model:"bbb"},{cid:"pslj5lho8lkbh1m4",project_cid:"pblj5lho3sfrgsvp",view_access:"restricted",access_token:"bNDBP6orwlnj5R57xaJ4y",password:"",view_count:0,view_prd:!0,screen_visible_switch:!1,screen_visible_list:[],wechat:!1,highlight:!0,sticky:!0,view_sticky:!0,comment_permission:"org_member",link_name:"\u5206\u4EAB",is_default_link:!1,creator_id:2735130,expire_type:"forever",device_model:"bbb"},{cid:"pslj5lho8lkbh1m4",project_cid:"pblj5lho3sfrgsvp",view_access:"restricted",access_token:"bNDBP6orwlnj5R57xaJ4y",password:"",view_count:0,view_prd:!0,screen_visible_switch:!1,screen_visible_list:[],wechat:!1,highlight:!0,sticky:!0,view_sticky:!0,comment_permission:"org_member",link_name:"\u5206\u4EAB",is_default_link:!1,creator_id:2735130,expire_type:"forever",device_model:"bbb"}]},F={owner_id:10,owner_name:"d9",owner_email:"<EMAIL>",owner_avatar:"/images/avatar.png",id:71,limitation:{storage:5e3,exportable:["png","pngs","htmlzip"],encryptable:!0,inspectable:!0,slices:!0,projects:65535,screens:65535,commentable:!0},screens_count:0,cid:"pb2lje6hu9dax5akx",team_cid:"teuaroot",space_cid:null,name:"sharing",type:"proto2",attr:{},created_at:1687863747e3,updated_at:1688043155e3,timestamp:"1688043155",access:"public",access_token:"32vJ5gBXrwws03fxYV0OK2",version:"v3",icon:null,splash:null,width:390,height:844,device:"iphone",model:"iphone_13_pro",scale:100,archived:!1,parent_cid:null,source_upper_cid:null,clones:0,shell_type:"device",password:"",wechat:!1,highlight:!0,preview_option:1,expired:!1,deleted:!1,duplicating:!1,permissions:[{user_id:10,role:"project_owner"}],is_org_project:!1,is_sub_project:!1,runner_mode:"preview",comment_permission:"org_member",tabs:null,visibility:"open",building:"view_sticky"},ie={owner_id:10,owner_name:"d9",owner_email:"<EMAIL>",owner_avatar:"/images/avatar.png",id:71,limitation:{storage:5e3,exportable:["png","pngs","htmlzip"],encryptable:!0,inspectable:!0,slices:!0,projects:65535,screens:65535,commentable:!0},screens_count:0,cid:"pb2lje6hu9dax5akx",team_cid:"teuaroot",space_cid:null,name:"sharing",type:"proto2",attr:{},created_at:1687863747e3,updated_at:1688043155e3,timestamp:"1688043155",access:"public",access_token:"32vJ5gBXrwws03fxYV0OK2",version:"v3",icon:null,splash:null,width:390,height:844,device:"iphone",model:"iphone_13_pro",scale:100,archived:!1,parent_cid:null,source_upper_cid:null,clones:0,shell_type:"device",password:"",wechat:!1,highlight:!0,preview_option:1,expired:!1,deleted:!1,duplicating:!1,permissions:[{user_id:10,role:"project_owner"}],is_org_project:!1,is_sub_project:!1,runner_mode:"preview",comment_permission:"org_member",tabs:null,visibility:"open",building:"view_sticky"};var D=n(54844),pe=n(54190),H=n(53008),G=n(5209),I=n(76844);let v=function(e){return e.acces="access",e.viewAccess="viewAccess",e.copy="copy",e.qrCode="qrCode",e.embed="Embed",e.setting="Setting",e.pwdProtection="PwdProtection",e.password="password",e.sticky="sticky",e.comment="comment",e.mobile="mobile",e}({});var C=function(e){return e.Edit="\u53EF\u7F16\u8F91",e.onlyView="\u4EC5\u67E5\u770B",e}(C||{}),f=function(e){return e.NoLogin="\u672A\u767B\u5F55\u7528\u6237",e.LoginButNotOrgMember="\u975E\u56E2\u961F\u767B\u5F55\u7528\u6237",e.ProjectMember="\u534F\u4F5C\u6210\u5458",e.TeamMember="\u56E2\u961F\u6210\u5458",e}(f||{}),y=function(e){return e.EditMode="\u7F16\u8F91\u6A21\u5F0F",e.ReadOnlyMode="\u53EA\u8BFB\u6A21\u5F0F",e.DeviceMode="\u771F\u673A\u6A21\u5F0F",e.InspectMode="\u6807\u6CE8\u6A21\u5F0F",e}(y||{});const h=e=>{let{userId:i,orgPermissions:l,spacePermissions:c,projectPermissions:w}=e;if(!i)return f.NoLogin;const _=(0,I.ox)(i,l),x=(0,I.ox)(i,c),k=(0,I.ox)(i,w);if(!_)return f.LoginButNotOrgMember;if(x||k)return f.ProjectMember;if(_)return f.TeamMember},b=e=>{let{userCanEdit:i,userId:l,orgPermissions:c,spacePermissions:w,projectPermissions:_,isEditMode:x,view_mode:k,isIframe:E}=e;const U=i?C.Edit:C.onlyView,Z=h({userId:l,orgPermissions:c,spacePermissions:w,projectPermissions:_}),$=E?"\u5DE5\u4F5C\u53F0":x?y.EditMode:k==="read_only"?y.ReadOnlyMode:k==="device"?y.DeviceMode:k==="inspect"?y.InspectMode:"";return{user_rights:U,user_type:Z,operation_from:$}},S=e=>{let{optionType:i,userCanEdit:l,userId:c,orgPermissions:w,spacePermissions:_,projectPermissions:x,isEditMode:k,view_mode:E,share_type:U,isIframe:Z,project_name:$,project_cid:O}=e;const{user_rights:ae,user_type:oe,operation_from:P}=b({userCanEdit:l,userId:c,orgPermissions:w,spacePermissions:_,projectPermissions:x,isEditMode:k,view_mode:E,isIframe:Z});(0,G.kH)("share_function_click_V8",{operation_type:i,user_rights:ae,user_type:oe,operation_from:P,project_name:$,project_cid:O,share_type:U})},A=e=>{(0,G.kH)("advanced_sharing_click",e)},d=(()=>{const e={project:void 0,flatKey:void 0,theme:"light",user:void 0,org:void 0,settingPageType:"create",subSettingPageType:"basic",currentSharing:V,hostType:"proto",topPageIndex:"edit",advancedSharingList:[],canEditByUser:!1,mainPage:"share",tabIndex:0,projShareToEdit:"",members:[],isEditMode:location.pathname.includes("/design/"),screenMetaList:void 0,initData:{},loading:!0,hostSharingData:void 0,sharingToast:"",currentSelectSharing:void 0,isOnlyMemberManager:!1,hostCurrentScreen:void 0},i=function(_,x){_===void 0&&(_=e);let{type:k="sharing:state:update",payload:E=e}=x;switch(k){case"sharing:state:update":return{..._,...E};case"sharing:state:clear":return{...e};default:return _}},l={"sharing:init":(_,x)=>{let{dispatch:k}=_,{payload:E}=x;k({type:"sharing:state:update",payload:E})},"sharing:currentSharing:update":(_,x)=>{let{getState:k,dispatch:E}=_,{payload:{updatedKV:U}}=x;const Z=k(),O={...c.getCurrentSharing(Z),...U};E({type:"sharing:state:update",payload:{currentSharing:O}})},"sharing:remote:sharing:update":async(_,x)=>{let{getState:k,dispatch:E}=_,{payload:{sharingData:U,updatedKV:Z}}=x;const $=k(),O=c.getOrg($),ae=c.getProject($);let oe;if(U.type==="default"){var P,re;oe=async()=>(0,pe.Yu)(ae.cid,{...Z}),await(0,H.eH)({updateType:"update",updateFn:oe,org:O});const le={...ae,...Z};E({type:"sharing:state:update",payload:{project:le}}),(P=MB)!=null&&P.action&&((re=MB)==null||re.action("current:update:state",{currentProject:le}))}else{const le=c.getAdvancedSharingList($),ge={...U,...Z};oe=async()=>(0,D.yq)({sharing:ge});const{expired_at:he}=await(0,H.eH)({updateType:"update",updateFn:oe,org:O,updatedKV:Z});E({type:w["sharing:currentSharing:update"],payload:{updatedKV:{expired_at:he}}});const ye=le.map(W=>W.cid===ge.cid?{...ge,expired_at:he}:W);E({type:w["sharing:advancedSharingList:update"],payload:{sharingList:ye}})}},"sharing:remote:sharing:delete":async(_,x)=>{let{getState:k,dispatch:E}=_,{payload:{sharingCid:U}}=x;const Z=k(),$=c.getProject(Z),O=c.getAdvancedSharingList(Z);await(0,D.g)({projectCid:$.cid,sharingCid:U});const ae=O.filter(oe=>oe.cid!==U);E({type:w["sharing:advancedSharingList:update"],payload:{sharingList:ae}})},"sharing:settingPage:advanced:confirm":async(_,x)=>{let{getState:k,dispatch:E}=_,{payload:{updatedKV:U,isCustom:Z}}=x;const $=k(),O=c.getSettingPageType($),ae=c.getCurrentSharing($),oe=c.getOrg($),P=c.getProject($);if(O==="create"){const re=async()=>(0,D.Or)({projectCid:P.cid,sharing:ae});await(0,H.eH)({updateType:"create",updateFn:re,org:oe}),E({type:w["sharing:topPageIndex:jump"],payload:{topPageIndex:"edit"}})}else{const re=async()=>(0,D.yq)({sharing:ae}),{expired_at:le}=await(0,H.eH)({updateType:"update",updateFn:re,org:oe,updatedKV:U,isCustom:Z});E({type:w["sharing:currentSharing:update"],payload:{updatedKV:{expired_at:le}}})}E({type:"sharing:advance:click:track"})},"sharing:settingPage:default:confirm":async _=>{var x,k;let{getState:E,dispatch:U}=_;const Z=E(),$=c.getCurrentSharing(Z),O=c.getProject(Z),ae=c.getOrg(Z),oe=async()=>(0,pe.Yu)(O.cid,Object.assign({},$,{building:$.view_sticky}));await(0,H.eH)({updateType:"update",updateFn:oe,org:ae});const P=Object.assign({},O,$,{building:$.view_sticky});U({type:"sharing:state:update",payload:{project:P}}),(x=MB)!=null&&x.action&&((k=MB)==null||k.action("current:update:state",{currentProject:P}))},"sharing:go-settingPage":(_,x)=>{let{dispatch:k}=_,{payload:{currentSharing:E,settingPageType:U,subSettingPageType:Z=e.subSettingPageType}}=x;k({type:"sharing:state:update",payload:{currentSharing:E,settingPageType:U,topPageIndex:"setting",subSettingPageType:Z}})},"sharing:topPageIndex:jump":(_,x)=>{let{dispatch:k}=_,{payload:{topPageIndex:E=e.topPageIndex}}=x;k({type:"sharing:state:update",payload:{topPageIndex:E}})},"sharing:advancedSharingList:update":(_,x)=>{let{dispatch:k}=_,{payload:{sharingList:E}}=x;k({type:"sharing:state:update",payload:{advancedSharingList:E}})},"sharing:projectAccess:init":async _=>{let{dispatch:x,getState:k}=_;const E=k(),U=c.getMembers(E),Z=c.getProject(E),$=Z==null?void 0:Z.cid,O=Z==null?void 0:Z.is_org_project;if(!$||!O){setTimeout(()=>{x({type:d.entryKey["sharing:init"],payload:{loading:!1}})},100);return}const{result:ae,statusOk:oe}=await(0,D.aX)($);if(x({type:d.entryKey["sharing:init"],payload:{loading:!1}}),!!oe&&ae){var P,re;const{project:le,org:ge}=ae;await x({type:"entry:projectAccess:initProjectData",payload:{initData:ae,members:U}}),x({type:d.entryKey["sharing:init"],payload:{project:le,org:ge,initData:ae}}),(P=MB)!=null&&P.action&&((re=MB)==null||re.action("current:update:state",{currentProject:le}))}},"sharing:function:track":(_,x)=>{let{getState:k}=_,{payload:{operation:E,viewMode:U,isDefault:Z}}=x;const $=k(),O=c.getProject($)||{},ae=c.getCanEditByUser($),oe=c.getOrg($)||{},P=c.getUser($),re=c.getInitData($)||{},le=(re==null?void 0:re.space)||{},ge=c.getIsEditMode($),he=c.getHostType($),{cid:ye,name:W}=O,xe={userCanEdit:ae,userId:P==null?void 0:P.id,orgPermissions:oe==null?void 0:oe.permissions,spacePermissions:le==null?void 0:le.permissions,projectPermissions:O==null?void 0:O.permissions,isEditMode:ge,view_mode:U,optionType:E,share_type:Z?"\u9ED8\u8BA4\u5206\u4EAB":"\u9AD8\u7EA7\u5206\u4EAB",isIframe:he==="iframe",project_name:W,project_cid:ye};S(xe)},"sharing:advance:click:track":(_,x)=>{let{getState:k}=_,{}=x;const E=k(),U=c.getSettingPageType(E),Z=c.getCurrentSharing(E),$=c.getProject(E),O=c.getAdvancedSharingList(E)||[],ae=c.getScreenMetaList(E),oe=c.getHostType(E),P=(0,H.RF)(ae.pageAttrMap),re=(0,H.Mj)(!Z.screen_visible_switch,Z.screen_visible_list,P);let le="\u5168\u90E8";Z.screen_visible_switch&&(le=""+re);const ge=O.length;A({link_name:Z.link_name,links_number:U==="create"?ge+1:ge,share_type:Z.device_model==="device"?"\u6F14\u793A\u6A21\u5F0F":"\u753B\u5E03\u6A21\u5F0F",page_number:le,operation_type:U==="create"?"\u65B0\u5EFA":"\u7F16\u8F91",source:oe==="iframe"?"\u5DE5\u4F5C\u53F0":"\u7F16\u8F91\u533A",project_name:$.name,project_cid:$.cid})}},c=q.B.genQuery(e,{getStateFn:_=>_.sharing}),w=q.B.genEntryKey(l);return{__initialState:e,__reducer:i,__entry:l,entryKey:w,query:c}})(),j=(0,p.HY)({sharing:d.__reducer,projectAccess:B.Ay}),T={...d.__entry,...N.s},X=()=>{const e=(0,u.RZ)(),i=(0,p.y$)(j,(0,p.Tw)(e.middleware));return e.setEntryMap(T),i};var g=n(18381),L=n(67787),ne=n(82738),ee=n(60690),se=n(53732),te=n.n(se),fe=n(83414);const De=L.Ay.div.withConfig({displayName:"styles__StyledSharingToolBar",componentId:"sc-1k4rs1h-0"})(["display:flex;align-items:center;justify-content:space-between;height:","px;padding-right:12px;border-bottom:1px solid ",";&.tool-upgrade{padding-right:24px;}.sharing-title{margin-left:24px;color:",";font-size:14px;font-family:PingFang SC;font-style:normal;font-weight:500;line-height:24px;}.upgrade-team-business{padding:0 10px;height:26px;border-radius:13px;display:flex;align-items:center;justify-content:center;background:",";font-size:14px;font-weight:500;position:relative;cursor:pointer;z-index:1;&:hover{background:",";}.team-business{background:linear-gradient(265.2deg,"," -21.19%,"," 105.08%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;text-fill-color:transparent;flex:none;order:0;flex-grow:0;}.content{font-size:12px;font-family:PingFang SC;font-style:normal;font-weight:400;line-height:18px;position:absolute;background:#454647;top:36px;right:0;width:300px;padding:12px 16px;border-radius:6px;.learn-text{margin-left:8px;color:#80BCFF;&:hover{color:#A6D2FF;}}&::before{content:'';position:absolute;top:0px;right:30px;transform:translateY(-100%);display:inline-block;width:0;height:0;border:6px solid transparent;border-bottom:6px solid #454647;}.top-bar{position:absolute;width:100%;height:10px;top:-10px;right:0;}span{color:",";}a{cursor:pointer;color:",";&:hover{color:",";}}}}"],fe.gE,e=>e.theme.color_bg_border_02,e=>e.theme.color_text_L1,e=>e.theme.color_background_AI_head_tag,e=>e.theme.color_background_AI_head_tag_hover,e=>e.theme.color_AI_switch_gradient_start,e=>e.theme.color_AI_switch_gradient_end,e=>e.theme.color_text_btn,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_hover),Ye=L.Ay.div.withConfig({displayName:"styles__StyledShareHeadMembers",componentId:"sc-1d78taz-0"})(["height:36px;border-radius:18px;display:flex;align-items:center;padding:0 4px;cursor:pointer;&:hover{background:",";}.avatar{height:24px;width:24px;border-radius:12px;box-sizing:border-box;border:1px solid ",";&:not(:first-child){margin-left:-6px;}&.avater-name{text-align:center;background:rgb(22,133,252);justify-content:center;color:white;font-size:12px;text-align:center;display:flex;align-items:center;}}.item-line{margin-left:6px;width:1px;height:16px;background:",";}.remaining{box-sizing:border-box;border-radius:12px;border:1px solid ",";background:rgb(22,133,252);justify-content:center;display:flex;align-items:center;margin-left:-6px;width:24px;height:24px;svg{width:24px;height:24px;}}.arror{margin-left:6px;width:14px;height:14px;}"],e=>e.theme.color_btn_secondary_active,e=>e.theme.color_share_member__border_color,e=>e.theme.color_bg_border_02,e=>e.theme.color_share_member__border_color);var ke=n(12603),Re=n(17307);const mt="__mb_delete_permission",Pe=[{value:"org_owner",label:"permissions_org_owner"},{value:"org_manager",label:"permissions_org_super_administrator"},{value:"org_admin",label:"permissions_org_administrator"},{value:"org_member",label:"permissions_org_collaboration_member"},{value:"org_viewer",label:"permissions_org_review_member"}],Q=[{value:"space_manager",label:"permissions_space_manager"},{value:"space_member",label:"permissions_space_collaboration_member"},{value:"space_viewer",label:"permissions_space_review_member"},{value:"space_limiter",label:"permissions_space_unregistered_member"}],Y=[{value:"team_manager",label:"permissions_manager"},{value:"team_member",label:"permissions_can_edit"},{value:"team_viewer",label:"permissions_only_view"},{value:"team_ban_viewer",label:"permissions_ban_view"}],me=[{value:"project_manager",label:"permissions_manager"},{value:"project_member",label:"permissions_can_edit"},{value:"project_viewer",label:"permissions_only_view"},{value:"project_ban_viewer",label:"permissions_ban_view"}],Ne={space_owner:"project_manager",space_manager:"project_manager",space_member:"project_member",space_viewer:"project_viewer",team_owner:"project_manager",team_manager:"project_manager",team_member:"project_member",team_viewer:"project_viewer",team_ban_viewer:"project_ban_viewer",team_none:"project_viewer",project_owner:"project_manager"},je={space_owner:"team_manager",space_manager:"team_manager",space_member:"team_member",space_viewer:"team_viewer",space_ban_viewer:"team_ban_viewer",team_none:"team_viewer",team_owner:"team_manager"},Ge=Object.fromEntries([...Pe,...Q,...Y,...me].map(e=>[e.value,e.label]));var Me=n(19481),Ie=n(29152),Je=n(50159);const nt=e=>{const i=(0,g.d4)(Ie.XW),l=i.get(e);return(0,Je.u)(l,i)},yt=(e,i)=>{const l=(0,g.d4)(Ie.XW),c=(0,g.d4)(Ie._B),w=(0,g.d4)(Ie.kG),_=(0,g.d4)(Ie.iF),x=(0,g.d4)(Ie.yZ);let k=null,E=null;const U=e;let Z=[],$=[],O=[];k=c.get(i),$=k.permissions,O=(0,I.Cj)(k.team_cid,l,_),E=ke.W.Org,Z=w,O=Object.values(Object.fromEntries(O.map(le=>[le.user_id,le])));const ae=Object.values(Object.fromEntries([...O,...$].map(le=>[le.user_id,le]))),oe=(0,s.useMemo)(()=>{const le=(0,I.Rc)(Z);return Z.reduce((ge,he)=>{const{user_id:ye}=he,W=(0,I.CF)({userId:ye,permissionsMap:le,permissionScope:E});return ge.set(Number(ye),W)},new Map)},[Z]),P=(0,s.useMemo)(()=>{const le=(0,I.Rc)(ae);return Z.reduce((ge,he)=>{const{user_id:ye}=he,W=(0,I.CF)({userId:ye,permissionsMap:le,permissionScope:U});return ge.set(Number(ye),W)},new Map)},[ae,Z]),re=(0,s.useMemo)(()=>{const le=(0,I.Rc)(O);return O.reduce((ge,he)=>{const{user_id:ye}=he,W=(0,I.CF)({userId:ye,permissionsMap:le,permissionScope:U});return ge.set(Number(ye),W)},new Map)},[O]);return{userCurrentSpacePermission:x,membersTopScopePermissonMap:oe,membersCurrentScopePermissonMap:P,membersInheritedCurrentScopePermissonMap:re,topScopePermissionMembers:Z,currentScopePermissionMembers:$,inheritedPermissionMembers:O,combinedCurrentPermissionMembers:ae}},xt=(e,i,l)=>{const c=(0,g.wA)();return(0,s.useCallback)(async(_,x)=>{const{name:k,email:E,mobile:U,permissionMap:Z}=_,{memberCurrentScopePermisson:$,currentScopeRoleName:O}=Z;if(!$)return;const ae=k||E||U;let oe=!1;x===mt&&(oe=!0),$.isUnknown?await c({type:"entry:projectAccess:inviteOrgMemberToProject",payload:{projectCid:i,userCid:_.user_cid,roleName:x,userName:ae}}):oe?await c({type:"entry:projectAccess:removeProjectMemberPermission",payload:{projectCid:i,userId:_.user_id,roleName:O,userName:ae}}):await c({type:"entry:projectAccess:updateMemberProjectPermission",payload:{projectCid:i,userId:_.user_id,roleName:x,userName:ae}})},[i,c])},bt=(e,i)=>{const l=(0,g.d4)(Ie.WR),c=(0,g.d4)(Ie.q7),w=(0,g.d4)(Ie.oV),_=(0,g.d4)(Ie.hG),{userCurrentSpacePermission:x,membersTopScopePermissonMap:k,membersCurrentScopePermissonMap:E,membersInheritedCurrentScopePermissonMap:U,topScopePermissionMembers:Z,inheritedPermissionMembers:$,currentScopePermissionMembers:O,combinedCurrentPermissionMembers:ae}=yt(e,i),oe=E.get(Number(l))||I.CZ,P=!!_.get(Number(l)),re=(0,s.useMemo)(()=>{const be=new Map;return Z.forEach(Oe=>{let{user_id:Le,role:Ze}=Oe;const We=k.get(Number(Le))||I.CZ,Qe=w.get(Number(Le))||I.CZ,ot=E.get(Number(Le))||I.CZ,at=U.get(Number(Le))||I.CZ,lt=!!_.get(Number(Le)),St=c.get(Number(Le));if(!St)return;const Et=St.permission,Lt=ot.isUnknown?Ze:ot.roleName;be.set(Number(Le),{...St,permissionMap:{currentScopeRoleName:Lt,userCurrentSpacePermission:x,userCurrentScopePermisson:oe,memberTopScopePermisson:We,memberCurrentScopePermisson:ot,memberInheritedPermisson:at,memberOrgPermisson:Et,isSelf:Number(Le)===Number(l),userIsSpaceLimiter:P,memberIsSpaceLimiter:lt,memberIsSpaceManager:Qe.isManager}})}),be},[Z,k,E,U,_]),le=(0,s.useMemo)(()=>O.sort((be,Oe)=>Me.Z8[Oe.role]-Me.Z8[be.role]).map(be=>re.get(Number(be.user_id))).filter(be=>be),[O,re]),ge=(0,s.useMemo)(()=>$.sort((be,Oe)=>Me.Z8[Oe.role]-Me.Z8[be.role]).map(be=>{const{user_id:Oe,role:Le}=be,Ze=U.get(Number(Oe))||I.CZ,We=re.get(Number(be.user_id));return We?{...We,permissionMap:{...We.permissionMap,currentScopeRoleName:Le,memberCurrentScopePermisson:Ze}}:null}).filter(be=>be),[$,re,U]),he=(0,s.useMemo)(()=>ae.sort((be,Oe)=>Me.Z8[Oe.role]-Me.Z8[be.role]).map(be=>re.get(Number(be.user_id))).filter(be=>be),[ae,re]),ye=(0,s.useMemo)(()=>{const be=he.map(Le=>Number(Le.user_id)),Oe=[];for(const[Le,Ze]of re.entries())be.includes(Number(Le))||Ze&&Oe.push(Ze);return Oe},[re,he]),W=(0,s.useMemo)(()=>{const be=ge.filter(Le=>{var Ze;return(Ze=Le.permissionMap)==null||(Ze=Ze.memberCurrentScopePermisson)==null?void 0:Ze.isManager});return Object.values(Object.fromEntries([...be,...le].map(Le=>[Le.user_id,Le])))},[ge,le]),xe=(0,s.useMemo)(()=>{const be=W.map(Le=>Number(Le.user_id)),Oe=[];for(const[Le,Ze]of re.entries())be.includes(Number(Le))||Ze&&Oe.push(Ze);return Oe},[re,W]);return{isOnlyOneManager:(0,s.useMemo)(()=>he.filter(be=>{var Oe;return(Oe=be.permissionMap)==null||(Oe=Oe.memberCurrentScopePermisson)==null?void 0:Oe.isManager}).length===1,[he]),userCurrentScopePermisson:oe,userCurrentSpacePermission:x,currentMembers:le,inheritedMembers:ge,joinedMembers:he,unjoinedMembers:ye,assignedMembers:W,unassignedMembers:xe,currentOrgMembersMap:c}};var wt=n(83199),r=n(72214);const ue=()=>{var e;const i=(0,g.d4)(d.query.getUser),l=(0,g.d4)(d.query.getProject),c=(0,g.d4)(d.query.getInitData),w=nt(l==null?void 0:l.team_cid),_=l==null?void 0:l.cid,x=(0,g.wA)(),k=(0,s.useCallback)(P=>{P.stopPropagation(),x({type:d.entryKey["sharing:init"],payload:{mainPage:"access"}}),x({type:d.entryKey["sharing:function:track"],payload:{operation:"\u56E2\u961F\u534F\u4F5C"}})},[x]),{currentMembers:E,inheritedMembers:U,joinedMembers:Z}=bt(ke.W.Project,_);let $=E;w>=1?U.forEach(P=>{$.find(le=>le.id===P.id)||$.push(P)}):$=Z;const ae=(e=$)==null?void 0:e.filter(P=>Number(P.user_id)!==Number(i==null?void 0:i.id));ae&&($=ae.slice(0,2));const oe=(0,s.useCallback)(P=>{var re;const{avatar:le,name:ge,id:he}=P;return le&&!le.includes("/images/avatar.png")?(0,r.jsx)("img",{className:"avatar",src:P.avatar,alt:P.name},he):(0,r.jsx)("div",{className:"avatar avater-name",children:ge==null||(re=ge.slice(0,1))==null?void 0:re.toUpperCase()},he)},[]);return!c||JSON.stringify(c)==="{}"?null:(0,r.jsx)(wt.A,{content:I18N.imockSharing.team_collaborator,direction:"down",distance:5,enterHoverTime:500,children:(0,r.jsxs)(Ye,{onClick:k,children:[oe(i),$.length>0&&$.map(P=>oe(P)),(0,r.jsx)("div",{className:"remaining",children:(0,r.jsx)(Re.C,{name:"sharing/add_member",isColorPure:!0})}),(0,r.jsx)(Re.C,{name:"sharing/member_arror",isColorPure:!0,className:"arror"})]})})},ve=(0,s.memo)(ue),Te=L.Ay.div.withConfig({displayName:"styles__StyledSharingSliderBar",componentId:"sc-1garbfp-0"})(["display:flex;height:100%;align-items:center;position:relative;padding-left:24px;.tab-item{color:",";font-size:14px;font-family:PingFang SC;cursor:pointer;height:100%;display:flex;align-items:center;&:not(:first-child){margin-left:14px;}&.tab-sel{color:",";font-weight:500;border-top:2px solid rgba(0,0,0,0);border-bottom:2px solid ",";box-sizing:border-box;}}"],e=>e.theme.color_share_switch_sel_color,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1),He=e=>{let{tabs:i,handleChangeTabIndex:l,tabIndex:c,className:w}=e;return(0,r.jsx)(Te,{className:w,children:i.map((_,x)=>(0,r.jsx)("div",{className:te()("tab-item",c===x&&"tab-sel"),id:"tab-item-"+x,onClick:()=>l(x),children:_},x))})},qe=(0,s.memo)(He);var dt=n(37342),It=n(84957);const Bt=()=>{let e=null;const i=(0,g.wA)(),l=(0,g.d4)(d.query.getProject),c=(0,g.d4)(d.query.getCanEditByUser),w=(0,g.d4)(d.query.getHostType),_=(0,g.d4)(d.query.getIsEditMode),x=(0,g.d4)(d.query.getInitData),k=(0,g.d4)(d.query.getTabIndex),E=(0,g.d4)(d.query.getUser),[U,Z]=(0,s.useState)(!1),$=(l==null?void 0:l.is_org_project)||!1,O=w==="iframe"||_,oe=new URLSearchParams(location.search).get("view_mode")||"read_only",P=O&&$&&x&&JSON.stringify(x)!=="{}",re=c&&!$,le=(0,s.useCallback)(()=>{const W=w==="iframe",xe=(W?"\u5DE5\u4F5C\u533A":"\u7F16\u8F91\u533A")+"-v8_\u4E2A\u4EBA_\u5206\u4EAB_\u56E2\u961F\u534F\u4F5C\u5347\u7EA7",_e=(W?"dashboard":"workspace")+"-v8_solo_share_upgrade",be={payEntrance:xe,checkoutPlace:_e,checkoutArea:W?"dashboard":"proto",mode:"org",isSelectOrg:!0};if(W){if(!E||!E.solo_org)return;const Oe="/workspace/"+E.solo_org.cid+"/admin/order?payment_param="+(0,It._)(be);(0,dt.JW)(Oe,"_blank","noreferrer")}else MB.global.popupHelper.chargeAsync(be)},[]),ge=()=>{e=setTimeout(()=>{Z(!0)},1e3)},he=()=>{e&&clearTimeout(e),Z(!1)},ye=(0,s.useCallback)(W=>{i({type:d.entryKey["sharing:init"],payload:{tabIndex:W}})},[i]);return(0,r.jsx)(De,{className:te()(re&&"tool-upgrade"),children:O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(qe,{tabIndex:k,tabs:[I18N.imockSharing.default_share,I18N.imockSharing.advanced_sharing],handleChangeTabIndex:ye}),re&&(0,r.jsxs)("div",{className:"upgrade-team-business",onClick:le,onMouseEnter:ge,onMouseLeave:he,children:[(0,r.jsx)("span",{className:"team-business",children:I18N.imockSharing.team_collaboration}),U&&(0,r.jsxs)("div",{className:"content",onClick:W=>W.stopPropagation(),children:[(0,r.jsx)("div",{className:"top-bar"}),(0,r.jsx)("span",{children:I18N.imockSharing.upgrade_to_team_business_plan}),(0,r.jsx)("a",{className:"learn-text",href:I18N.link.link_upgrade_enterprise,target:"_blank",rel:"noopener noreferrer",children:I18N.imockSharing.learn_more})]})]}),P&&(0,r.jsx)(ve,{})]}):(0,r.jsx)("div",{className:"sharing-title",children:oe==="read_only"?I18N.pPreviewToolbar.share:I18N.pPreviewToolbar.share_preview})})},Vt=(0,s.memo)(Bt),$t=L.Ay.div.withConfig({displayName:"styles__StyledSharingAdvancedPage",componentId:"sc-1h5adoi-0"})(["width:100%;height:calc(100% - ","px);padding:16px 20px 5px;overflow-y:auto;.create-item{display:flex;align-items:center;padding:6px 12px;height:32px;color:",";text-align:center;font-size:14px;font-family:PingFang SC;width:fit-content;border-radius:6px;cursor:pointer;svg{width:12px;margin-right:4px;svg path{fill:",";}}&:not(.no-edit):hover{background:",";}&:not(.no-edit):active{background:",";}&.no-edit{opacity:0.3;pointer-events:none;}}"],fe.gE,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_btn_secondary_active),Rn=L.Ay.div.withConfig({displayName:"styles__StyledSharingEmpty",componentId:"sc-1h5adoi-1"})(["width:100%;height:calc(100% - ","px);display:flex;justify-content:center;flex-direction:column;align-items:center;svg{width:254px;}.create-sharing{width:134px;margin-top:40px;}"],fe.gE);var un=n(25234),Nn=n(21066),pn=n(51090);const In=L.Ay.div.withConfig({displayName:"styles__StyledSharingNormalItem",componentId:"sc-11k1y99-0"})(["margin-top:12px;padding:0px 13px;height:134px;display:flex;flex-direction:column;justify-content:center;background:",";border-radius:8px;.share-icon{width:24px;height:24px;border-radius:4px;margin-left:6px;cursor:pointer;&.tempDisabled{pointer-events:none;}svg{width:100%;height:100%;}}"],e=>e.theme.color_bg_canvas),mn=L.Ay.div.withConfig({displayName:"styles__StyledCenterItem",componentId:"sc-11k1y99-1"})(["display:flex;min-height:36px;width:100%;margin-top:12px;margin-bottom:16px;&.default-link{margin-bottom:0px;}&.advance-item{margin-bottom:12px;}&.userNoEdit{.left.normal-hover{cursor:not-allowed;.view-access-select{svg,path{fill:",";}}}}&.notView{cursor:not-allowed;.url-copy-button{background:",";border:"," solid 1px;color:",";pointer-events:none;}}.left{flex-grow:1;background-color:",";display:flex;align-items:center;border:"," solid 1px;border-right:none;border-radius:6px 0 0 6px;&.can-not-edit{pointer-events:none;}&.normal-hover{cursor:pointer;&:hover{background-color:",";}}.chore{margin-right:21px;color:",";}.view-access-select{margin-left:10px;display:flex;align-items:center;.access-name{color:",";}&.is-expired{","}.svg-icon{color:",";transition:all 0.2s;transform :",";}}.single-preview{width:100%;padding:0 6px;color:",";}}.url-copy-button{flex-grow:0;width:97px;}"],e=>e.theme.color_share_access__button_disable,e=>e.theme.color_share_copy__button_disable_bg,e=>e.theme.color_bg_border_02,e=>e.theme.color_share_copy__button_disable,e=>e.isViewActiveDropdown?e.theme.color_btn_secondary_hover:e.theme.color_bg_white,e=>e.theme.color_bg_border_02,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_text_L3,e=>e.theme.color_text_L1,e=>e.theme.color_text_L3,e=>e.theme.color_text_L1,e=>e.isViewActiveDropdown?"rotate(-180deg)":"",e=>e.theme.color_text_L1),kn=(0,L.Ay)(Nn.Ay).withConfig({displayName:"styles__StyledMenuContent",componentId:"sc-11k1y99-2"})(["ul{width:270px;li > a{padding-left:10px;svg{position:absolute;right:8px;}}}",";&.advance_menu ul{width:244px;}&.misc ul{width:80px;min-width:auto;}"],pn.ZJ),tn=L.Ay.button.withConfig({displayName:"styles__StyledCopyButton",componentId:"sc-11k1y99-3"})(["display:flex;position:relative;width:82px;font-size:12px;font-weight:500;border-top-right-radius:6px;border-bottom-right-radius:6px;background-color:",";&:hover{background-color:",";}&:active{background-color:",";}color:",";&.is-disabled{cursor:not-allowed;pointer-events:none;color:",";background-color:",";}.state{position:absolute;left:0;top:0;display:flex;justify-content:center;align-items:center;width:100%;height:100%;transition:all 0.15s ease-out;}.state-1{opacity:0;.copy-check{color:#ffffff;width:24px;}}&.is-state-1 .state-0{opacity:0;transform:translateY(50%);}&.is-state-1 .state-1{opacity:1;transform:none;}"],e=>e.theme.color_btn_primary_normal,e=>e.theme.color_btn_primary_hover,e=>e.theme.color_btn_primary_clicked,e=>e.theme.color_text_btn,e=>e.theme.color_text_disabled02,e=>e.theme.color_btn_primary_disabled),hn=L.Ay.div.withConfig({displayName:"styles__StyledSharingItemHead",componentId:"sc-1tpfzbt-0"})(["display:flex;align-items:center;justify-content:space-between;color:",";font-size:12px;font-family:PingFang SC;.head-left{display:flex;font-size:14px;font-weight:500;align-items:center;color:",";.public-access{margin-right:8px;}.link-form{width:176px;input{width:176px;}}.link-name{width:176px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;cursor:text;&.link-name-not-edit{pointer-events:none;}&:not(.link-name-not-edit):hover{border-bottom:1px solid ",";}position:relative;height:24px;line-height:24px;}input{height:24px;line-height:24px;color:",";width:240px;border-bottom:1px solid ",";}.edit-name{width:20px;height:20px;margin-left:5px;}}.head-right{display:flex;justify-content:center;align-items:center;border-radius:4px;padding-left:4px;&:hover{background-color:",";}}.foot-right{display:flex;align-items:center;}.view-mode-icon{width:16px;height:16px;margin-right:6px;[stroke]{stroke:currentColor;fill:none;}}.share-icon{margin-left:5px;}&.canNotEdit{label{pointer-events:none;opacity:0.3;cursor:not-allowed;}}"],e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_proto,e=>e.theme.color_text_L1,e=>e.theme.color_proto,e=>e.theme.color_btn_secondary_active);function Gs(e){const{fBlur:i,name:l}=e,[c,w]=(0,s.useState)(!1),[_,x]=(0,s.useState)(l),k=(0,s.useRef)(null);(0,s.useEffect)(()=>{if(c){var O;(O=k.current)==null||O.focus()}},[c]);const E=()=>{i(),w(!1)};return{inputRef:k,isRenaming:c,setIsRenaming:w,inputName:_,setInputName:x,handleFocus:()=>{var O;(O=k.current)==null||O.select()},handleBlur:E,handleKeyDown:O=>{O.key==="Enter"&&E()},handleInput:O=>{let ae=O.target.value;ae.length>200&&(ae=ae.substring(0,200)),x(ae)}}}const Dt={embed:"embed",QRcode:"QRcode",setting:"setting",mkt:"mkt_icon",showPage:"showPage"},Ys=L.Ay.div.withConfig({displayName:"styles__StyledIconBox",componentId:"sc-1juwfby-0"})(["display:flex;justify-content:center;align-items:center;width:20px;height:20px;color:",";cursor:pointer;border-radius:4px;position:relative;transition:background-color 0.2s ease-in-out;.Tooltip{display:flex;}&.tempDisabled{opacity:0.3;}&.isBorder{border-radius:50%;border:1px solid ",";}&.disabled{cursor:not-allowed;color:",";}&:not(.disabled):hover{background:",";}&:not(.disabled):active{background:",";}"],e=>e.theme.color_text_L2,e=>e.theme.color_bg_border_02,e=>e.theme.color_text_disabled01,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_btn_secondary_active);function _n(e){const{className:i,size:l=20,name:c,onClick:w,onMouseEnter:_,onMouseLeave:x,children:k,tempDisabled:E,direction:U,toolTipName:Z,type:$,isColorPure:O,isBorder:ae}=e,oe=P=>{E||w(P,$)};return(0,r.jsx)(wt.A,{content:Z,direction:U||"up",distance:5,enterHoverTime:500,children:(0,r.jsxs)(Ys,{className:te()(i,E&&"tempDisabled",ae&&"isBorder"),onClick:oe,onMouseEnter:_,onMouseLeave:x,children:[(0,r.jsx)(Re.C,{size:l,name:c,isColorPure:O}),k]})})}const ys=e=>{let{sharingData:i,handleItemAction:l}=e;const{link_name:c}=i,w=(0,g.wA)(),_=i.device_model,[x,k]=(0,s.useState)(c),E=(0,g.d4)(d.query.getCanEditByUser),U=(0,g.d4)(d.query.getIsEditMode),Z=(0,g.d4)(d.query.getHostType),$=(0,g.d4)(d.query.getScreenMetaList),O=(0,g.d4)(d.query.getAdvancedSharingList),ae=Z==="iframe"||U,oe=ae&&E,P=ae&&E,re=()=>{const Qe=_e.current.value;if(Qe==="")Oe(x),w({type:d.entryKey["sharing:init"],payload:{sharingToast:I18N.Common.name_cannot_be_empty}});else if((0,H.it)({value:Qe,currentSharing:i,sharingList:O}))Oe(x),w({type:d.entryKey["sharing:init"],payload:{sharingToast:I18N.imockSharing.sharing_name_repeate_wran}});else{k(Qe);const ot={link_name:Qe};w({type:d.entryKey["sharing:remote:sharing:update"],payload:{sharingData:i,updatedKV:ot}})}},le=(0,s.useMemo)(()=>i.type==="advanced"?(0,H.Sy)($,i):null,[i,$]),{isRenaming:ge,setIsRenaming:he,handleKeyDown:ye,handleBlur:W,handleFocus:xe,inputRef:_e,handleInput:be,setInputName:Oe,inputName:Le}=Gs({name:x||"",fBlur:re}),Ze=(0,s.useCallback)(()=>{he(!0)},[he]),We=(0,s.useMemo)(()=>[{name:"sharing/embed_icon",toolTipName:I18N.imockSharing.insert,className:Dt.embed,tempDisabled:!1},ENV.IS_ON_PREMISES?null:{name:"sharing/QRcode_icon",toolTipName:I18N.imockSharing.qr_code,className:Dt.QRcode,tempDisabled:!1},{name:"sharing/more",toolTipName:I18N.LeftSidePanel.menu,className:Dt.setting,tempDisabled:!P}].filter(Boolean),[P]);return(0,r.jsxs)(hn,{className:te()(!oe&&"canNotEdit"),children:[(0,r.jsxs)("div",{className:"head-left",children:[(0,r.jsx)(Re.C,{className:"view-mode-icon",name:_==="device"?"sharing/mode_device":"sharing/mode_canvas"}),ge?(0,r.jsx)("form",{className:"link-form",children:(0,r.jsx)("input",{value:Le,onFocus:xe,onChange:be,ref:_e,onBlur:W,onKeyPress:ye})}):(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:te()("link-name",!P&&"link-name-not-edit"),onClick:Ze,children:x})})]}),(0,r.jsx)(wt.A,{content:I18N.imockSharing.tab_screen,direction:"up",distance:5,enterHoverTime:500,children:(0,r.jsxs)("div",{className:"head-right",children:[(0,r.jsx)("div",{children:le}),(0,r.jsx)(_n,{className:"share-icon",name:"sharing/page_visible",tempDisabled:!P,size:20,onClick:Qe=>l(Qe,Dt.showPage)})]})}),(0,r.jsx)("div",{className:"foot-right",children:We.map((Qe,ot)=>Qe&&(0,r.jsx)(_n,{className:te()("share-icon",Qe.className),name:Qe.name,type:Qe.className,toolTipName:Qe.toolTipName,onClick:l,tempDisabled:Qe.tempDisabled,isColorPure:Qe.isColorPure,size:20},ot))})]})},$s=(0,s.memo)(ys),Qs=L.Ay.div.withConfig({displayName:"styles__StyledSharingItemFoot",componentId:"sc-1b50v3w-0"})(["display:flex;align-items:center;justify-content:space-between;color:",";font-size:12px;font-family:PingFang SC;.visit_count{color:",";.svg-icon{path{fill:rgba(22,132,252,1);}path:last-of-type{fill:#fff;}}}.expiration-time{display:flex;align-items:center;color:",";.expire-text{margin:0 4px;cursor:pointer;.expired-desc{color:",";}.expired-no{color:",";}}.expire-icon{width:24px;height:24px;}&.is-expired{color:",";}svg{color:",";cursor:pointer;}.self-transform{svg{transition:all 0.4s;transform:rotate(360deg);}}}"],e=>e.theme.color_text_L2,e=>e.theme.color_text_L1,e=>e.theme.color_text_L3,e=>e.theme.color_text_L1,e=>e.theme.color_text_L3,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1),Fn=(0,L.DU)([".visit-tips{padding:6px;.tip-updated{display:flex;cursor:pointer;align-items:center;font-size:12px;p{margin:0;display:flex;align-items:center;}.icon-updated{display:inline-flex;align-items:center;margin-left:10px;line-height:14px;color:rgba(112,188,246,1);&:hover{color:rgba(149,209,248,1);}.svg-icon{margin-right:2px;path{fill:rgba(22,132,252,1);}path:last-of-type{fill:#fff;}}}}}"]);var Bn=n(79287),qn=n.n(Bn);function En(e,i,l){const c=(0,s.useMemo)(()=>{const x=qn()(),k=qn()(e.expired_at).subtract(2,"s");return k.isBefore(x)?0:k.diff(x,"days")+1},[e]),w=c<=0;let _;return i?_="\u5206\u4EAB\u8D85\u9650\uFF0C\u5347\u7EA7\u89E3\u9501":c>0&&c<=9999?_=c+" \u5929\u540E\u5931\u6548":c>9999?_=l===1?"\u6C38\u4E45\uFF08\u9650\u65F6\u514D\u8D39\uFF09":"\u6C38\u4E45\u6709\u6548":_="\u91CD\u65B0\u751F\u6548",{isExpired:w,remainTimeText:_}}var an=n(74533),jn=n(53559);const Js=e=>{let{sharingData:i,handleUpdateSharing:l}=e;const c=(0,g.wA)(),[w,_]=(0,s.useState)(null),[x,k]=(0,s.useState)(!1),{remainTimeText:E,isExpired:U}=En(i,!1,999),Z=i.device_model,$=i.expire_type==="forever",O=(0,g.d4)(d.query.getUser),ae=(0,g.d4)(d.query.getOrg),oe=(0,g.d4)(d.query.getProject),P=(0,g.d4)(Ie.cb),re=oe==null?void 0:oe.is_org_project;let le=!1,ge=!1;if(re){const{plan:_e,trial:be}=(0,jn.rM)(ae);ge=be,le=_e===an.Sj.OrgFull&&!ge}const he=ge||le,ye=P.roleName&&["org_owner","org_admin","org_manager"].includes(P.roleName),W=he?ye?"enterpriseAdmin":"enterpriseUser":"nonEnterprise",xe=(0,s.useCallback)(()=>{l({expire_type:i.expire_type}),k(!0),setTimeout(()=>k(!1),500),c({type:d.entryKey["sharing:function:track"],payload:{operation:"\u91CD\u7F6E\u6709\u6548\u671F",viewMode:Z,isDefault:!1}})},[c,l,i.expire_type,Z]);return(0,s.useEffect)(()=>{(async()=>{if(i.access_token)try{const be=await(0,D.dJ)(i.access_token);_(be.count)}catch(be){console.error("Error fetching visit count:",be)}})()},[i.access_token]),(0,r.jsxs)(Qs,{children:[(0,r.jsxs)("div",{className:"visit_count",children:[I18N.imockSharing.visit_count," ",w]}),(0,r.jsxs)("div",{className:te()("expiration-time",{"is-expired":U}),children:[(0,r.jsx)("div",{className:"expire-text",children:(0,r.jsx)("span",{className:U?"expired-desc":"expired-no",children:E})}),!$&&(0,r.jsx)(_n,{direction:"down",toolTipName:I18N.imockSharing.resetLink,onClick:xe,className:te()("expire-icon",{"self-transform":x}),size:12,name:"sharing/refresh_24"})]}),(0,r.jsx)(Fn,{})]})},Xs=(0,s.memo)(Js);var ut=n(15515);const es=e=>{const{project:i,sharing:l,user:c}=e,[w,_]=(0,s.useState)(!1),[x,k]=(0,s.useState)(void 0);return{isURLCopied:w,handleCopyURL:()=>{(0,H.kv)({project:i,action:l.password!==""?"\u590D\u5236\u94FE\u63A5\u548C\u5BC6\u7801":"\u590D\u5236\u94FE\u63A5",linkName:l.link_name}),_(!0),clearTimeout(x);const U=setTimeout(()=>_(!1),3e3);k(U);const Z=qn()().format("YYYY-MM-DD HH:mm:ss"),$=(0,H.EL)(c)&&!(0,ut.kV)(fe.nM);(0,ut.cH)(fe.nM,Z),$&&(0,G.Sx)({prototype_activation_time:Z})}}};var wn=n(43641);function Tn(e){const{password:i}=e;return{isHavePassword:(0,s.useMemo)(()=>i!==null&&i!=="",[i])}}var ts=n(70812);const nn=e=>{var i;let{sharingData:l,setShowDelConfirmModal:c,fromDefault:w}=e;const _=(0,g.wA)(),x=(0,s.useRef)(null),k=l.view_access,E=l.device_model,U=(0,g.d4)(d.query.getProject),Z=(0,g.d4)(d.query.getCanEditByUser),$=(0,g.d4)(d.query.getIsEditMode),O=(0,g.d4)(d.query.getHostType),ae=(0,g.d4)(d.query.getUser),oe=(0,g.d4)(d.query.getHostCurrentScreen),P=oe==null?void 0:oe.cid,re=(U==null?void 0:U.is_org_project)||!1,le=O==="iframe"||$,ge=le&&Z,[he,ye]=(0,s.useState)({top:0,left:0}),[W,xe]=(0,s.useState)(!1),{isURLCopied:_e,handleCopyURL:be}=es({project:U,sharing:l,user:ae}),{isHavePassword:Oe}=Tn(l),Le=(0,s.useCallback)(()=>{const gt=(0,H.O8)(U,l,P);(0,wn.$)(gt),be(),_({type:d.entryKey["sharing:function:track"],payload:{operation:"\u9AD8\u7EA7\u5206\u4EAB\u590D\u5236\u94FE\u63A5",viewMode:E,isDefault:!1}}),le&&ts.U.protoEditorShareTrack(ae==null?void 0:ae.id,O==="iframe"?"\u5DE5\u4F5C\u53F0\u53F3\u952E\u9AD8\u7EA7\u5206\u4EAB":"\u9AD8\u7EA7\u5206\u4EAB")},[_,l,P,be,U,E]),Ze=(0,s.useCallback)(gt=>{const jt={view_access:gt};_({type:d.entryKey["sharing:remote:sharing:update"],payload:{sharingData:l,updatedKV:jt}}),_({type:d.entryKey["sharing:function:track"],payload:{operation:gt==="public"?"\u6240\u6709\u4EBA":"\u5207\u6362\u56E2\u961F",viewMode:E,isDefault:!1}})},[l,_,E]),We=(0,s.useCallback)(()=>{c({isOpened:!0,item:l})},[c,l]),Qe=(0,s.useCallback)(()=>{_({type:d.entryKey["sharing:go-settingPage"],payload:{currentSharing:l,settingPageType:"edit"}}),_({type:d.entryKey["sharing:function:track"],payload:{operation:"\u8BBE\u7F6E",viewMode:E,isDefault:!1}})},[_,l,E]),ot=(0,s.useCallback)(gt=>{_({type:d.entryKey["sharing:remote:sharing:update"],payload:{sharingData:l,updatedKV:gt}})},[_,l]),[at,lt]=(0,s.useState)(void 0),St=(0,s.useMemo)(()=>{if(at==="viewAccess"){const gt=(0,r.jsx)(Re.C,{name:"toolbar/selected",className:"selected-icon"});if(re){const jt=k==="public";return[{label:I18N.imockSharing.share_anyone_view,onclick:()=>Ze("public"),icon:jt?gt:(0,r.jsx)("div",{style:{width:0}})},{label:I18N.imockSharing.org_members_only,onclick:()=>Ze("restricted"),icon:jt?(0,r.jsx)("div",{style:{width:0}}):gt}]}else return[]}else return[{label:I18N.imockSharing.setting,onclick:Qe},{label:I18N.imockSharing.delete,onclick:We}]},[at,Ze,We,Qe,k,re]),Et=(0,s.useCallback)(gt=>{if(!re||!ge)return;lt("viewAccess");const jt=gt.currentTarget.getBoundingClientRect();ye({top:jt.y+jt.height+3,left:jt.x-1}),xe(!W)},[re,ge,W,lt,ye]),Lt=(0,s.useCallback)((gt,jt)=>{if(gt!=null&&gt.currentTarget)switch(jt){case Dt.QRcode:gt.stopPropagation(),_({type:d.entryKey["sharing:init"],payload:{topPageIndex:"qrCode",currentSelectSharing:l}}),_({type:d.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u626B\u7801",viewMode:E,isDefault:!1}});break;case Dt.embed:_({type:d.entryKey["sharing:init"],payload:{topPageIndex:"embed",currentSelectSharing:l}}),_({type:d.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u5D4C\u5165\u4E09\u65B9",viewMode:E,isDefault:!1}});break;case Dt.mkt:(0,dt.JW)("/com24/upload?project_cid="+(U==null?void 0:U.cid),"_blank","noreferrer");break;case Dt.setting:{lt("misc");const Ot=gt.currentTarget.getBoundingClientRect(),on=document.getElementById("v8-share-page").getBoundingClientRect().bottom-Ot.bottom<120?Ot.y-75:Ot.bottom+5;ye({top:on,left:O==="iframe"?Ot.x+20:Ot.x-60}),xe(!W)}break;case Dt.showPage:_({type:d.entryKey["sharing:go-settingPage"],payload:{currentSharing:l,settingPageType:"edit",subSettingPageType:"visible"}});break;default:break}},[W,lt,ye,xe,_,U,l,E,O]),Pt=(0,s.useCallback)(()=>{var gt;ye({...he,left:((gt=x.current)==null?void 0:gt.getBoundingClientRect().x)-1})},[he]);(0,s.useEffect)(()=>(window.addEventListener("resize",Pt),()=>window.removeEventListener("resize",Pt)));const{isExpired:Ht}=En(l,!1,999);return(0,r.jsxs)(In,{className:te()(w&&"from-default"),children:[(0,r.jsx)($s,{sharingData:l,handleItemAction:Lt}),(0,r.jsxs)(mn,{className:te()("item-center",!ge&&"userNoEdit","advance-item"),isViewActiveDropdown:W&&at==="viewAccess",children:[(0,r.jsx)("div",{ref:x,className:te()("left","sharingV2-click-visible",{"normal-hover":!0},!re&&"can-not-edit"),onClick:Et,children:(0,r.jsxs)("div",{className:"view-access-select",children:[(0,r.jsx)("div",{className:"chore",children:I18N.imockSharing.permission_access}),(0,r.jsx)("div",{className:"access-name",children:(i=I18N.imockSharing[fe.nQ[k]])!=null?i:"missing data"}),re&&(0,r.jsx)(Re.C,{size:24,name:"sharing/dropdown_24"})]})}),(0,r.jsxs)(tn,{type:"button",className:te()("copy","sharingV2-click-visible","url-copy-button","is-state-"+Number(_e),{"is-disabled":Ht}),onClick:Le,children:[(0,r.jsx)("span",{className:"state state-0",children:Ht?I18N.dModule.link_expired_err_title:Oe?I18N.imockSharing.copy_url_pwd:I18N.imockSharing.copy_url}),(0,r.jsx)("span",{className:"state state-1",children:(0,r.jsx)(Re.C,{name:"new_replace/check",className:"copy-check"})})]})]}),(0,r.jsx)(Xs,{sharingData:l,handleUpdateSharing:ot}),W&&(0,r.jsx)(kn,{className:te()("design-avoid",at,"advance_menu"),position:he,onClose:()=>{xe(!1)},children:St.map((gt,jt)=>(0,r.jsx)(Nn.Dr,{text:gt.label,onClick:gt.onclick,icon:gt.icon,disabled:gt.disabled,canClick:gt.tempDisabled},jt))})]})},qs=(0,s.memo)(nn),xs=L.Ay.div.withConfig({displayName:"styles__StyledDeletingConfirmModal",componentId:"sc-1ws30dd-0"})(["display:flex;position:absolute;top:0;bottom:0;left:0;width:100%;height:100%;justify-content:center;align-items:center;background:rgba(0,0,0,0.4);.confirm-content{display:flex;height:fit-content;border-radius:10px;background:",";width:calc(100% - 54px);color:",";font-family:PingFang SC;font-style:normal;flex-direction:column;padding-bottom:18px;border:1px solid ",";box-shadow:0px 9px 28px 0px rgba(0,0,0,0.09),0px 6px 16px 0px rgba(0,0,0,0.02),0px 3px 6px 0px rgba(0,0,0,0.02);.top{width:100%;height:","px;display:flex;align-items:center;justify-content:center;justify-content:space-between;padding:0 24px;font-size:16px;font-weight:500;line-height:normal;border-bottom:1px solid ",";.close{width:24px;height:24px;svg{width:24px;height:24px;}}}.center{font-size:14px;line-height:22px;width:100%;padding:20px 24px;}.bottom{width:100%;padding-right:24px;display:flex;align-items:center;justify-content:flex-end;.delete{margin-left:12px;}}}"],e=>e.theme.color_bg_white,e=>e.theme.color_text_L1,e=>e.theme.color_share_del_confirm_border_color,fe.gE,e=>e.theme.color_bg_border_02);function Cn(e){let{handleConfirmAction:i}=e;return(0,r.jsx)(xs,{children:(0,r.jsxs)("div",{className:"confirm-content",children:[(0,r.jsxs)("div",{className:"top",children:[(0,r.jsx)("div",{children:I18N.imockSharing.delete_share}),(0,r.jsx)(_n,{name:"sharing/close",onClick:()=>i(!1),className:"close"})]}),(0,r.jsx)("div",{className:"center",children:I18N.imockSharing.deletion_shared_links_warning}),(0,r.jsxs)("div",{className:"bottom",children:[(0,r.jsx)(un.tA,{onClick:()=>i(!1),children:I18N.imockSharing.cancel}),(0,r.jsx)(un.Qi,{className:"delete",onClick:()=>i(!0),children:I18N.imockSharing.delete})]})]})})}const zn=s.memo(Cn),An=()=>{const e=(0,g.d4)(d.query.getUser),i=(0,g.d4)(d.query.getOrg),{cid:l,name:c,is_org_project:w}=(0,g.d4)(d.query.getProject),_=(0,g.d4)(d.query.getHostType),x=(0,g.d4)(d.query.getAdvancedSharingList),k=(0,g.d4)(d.query.getCanEditByUser),E=(0,g.d4)(d.query.getProjShareToEdit),U=(0,g.d4)(d.query.getTheme),[Z,$]=(0,s.useState)({isOpened:!1,item:null}),[O,ae]=(0,s.useState)(!1),oe=(0,g.wA)(),P=x.length===0,re=(0,s.useCallback)(async ge=>{ge&&oe({type:d.entryKey["sharing:remote:sharing:delete"],payload:{sharingCid:Z.item.cid}}),$({isOpened:!1,item:null})},[Z.item,$,oe]);(0,s.useEffect)(()=>{(async()=>{const ye=(await(0,D.X0)({projectCid:l})).map(W=>({...W,type:"advanced"})).sort((W,xe)=>new Date(xe.created_at).getTime()-new Date(W.created_at).getTime());if(E){const W=ye.find(xe=>xe.access_token===E);W&&(oe({type:d.entryKey["sharing:go-settingPage"],payload:{currentSharing:W,settingPageType:"edit"}}),oe({type:d.entryKey["sharing:init"],payload:{projShareToEdit:""}}))}oe({type:d.entryKey["sharing:advancedSharingList:update"],payload:{sharingList:ye}}),ae(!0)})()},[l,oe]);const le=async()=>{const ge=(0,H.jc)(e,i),he=()=>{(0,G.kH)("create_advanced_sharing_click",{project_name:c,project_cid:l,source:_==="iframe"?"\u5DE5\u4F5C\u53F0":"\u7F16\u8F91\u533A"})};if(x.length>=ge&&!ENV.IS_ON_PREMISES){const W=(i==null?void 0:i.plan)==="org_full"?"MaxOrgSize":"NormalOrgSize";window.SharingEventEmitter.emit("sharing:count",{whichModal:W}),window.top.postMessage(JSON.stringify({sharingMessage:"sharing:count",payload:{whichModal:W}}),"*"),he();return}const ye=(0,H.zM)(l,x,{view_access:w?"restricted":"public"});oe({type:d.entryKey["sharing:go-settingPage"],payload:{currentSharing:ye,settingPageType:"create"}}),he()};return O?P?(0,r.jsxs)(Rn,{children:[(0,r.jsx)(Re.C,{name:U==="dark"?"sharing/empty_dark":"sharing/empty",isColorPure:!0}),(0,r.jsx)(un.jn,{onClick:le,className:"create-sharing",children:I18N.imockSharing.new_share})]}):(0,r.jsxs)($t,{children:[(0,r.jsxs)("div",{className:"create-item "+(k?"":"no-edit"),onClick:le,children:[(0,r.jsx)(Re.C,{name:"sharing/add_new"}),I18N.imockSharing.new_share]}),x.map((ge,he)=>(0,r.jsx)(qs,{setShowDelConfirmModal:$,sharingData:ge},ge.cid+"-"+he)),Z.isOpened&&(0,r.jsx)(zn,{handleConfirmAction:re})]}):null},ns=(0,s.memo)(An),ss=L.Ay.div.withConfig({displayName:"styles__StyledSharingDefaultMainPage",componentId:"sc-zm7bo-0"})(["width:100%;height:calc(100% - ","px);.default-top{height:calc(100% - ","px);display:flex;flex-direction:column;justify-content:center;padding:0 24px;.switch-content{height:24px;display:flex;align-items:center;color:",";font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:500;line-height:normal;justify-content:space-between;span{margin-right:10px;}.visit-count{font-weight:400;font-size:12px;color:",";}}.wm-tip{height:25px;margin-top:12px;display:flex;span{height:25px;font-size:12px;font-weight:400;line-height:25px;color:",";}.divider{width:1px;height:13px;margin:6px 10px;background:",";}.btn{display:inline-block;padding:4px 6px;width:60px;height:25px;line-height:17px;font-size:12px;font-weight:500;border-radius:4px;background:",";color:",";&:hover{background:",";}}}}.default-bottom{height:","px;display:flex;align-items:center;justify-content:space-between;padding:0 24px;color:",";background:",";.bottom-left{display:flex;align-items:center;font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:500;line-height:normal;}.share-icon{width:28px;height:28px;margin-left:14px;&:hover,&:active{background:",";}}svg{width:24px;height:24px;}.default-setting{display:flex;align-items:center;font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:400;line-height:14px;padding:6px 12px 6px 6px;height:28px;border-radius:6px;cursor:pointer;svg{margin-right:2px;}&:not(.no-edit):hover,&:not(.no-edit):active{background:",";}&.no-edit{opacity:0.3;pointer-events:none;}}}&.canNotEdit{label{pointer-events:none;opacity:0.3;cursor:not-allowed;}}"],fe.gE,fe.gE,e=>e.theme.color_text_L1,e=>e.theme.color_text_L2,e=>e.theme.color_text_L2,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_info_bg,e=>e.theme.color_text_link_normal,e=>e.theme.color_grid,fe.gE,e=>e.theme.color_text_L1,e=>e.theme.color_bg_canvas,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_btn_secondary_active);var Ct=n(92114),sn=n(85449),bs=n(67771),os=n(21770),rs=n(35428),Un=n(17573);const Wn=e=>{var i;let{defaultShare:l}=e;const c=(0,g.wA)(),w=(0,s.useRef)(null),_=(0,g.d4)(d.query.getProject),x=l,[k,E]=(0,s.useState)(null);_&&(x.visibility=_.visibility);const U=(0,g.d4)(d.query.getHostType),Z=(0,g.d4)(d.query.getIsEditMode),$=(0,g.d4)(d.query.getUser),O=U==="iframe"||Z,ae=(0,g.d4)(d.query.getCanEditByUser),oe=(0,g.d4)(d.query.getHostCurrentScreen),P=oe==null?void 0:oe.cid,re=(0,g.d4)(d.query.getUser),le=(0,g.d4)(d.query.getOrg),ge=(0,g.d4)(Ie.cb),he=(_==null?void 0:_.is_org_project)||!1,ye=!he&&ae,[W,xe]=(0,s.useState)({top:0,left:0}),[_e,be]=(0,s.useState)(!1),[Oe,Le]=(0,s.useState)(x==null?void 0:x.visibility);let Ze=!1,We=!1;if(he){const{plan:tt,trial:Nt}=(0,jn.rM)(le);We=Nt,Ze=tt===an.Sj.OrgFull&&!We}const Qe=We||Ze,ot=ge.roleName&&["org_owner","org_admin","org_manager"].includes(ge.roleName),at=Qe?ot?"enterpriseAdmin":"enterpriseUser":"nonEnterprise",lt=O&&ae&&Oe==="open",St=O&&ae,{isURLCopied:Et,handleCopyURL:Lt}=es({project:_,sharing:x,user:$}),[Pt,Ht]=(0,s.useState)(!1),[gt,jt]=(0,s.useState)(!0);(0,s.useEffect)(()=>{const tt=(0,sn.fV)();if(tt){const{mdWMMktList:Nt,mtWMMktList:Xt,noWMMktList:Zs}=tt;(Nt.size||Xt.size)&&(0,Un._5)()&&jt(!1);const Jo=[...Nt,...Xt,...Zs];Jo.length&&(0,bs.q)(Jo).then(Xo=>{if(Xo.mdWMMktList.length>0||Xo.mtWMMktList.length>0){if(!(0,Un._5)())return;Ht(!0),ts.U.watermarkExposureTrack("share")}})}},[]);const Ot=()=>{const{isSoloMdMember:tt}=(0,os.s)(re,MB.currentOrg),Nt=!!MB.currentOrg,Xt={mode:"mat",payEntrance:"\u7F16\u8F91\u533A-v8-"+(Nt?"\u56E2\u961F":"\u4E2A\u4EBA")+"-\u4ED8\u8D39\u7D20\u6750\u8BD5\u7528-\u5206\u4EAB\u884C\u4E3A\u63D0\u793A-"+(tt?"\u5347\u7EA7\u7D20\u6750\u4F1A\u5458":"\u5347\u7EA7\u53CC\u4F1A\u5458"),checkoutPlace:"workspace_v8_"+(Nt?"org":"solo")+"_watermark_share_"+(tt?"template":"vip-template"),checkoutArea:"proto"};MB.global.popupHelper.chargeAsync({...Xt}),(0,rs._)({click_button:"\u5206\u4EAB-"+(tt?"\u5347\u7EA7\u7D20\u6750\u4F1A\u5458":"\u5347\u7EA7\u53CC\u4F1A\u5458")})},Jt=(0,s.useCallback)(tt=>{const Nt={access:tt};c({type:d.entryKey["sharing:remote:sharing:update"],payload:{sharingData:x,updatedKV:Nt}}),c({type:d.entryKey["sharing:function:track"],payload:{operation:tt==="public"?"\u6240\u6709\u4EBA":"\u5207\u6362\u56E2\u961F",viewMode:x.view_mode,isDefault:!0}})},[x,c]),ft=(0,s.useMemo)(()=>{const tt=(0,r.jsx)(Re.C,{name:"toolbar/selected",className:"selected-icon"});if(he){const Nt=(x==null?void 0:x.view_access)==="public";return[{label:I18N.imockSharing.share_anyone_view,onclick:()=>Jt("public"),icon:Nt?tt:(0,r.jsx)("div",{style:{width:0}})},{label:I18N.imockSharing.org_members_only,onclick:()=>Jt("restricted"),icon:Nt?(0,r.jsx)("div",{style:{width:0}}):tt}]}else return[]},[Jt,he,x]),vt=(0,s.useMemo)(()=>{const tt=[{name:"sharing/embed_icon",toolTipName:I18N.imockSharing.insert,className:Dt.embed,tempDisabled:Oe==="close",isBorder:!0}];return ENV.IS_ON_PREMISES||tt.unshift({name:"sharing/QRcode_icon",toolTipName:I18N.imockSharing.qr_code,className:Dt.QRcode,tempDisabled:Oe==="close",isBorder:!0}),ye&&tt.push({name:"sharing/mkt_icon",toolTipName:gt?I18N.imockSharing.publish_to_community:"\u542B\u4ED8\u8D39\u7D20\u6750\uFF0C\u4E0D\u652F\u6301\u4E0A\u4F20",className:Dt.mkt,tempDisabled:Oe==="close"||!gt,isColorPure:!0,isBorder:!0}),tt},[Oe,ye,gt]),on=(0,s.useCallback)(()=>{const tt=Oe==="close"?"open":"close",Nt={visibility:tt};Le(tt),c({type:d.entryKey["sharing:remote:sharing:update"],payload:{sharingData:x,updatedKV:Nt}}),c({type:d.entryKey["sharing:function:track"],payload:{operation:tt==="open"?"\u6253\u5F00\u8BBF\u95EE":"\u5173\u95ED\u8BBF\u95EE",viewMode:x.view_mode,isDefault:!0}})},[Oe,c,x,Le]),Jn=(0,s.useCallback)(tt=>{if(!he||!lt)return;const Nt=tt.currentTarget.getBoundingClientRect();let Xt=Nt.y+Nt.height+3;U==="iframe"&&(Xt=Xt+56),xe({top:Xt,left:Nt.x-1}),be(!_e)},[he,lt,_e,xe,U]),Xn=(0,s.useCallback)(()=>{const tt=(0,H.O8)(_,x,P);(0,wn.$)(tt),Lt(),c({type:d.entryKey["sharing:function:track"],payload:{operation:"\u9ED8\u8BA4\u5206\u4EAB\u590D\u5236\u94FE\u63A5",viewMode:x.view_mode,isDefault:!0}}),O&&ts.U.protoEditorShareTrack(re==null?void 0:re.id,U==="iframe"?"\u5DE5\u4F5C\u53F0\u53F3\u952E\u666E\u901A\u5206\u4EAB":"\u666E\u901A\u5206\u4EAB")},[_,x,P,Lt,c]),vn=(0,s.useCallback)(()=>{const tt=x,{view_mode:Nt}=x;c({type:d.entryKey["sharing:go-settingPage"],payload:{currentSharing:tt,settingPageType:"edit"}}),c({type:d.entryKey["sharing:function:track"],payload:{operation:"\u8BBE\u7F6E",viewMode:Nt,isDefault:!0}})},[x,c]),rn=(0,s.useCallback)((tt,Nt)=>{if(!(tt!=null&&tt.currentTarget))return;const Xt=x,{view_mode:Zs}=x;switch(Nt){case Dt.QRcode:tt.stopPropagation(),c({type:d.entryKey["sharing:init"],payload:{topPageIndex:"qrCode",currentSelectSharing:Xt}}),c({type:d.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u626B\u7801",viewMode:Zs,isDefault:!1}});break;case Dt.embed:c({type:d.entryKey["sharing:init"],payload:{topPageIndex:"embed",currentSelectSharing:Xt}}),c({type:d.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u5D4C\u5165\u4E09\u65B9",viewMode:Zs,isDefault:!1}});break;case Dt.mkt:(0,dt.JW)("/com24/upload?project_cid="+(_==null?void 0:_.cid),"_blank","noreferrer");break;default:break}},[c,_,x]),Yt=(0,s.useCallback)(()=>{var tt;xe({...W,left:((tt=w.current)==null?void 0:tt.getBoundingClientRect().x)-1})},[W]);return(0,s.useEffect)(()=>(window.addEventListener("resize",Yt),()=>window.removeEventListener("resize",Yt))),(0,s.useEffect)(()=>{(async()=>{const Nt=x.access_token;if(Nt)try{const Xt=await(0,D.dJ)(Nt);E(Xt.count)}catch(Xt){console.error("Error fetching visit count:",Xt)}})()},[x.access_token]),x?(0,r.jsxs)(ss,{className:te()(!St&&"canNotEdit"),children:[(0,r.jsxs)("div",{className:"default-top",children:[O?(0,r.jsxs)("div",{className:"switch-content",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{children:I18N.imockSharing.public_access}),(0,r.jsx)(Ct.A,{size:"small",readOnly:!0,isIOS:!0,isChecked:Oe==="open",onChange:on})]}),(0,r.jsx)("div",{className:"visit-count",children:k!==null&&k!==0&&(0,r.jsxs)(r.Fragment,{children:[I18N.imockSharing.visit_count," ",k]})})]}):(0,r.jsxs)("div",{className:"switch-content",children:[I18N.imockSharing.default_share,k!==null&&k!==0&&(0,r.jsxs)("div",{className:"visit-count",children:[I18N.imockSharing.visit_count," ",k]})]}),(0,r.jsxs)(mn,{className:te()("item-center","default-link",Oe==="close"&&"notView",!lt&&"userNoEdit"),isViewActiveDropdown:_e,children:[(0,r.jsx)("div",{ref:w,className:te()("left","sharingV2-click-visible",{"normal-hover":!0},!he&&"can-not-edit"),onClick:Jn,children:(0,r.jsxs)("div",{className:"view-access-select",children:[(0,r.jsx)("div",{className:"chore",children:I18N.imockSharing.permission_access}),(0,r.jsx)("div",{className:"access-name",children:(i=I18N.imockSharing[fe.nQ[x.view_access]])!=null?i:"missing data"}),he&&(0,r.jsx)(Re.C,{size:24,name:"sharing/dropdown_24"})]})}),(0,r.jsxs)(tn,{type:"button",className:te()("copy","sharingV2-click-visible","url-copy-button","is-state-"+Number(Et)),onClick:Xn,children:[(0,r.jsx)("span",{className:"state state-0",children:x.password&&x.password.length>0?I18N.imockSharing.copy_url_pwd:I18N.imockSharing.copy_url}),(0,r.jsx)("span",{className:"state state-1",children:(0,r.jsx)(Re.C,{name:"new_replace/check",className:"copy-check"})})]})]}),Pt&&(0,r.jsxs)("div",{className:"wm-tip",children:[(0,r.jsx)("span",{children:I18N.WaterMark.mkt.share.tip}),(0,r.jsx)("div",{className:"divider"}),(0,r.jsx)("div",{className:"btn",onClick:Ot,children:I18N.WaterMark.mkt.remove_wm})]})]}),(0,r.jsxs)("div",{className:"default-bottom",children:[(0,r.jsxs)("div",{className:"bottom-left",children:[(0,r.jsx)("div",{children:I18N.imockSharing.share_mode}),vt.map((tt,Nt)=>(0,r.jsx)(_n,{className:te()("share-icon",tt.className),name:tt.name,type:tt.className,toolTipName:tt.toolTipName,onClick:rn,tempDisabled:tt.tempDisabled,isColorPure:tt.isColorPure,isBorder:tt.isBorder,size:24},Nt))]}),O&&(0,r.jsxs)("div",{className:te()("default-setting",!lt&&"no-edit"),onClick:vn,children:[(0,r.jsx)(Re.C,{name:"sharing/default_setting"}),I18N.imockSharing.setting]})]}),_e&&(0,r.jsx)(kn,{className:te()("design-avoid","viewAccess"),position:W,onClose:()=>{be(!1)},children:ft.map((tt,Nt)=>(0,r.jsx)(Nn.Dr,{text:tt.label,onClick:tt.onclick,icon:tt.icon,disabled:tt.disabled,canClick:tt.tempDisabled},Nt))}),(0,r.jsx)(Fn,{})]}):null},eo=(0,s.memo)(Wn),to=L.Ay.div.withConfig({displayName:"styles__StyledSharingMainPage",componentId:"sc-7p2gxy-0"})(["width:100%;position:relative;height:100%;"]),no=L.Ay.div.withConfig({displayName:"styles__StyledSharingEmbedded",componentId:"sc-xvloju-0"})(["width:100%;padding:20px 32px 0 32px;.desc{line-height:17px;font-size:12px;font-family:PingFang SC;color:",";margin-bottom:20px;}.copy-wrapper{display:flex;justify-content:space-between;height:112px;.demo-img{width:192px;height:100%;object-fit:cover;flex-shrink:0;}.copy-box{display:flex;flex-direction:column;width:148px;height:100%;border:1px solid ",";border-radius:2px;overflow:hidden;.sharing-embedded-url{flex-grow:1;height:0;padding:6px 8px;white-space:normal;word-wrap:break-word;color:",";font-size:12px;font-weight:500;}.copy-button{width:100%;height:28px;font-size:12px;background:",";border-top:1px solid ",";color:",";&:hover{background:",";}}}}.open-setting{justify-content:flex-start;.opener-wrapper{display:flex;align-items:center;span{color:#1684fc;margin-right:3px;cursor:pointer;}.svg-icon{width:6px;height:4px;cursor:pointer;transition:transform 0.2s ease-in-out;color:",";}&.is-open{.svg-icon{transform:rotate(-180deg);}}}}.open-setting.line-item.embedded-padding{padding:0;margin-top:14px;}.hidden{height:0;overflow:hidden;transition:height 0.2s;}.hidden.is-open{height:156px;}"],e=>e.theme.color_text_L1,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_L2,e=>e.theme.color_bg_card,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_L1,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_L3);var yn=n(53940);const Qt=L.Ay.div.withConfig({displayName:"styles__StyledSharingTitleBar",componentId:"sc-174zu8p-0"})(["width:100%;height:","px;display:flex;align-items:center;border-bottom:1px solid ",";color:",";font-size:14px;font-family:PingFang SC;font-style:normal;font-weight:500;line-height:24px;.title{cursor:pointer;display:flex;align-items:center;svg{width:32px;height:32px;margin-left:10px;}}"],fe.gE,e=>e.theme.color_border_state,e=>e.theme.color_text_L1),Sn=e=>{let{title:i,handleClick:l}=e;return(0,r.jsx)(Qt,{children:(0,r.jsxs)("div",{className:"title",onClick:l,children:[(0,r.jsx)(Re.C,{name:"sharing/nav_back"}),i]})})},Rt=()=>{let e="read_only";const i=(0,g.wA)(),l=(0,g.d4)(d.query.getCurrentSelectSharing),c=(0,g.d4)(d.query.getProject),w=(0,g.d4)(d.query.getTheme),_=(0,g.d4)(d.query.getHostCurrentScreen),x=(0,g.d4)(d.query.getHostType),k=_==null?void 0:_.cid,{screen_visible_list:E,access_token:U,is_first_canvas_open:Z}=l||{};if(l.type==="default"){const{view_mode:he}=l;e=he}else{const{device_model:he}=l;e=he}let $="";const O=(0,g.d4)(d.query.getIsEditMode);l.type==="default"?$=O||e==="read_only"?k:new URLSearchParams(location.search).get("screen")||"":$=k;let ae="";const oe=(0,H.a2)(U);x==="iframe"?ae='<iframe src="'+oe+"/embed/v2/sharing?view_mode="+e+'" allowTransparency="true" frameborder="0"></iframe>':E&&E.length!==0?ae='<iframe src="'+oe+"/embed/v2/sharing?view_mode="+e+"&selection="+E[0]+(Z?"":"&screen="+$)+'" allowTransparency="true" frameborder="0"></iframe>':ae='<iframe src="'+oe+"/embed/v2/sharing?view_mode="+e+(Z?"":"&screen="+$)+'" allowTransparency="true" frameborder="0"></iframe>';let P=null;const re=(0,s.useCallback)(he=>{const ye=he.currentTarget;ye.innerHTML=I18N.imockSharing.successfully_copied,clearTimeout(P),P=setTimeout(()=>{ye.innerHTML=I18N.imockSharing.successfully_copied},3e3),(0,H.kv)({action:"\u590D\u5236\u4EE3\u7801",project:c||{},linkName:l.link_name});try{MB.notice({text:"\u590D\u5236\u6210\u529F"})}catch(W){window.top.postMessage(JSON.stringify({sharingMessage:"sharing:notice",payload:{type:"embedCopy"}}),"*")}},[l,P,c]),le=(0,s.useCallback)(he=>{(0,wn.$)(ae),re(he)},[re,ae]),ge=(0,s.useCallback)(()=>{i({type:d.entryKey["sharing:topPageIndex:jump"],payload:{topPageIndex:"edit"}})},[i]);return(0,r.jsxs)(s.Fragment,{children:[(0,r.jsx)(Sn,{title:I18N.imockSharing.insert,handleClick:ge}),(0,r.jsxs)(no,{children:[(0,r.jsx)("p",{className:"desc",children:I18N.imockSharing.zoom_insertion_desc}),(0,r.jsxs)("div",{className:"copy-wrapper",children:[(0,r.jsx)("img",{src:w===yn.Sx.DARK?"/mb-workspace/images/sharing/share_embedded_dark.png":"/mb-workspace/images/sharing/share_embedded_light.png",className:"demo-img"}),(0,r.jsxs)("div",{className:"copy-box",children:[(0,r.jsx)("p",{className:"sharing-embedded-url",children:ae}),(0,r.jsx)("button",{className:"copy-button copy","data-clipboard-text":ae,onClick:le,children:I18N.imockSharing.copy_code})]})]})]})]})},kt=(0,s.memo)(Rt);var zt=n(33233);const xn=(0,L.AH)([".vip-title{display:inline-block;color:",";background-color:",";font-weight:500;font-size:12px;padding:0 4px;height:20px;text-align:center;border-radius:4px;line-height:20px;}"],e=>e.isIframe?zt.fm.color_text_link_normal.value_dark:e.theme.color_text_link_normal,e=>e.isIframe?zt.fm.color_info_bg.value_dark:e.theme.color_info_bg),Ln=L.Ay.div.withConfig({displayName:"styles__StyledNewSharePage",componentId:"sc-v150qt-0"})(["width:100%;height:100%;background-color:",";.basic-content{width:100%;height:calc(100% - ","px);:first-child{margin-bottom:16px;}}.header{height:","px;display:flex;align-items:center;border-bottom:1px solid ",";span{margin-left:24px;font-size:14px;font-weight:500;color:",";}}.share-link-title{margin-top:20px;margin-bottom:10px;display:flex;flex-direction:column;justify-content:center;margin-left:24px;font-size:12px;.title-text{color:",";}.name-input{display:flex;align-items:center;height:30px;font-size:14px;font-weight:500;color:",";span{max-width:212px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;position:relative;height:24px;line-height:24px;&:hover:after{box-sizing:border-box;content:'';position:absolute;top:0;left:0;width:100%;height:100%;border-bottom:1px solid rgba(22,133,252,1);}}.input-icon{margin-left:8px;}input{width:100%;margin-right:32px;height:24px;line-height:24px;color:",";border-bottom:1px solid rgba(22,133,252,1);}}}.tabs{margin:8px 24px 18px 24px;border-bottom:1px solid ",";.sharing-nav{width:90px;height:25px;min-height:25px;padding:0px;}.nav-item{flex:0;padding:0 24px 0 0;}.nav-label{height:24px;}.active .nav-label{color:",";font-weight:600;}.active.basic{.nav-label{&::after{content:'';position:absolute;bottom:-1px;width:30px;height:2px;background:",";}}}.active.visible{.nav-label{&::after{content:'';position:absolute;bottom:-1px;width:50px;height:2px;background:",";}}}}.line-item{display:flex;padding:0 24px;height:34px;align-items:center;justify-content:space-between;flex-grow:0;.item-title{color:",";}.item-expired{display:flex;.WorkspaceSelect{border-radius:6px;}.general-input{width:65px;height:28px;border-radius:6px;margin-right:12px;}}.RadioGroup label{min-height:unset;margin-right:unset;font-size:12px;&:not(:last-child){margin-right:16px;}}.switch-group{display:flex;.view-sticky{display:flex;align-items:center;margin-right:17px;label{margin-right:4px;}}.view-prd{display:flex;align-items:center;.question-action{display:flex;align-items:center;margin:0 8px;color:",";&:hover{color:",";}}}label.checked{color:",";}}}"],e=>e.theme.color_bg_white,fe.gE,fe.gE,e=>e.theme.color_bg_border_02,e=>e.theme.color_text_L1,e=>e.theme.color_text_L2,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_background_icon_hover2,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_L3,e=>e.theme.color_text_L2,e=>e.theme.color_text_L1),vs=L.Ay.div.withConfig({displayName:"styles__StyledBasic",componentId:"sc-v150qt-1"})(["overflow-y:overlay;.divider{height:1px;background:",";margin:4px 24px;}&::-webkit-scrollbar-track{background-color:transparent;}&::-webkit-scrollbar{width:6px;height:6px;}&::-webkit-scrollbar-thumb{background-color:#dbdbdb;border-radius:4px;&:hover{background-color:#cccccc;}}&::-webkit-scrollbar-corner{background-color:transparent;}.expiration,.menu-list-type{cursor:pointer;display:flex;align-items:center;color:",";.misc{display:flex;align-items:center;}.drop-down-icon{margin-left:6px;}}.expiration{.drop-down-icon .svg-icon{transition:all 0.2s;transform :",";}}.menu-list-type{.drop-down-icon .svg-icon{transition:all 0.2s;transform :",";}}.password-input-item{display:flex;align-items:center;> :last-child{margin-left:10px;}}.last{margin-bottom:-8px;}.line-item.version-control{"," .vip-title{cursor:pointer;margin-left:4px;}}"],e=>e.theme.color_background_icon_hover2,e=>e.theme.color_text_L1,e=>e.isActiveContextMenu&&e.menuListType==="expiration"?"rotate(-180deg)":"rotate(0deg)",e=>e.isActiveContextMenu&&e.menuListType==="viewMode"?"rotate(-180deg)":"rotate(0deg)",xn),Mn=L.Ay.div.withConfig({displayName:"styles__StyledVisible",componentId:"sc-v150qt-2"})(["height:328px;.visible-check{height:32px;}"]),_s=(0,L.DU)([".custom-title{display:flex;justify-content:space-between;align-items:baseline;","}}#IBOT_SELECT_MENU_ROOT .SelectMenuPortal .WorkspaceSelectMenu .SelectOption{height:28px;&:hover{.vip-title{background-color:",";}}}#IBOT_SELECT_MENU_ROOT .SelectMenuPortal .WorkspaceSelectMenu.is-open{right:-14px;}"],xn,e=>e.isIframe?zt.fm.color_bg_white.value_dark:e.theme.color_bg_white);var Hn=n(90108);const ws=L.Ay.div.withConfig({displayName:"styles__StyledPasswordTextInput",componentId:"sc-k8kmsm-0"})(["display:flex;background-color:",";height:28px;&.is-warning{.password-input{border:1px solid #ff6161;border-radius:4px 0 0 4px;&:focus{border:1px solid #ff6161;}}}.divider{height:100%;width:1px;background-color:",";}.password-input-wrapper{position:relative;display:flex;align-items:center;width:100px;.password-input{position:relative;width:100%;font-size:12px;padding-right:22px;padding-left:8px;overflow:hidden;white-space:nowrap;text-overflow:clip;color:",";border:1px solid ",";border-radius:6px;height:100%;&:focus{border:1px solid ",";outline:1px solid ",";outline-offset:-2px;}&:hover{border:1px solid ",";}}.reset-pwd{width:20px;height:20px;position:absolute;border-radius:4px;right:4px;color:",";cursor:pointer;&:hover{path{fill:",";}color:",";background-color:",";}&:active{path{fill:",";}color:",";background-color:",";}}}.password-button{width:72px;padding:8px 12px;color:",";font-size:12px;line-height:16px;white-space:nowrap;border:1px solid ",";border-radius:0 4px 4px 0;border-left:none;background-color:",";&.saved{background-color:",";color:#999;pointer-events:none;}}"],e=>e.theme.color_bg_white,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_L1,e=>e.theme.color_bg_border_02,e=>e.theme.color_proto,e=>e.theme.color_proto,e=>e.theme.color_proto,e=>e.theme.color_text_L3,e=>e.theme.color_text_L2,e=>e.theme.color_text_L2,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_text_L2,e=>e.theme.color_text_L2,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_L1,e=>e.theme.color_bg_border_02,e=>e.theme.color_bg_canvas,e=>e.theme.color_bg_canvas);function Cs(e){return Math.random().toString(36).slice(-e)}function so(e){const{onSubmit:i,value:l}=e,[c,w]=(0,s.useState)(e.value),_=(0,s.useRef)(null),x=Z=>{w(Z.target.value)},k=()=>{const Z=Cs(6);w(Z),i(Z)},E=()=>{if(!i(c)){w(l);try{MB.notice({text:"\u5BC6\u7801\u683C\u5F0F\u9519\u8BEF\uFF0C\u4E0D\u5F97\u4E3A\u7A7A\u6216\u7279\u6B8A\u5B57\u7B26",type:"warning"})}catch($){window.top.postMessage(JSON.stringify({sharingMessage:"sharing:notice"}),"*")}}},U=Z=>{Z.key==="Enter"&&(E(),_.current.blur())};return(0,r.jsx)(ws,{children:(0,r.jsxs)("div",{className:"password-input-wrapper",children:[(0,r.jsx)("input",{ref:_,type:"text",className:"password-input",value:c,onChange:x,onBlur:E,onKeyPress:U}),(0,r.jsx)(wt.A,{content:I18N.imockSharing.gen_pwd_randomly,direction:"down",children:(0,r.jsx)(Re.C,{className:"reset-pwd",name:"common/spinner",onClick:k})})]})})}function Ss(e){const{isOrgProject:i,sharingData:l,updateSharing:c}=e,w=l.view_mode,{isHavePassword:_}=Tn(l),x=(0,g.wA)(),E=(0,g.d4)(d.query.getHostType)==="iframe",{comment_permission:U,wechat:Z,view_sticky:$,is_first_canvas_open:O}=l,ae=(0,s.useMemo)(()=>(0,H.R0)(i),[i]),oe=()=>{if(_)c({password:""}),x({type:d.entryKey["sharing:settingPage:default:confirm"]});else{const xe=Cs(6);P(xe)}x({type:d.entryKey["sharing:function:track"],payload:{operation:_?"\u5173\u95ED\u5BC6\u7801\u4FDD\u62A4":"\u6253\u5F00\u5BC6\u7801\u4FDD\u62A4",viewMode:w}})},P=xe=>/^\s*$/.test(xe)||!/^\w+$/.test(xe)?!1:(c({password:xe}),x({type:d.entryKey["sharing:settingPage:default:confirm"]}),x({type:d.entryKey["sharing:function:track"],payload:{operation:"\u5237\u65B0\u5BC6\u7801",viewMode:w}}),!0),re=()=>{c({wechat:!Z}),x({type:d.entryKey["sharing:settingPage:default:confirm"]}),x({type:d.entryKey["sharing:function:track"],payload:{operation:Z?"\u5173\u95ED\u8DF3\u8FC7\u79FB\u52A8\u7AEF\u63D0\u793A\u9875":"\u6253\u5F00\u8DF3\u8FC7\u79FB\u52A8\u7AEF\u63D0\u793A\u9875",viewMode:w}})},le=()=>{c({is_first_canvas_open:!O}),x({type:d.entryKey["sharing:settingPage:default:confirm"]}),x({type:d.entryKey["sharing:function:track"],payload:{operation:O?"\u5173\u95ED\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03":"\u6253\u5F00\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03",viewMode:w}})},ge=(0,s.useMemo)(()=>U==="org_member",[U]),he=()=>{c({view_sticky:$==="view_sticky"?"no_sticky":"view_sticky"}),x({type:d.entryKey["sharing:settingPage:default:confirm"]}),x({type:d.entryKey["sharing:function:track"],payload:{operation:$?"\u5173\u95ED\u6279\u6CE8\u53EF\u89C1":"\u6253\u5F00\u6279\u6CE8\u53EF\u89C1",viewMode:w}})},ye=()=>{c({comment_permission:U==="org_member"?"off":"org_member"}),x({type:d.entryKey["sharing:settingPage:default:confirm"]}),x({type:d.entryKey["sharing:function:track"],payload:{operation:ge?"\u5173\u95ED\u8BC4\u8BBA":"\u6253\u5F00\u8BC4\u8BBA",viewMode:w}})},W=xe=>{let{value:_e}=xe;c({view_access:_e}),x({type:d.entryKey["sharing:settingPage:default:confirm"]}),x({type:d.entryKey["sharing:remote:sharing:update"],payload:{sharingData:l,updatedKV:{access:_e}}}),x({type:d.entryKey["sharing:function:track"],payload:{operation:_e==="public"?"\u6240\u6709\u4EBA":"\u5207\u6362\u56E2\u961F",viewMode:w,isDefault:!1}})};return(0,r.jsxs)(vs,{children:[i&&(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.permission_access}),(0,r.jsx)(Hn.A,{name:"access",optionList:ae,value:l.view_access,onChange:W})]}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.permission_password_protect}),(0,r.jsxs)("div",{className:"password-input-item",children:[_&&(0,r.jsx)(so,{value:l.password,onSubmit:P}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:_,onChange:oe})]})]}),(0,r.jsx)("div",{className:"divider"}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.note_display}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:$==="view_sticky",onChange:he})]}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.comment_allowed}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:ge,onChange:ye})]}),(0,r.jsx)("div",{className:"divider"}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.share_skip_install_run_inwechat}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:Z,onChange:re})]}),!E&&(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:"\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03"}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:O,onChange:le})]})]})}var oo=n(69623);const Ms=()=>[{key:"read_only",label:I18N.imockSharing.read_only_mode,icon:"sharing/mode_canvas"},{key:"device",label:I18N.imockSharing.device_mode,icon:"sharing/mode_device"},{key:"inspect",label:I18N.imockSharing.inspect_mode,icon:"sharing/mode_inspect"}],Ns=e=>{let{sharingData:i,updateSharing:l}=e;const c=O=>()=>{l({device_model:O})},[w,_]=(0,s.useState)(!1),x=(0,s.createRef)(),k=(0,g.d4)(d.query.getTheme),[E,U]=(0,s.useState)({left:0,top:0}),Z=()=>{if(!(x!=null&&x.current))return;const{left:O,top:ae,height:oe}=x.current.getBoundingClientRect();U({left:O-58,top:ae+oe+6}),setTimeout(()=>{_(!0)},0)},$=()=>{_(!1)};return(0,r.jsx)(ro,{className:te()({dark:k==="dark"}),children:(0,r.jsxs)("div",{className:"device-model",children:[(0,r.jsxs)("div",{className:"device-model-header",children:[(0,r.jsx)("div",{className:"device-model-title",children:I18N.imockSharing.default_mode}),(0,r.jsxs)("div",{className:"modal-container",ref:x,children:[(0,r.jsx)(Re.C,{onMouseEnter:Z,onMouseLeave:$,className:"tipsIcon",name:"sharing/question",size:14}),w&&(0,r.jsx)(Vn,{theme:k,position:E})]})]}),(0,r.jsx)("div",{className:"device-model-type-container",children:Ms().map((O,ae)=>{let{key:oe,label:P,icon:re}=O;const le="device-model-"+oe+"   ";return(0,r.jsxs)("div",{className:te()("device-model-type-item",le,{"is-active":oe===i.device_model,dark:k==="dark"}),onClick:c(oe),children:[(0,r.jsx)(Re.C,{name:re,size:16}),(0,r.jsx)("span",{children:P})]},ae)})})]})})},Vn=(0,s.memo)(e=>{const{theme:i,position:l}=e;return(0,oo.createPortal)((0,r.jsxs)(Is,{className:te()("device-model-modal",{light:i==="light",dark:i==="dark"}),style:{left:l.left,top:l.top},children:[(0,r.jsx)("div",{className:"icon-triangle"}),(0,r.jsxs)("div",{className:te()("modal-content"),children:[(0,r.jsxs)("div",{className:"content-item read_only",children:[(0,r.jsx)("img",{src:"/mb-proto2/images/sharing/intro_read_only_"+i+"_2.png"}),(0,r.jsxs)("div",{className:"content-item-text",children:[(0,r.jsx)("div",{className:"intro-title",children:I18N.imockSharing.read_only_mode}),(0,r.jsx)("div",{className:"intro-content",children:I18N.imockSharing.read_only_mode_intro})]})]}),(0,r.jsxs)("div",{className:"content-item device",children:[(0,r.jsx)("img",{src:"/mb-proto2/images/sharing/intro_device_"+i+"_2.png"}),(0,r.jsxs)("div",{className:"content-item-text",children:[(0,r.jsx)("div",{className:"intro-title",children:I18N.imockSharing.device_mode}),(0,r.jsx)("div",{className:"intro-content",children:I18N.imockSharing.device_mode_intro})]})]}),(0,r.jsxs)("div",{className:"content-item inspect",children:[(0,r.jsx)("img",{src:"/mb-proto2/images/sharing/intro_inspect_"+i+"_2.png"}),(0,r.jsxs)("div",{className:"content-item-text",children:[(0,r.jsx)("div",{className:"intro-title",children:I18N.imockSharing.inspect_mode}),(0,r.jsx)("div",{className:"intro-content",children:I18N.imockSharing.inspect_mode_intro})]})]})]})]}),document.body)}),ro=L.Ay.div.withConfig({displayName:"ViewModeModel__StyledDeviceModel",componentId:"sc-spi606-0"})(["position:relative;margin-bottom:14px;margin-top:20px;.device-model{padding:0 24px;.device-model-header{display:flex;align-items:center;margin-bottom:10px;color:",";.device-model-title{margin-right:4px;line-height:14px;}}.modal-container{display:flex;.tipsIcon{color:",";&:hover{color:",";}}}.device-model-type-container{display:flex;justify-content:space-between;.svg-icon > *{fill:currentColor;}.device-model-type-item{width:111px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer;border:1px solid rgba(219,219,219,1);border-radius:6px;color:",";&.dark{border:1px solid #4f5052;}span{margin-left:4px;font-size:14px;}&:hover{border:1px solid  ",";}.svg-icon{[stroke]{stroke:currentColor;fill:none;}}&.is-active{border:1px solid  ",";color:",";.svg-icon{color:",";}}}}}"],e=>e.theme.color_text_L2,e=>e.theme.color_text_L3,e=>e.theme.color_text_L2,e=>e.theme.color_text_L1,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_normal),Is=L.Ay.div.withConfig({displayName:"ViewModeModel__StyledDeviceModelModal",componentId:"sc-spi606-1"})(["&.device-model-modal{z-index:100;position:absolute;border-radius:8px;&.dark{.modal-content{outline:1px solid ",";}}&.light{box-shadow:0px 2px 10px 0px #27364E14;box-shadow:4px 12px 40px 0px #27364E1A;}.icon-triangle{z-index:1;position:absolute;width:10px;height:10px;top:-4px;left:58px;background:",";transform:rotate(45deg);border-top-left-radius:1px;}&.dark .icon-triangle{width:9px;height:9px;top:-5px;left:59px;border-top:1px solid ",";border-left:1px solid ",";}.modal-content{display:flex;flex-direction:column;padding:20px;width:374px;height:366px;border-radius:8px;background:",";&:lang(en){height:250px;}img{width:160px;height:96px;}.content-item:not(:last-child){margin-bottom:16px;}.content-item-text{display:flex;flex-direction:column;justify-content:center;height:100%;margin-left:16px;}.device,.read_only,.inspect{width:100%;height:96px;display:flex;flex-direction:row;justify-content:space-between;align-items:center;.intro-title{width:56px;height:22px;color:",";font-size:14px;font-weight:600;margin-bottom:6px;&:lang(en){width:max-content;}}.intro-content{color:",";}}}}"],e=>e.theme.color_bg_border_02,e=>e.theme.color_bg_white,e=>e.theme.color_bg_border_02,e=>e.theme.color_bg_border_02,e=>e.theme.color_bg_white,e=>e.theme.color_text_L1,e=>e.theme.color_text_L2);function io(){const e=(0,g.d4)(d.query.getCurrentSharing),i=(0,g.d4)(d.query.getProject),l=(0,g.wA)(),c=(0,s.useCallback)(x=>{l({type:d.entryKey["sharing:currentSharing:update"],payload:{updatedKV:x}})},[l]),w=x=>{l({type:d.entryKey["sharing:currentSharing:update"],payload:{updatedKV:x}}),(0,ut.a0)(i.cid+"_default_sharing_view_mode",x.device_model,ut.qW.String),l({type:d.entryKey["sharing:settingPage:default:confirm"]})},_=()=>{l({type:d.entryKey["sharing:topPageIndex:jump"],payload:{topPageIndex:"edit"}})};return(0,r.jsxs)(Ln,{children:[(0,r.jsx)(Sn,{title:I18N.imockSharing.sharing_setting_title,handleClick:_}),(0,r.jsxs)("div",{className:"basic-content",children:[(0,r.jsx)(Ns,{sharingData:e,updateSharing:w}),(0,r.jsx)(Ss,{isOrgProject:i.is_org_project,sharingData:e,updateSharing:c})]})]})}var is=n(89208);const ks=()=>(0,r.jsx)(Es,{}),Es=L.Ay.div.withConfig({displayName:"Divider__StyledDivider",componentId:"sc-x06h85-0"})(["height:1px;background:",";margin:8px 24px;"],e=>e.theme.color_background_icon_hover2);var ao=n(23089),lo=n(5649),js=n(12246),co=n(51785);function uo(e){const{isOrgProject:i,sharingData:l,updateSharing:c,settingPageType:w}=e,_=l.device_model,x=(0,s.useMemo)(()=>(0,H.R0)(i),[i]),{isHavePassword:k}=Tn(l),E=(0,g.d4)(d.query.getUser),U=(0,g.d4)(d.query.getOrg),$=(0,g.d4)(d.query.getHostType)==="iframe",O=(0,g.wA)(),ae="v8-\u5206\u4EAB\u5F39\u7A97_\u81EA\u5B9A\u4E49\u94FE\u63A5\u6709\u6548\u671F";let oe=!1,P=!1;if(i){const{plan:ft}=(0,jn.rM)(U);oe=[an.Sj.OrgExpired,an.Sj.OrgFree].includes(ft),P=ft!==an.Sj.OrgFull}else{const ft=co.tz.InitialUser(E),vt=ft.planSdk.prototypePlan.getUserPlan(),on=ft.planSdk.prototypePlan.getUserStatus();oe=![an.L1.Lifetime,an.L1.PremiumLifetime].includes(vt)&&on!==an.pZ.Trial,P=![an.L1.Lifetime,an.L1.PremiumLifetime].includes(vt)}const{view_sticky:re,comment_permission:le,wechat:ge,enable_version_record:he,is_first_canvas_open:ye}=l,W=ft=>{let{value:vt}=ft;c({view_access:vt})},xe=()=>{if(k)c({password:""});else{const ft=Cs(6);_e(ft)}O({type:d.entryKey["sharing:function:track"],payload:{operation:k?"\u5173\u95ED\u5BC6\u7801\u4FDD\u62A4":"\u6253\u5F00\u5BC6\u7801\u4FDD\u62A4",viewMode:_,isDefault:!0}})},_e=ft=>/^\s*$/.test(ft)||!/^\w+$/.test(ft)?!1:(c({password:ft}),O({type:d.entryKey["sharing:function:track"],payload:{operation:"\u5237\u65B0\u5BC6\u7801",viewMode:_,isDefault:!0}}),!0),be=()=>{c({wechat:!ge}),O({type:d.entryKey["sharing:function:track"],payload:{operation:ge?"\u5173\u95ED\u8DF3\u8FC7\u79FB\u52A8\u7AEF\u63D0\u793A\u9875":"\u6253\u5F00\u8DF3\u8FC7\u79FB\u52A8\u7AEF\u63D0\u793A\u9875",viewMode:_,isDefault:!0}})},Oe=()=>{c({is_first_canvas_open:!ye}),O({type:d.entryKey["sharing:function:track"],payload:{operation:ye?"\u6253\u5F00\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03":"\u5173\u95ED\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03",viewMode:_,isDefault:!0}})},Le=(0,s.useMemo)(()=>le==="org_member",[le]),Ze=()=>{c({view_sticky:!re,sticky:!re}),O({type:d.entryKey["sharing:function:track"],payload:{operation:re?"\u5173\u95ED\u6279\u6CE8\u53EF\u89C1":"\u6253\u5F00\u6279\u6CE8\u53EF\u89C1",viewMode:_,isDefault:!0}})},We=()=>{c({comment_permission:le==="org_member"?"off":"org_member"}),O({type:d.entryKey["sharing:function:track"],payload:{operation:Le?"\u5173\u95ED\u8BC4\u8BBA":"\u6253\u5F00\u8BC4\u8BBA",viewMode:_,isDefault:!0}})},[Qe,ot]=(0,s.useState)("1"),[at,lt]=(0,s.useState)("forever"),{remainTimeText:St}=En(l,!1,999);(0,s.useEffect)(()=>{l&&l.expire_type==="forever"?lt(l.expire_type):(ot(l.expire_type),lt(St))},[l,St]);const Et=(0,s.useRef)(null),Lt=()=>{if(oe){const ft=(0,js.l)(U,ae);if(U||(ft.mode="org",ft.isSelectOrg=!0),$){const vt="/workspace?payment_param="+(0,It._)(ft);(0,dt.JW)(vt,"_blank","noreferrer")}else MB.global.popupHelper.chargeAsync(ft);return!0}return!1},Pt=ft=>{if(ft==="forever")c({expire_type:ft});else if(Lt()){c({expire_type:"forever"},{isCustom:!0});return}O({type:d.entryKey["sharing:function:track"],payload:{operation:ft==="forever"?"\u6709\u6548\u671F\u6C38\u4E45":"\u6709\u6548\u671F\u81EA\u5B9A\u4E49",viewMode:_,isDefault:!1}}),lt(ft)},Ht=[{label:"\u6C38\u4E45\u6709\u6548",value:"forever"},{label:oe?(0,r.jsxs)("div",{className:"custom-title",children:["\u81EA\u5B9A\u4E49",(0,r.jsx)("span",{className:"vip-title",children:"\u56E2\u961F\u7248"})]}):"\u81EA\u5B9A\u4E49",value:"custom"}],gt=ft=>/^([1-9]\d{0,3}|9999)$/.test(ft),jt=ft=>{const vt=ft.target.value;if(!gt(vt))try{MB.notice({text:"\u8F93\u5165\u503C\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165",type:"warning"});return}catch(Jn){window.top.postMessage(JSON.stringify({sharingMessage:"sharing:notice"}),"*")}c({expire_type:vt}),ot(vt)},Ot=()=>{const ft=(0,js.l)(U,"\u7F16\u8F91\u533A_v8\u539F\u578B\u5206\u4EAB_\u7248\u672C\u8BB0\u5F55\u5F00\u5173","v8proto-share-set-version",!0);if(ft.immediateUse="org_full",$){const vt="/workspace?payment_param="+(0,It._)(ft);(0,dt.JW)(vt,"_blank","noreferrer")}else MB.global.popupHelper.chargeAsync(ft)},Jt=()=>{P?Ot():(c({enable_version_record:!he}),O({type:d.entryKey["sharing:function:track"],payload:{operation:he?"\u5173\u95ED\u67E5\u770B\u7248\u672C\u8BB0\u5F55":"\u6253\u5F00\u67E5\u770B\u7248\u672C\u8BB0\u5F55",viewMode:_,isDefault:!1}}))};return(0,r.jsxs)(s.Fragment,{children:[(0,r.jsxs)(vs,{children:[l.type==="advanced"&&i&&(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.permission_access}),(0,r.jsx)(Hn.A,{name:"access",optionList:x,value:l.view_access,onChange:W})]}),(0,r.jsxs)("div",{className:"line-item "+(i?"mt":""),children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.permission_password_protect}),(0,r.jsxs)("div",{className:"password-input-item",children:[k&&(0,r.jsx)(so,{value:l.password,onSubmit:_e}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:k,onChange:xe})]})]}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.permission_link_expired}),(0,r.jsxs)("div",{className:"item-expired",children:[at==="custom"&&(0,r.jsx)(lo.A,{className:"general-input",attr:"width",value:Qe,cursorDirection:!1,min:1,max:9999,rightTitle:"\u5929",actionButton:!1,ref:Et,isSelect:!0,onBlur:jt,disableKeyboardEvents:!0}),(0,r.jsx)(ao.mq,{value:at,attr:"expire_type",placeholder:w==="create"?Qe+"\u5929\u540E\u5931\u6548":at,optionList:Ht,onChange:ft=>Pt(ft)})]})]}),(0,r.jsx)(ks,{}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.note_display}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:re,onChange:Ze})]}),(0,r.jsxs)("div",{className:"line-item mt",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.comment_allowed}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:Le,onChange:We})]}),(0,r.jsxs)("div",{className:"line-item version-control mt",children:[(0,r.jsxs)("label",{className:"item-title",children:[I18N.imockSharing.version_control,P&&(0,r.jsx)("span",{className:"vip-title",onClick:Ot,children:I18N.Common.business_plan})]}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:he,readOnly:P,onChange:Jt})]}),(0,r.jsx)(ks,{}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.share_skip_install_run_inwechat}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:ge,onChange:be})]}),!$&&(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:"\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03"}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:ye,onChange:Oe})]})]}),(0,r.jsx)(_s,{isIframe:$})]})}var ln=n(95549),po=n(25582),_t=n.n(po);const Ts=L.Ay.div.withConfig({displayName:"styles__StyledCheck",componentId:"sc-1wxzhto-0"})(["position:relative;display:flex;align-items:center;cursor:pointer;color:",";&.readonly{cursor:default;}&.is-disabled{cursor:not-allowed;&::after{content:'';position:absolute;left:0;top:0;width:100%;height:100%;background-color:#f2f2f2;}}> input[type=radio],> input[type=checkbox]{position:absolute;opacity:0;width:12px;height:12px;}.Check-state{position:relative;width:12px;height:12px;font-size:12px;display:flex;align-items:center;border:1px solid ",";border-radius:",";transition:all 0.2s ease-in-out;background-color:",";.icon{transition:all 0.2s ease-in-out;position:absolute;top:-1px;left:-1px;}}.Check-label{margin-left:0.33333em;color:",";}&.is-checked .Check-state{background-color:",";border:1px solid ",";color:#fff;.icon{transform:scale(0.833);color:#f2f4f5;}}&:not(.is-checked) .Check-state .icon{speak:none;opacity:0;}"],e=>e.theme.color_text_L2,e=>e.theme.color_text_disabled01,e=>e.isCircle?"50%":"2px",e=>e.theme.color_bg_white,e=>e.theme.color_text_L2,e=>e.theme.color_text_L3,e=>e.theme.color_text_L2);class Kn extends s.PureComponent{constructor(){super(...arguments),(0,ln.A)(this,"onToggle",i=>{const{onChange:l}=this.props;l(i)})}render(){const{className:i,isDisabled:l,readOnly:c,isChecked:w,isCircle:_}=this.props,x=te()(i,{"is-checked":w,"is-disabled":l,readonly:c});return(0,r.jsx)(Ts,{className:x,onClick:this.onToggle,isCircle:_,children:(0,r.jsx)("span",{className:"Check-state",children:(0,r.jsx)(Re.C,{name:"new_replace/box_check"})})})}}(0,ln.A)(Kn,"propTypes",{isChecked:_t().bool,isDisabled:_t().bool,readOnly:_t().bool,onChange:_t().func,label:_t().any,name:_t().string,value:_t().any,className:_t().string,isCircle:_t().bool}),(0,ln.A)(Kn,"defaultProps",{isChecked:!1,label:"",className:"",isCircle:!1,onChange:()=>null,onToggle:()=>null});var as=n(61068),ls=n(69246),As=n(85584);const ho=L.Ay.div.withConfig({displayName:"styles__StyledSharingScreenHiddenList",componentId:"sc-1bmii7a-0"})(["padding:0 17px;.sm-hidden-check{display:flex;height:40px;align-items:center;justify-content:space-between;.item-title{color:",";}}.sm-check{.Check-state{background-color:transparent;border:1px solid ",";}.Check-label{color:",";}&.is-checked .Check-state{background-color:#1684fc;border:1px solid #1684fc;.icon{color:#fff;}}&.is-checked.is-disabled .Check-state{background-color:",";border:1px solid ",";span.icon{color:",";}}&.is-disabled .Check-state{background-color:",";border:1px solid ",";}&.is-disabled::after{background-color:unset;}}.screen-config-header{display:flex;padding-left:8px;flex-direction:row;height:36px;&.is-disabled{cursor:not-allowed;color:#ccc;.screen-name{color:",";}> label{&.is-disabled::after{background-color:unset;}}span.Check-label{color:",";}.content-wrapper{span{color:",";}}}span.Check-label{color:",";margin-left:10px;}.content-wrapper{display:flex;align-items:center;span{&.divider{margin-left:10px;margin-right:11px;}color:",";}}}.screen-list-content{height:210px;overflow-y:overlay;background:",";margin:0 8px;&::-webkit-scrollbar-track{background-color:transparent;}&::-webkit-scrollbar{width:6px;height:6px;}&::-webkit-scrollbar-thumb{background-color:#dbdbdb;border-radius:4px;&:hover{background-color:#cccccc;}}&.dark{&::-webkit-scrollbar-thumb{background-color:#666;border-radius:4px;&:hover{background-color:#cccccc;}}}&::-webkit-scrollbar-corner{background-color:transparent;}&::-webkit-scrollbar{width:6px;}.screen-name{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}&.is-disabled{cursor:not-allowed;color:#ccc;.screen-name{color:",";}}ol,li{list-style:none;}.sm-list{height:auto;overflow-y:overlay;&::-webkit-scrollbar-track{background-color:transparent;}&::-webkit-scrollbar{width:6px;height:6px;}&::-webkit-scrollbar-thumb{background-color:#dbdbdb;border-radius:4px;&:hover{background-color:#cccccc;}}&::-webkit-scrollbar-corner{background-color:transparent;}margin-left:8px;padding-left:15px;.sm-box{display:flex;align-items:center;height:36px;&.is-disabled{cursor:not-allowed;.sm-item{cursor:not-allowed;.expander{svg{color:",";cursor:not-allowed;}}.screen-name{color:",";}}}}.sm-item{display:flex;height:100%;width:100%;align-items:center;cursor:pointer;.sm-icon{min-width:20px;min-height:20px;margin-right:4px;color:",";}.expander{display:flex;align-items:center;justify-content:center;margin-left:-15px;margin-right:1px;width:14px;height:14px;.arrow-icon{margin-right:0;color:#999;width:8px;&:not(.is-expand){transform:rotate(-90deg);}}}.screen-icon{margin-right:4px;}.screen-name{color:",";}}}}"],e=>e.theme.color_text_L1,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_L1,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_disabled01,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_L2,e=>e.theme.color_text_L2,e=>e.theme.color_bg_card,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_L2,e=>e.theme.color_text_L1);class Ls extends s.PureComponent{constructor(i){super(i),(0,ln.A)(this,"handleCheck",x=>{const{onUpdateScreenVisibleList:k,screenVisibleList:E}=this.props,U=new Set(E),Z=(0,As.YU)(x),$=U.has(x.cid);Z.forEach(ae=>{let{cid:oe}=ae;$?U.delete(oe):U.add(oe)});const O=[];for(const ae of U){const oe=this.screenDataMap[ae];oe&&!(0,As.Mo)(oe)&&O.push(oe)}k(Array.from(U))}),(0,ln.A)(this,"onSelectAll",x=>{const{onUpdateScreenVisibleList:k}=this.props;k(x?this.pageKeyList:[])});const{screenMetaList:l}=i,{treeData:c,pageAttrMap:w,pageKeyList:_}=l;this.screenDataMap=w,this.screenTreeData=c,this.pageKeyList=_,this.pageExceptFolderList=(0,H.RF)(w)}componentDidMount(){const i=(0,ut.Yt)("currentJumpingSharingScreenMetaCid",void 0,ut.qW.String);(0,ut.G5)("currentJumpingSharingScreenMetaCid"),i!==void 0&&setTimeout(()=>document.getElementById("app-sharing").querySelector('.content-item[data-cid="'+i+'"]').scrollIntoView(),300)}render(){const{theme:i,screenVisibleList:l,isDisplayScreenAll:c}=this.props,w=(l==null?void 0:l.length)===this.pageKeyList.length,_=(0,H.Mj)(c,l,this.pageExceptFolderList);return(0,r.jsxs)(ho,{children:[(0,r.jsxs)("div",{className:te()("screen-config-header",{"is-disabled":c}),children:[(0,r.jsx)(as.A,{className:"sm-check",label:I18N.imockSharing.select_all,onChange:this.onSelectAll,isDisabled:c,isChecked:w}),(0,r.jsxs)("div",{className:"content-wrapper",children:[(0,r.jsx)("span",{className:"divider",children:"|"}),(0,r.jsx)("span",{children:I18N.imockSharing.select_n_screens.replace("%1",_).replace("%2",this.pageExceptFolderList.length)})]})]}),(0,r.jsx)("div",{className:te()("screen-list-content",{"is-disabled":c,dark:i==="dark"}),children:(0,r.jsx)("div",{className:"sm-list",children:(0,r.jsx)(Os,{theme:i,isDisplayScreenAll:c,screenTreeData:this.screenTreeData,screenVisibleList:l,onCheck:this.handleCheck})})})]})}}(0,ln.A)(Ls,"propTypes",{theme:_t().string,screenMetaList:_t().object,screenVisibleList:_t().array,onUpdateScreenVisibleList:_t().func,isDisplayScreenAll:_t().bool});const Os=e=>{let{theme:i,isDisplayScreenAll:l,screenTreeData:c,screenVisibleList:w,onCheck:_}=e;return(0,r.jsx)(r.Fragment,{children:c.children.map(x=>(0,r.jsx)(Zn,{theme:i,treeNode:x,root:!0,depth:0,screenVisibleList:w,isDisplayScreenAll:l,onCheck:_},x.cid))})};Os.propTypes={theme:_t().string,isDisplayScreenAll:_t().bool,screenTreeData:_t().object,screenVisibleList:_t().array,onCheck:_t().func};class Zn extends s.PureComponent{constructor(i){super(i),(0,ln.A)(this,"handleCheck",l=>{l.stopPropagation();const{isDisplayScreenAll:c}=this.props;if(c)return;const{treeNode:w,onCheck:_}=this.props;_(w)}),(0,ln.A)(this,"handleExpand",l=>{const{isDisplayScreenAll:c}=this.props;c||(l.stopPropagation(),this.setState({expand:!this.state.expand}))}),this.state={expand:!0}}render(){const{isDisplayScreenAll:i,treeNode:l,root:c,depth:w,screenVisibleList:_,onCheck:x,theme:k}=this.props,{children:E,data:U}=l,Z=E.length!==0,{expand:$}=this.state,O=_&&_.includes(l.cid);return(0,r.jsxs)("li",{className:te()("content-item"),"data-cid":l.cid,children:[(0,r.jsxs)("div",{className:te()("sm-box",{"is-display":O,"is-disabled":i}),children:[(0,r.jsx)(Kn,{isDisabled:i,className:"sm-check",isChecked:O,onChange:this.handleCheck}),(0,r.jsx)(Ps,{isDisabled:i,theme:k,root:c,depth:w,showExpander:Z,data:U,expand:$,onClick:this.handleCheck,onExpand:this.handleExpand})]}),E.length>0&&$&&(0,r.jsx)("ol",{className:"sm-child",children:E.map(ae=>(0,r.jsx)(Zn,{theme:k,treeNode:ae,depth:w+1,screenVisibleList:_,isDisplayScreenAll:i,onCheck:x},ae.cid))})]})}}(0,ln.A)(Zn,"propTypes",{theme:_t().string,isDisplayScreenAll:_t().bool,treeNode:_t().object,screenVisibleList:_t().array,depth:_t().number,root:_t().bool,onCheck:_t().func});class Ps extends s.PureComponent{render(){const{depth:i,showExpander:l,data:c,expand:w,onExpand:_,onClick:x,theme:k,isDisabled:E}=this.props,U=19*(i+1);return(0,r.jsxs)("div",{className:te()("sm-item"),style:{paddingLeft:U},onClick:x,children:[l&&(0,r.jsx)("a",{className:"expander",onClick:_,children:(0,r.jsx)(Re.C,{className:te()("arrow-icon",{"is-expand":w}),name:"common/expand"})}),(0,r.jsx)(ls.k,{icon:c.icon,theme:k,disabled:E,isExpand:w}),(0,r.jsx)("span",{className:"screen-name",children:c.name})]})}}(0,ln.A)(Ps,"propTypes",{theme:_t().string,isDisabled:_t().bool,showExpander:_t().bool,data:_t().object,depth:_t().number,expand:_t().bool,onExpand:_t().func,onClick:_t().func});function mo(e){const i=[{value:1,label:I18N.imockSharing.all_pages},{value:2,label:I18N.imockSharing.partial_pages}],{sharingData:l,updateSharing:c}=e,[w,_]=(0,s.useState)(l.screen_visible_switch?2:1),x=(0,g.d4)(d.query.getTheme),k=(0,g.d4)(d.query.getScreenMetaList),E=Z=>{c({screen_visible_list:Z})},U=Z=>{let{value:$}=Z;c({screen_visible_switch:$==="2",screen_visible_list:[]}),_($)};return(0,r.jsxs)(Mn,{children:[(0,r.jsx)("div",{className:"line-item visible-check",children:(0,r.jsx)(Hn.A,{name:"access",optionList:i,value:w,onChange:U})}),k&&(0,r.jsx)(Ls,{theme:x,screenMetaList:k,screenVisibleList:l.screen_visible_list,isDisplayScreenAll:!l.screen_visible_switch,onUpdateScreenVisibleList:E})]})}var Ds=n(79150);const go=e=>{let{handleConfirm:i,handleCancel:l}=e;const c=(0,g.d4)(d.query.getCurrentSharing),w=(0,g.d4)(d.query.getTheme),_=(0,g.d4)(d.query.getScreenMetaList),x=c.type==="default"?!1:c.screen_visible_switch&&(0,H.Mb)(_,c).selectedSize===0,k=()=>{x||i()};return(0,r.jsxs)(Rs,{className:"confirm-bar",children:[(0,r.jsx)(un.jn,{className:te()("confirm",{dark:w==="dark"}),disabled:x,onClick:k,children:I18N.SettingPanel.confirm}),(0,r.jsx)(un.tA,{className:"cancel",onClick:l,children:I18N.imockSharing.cancel})]})},Rs=L.Ay.div.withConfig({displayName:"ConfirmBar__StyledConfirmBar",componentId:"sc-1m75upe-0"})(["&.confirm-bar{display:inline-flex;flex-direction:row-reverse;padding:20px 24px;transition:all 0.2s ease-in-out 0s;position:absolute;bottom:0px;right:0px;.confirm{margin-left:12px;}}"]),Fs=()=>[{key:0,name:"basic",label:I18N.imockSharing.sharing_basic},{key:1,name:"visible",label:I18N.imockSharing.sharing_visible}];function cs(){const e=(0,g.d4)(d.query.getProject),i=(0,g.d4)(d.query.getSettingPageType),l=(0,g.d4)(d.query.getSubSettingPageType),c=(0,g.d4)(d.query.getCurrentSharing),w=(0,g.d4)(d.query.getAdvancedSharingList),_="recommend",x=(0,g.wA)(),[k,E]=(0,s.useState)(l==="basic"?0:1),[U,Z]=(0,s.useState)(c.link_name),[$,O]=(0,s.useState)(!1),ae=We=>{E(We)},oe=(0,s.useCallback)(async(We,Qe)=>{const{isCustom:ot}=Qe||{};x({type:d.entryKey["sharing:settingPage:advanced:confirm"],payload:{updatedKV:We,...ot!==void 0&&{isCustom:ot}}})},[x]),P=(0,s.useCallback)(()=>{x({type:d.entryKey["sharing:topPageIndex:jump"],payload:{topPageIndex:"edit"}}),i==="edit"&&$&&x({type:"sharing:advance:click:track"})},[x,i,$]),re=(0,s.useCallback)((We,Qe)=>{x({type:d.entryKey["sharing:currentSharing:update"],payload:{updatedKV:We}}),i==="edit"&&(O(!0),oe(We,Qe))},[x,i,oe,O]),le=()=>{const We=_e.current.value;We===""?(x({type:d.entryKey["sharing:init"],payload:{sharingToast:I18N.Common.name_cannot_be_empty}}),Le(U)):(0,H.it)({value:We,currentSharing:c,sharingList:w})?(Le(U),x({type:d.entryKey["sharing:init"],payload:{sharingToast:I18N.imockSharing.sharing_name_repeate_wran}})):(Z(We),re({link_name:We}))},{isRenaming:ge,setIsRenaming:he,handleKeyDown:ye,handleBlur:W,handleFocus:xe,inputRef:_e,handleInput:be,inputName:Oe,setInputName:Le}=Gs({name:c.link_name,fBlur:le}),Ze=()=>{he(!0)};return(0,r.jsxs)(Ln,{children:[i==="create"?(0,r.jsx)("div",{className:"header",children:(0,r.jsx)("span",{children:I18N.imockSharing.sharing_create_title})}):(0,r.jsx)(Sn,{title:I18N.imockSharing.sharing_setting_title,handleClick:P}),(0,r.jsxs)("div",{className:"share-link-title",children:[(0,r.jsx)("div",{className:"title-text",children:I18N.SmartFill.title}),(0,r.jsx)("div",{className:te()("name-input",{"is-renaming":ge}),children:ge?(0,r.jsx)("form",{children:(0,r.jsx)("input",{value:Oe,onFocus:xe,onChange:be,ref:_e,onBlur:W,onKeyPress:ye})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{onClick:Ze,children:U}),(0,r.jsx)(Ds.A,{size:20,name:"sharing/pen_20",className:"input-icon",onClick:Ze})]})})]}),(0,r.jsx)(Ns,{sharingData:c,updateSharing:re}),(0,r.jsxs)("div",{className:"tabs",children:[(0,r.jsx)(is.A,{className:"sharing-nav",activeIndex:k,onTabChange:ae,layoutV9:_,children:Fs().map(We=>{let{key:Qe,name:ot,...at}=We;return(0,r.jsx)(is.n,{...at,active:k===Qe,className:te()({visible:ot==="visible",basic:ot==="basic"})},Qe)})}),(0,r.jsx)("div",{})]}),k===0?(0,r.jsx)(uo,{isOrgProject:e.is_org_project,sharingData:c,updateSharing:re,settingPageType:i}):(0,r.jsx)(mo,{sharingData:c,updateSharing:re}),i==="create"&&(0,r.jsx)(go,{handleConfirm:oe,handleCancel:P})]})}function fo(){return(0,g.d4)(d.query.getCurrentSharing).type==="default"?(0,r.jsx)(io,{}):(0,r.jsx)(cs,{})}const qt=L.Ay.div.withConfig({displayName:"styles__StyledSharingToast",componentId:"sc-lv8qo5-0"})(["z-index:1;border-radius:4px;position:absolute;display:flex;align-items:center;padding:6px 10px;color:#FFF;font-size:14px;font-family:PingFang SC;font-style:normal;font-weight:400;line-height:24px;background:#454647;top:72px;left:50%;transform:translateX(-50%);svg{width:21px;height:21px;margin-right:4px;}"]),yo=(0,s.memo)(function(){const i=(0,g.d4)(d.query.getSharingToast),l=(0,g.wA)();return(0,s.useEffect)(()=>{setTimeout(()=>{l({type:d.entryKey["sharing:init"],payload:{sharingToast:""}})},1500)},[l]),i?(0,r.jsxs)(qt,{children:[(0,r.jsx)(Re.C,{name:"sharing/toast_waring",isColorPure:!0}),i]}):null}),xo=L.Ay.div.withConfig({displayName:"styles__StyledSharingQrCode",componentId:"sc-17am38q-0"})(["display:flex;align-items:center;justify-content:center;flex-direction:column;height:calc(100% - ","px);p{margin-top:16px;color:","}"],fe.gE,e=>e.theme.color_text_L3);var Gn=n(37810);const bo=L.Ay.div.withConfig({displayName:"styles__StyledQRcodeBox",componentId:"sc-1xfoi8f-0"})(["display:flex;justify-content:center;align-items:center;background-color:",";border-radius:8px;width:180px;height:180px;filter:",";border:1px solid ",";.qrcode{width:160px;height:160px;}&.qrcode-and-icon{.p-icon-box{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:24px;height:24px;overflow:hidden;background-color:#f2f2f3;border-radius:2px;img{display:block;width:100%;height:100%;}}}"],e=>e.theme.color_sharing_qrcode_bg,e=>e.theme.color_sharing_qrcode_filter,e=>e.theme.color_share_del_confirm_border_color);function Bs(e){const{isQRcodePanel:i,text:l,project:c}=e,w=c.icon||null;return i?(0,r.jsxs)(bo,{className:"qrcode-and-icon",children:[(0,r.jsx)(Gn.A,{className:"qrcode",text:l,width:160,height:160}),w&&(0,r.jsx)("div",{className:"p-icon-box",children:(0,r.jsx)("img",{src:w})})]}):null}const vo=()=>{const e=(0,g.wA)(),i=(0,g.d4)(d.query.getCurrentSelectSharing),l=(0,g.d4)(d.query.getProject),c=(0,g.d4)(d.query.getHostCurrentScreen),w=c==null?void 0:c.cid,_=(0,s.useMemo)(()=>(0,H.O8)(l,i,w,!0),[i,l,w]),x=(0,s.useCallback)(()=>{e({type:d.entryKey["sharing:topPageIndex:jump"],payload:{topPageIndex:"edit"}})},[e]);return(0,r.jsxs)(s.Fragment,{children:[(0,r.jsx)(Sn,{title:I18N.imockSharing.qr_code,handleClick:x}),(0,r.jsxs)(xo,{children:[(0,r.jsx)(Bs,{isQRcodePanel:!0,project:l,text:_}),(0,r.jsx)("p",{children:I18N.imockSharing.qr_tips})]})]})},_o=(0,s.memo)(vo),ds=()=>{var e;const i=(0,g.wA)(),l=(0,g.d4)(d.query.getHostSharingData),c=(0,g.d4)(d.query.getProject),w=c==null?void 0:c.is_org_project,{link_name:_,view_access:x}=l,k=(0,g.d4)(d.query.getUser),E=(0,g.d4)(d.query.getHostType),U=(0,g.d4)(d.query.getIsEditMode),Z=E==="iframe"||U,$=(0,g.d4)(d.query.getCanEditByUser),O=(0,g.d4)(d.query.getHostCurrentScreen),ae=O==null?void 0:O.cid,{isURLCopied:oe,handleCopyURL:P}=es({project:c,sharing:l,user:k}),re=Z&&$,le=(0,s.useMemo)(()=>[{name:"sharing/QRcode_icon",toolTipName:I18N.imockSharing.qr_code,className:Dt.QRcode,tempDisabled:!1,isBorder:!0},{name:"sharing/embed_icon",toolTipName:I18N.imockSharing.insert,className:Dt.embed,tempDisabled:!1,isBorder:!0}],[]),ge=(0,s.useCallback)(()=>{const ye=(0,H.O8)(c,l,ae);(0,wn.$)(ye),P(),i({type:d.entryKey["sharing:function:track"],payload:{operation:"\u9AD8\u7EA7\u5206\u4EAB\u590D\u5236\u94FE\u63A5",viewMode:l.device_model,isDefault:!1}})},[i,l,P,ae,c]),he=(0,s.useCallback)((ye,W)=>{if(!(ye!=null&&ye.currentTarget))return;const xe=l,{device_model:_e}=l;switch(W){case Dt.QRcode:ye.stopPropagation(),i({type:d.entryKey["sharing:init"],payload:{topPageIndex:"qrCode",currentSelectSharing:xe}}),i({type:d.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u626B\u7801",viewMode:_e,isDefault:!1}});break;case Dt.embed:i({type:d.entryKey["sharing:init"],payload:{topPageIndex:"embed",currentSelectSharing:xe}}),i({type:d.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u5D4C\u5165\u4E09\u65B9",viewMode:_e,isDefault:!1}});break;default:break}},[i,l]);return(0,r.jsxs)(ss,{children:[(0,r.jsxs)("div",{className:"default-top",children:[(0,r.jsx)("div",{className:"switch-content",children:_}),(0,r.jsxs)(mn,{className:te()("item-center","default-link",!re&&"userNoEdit"),children:[(0,r.jsx)("div",{className:te()("left","sharingV2-click-visible",{"normal-hover":!0},!w&&"can-not-edit"),children:(0,r.jsxs)("div",{className:"view-access-select",children:[(0,r.jsx)("div",{className:"chore",children:I18N.imockSharing.permission_access}),(0,r.jsx)("div",{className:"access-name",children:(e=I18N.imockSharing[fe.nQ[x]])!=null?e:"missing data"}),w&&(0,r.jsx)(Re.C,{size:24,name:"sharing/dropdown_24"})]})}),(0,r.jsxs)(tn,{type:"button",className:te()("copy","sharingV2-click-visible","url-copy-button","is-state-"+Number(oe)),onClick:ge,children:[(0,r.jsx)("span",{className:"state state-0",children:l.password&&l.password.length>0?I18N.imockSharing.copy_url_pwd:I18N.imockSharing.copy_url}),(0,r.jsx)("span",{className:"state state-1",children:(0,r.jsx)(Re.C,{name:"new_replace/check",className:"copy-check"})})]})]})]}),(0,r.jsx)("div",{className:"default-bottom",children:(0,r.jsxs)("div",{className:"bottom-left",children:[(0,r.jsx)("div",{children:I18N.imockSharing.share_mode}),le.map((ye,W)=>(0,r.jsx)(_n,{className:te()("share-icon",ye.className),name:ye.name,type:ye.className,toolTipName:ye.toolTipName,onClick:he,tempDisabled:ye.tempDisabled,isColorPure:ye.isColorPure,isBorder:ye.isBorder,size:24},W))]})})]})},wo=(0,s.memo)(ds),Co=()=>{const e=(0,g.d4)(d.query.getTopPageIndex),i=(0,g.d4)(d.query.getTabIndex),l=(0,g.d4)(d.query.getSharingToast),c=(0,g.d4)(d.query.getHostSharingData),w=!c||c.type==="default",_=(0,g.d4)(d.query.getProject),x=(0,g.d4)(d.query.getIsEditMode),E=(0,g.d4)(d.query.getHostType)==="iframe"||x,[U,Z]=(0,s.useState)(c);return(0,s.useEffect)(()=>{const{sharing:$}=(0,D.Y5)(_);Z($)},[c,_]),(0,r.jsxs)(to,{children:[e==="edit"&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Vt,{}),i===0?!E&&!w?(0,r.jsx)(wo,{}):U?(0,r.jsx)(eo,{defaultShare:U}):(0,r.jsx)(r.Fragment,{}):(0,r.jsx)(ns,{})]}),e==="embed"&&(0,r.jsx)(kt,{}),e==="setting"&&(0,r.jsx)(fo,{}),e==="qrCode"&&(0,r.jsx)(_o,{}),l&&(0,r.jsx)(yo,{})]})},bn=(0,s.memo)(Co),Yn=/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z0-9-]{2,63}$/i,On=/^(?:\+?86)?1(?:3\d{3}|5[^4\D]\d{2}|8\d{3}|7(?:[0-35-9]\d{2}|4(?:0\d|1[0-2]|9\d))|9[0-35-9]\d{2}|6[2567]\d{2}|4[579]\d{2})\d{6}$/;var us=n(19249);const ps=async(e,i)=>{try{let c=null;return e?c=await(0,us.DE)("/api/dashboard/v4/users/search?email="+e):i&&(c=await(0,us.DE)("/api/dashboard/v4/users/search?mobile="+i)),c}catch(c){var l;return c!=null&&(l=c.message)!=null&&l.includes("failed with 404")?{}:null}};var hs=n(35603);const So=(0,L.Ay)(hs.A).withConfig({displayName:"styles__StyledPermissionTipDropdown",componentId:"sc-1ofnwys-0"})(["display:flex;align-items:center;justify-content:center;line-height:14px;button{.permisison-tip-icon{margin:5px 6px 0 6px;&:hover{path{fill:","}}}}"],e=>e.theme.color_project_access_tip_hover),ms=(0,L.DU)(['#IBOT_DROPDOWN_MENU_ROOT{.DropdownMenuBase{.permission-tip{.content{min-height:fit-content;padding:0;background-color:#333;border:1px solid #4f5052;.multi-line{padding:14px 14px 20px 14px;p,ul{padding:0;padding-left:3px;}&::before{content:"";width:0;height:0;border:11px solid transparent;border-bottom-color:#4F5052;position:absolute;top:-1px;left:',';margin-top:-18px;border-bottom-width:9px;}&::after{content:"";width:0;height:0;border:9px solid transparent;border-bottom-color:#333;position:absolute;top:3px;left:',";margin-top:-18px;border-bottom-width:7px;}}.single-line{padding:4px 12px;p{margin:0;padding:0;}}h4{font-weight:500;font-size:14px;line-height:16px;margin:0;margin-bottom:10px;}ul{text-align:justify;list-style:none;li{list-style:none;margin-bottom:8px;&:last-child{margin-bottom:0px;}}}.desc{font-weight:400;font-size:12px;line-height:18px;white-space:nowrap;.strong{font-weight:500;}.link{margin-left:4px;text-align:left;color:#1684fc;cursor:pointer;&:hover{text-decoration:underline;}}}.extra{white-space:nowrap;margin:12px 0 8px;font-weight:400;font-size:12px;color:rgba(255,255,255,0.5);}}}}}"],e=>e.isOnlyMemberManager?"66px":"88px",e=>e.isOnlyMemberManager?"68px":"90px");let gn=function(e){return e.OrgSpace="orgspace-permission-tip",e.OrgSpaceLimiter="orgspace-limiter-permission-tip",e.RootFolder="root-folder-permission-tip",e.SubFolder="sub-folder-permission-tip",e.Project="project-permission-tip",e}({});const Mo=e=>{const{tipType:i,position:l="center",className:c,isOnlyMemberManager:w=!1}=e,[_,x]=(0,s.useState)({}),k=(0,g.d4)(Ie.il);(0,s.useEffect)(()=>{const U=document.querySelector(".sharingBoxV2 ");if(U){const{left:Z,top:$}=U.getBoundingClientRect();x({left:Number(Z)+13,top:Number($)+29})}},[]);const E=()=>{switch(i){case gn.Project:return(0,r.jsxs)("section",{className:"multi-line",children:[(0,r.jsxs)("h4",{children:["\u{1F537} ",I18N.imockSharing.permissions]}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_manager,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_manager_file]}),(0,r.jsxs)("li",{children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_can_edit,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_can_edit_file]}),(0,r.jsxs)("li",{children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_only_view,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_only_view_file]}),(0,r.jsxs)("li",{children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_ban_view,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_ban_view_file]})]}),(0,r.jsx)("p",{className:"extra",dangerouslySetInnerHTML:{__html:I18N.imockSharing.permissions_automatically_sync_desc}})]});case gn.SubFolder:return(0,r.jsxs)("section",{className:"multi-line",children:[(0,r.jsx)("h4",{children:I18N.imockSharing.permissions}),(0,r.jsxs)("p",{className:"extra",children:["\u6839\u6587\u4EF6\u5939\u6210\u5458\u53CA\u6743\u9650\u81EA\u52A8\u540C\u6B65\u81F3\u7EC4\u5185\uFF0C\u82E5\u6709\u8C03\u6574\uFF0C",(0,r.jsx)("br",{}),"\u8BF7\u524D\u5F80\u6839\u6587\u4EF6\u5939\u8FDB\u884C\u6210\u5458\u53CA\u6743\u9650\u8C03\u6574"]})]});case gn.RootFolder:return(0,r.jsxs)("section",{className:"multi-line",children:[(0,r.jsx)("h4",{children:I18N.imockSharing.permissions}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_manager,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_manager_folder]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_can_edit,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_can_edit_folder]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_only_view,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_only_view_folder]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_ban_view,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_ban_view_folder]})]})]});case gn.OrgSpace:return(0,r.jsxs)("section",{className:"multi-line",children:[(0,r.jsx)("h4",{children:I18N.imockSharing.permissions}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_space_manager,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_space_manager_desc]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_space_collaboration_member,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_space_collaboration_member_desc]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_space_review_member,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_space_review_member_desc]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_space_unregistered_member,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_space_unregistered_member_desc]})]})]});case gn.OrgSpaceLimiter:return(0,r.jsx)("section",{className:"single-line",children:(0,r.jsxs)("p",{className:"desc",children:["\u8BE5\u6210\u5458\u53EA\u5BF9\u56E2\u961F\u5185\u6307\u5B9A\u6587\u4EF6/\u6587\u4EF6\u5939\u53EF\u67E5\u770B/\u7F16\u8F91 ",(0,r.jsx)("a",{href:"/hc/articles/222",className:"link",children:" \u67E5\u770B\u8BE6\u60C5"})]})});default:break}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(So,{opener:(0,r.jsx)("svg",{className:"permisison-tip-icon",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.33341 7.00008C2.33341 4.42275 4.42275 2.33341 7.00008 2.33341C9.57741 2.33341 11.6667 4.42275 11.6667 7.00008C11.6667 9.57741 9.57741 11.6667 7.00008 11.6667C4.42275 11.6667 2.33341 9.57741 2.33341 7.00008ZM7.00008 1.16675C3.77842 1.16675 1.16675 3.77842 1.16675 7.00008C1.16675 10.2217 3.77842 12.8334 7.00008 12.8334C10.2217 12.8334 12.8334 10.2217 12.8334 7.00008C12.8334 3.77842 10.2217 1.16675 7.00008 1.16675ZM6.00842 5.82717V5.69267C6.00912 5.53262 6.06636 5.35509 6.19406 5.22162C6.31201 5.09835 6.54189 4.95841 6.99673 4.95841C7.42386 4.95841 7.72087 5.16234 7.87234 5.39386C8.0341 5.6411 8.00223 5.84856 7.92545 5.9522C7.82971 6.08146 7.71083 6.20085 7.56715 6.33019C7.52345 6.36954 7.46957 6.41642 7.4118 6.46669L7.41179 6.4667C7.30849 6.55659 7.19276 6.6573 7.10047 6.74511C6.77279 7.05691 6.41686 7.49208 6.41686 8.16675L6.41875 8.46216L7.5854 8.45467L7.58354 8.16515C7.58397 7.96563 7.66576 7.81765 7.90469 7.5903C7.985 7.51388 8.0572 7.45142 8.1398 7.37995L8.13984 7.37992C8.20093 7.32707 8.26773 7.26928 8.34772 7.19727C8.51407 7.04752 8.69919 6.8677 8.86295 6.64662C9.30517 6.0496 9.20176 5.29489 8.84863 4.75513C8.48521 4.19965 7.82399 3.79175 6.99673 3.79175C6.28084 3.79175 5.72583 4.02342 5.35109 4.41508C4.98633 4.79631 4.84308 5.27667 4.84175 5.68971V5.82717H6.00842ZM6.41874 9.04175V10.2107H7.58541V9.04175H6.41874Z",fill:"#999999"})}),mode:"dark",menuX:l,shouldOpenOnHover:!0,hoverDelay:100,menuClassName:te()("permission-tip",c&&c),menu:E(),menuBaseStyle:_}),(0,r.jsx)(ms,{isOnlyMemberManager:w})]})};var zs=n(79186);const No=L.Ay.div.withConfig({displayName:"styles__StyledReadOnlyOption",componentId:"sc-1j0j1bo-0"})(["min-width:65px;text-align:start;padding-left:10px;cursor:default;&:lang(en){width:auto;}&.disabled{cursor:not-allowed;}.label{color:#9EA9BC;font-size:12px;}"]),Us=(0,L.Ay)(zs.Ay).withConfig({displayName:"styles__StyledPermissionSelector",componentId:"sc-1j0j1bo-1"})(["min-width:65px;&:lang(en){width:auto;}&.is-disabled{opacity:1;.caret{display:none;}button{span{color:#9EA9BC;}}}&.is-open .caret{transform:rotate(180deg);svg{transform:translateY(-3px) rotate(-45deg);}}button{text-align:right;span{color:#35445D;font-size:12px;}}.caret{margin:0 0 0 6px;transition:transform .2s;svg{width:6px;height:6px;margin:1px;border:solid 1px #35445D;border-top-color:transparent;border-right-color:transparent;transform:translateY(0) rotate(-45deg);path{opacity:0;}}}"]),Io=(0,L.DU)([".permission-select.CheckSelectMenu{padding:8px;border-radius:8px;border:1px solid ",";color:",";background:",";box-shadow:",";&:lang(en){min-width:136px;}&:not(.is-empty){padding:8px;}.SelectOption{height:24px;margin-bottom:2px;padding:0;color:",";border-radius:4px;line-height:24px;&:last-child{margin-bottom:0;}&.is-active,&:not(.empty-msg):not(.is-disabled):hover{background-color:",";color:",";}&:hover{color:",";}> span.Ellipsis{min-width:80px;padding:0 10px;margin-left:20px;&:lang(en){width:calc(100% - 22px);}}> .svg-icon{position:absolute;margin:0 0 0 10px;path{fill:",";}}&[data-value=\"__mb_delete_permission\"]{position:relative;margin-top:9px;&::before{position:absolute;top:-5px;right:0;left:0;border-top:solid 1px rgba(255,255,255,.1);content:'';}}}}"],e=>e.theme.color_bg_border_01,e=>e.theme.color_text_L1,e=>e.theme.color_bg_white,e=>e.theme.shadow_m,e=>e.theme.color_text_L1,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1);var Ws=n(46219);const Hs=e=>{let i=[],l={};switch(e){case ke.W.OrgSpace:i=Q,l=Me.FU;break;case ke.W.Folder:i=Y,l=Me.lY;break;case ke.W.Project:i=me,l=Me.au;break;default:break}return i.map(c=>({...c,permission:(0,I.pf)(c.value,l)}))},ko=(e,i)=>{let l={};switch(e){case ke.W.OrgSpace:return i;case ke.W.Folder:l=je;break;case ke.W.Project:l=Ne;break;default:return i}return l[i]||i},Eo=e=>{switch(e){case ke.W.OrgSpace:return I18N.imockSharing.permission_no_right_project;case ke.W.Folder:return I18N.imockSharing.permission_no_right_folder;case ke.W.Project:return I18N.imockSharing.permission_no_right_project;default:return I18N.imockSharing.permission_no_right}},jo=e=>{const{value:i,optionList:l,handleChange:c}=e,w=(0,s.useRef)(null),[_,x]=(0,s.useState)(!0),k=l.filter(U=>U.value===i),E=(0,s.useCallback)(()=>{w.current&&w.current.close()},[]);return(0,s.useEffect)(()=>(document.body.addEventListener("wheel",E),()=>{document.body.removeEventListener("wheel",E)}),[E]),(0,s.useEffect)(()=>{const U=setTimeout(()=>{x(!1)},1e3);return()=>{clearTimeout(U)}},[]),_?(0,r.jsx)(Us,{unstyled:!0,ref:w,placeholder:I18N.imockSharing.submit,menuTheme:"check",value:i,menuX:"right",onChange:c,optionList:k,menuClassName:"permission-select"}):(0,r.jsx)(Us,{unstyled:!0,ref:w,placeholder:I18N.imockSharing.submit,menuTheme:"check",value:i,menuX:"right",onChange:c,optionList:l,menuClassName:"permission-select"})},To=(0,s.memo)(jo);var Ao=n(46710);const Lo=(e,i)=>{const{isSelf:l,userCurrentSpacePermission:c,userCurrentScopePermisson:w,memberCurrentScopePermisson:_,memberTopScopePermisson:x,memberOrgPermisson:k,memberIsSpaceManager:E}=i;return e.map(Z=>{let{value:$,label:O,permission:ae}=Z,oe=!1;const P=ae.roleName.includes("ban_viewer");return c.isManager&&l?oe=P:w.isMember?w.level<_.level||w.level<ae.level?oe=!0:(!x.isMember&&x.isViewer&&!k.isMember&&ae.isMember&&(oe=!0),P&&(oe=!w.isManager||l||E)):oe=!0,Object.assign({},{value:$,label:I18N.imockSharing[O]||O},{isDisabled:oe})})},gs=e=>{const{readOnlyTooltip:i,isReset:l=!1,member:c,isOnlyOneManager:w,permissionScope:_,onPermissionChange:x,currentOrg:k}=e;let{readOnly:E}=e,U=E,Z=i;const $=Hs(_),{permissionMap:O}=c,{memberCurrentScopePermisson:ae,userCurrentScopePermisson:oe,userCurrentSpacePermission:P,currentScopeRoleName:re,isSelf:le,userIsSpaceLimiter:ge,memberIsSpaceLimiter:he}=O;let ye=Lo($,O);_===ke.W.OrgSpace&&!he&&(ye=ye.filter(_e=>_e.value!=="space_limiter")),(_===ke.W.OrgSpace||he)&&!ae.isUnknown&&!ae.isInherited&&!l&&(le&&!ae.isUnknown||oe.isManager&&ye.push({value:mt,label:I18N.imockSharing.remove,isDisabled:!1})),!U&&_===ke.W.OrgSpace&&ge&&!l&&(E=!0,U=!0),!U&&([ke.W.Project,ke.W.Folder].includes(_)||ae.isViewer)&&!l&&(oe.isMember?oe.level<ae.level?(U=!0,Z="\u65E0\u6743\u66F4\u6539\u6743\u9650\u9AD8\u7684\u6210\u5458\u6743\u9650"):ae.isManager&&w?(U=!0,Z=Eo(_)):(_===ke.W.OrgSpace&&!oe.isManager||[ke.W.Project,ke.W.Folder].includes(_)&&!oe.isManager&&ae.isBanned)&&(U=!0):P!=null&&P.isManager&&le||(U=!0));let W=ko(_,re);l&&(W="");const xe=_e=>{U||x(c,_e)};if(k){const{protoSeat:_e}=(0,Ao.rA)(k,Number(c.user_id)),Oe=_e?"":"(\u65E0\u5E2D\u4F4D)";ye.map(Le=>{(Le.value==="project_manager"||Le.value==="project_member")&&(Le.label+=Oe)})}if(U||E){const _e=ye.find(be=>be.value===W);return _e?(0,r.jsx)(No,{className:te()("read-only-option",!E&&U&&"disabled"),children:(0,r.jsx)(Ws.A,{content:Z,position:"bottom",children:(0,r.jsx)("span",{className:"label",children:_e.label})})}):null}return(0,r.jsx)(To,{value:W,handleChange:xe,optionList:ye})},$n=(0,s.memo)(gs),Vs=(0,L.DU)(["#IBOT_DROPDOWN_MENU_ROOT{.add-outer-member-tip{.content{min-height:fit-content;padding:2px 8px;background-color:#333;.desc{white-space:nowrap;.copy-text{text-decoration:underline;cursor:pointer;}}}}}"]),fs=L.Ay.section.withConfig({displayName:"styles__StyledMemberList",componentId:"sc-188anjy-0"})(['margin-bottom:10px;width:406px;&.sharing{.members > .enterTip > span > div{display:flex;flex-direction:column;align-items:center;justify-content:center;}}&[data-expanded="false"]{.members{display:none;}.caption{margin-bottom:15px;}}&+.member-list{margin-top:10px;}.caption{position:relative;display:flex;align-items:center;justify-content:flex-start;padding:0;font-weight:400;font-size:14px;line-height:20px;color:',";span{color:",";}.invite-item{display:flex;align-items:center;height:32px;text-align:center;font-family:PingFang SC;width:fit-content;border-radius:6px;font-weight:500;font-size:14px;color:",";cursor:pointer;position:absolute;right:20px;z-index:1;&:hover{color:",";}&:active{color:",";}.svg-icon{width:32px;color:inherit;& > *{fill:currentColor;}}}.btn-add-collaborator{position:absolute;right:0;display:flex;align-items:center;color:#1883FB;.svg-icon{margin-right:4px;width:12px;height:12px;}}.btn-toggle-expand{margin-left:10px;user-select:none;color:#1883FB;cursor:pointer;&.with-red-point{position:relative;&::after{content:'';background:rgb(228,33,33);position:absolute;width:6px;height:6px;top:-2px;left:100%;border-radius:50%;}}}}.members{.item{display:flex;align-items:center;justify-content:space-between;padding:10px 20px;&:hover{background:",";border-radius:4px;}.title,.account,.Select{button{font-weight:500;font-size:14px;line-height:20px;letter-spacing:1.2px;color:#545F6F;.Ellipsis{max-width:100px;&:lang(en){max-width:none;}}}}.title{flex:1;display:flex;justify-content:flex-start;align-items:center;.avatar{width:30px;height:30px;overflow:hidden;border-radius:50%;margin-right:12px;border:1px solid ",";&.avater-name{text-align:center;background:rgb(22,133,252);justify-content:center;color:white;font-size:14px;text-align:center;display:flex;align-items:center;}}.name{width:120px;overflow:hidden;color:#1f292e;white-space:nowrap;text-overflow:ellipsis;}}.account{flex:1;width:160px;color:#999;}.register{display:inline-block;text-align:center;height:22px;line-height:22px;border:1px solid rgb(41,141,248);box-sizing:border-box;border-radius:4px;width:48px;color:rgb(41,141,248);font-size:12px;font-weight:400;margin-left:8px;position:relative;color:red;&:lang(en){display:block;background:#F1F8FF;border:none;color:#1684FC;padding:3px 4px;width:max-content;margin-left:10px;font-weight:500;font-size:12px;line-height:12px;height:auto;}}}}.invite{.item{.title{.name{width:150px;&:lang(en){width:max-content;flex:1;display:flex;align-items:center;}}}.account{width:150px;display:inline-block;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;word-break:break-all;&:lang(en){margin:10px;}}}.addOuter{color:#999999;width:108px;text-align:right;.add{font-size:12px;margin-right:2px;}button{color:rgb(91,107,115);position:relative;top:2px;left:4px;}}.enterTip{font-size:12px;color:",";}}.inviteJoin{border-radius:4px;background:#298DF8;color:#fff;font-size:12px;font-weight:400;padding:7px 11px;cursor:pointer;word-break:keep-all;&:lang(en){padding:5px 11px;}}&.dark{.members .item .register{&:lang(en){background:rgba(22,132,252,0.1);}}}"],e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_hover,e=>e.theme.color_text_link_hover,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_share_member__border_color,e=>e.theme.color_AI_Text_Auto_fill_path_hover);var a=n(94586),t=n(94827),o=n(18941),m=Number.isNaN||function(i){return typeof i=="number"&&i!==i};function z(e,i){return!!(e===i||m(e)&&m(i))}function de(e,i){if(e.length!==i.length)return!1;for(var l=0;l<e.length;l++)if(!z(e[l],i[l]))return!1;return!0}function Ae(e,i){i===void 0&&(i=de);var l,c=[],w,_=!1;function x(){for(var k=[],E=0;E<arguments.length;E++)k[E]=arguments[E];return _&&l===this&&i(k,c)||(w=e.apply(this,k),_=!0,l=this,c=k),w}return x}const Ee=Ae;var Ue=typeof performance=="object"&&typeof performance.now=="function",Ke=Ue?function(){return performance.now()}:function(){return Date.now()};function Fe(e){cancelAnimationFrame(e.id)}function Xe(e,i){var l=Ke();function c(){Ke()-l>=i?e.call(null):w.id=requestAnimationFrame(c)}var w={id:requestAnimationFrame(c)};return w}var we=-1;function Ce(e){if(e===void 0&&(e=!1),we===-1||e){var i=document.createElement("div"),l=i.style;l.width="50px",l.height="50px",l.overflow="scroll",document.body.appendChild(i),we=i.offsetWidth-i.clientWidth,document.body.removeChild(i)}return we}var ze=null;function $e(e){if(e===void 0&&(e=!1),ze===null||e){var i=document.createElement("div"),l=i.style;l.width="50px",l.height="50px",l.overflow="scroll",l.direction="rtl";var c=document.createElement("div"),w=c.style;return w.width="100px",w.height="100px",i.appendChild(c),document.body.appendChild(i),i.scrollLeft>0?ze="positive-descending":(i.scrollLeft=1,i.scrollLeft===0?ze="negative":ze="positive-ascending"),document.body.removeChild(i),ze}return ze}var pt=150,Tt=function(i){var l=i.columnIndex,c=i.data,w=i.rowIndex;return w+":"+l},Mt=null,Kt=null,Ut=null;function Zt(e){var i,l=e.getColumnOffset,c=e.getColumnStartIndexForOffset,w=e.getColumnStopIndexForStartIndex,_=e.getColumnWidth,x=e.getEstimatedTotalHeight,k=e.getEstimatedTotalWidth,E=e.getOffsetForColumnAndAlignment,U=e.getOffsetForRowAndAlignment,Z=e.getRowHeight,$=e.getRowOffset,O=e.getRowStartIndexForOffset,ae=e.getRowStopIndexForStartIndex,oe=e.initInstanceProps,P=e.shouldResetStyleCacheOnItemSizeChange,re=e.validateProps;return i=function(le){(0,o.A)(ge,le);function ge(ye){var W;return W=le.call(this,ye)||this,W._instanceProps=oe(W.props,(0,t.A)(W)),W._resetIsScrollingTimeoutId=null,W._outerRef=void 0,W.state={instance:(0,t.A)(W),isScrolling:!1,horizontalScrollDirection:"forward",scrollLeft:typeof W.props.initialScrollLeft=="number"?W.props.initialScrollLeft:0,scrollTop:typeof W.props.initialScrollTop=="number"?W.props.initialScrollTop:0,scrollUpdateWasRequested:!1,verticalScrollDirection:"forward"},W._callOnItemsRendered=void 0,W._callOnItemsRendered=Ee(function(xe,_e,be,Oe,Le,Ze,We,Qe){return W.props.onItemsRendered({overscanColumnStartIndex:xe,overscanColumnStopIndex:_e,overscanRowStartIndex:be,overscanRowStopIndex:Oe,visibleColumnStartIndex:Le,visibleColumnStopIndex:Ze,visibleRowStartIndex:We,visibleRowStopIndex:Qe})}),W._callOnScroll=void 0,W._callOnScroll=Ee(function(xe,_e,be,Oe,Le){return W.props.onScroll({horizontalScrollDirection:be,scrollLeft:xe,scrollTop:_e,verticalScrollDirection:Oe,scrollUpdateWasRequested:Le})}),W._getItemStyle=void 0,W._getItemStyle=function(xe,_e){var be=W.props,Oe=be.columnWidth,Le=be.direction,Ze=be.rowHeight,We=W._getItemStyleCache(P&&Oe,P&&Le,P&&Ze),Qe=xe+":"+_e,ot;if(We.hasOwnProperty(Qe))ot=We[Qe];else{var at=l(W.props,_e,W._instanceProps),lt=Le==="rtl";We[Qe]=ot={position:"absolute",left:lt?void 0:at,right:lt?at:void 0,top:$(W.props,xe,W._instanceProps),height:Z(W.props,xe,W._instanceProps),width:_(W.props,_e,W._instanceProps)}}return ot},W._getItemStyleCache=void 0,W._getItemStyleCache=Ee(function(xe,_e,be){return{}}),W._onScroll=function(xe){var _e=xe.currentTarget,be=_e.clientHeight,Oe=_e.clientWidth,Le=_e.scrollLeft,Ze=_e.scrollTop,We=_e.scrollHeight,Qe=_e.scrollWidth;W.setState(function(ot){if(ot.scrollLeft===Le&&ot.scrollTop===Ze)return null;var at=W.props.direction,lt=Le;if(at==="rtl")switch($e()){case"negative":lt=-Le;break;case"positive-descending":lt=Qe-Oe-Le;break}lt=Math.max(0,Math.min(lt,Qe-Oe));var St=Math.max(0,Math.min(Ze,We-be));return{isScrolling:!0,horizontalScrollDirection:ot.scrollLeft<Le?"forward":"backward",scrollLeft:lt,scrollTop:St,verticalScrollDirection:ot.scrollTop<Ze?"forward":"backward",scrollUpdateWasRequested:!1}},W._resetIsScrollingDebounced)},W._outerRefSetter=function(xe){var _e=W.props.outerRef;W._outerRef=xe,typeof _e=="function"?_e(xe):_e!=null&&typeof _e=="object"&&_e.hasOwnProperty("current")&&(_e.current=xe)},W._resetIsScrollingDebounced=function(){W._resetIsScrollingTimeoutId!==null&&Fe(W._resetIsScrollingTimeoutId),W._resetIsScrollingTimeoutId=Xe(W._resetIsScrolling,pt)},W._resetIsScrolling=function(){W._resetIsScrollingTimeoutId=null,W.setState({isScrolling:!1},function(){W._getItemStyleCache(-1)})},W}ge.getDerivedStateFromProps=function(W,xe){return it(W,xe),re(W),null};var he=ge.prototype;return he.scrollTo=function(W){var xe=W.scrollLeft,_e=W.scrollTop;xe!==void 0&&(xe=Math.max(0,xe)),_e!==void 0&&(_e=Math.max(0,_e)),this.setState(function(be){return xe===void 0&&(xe=be.scrollLeft),_e===void 0&&(_e=be.scrollTop),be.scrollLeft===xe&&be.scrollTop===_e?null:{horizontalScrollDirection:be.scrollLeft<xe?"forward":"backward",scrollLeft:xe,scrollTop:_e,scrollUpdateWasRequested:!0,verticalScrollDirection:be.scrollTop<_e?"forward":"backward"}},this._resetIsScrollingDebounced)},he.scrollToItem=function(W){var xe=W.align,_e=xe===void 0?"auto":xe,be=W.columnIndex,Oe=W.rowIndex,Le=this.props,Ze=Le.columnCount,We=Le.height,Qe=Le.rowCount,ot=Le.width,at=this.state,lt=at.scrollLeft,St=at.scrollTop,Et=Ce();be!==void 0&&(be=Math.max(0,Math.min(be,Ze-1))),Oe!==void 0&&(Oe=Math.max(0,Math.min(Oe,Qe-1)));var Lt=x(this.props,this._instanceProps),Pt=k(this.props,this._instanceProps),Ht=Pt>ot?Et:0,gt=Lt>We?Et:0;this.scrollTo({scrollLeft:be!==void 0?E(this.props,be,_e,lt,this._instanceProps,gt):lt,scrollTop:Oe!==void 0?U(this.props,Oe,_e,St,this._instanceProps,Ht):St})},he.componentDidMount=function(){var W=this.props,xe=W.initialScrollLeft,_e=W.initialScrollTop;if(this._outerRef!=null){var be=this._outerRef;typeof xe=="number"&&(be.scrollLeft=xe),typeof _e=="number"&&(be.scrollTop=_e)}this._callPropsCallbacks()},he.componentDidUpdate=function(){var W=this.props.direction,xe=this.state,_e=xe.scrollLeft,be=xe.scrollTop,Oe=xe.scrollUpdateWasRequested;if(Oe&&this._outerRef!=null){var Le=this._outerRef;if(W==="rtl")switch($e()){case"negative":Le.scrollLeft=-_e;break;case"positive-ascending":Le.scrollLeft=_e;break;default:var Ze=Le.clientWidth,We=Le.scrollWidth;Le.scrollLeft=We-Ze-_e;break}else Le.scrollLeft=Math.max(0,_e);Le.scrollTop=Math.max(0,be)}this._callPropsCallbacks()},he.componentWillUnmount=function(){this._resetIsScrollingTimeoutId!==null&&Fe(this._resetIsScrollingTimeoutId)},he.render=function(){var W=this.props,xe=W.children,_e=W.className,be=W.columnCount,Oe=W.direction,Le=W.height,Ze=W.innerRef,We=W.innerElementType,Qe=W.innerTagName,ot=W.itemData,at=W.itemKey,lt=at===void 0?Tt:at,St=W.outerElementType,Et=W.outerTagName,Lt=W.rowCount,Pt=W.style,Ht=W.useIsScrolling,gt=W.width,jt=this.state.isScrolling,Ot=this._getHorizontalRangeToRender(),Jt=Ot[0],ft=Ot[1],vt=this._getVerticalRangeToRender(),on=vt[0],Jn=vt[1],Xn=[];if(be>0&&Lt)for(var vn=on;vn<=Jn;vn++)for(var rn=Jt;rn<=ft;rn++)Xn.push((0,s.createElement)(xe,{columnIndex:rn,data:ot,isScrolling:Ht?jt:void 0,key:lt({columnIndex:rn,data:ot,rowIndex:vn}),rowIndex:vn,style:this._getItemStyle(vn,rn)}));var Yt=x(this.props,this._instanceProps),tt=k(this.props,this._instanceProps);return(0,s.createElement)(St||Et||"div",{className:_e,onScroll:this._onScroll,ref:this._outerRefSetter,style:(0,a.A)({position:"relative",height:Le,width:gt,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:Oe},Pt)},(0,s.createElement)(We||Qe||"div",{children:Xn,ref:Ze,style:{height:Yt,pointerEvents:jt?"none":void 0,width:tt}}))},he._callPropsCallbacks=function(){var W=this.props,xe=W.columnCount,_e=W.onItemsRendered,be=W.onScroll,Oe=W.rowCount;if(typeof _e=="function"&&xe>0&&Oe>0){var Le=this._getHorizontalRangeToRender(),Ze=Le[0],We=Le[1],Qe=Le[2],ot=Le[3],at=this._getVerticalRangeToRender(),lt=at[0],St=at[1],Et=at[2],Lt=at[3];this._callOnItemsRendered(Ze,We,lt,St,Qe,ot,Et,Lt)}if(typeof be=="function"){var Pt=this.state,Ht=Pt.horizontalScrollDirection,gt=Pt.scrollLeft,jt=Pt.scrollTop,Ot=Pt.scrollUpdateWasRequested,Jt=Pt.verticalScrollDirection;this._callOnScroll(gt,jt,Ht,Jt,Ot)}},he._getHorizontalRangeToRender=function(){var W=this.props,xe=W.columnCount,_e=W.overscanColumnCount,be=W.overscanColumnsCount,Oe=W.overscanCount,Le=W.rowCount,Ze=this.state,We=Ze.horizontalScrollDirection,Qe=Ze.isScrolling,ot=Ze.scrollLeft,at=_e||be||Oe||1;if(xe===0||Le===0)return[0,0,0,0];var lt=c(this.props,ot,this._instanceProps),St=w(this.props,lt,ot,this._instanceProps),Et=!Qe||We==="backward"?Math.max(1,at):1,Lt=!Qe||We==="forward"?Math.max(1,at):1;return[Math.max(0,lt-Et),Math.max(0,Math.min(xe-1,St+Lt)),lt,St]},he._getVerticalRangeToRender=function(){var W=this.props,xe=W.columnCount,_e=W.overscanCount,be=W.overscanRowCount,Oe=W.overscanRowsCount,Le=W.rowCount,Ze=this.state,We=Ze.isScrolling,Qe=Ze.verticalScrollDirection,ot=Ze.scrollTop,at=be||Oe||_e||1;if(xe===0||Le===0)return[0,0,0,0];var lt=O(this.props,ot,this._instanceProps),St=ae(this.props,lt,ot,this._instanceProps),Et=!We||Qe==="backward"?Math.max(1,at):1,Lt=!We||Qe==="forward"?Math.max(1,at):1;return[Math.max(0,lt-Et),Math.max(0,Math.min(Le-1,St+Lt)),lt,St]},ge}(s.PureComponent),i.defaultProps={direction:"ltr",itemData:void 0,useIsScrolling:!1},i}var it=function(i,l){var c=i.children,w=i.direction,_=i.height,x=i.innerTagName,k=i.outerTagName,E=i.overscanColumnsCount,U=i.overscanCount,Z=i.overscanRowsCount,$=i.width,O=l.instance},Ve=50,st=function(i,l){var c=i.rowCount,w=l.rowMetadataMap,_=l.estimatedRowHeight,x=l.lastMeasuredRowIndex,k=0;if(x>=c&&(x=c-1),x>=0){var E=w[x];k=E.offset+E.size}var U=c-x-1,Z=U*_;return k+Z},et=function(i,l){var c=i.columnCount,w=l.columnMetadataMap,_=l.estimatedColumnWidth,x=l.lastMeasuredColumnIndex,k=0;if(x>=c&&(x=c-1),x>=0){var E=w[x];k=E.offset+E.size}var U=c-x-1,Z=U*_;return k+Z},rt=function(i,l,c,w){var _,x,k;if(i==="column"?(_=w.columnMetadataMap,x=l.columnWidth,k=w.lastMeasuredColumnIndex):(_=w.rowMetadataMap,x=l.rowHeight,k=w.lastMeasuredRowIndex),c>k){var E=0;if(k>=0){var U=_[k];E=U.offset+U.size}for(var Z=k+1;Z<=c;Z++){var $=x(Z);_[Z]={offset:E,size:$},E+=$}i==="column"?w.lastMeasuredColumnIndex=c:w.lastMeasuredRowIndex=c}return _[c]},ht=function(i,l,c,w){var _,x;i==="column"?(_=c.columnMetadataMap,x=c.lastMeasuredColumnIndex):(_=c.rowMetadataMap,x=c.lastMeasuredRowIndex);var k=x>0?_[x].offset:0;return k>=w?Ft(i,l,c,x,0,w):Wt(i,l,c,Math.max(0,x),w)},Ft=function(i,l,c,w,_,x){for(;_<=w;){var k=_+Math.floor((w-_)/2),E=rt(i,l,k,c).offset;if(E===x)return k;E<x?_=k+1:E>x&&(w=k-1)}return _>0?_-1:0},Wt=function(i,l,c,w,_){for(var x=i==="column"?l.columnCount:l.rowCount,k=1;w<x&&rt(i,l,w,c).offset<_;)w+=k,k*=2;return Ft(i,l,c,Math.min(w,x-1),Math.floor(w/2),_)},At=function(i,l,c,w,_,x,k){var E=i==="column"?l.width:l.height,U=rt(i,l,c,x),Z=i==="column"?et(l,x):st(l,x),$=Math.max(0,Math.min(Z-E,U.offset)),O=Math.max(0,U.offset-E+k+U.size);switch(w==="smart"&&(_>=O-E&&_<=$+E?w="auto":w="center"),w){case"start":return $;case"end":return O;case"center":return Math.round(O+($-O)/2);case"auto":default:return _>=O&&_<=$?_:O>$||_<O?O:$}},Be=Zt({getColumnOffset:function(i,l,c){return rt("column",i,l,c).offset},getColumnStartIndexForOffset:function(i,l,c){return ht("column",i,c,l)},getColumnStopIndexForStartIndex:function(i,l,c,w){for(var _=i.columnCount,x=i.width,k=rt("column",i,l,w),E=c+x,U=k.offset+k.size,Z=l;Z<_-1&&U<E;)Z++,U+=rt("column",i,Z,w).size;return Z},getColumnWidth:function(i,l,c){return c.columnMetadataMap[l].size},getEstimatedTotalHeight:st,getEstimatedTotalWidth:et,getOffsetForColumnAndAlignment:function(i,l,c,w,_,x){return At("column",i,l,c,w,_,x)},getOffsetForRowAndAlignment:function(i,l,c,w,_,x){return At("row",i,l,c,w,_,x)},getRowOffset:function(i,l,c){return rt("row",i,l,c).offset},getRowHeight:function(i,l,c){return c.rowMetadataMap[l].size},getRowStartIndexForOffset:function(i,l,c){return ht("row",i,c,l)},getRowStopIndexForStartIndex:function(i,l,c,w){for(var _=i.rowCount,x=i.height,k=rt("row",i,l,w),E=c+x,U=k.offset+k.size,Z=l;Z<_-1&&U<E;)Z++,U+=rt("row",i,Z,w).size;return Z},initInstanceProps:function(i,l){var c=i,w=c.estimatedColumnWidth,_=c.estimatedRowHeight,x={columnMetadataMap:{},estimatedColumnWidth:w||Ve,estimatedRowHeight:_||Ve,lastMeasuredColumnIndex:-1,lastMeasuredRowIndex:-1,rowMetadataMap:{}};return l.resetAfterColumnIndex=function(k,E){E===void 0&&(E=!0),l.resetAfterIndices({columnIndex:k,shouldForceUpdate:E})},l.resetAfterRowIndex=function(k,E){E===void 0&&(E=!0),l.resetAfterIndices({rowIndex:k,shouldForceUpdate:E})},l.resetAfterIndices=function(k){var E=k.columnIndex,U=k.rowIndex,Z=k.shouldForceUpdate,$=Z===void 0?!0:Z;typeof E=="number"&&(x.lastMeasuredColumnIndex=Math.min(x.lastMeasuredColumnIndex,E-1)),typeof U=="number"&&(x.lastMeasuredRowIndex=Math.min(x.lastMeasuredRowIndex,U-1)),l._getItemStyleCache(-1),$&&l.forceUpdate()},x},shouldResetStyleCacheOnItemSizeChange:!1,validateProps:function(i){var l=i.columnWidth,c=i.rowHeight}}),ct=150,Gt=function(i,l){return i},en=null,Pn=null;function fn(e){var i,l=e.getItemOffset,c=e.getEstimatedTotalSize,w=e.getItemSize,_=e.getOffsetForIndexAndAlignment,x=e.getStartIndexForOffset,k=e.getStopIndexForStartIndex,E=e.initInstanceProps,U=e.shouldResetStyleCacheOnItemSizeChange,Z=e.validateProps;return i=function($){(0,o.A)(O,$);function O(oe){var P;return P=$.call(this,oe)||this,P._instanceProps=E(P.props,(0,t.A)(P)),P._outerRef=void 0,P._resetIsScrollingTimeoutId=null,P.state={instance:(0,t.A)(P),isScrolling:!1,scrollDirection:"forward",scrollOffset:typeof P.props.initialScrollOffset=="number"?P.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},P._callOnItemsRendered=void 0,P._callOnItemsRendered=Ee(function(re,le,ge,he){return P.props.onItemsRendered({overscanStartIndex:re,overscanStopIndex:le,visibleStartIndex:ge,visibleStopIndex:he})}),P._callOnScroll=void 0,P._callOnScroll=Ee(function(re,le,ge){return P.props.onScroll({scrollDirection:re,scrollOffset:le,scrollUpdateWasRequested:ge})}),P._getItemStyle=void 0,P._getItemStyle=function(re){var le=P.props,ge=le.direction,he=le.itemSize,ye=le.layout,W=P._getItemStyleCache(U&&he,U&&ye,U&&ge),xe;if(W.hasOwnProperty(re))xe=W[re];else{var _e=l(P.props,re,P._instanceProps),be=w(P.props,re,P._instanceProps),Oe=ge==="horizontal"||ye==="horizontal",Le=ge==="rtl",Ze=Oe?_e:0;W[re]=xe={position:"absolute",left:Le?void 0:Ze,right:Le?Ze:void 0,top:Oe?0:_e,height:Oe?"100%":be,width:Oe?be:"100%"}}return xe},P._getItemStyleCache=void 0,P._getItemStyleCache=Ee(function(re,le,ge){return{}}),P._onScrollHorizontal=function(re){var le=re.currentTarget,ge=le.clientWidth,he=le.scrollLeft,ye=le.scrollWidth;P.setState(function(W){if(W.scrollOffset===he)return null;var xe=P.props.direction,_e=he;if(xe==="rtl")switch($e()){case"negative":_e=-he;break;case"positive-descending":_e=ye-ge-he;break}return _e=Math.max(0,Math.min(_e,ye-ge)),{isScrolling:!0,scrollDirection:W.scrollOffset<_e?"forward":"backward",scrollOffset:_e,scrollUpdateWasRequested:!1}},P._resetIsScrollingDebounced)},P._onScrollVertical=function(re){var le=re.currentTarget,ge=le.clientHeight,he=le.scrollHeight,ye=le.scrollTop;P.setState(function(W){if(W.scrollOffset===ye)return null;var xe=Math.max(0,Math.min(ye,he-ge));return{isScrolling:!0,scrollDirection:W.scrollOffset<xe?"forward":"backward",scrollOffset:xe,scrollUpdateWasRequested:!1}},P._resetIsScrollingDebounced)},P._outerRefSetter=function(re){var le=P.props.outerRef;P._outerRef=re,typeof le=="function"?le(re):le!=null&&typeof le=="object"&&le.hasOwnProperty("current")&&(le.current=re)},P._resetIsScrollingDebounced=function(){P._resetIsScrollingTimeoutId!==null&&Fe(P._resetIsScrollingTimeoutId),P._resetIsScrollingTimeoutId=Xe(P._resetIsScrolling,ct)},P._resetIsScrolling=function(){P._resetIsScrollingTimeoutId=null,P.setState({isScrolling:!1},function(){P._getItemStyleCache(-1,null)})},P}O.getDerivedStateFromProps=function(P,re){return cn(P,re),Z(P),null};var ae=O.prototype;return ae.scrollTo=function(P){P=Math.max(0,P),this.setState(function(re){return re.scrollOffset===P?null:{scrollDirection:re.scrollOffset<P?"forward":"backward",scrollOffset:P,scrollUpdateWasRequested:!0}},this._resetIsScrollingDebounced)},ae.scrollToItem=function(P,re){re===void 0&&(re="auto");var le=this.props,ge=le.itemCount,he=le.layout,ye=this.state.scrollOffset;P=Math.max(0,Math.min(P,ge-1));var W=0;if(this._outerRef){var xe=this._outerRef;he==="vertical"?W=xe.scrollWidth>xe.clientWidth?Ce():0:W=xe.scrollHeight>xe.clientHeight?Ce():0}this.scrollTo(_(this.props,P,re,ye,this._instanceProps,W))},ae.componentDidMount=function(){var P=this.props,re=P.direction,le=P.initialScrollOffset,ge=P.layout;if(typeof le=="number"&&this._outerRef!=null){var he=this._outerRef;re==="horizontal"||ge==="horizontal"?he.scrollLeft=le:he.scrollTop=le}this._callPropsCallbacks()},ae.componentDidUpdate=function(){var P=this.props,re=P.direction,le=P.layout,ge=this.state,he=ge.scrollOffset,ye=ge.scrollUpdateWasRequested;if(ye&&this._outerRef!=null){var W=this._outerRef;if(re==="horizontal"||le==="horizontal")if(re==="rtl")switch($e()){case"negative":W.scrollLeft=-he;break;case"positive-ascending":W.scrollLeft=he;break;default:var xe=W.clientWidth,_e=W.scrollWidth;W.scrollLeft=_e-xe-he;break}else W.scrollLeft=he;else W.scrollTop=he}this._callPropsCallbacks()},ae.componentWillUnmount=function(){this._resetIsScrollingTimeoutId!==null&&Fe(this._resetIsScrollingTimeoutId)},ae.render=function(){var P=this.props,re=P.children,le=P.className,ge=P.direction,he=P.height,ye=P.innerRef,W=P.innerElementType,xe=P.innerTagName,_e=P.itemCount,be=P.itemData,Oe=P.itemKey,Le=Oe===void 0?Gt:Oe,Ze=P.layout,We=P.outerElementType,Qe=P.outerTagName,ot=P.style,at=P.useIsScrolling,lt=P.width,St=this.state.isScrolling,Et=ge==="horizontal"||Ze==="horizontal",Lt=Et?this._onScrollHorizontal:this._onScrollVertical,Pt=this._getRangeToRender(),Ht=Pt[0],gt=Pt[1],jt=[];if(_e>0)for(var Ot=Ht;Ot<=gt;Ot++)jt.push((0,s.createElement)(re,{data:be,key:Le(Ot,be),index:Ot,isScrolling:at?St:void 0,style:this._getItemStyle(Ot)}));var Jt=c(this.props,this._instanceProps);return(0,s.createElement)(We||Qe||"div",{className:le,onScroll:Lt,ref:this._outerRefSetter,style:(0,a.A)({position:"relative",height:he,width:lt,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:ge},ot)},(0,s.createElement)(W||xe||"div",{children:jt,ref:ye,style:{height:Et?"100%":Jt,pointerEvents:St?"none":void 0,width:Et?Jt:"100%"}}))},ae._callPropsCallbacks=function(){if(typeof this.props.onItemsRendered=="function"){var P=this.props.itemCount;if(P>0){var re=this._getRangeToRender(),le=re[0],ge=re[1],he=re[2],ye=re[3];this._callOnItemsRendered(le,ge,he,ye)}}if(typeof this.props.onScroll=="function"){var W=this.state,xe=W.scrollDirection,_e=W.scrollOffset,be=W.scrollUpdateWasRequested;this._callOnScroll(xe,_e,be)}},ae._getRangeToRender=function(){var P=this.props,re=P.itemCount,le=P.overscanCount,ge=this.state,he=ge.isScrolling,ye=ge.scrollDirection,W=ge.scrollOffset;if(re===0)return[0,0,0,0];var xe=x(this.props,W,this._instanceProps),_e=k(this.props,xe,W,this._instanceProps),be=!he||ye==="backward"?Math.max(1,le):1,Oe=!he||ye==="forward"?Math.max(1,le):1;return[Math.max(0,xe-be),Math.max(0,Math.min(re-1,_e+Oe)),xe,_e]},O}(s.PureComponent),i.defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},i}var cn=function(i,l){var c=i.children,w=i.direction,_=i.height,x=i.layout,k=i.innerTagName,E=i.outerTagName,U=i.width,Z=l.instance;if(0)var $},Dn=50,Qn=function(i,l,c){var w=i,_=w.itemSize,x=c.itemMetadataMap,k=c.lastMeasuredIndex;if(l>k){var E=0;if(k>=0){var U=x[k];E=U.offset+U.size}for(var Z=k+1;Z<=l;Z++){var $=_(Z);x[Z]={offset:E,size:$},E+=$}c.lastMeasuredIndex=l}return x[l]},qo=function(i,l,c){var w=l.itemMetadataMap,_=l.lastMeasuredIndex,x=_>0?w[_].offset:0;return x>=c?Uo(i,l,_,0,c):er(i,l,Math.max(0,_),c)},Uo=function(i,l,c,w,_){for(;w<=c;){var x=w+Math.floor((c-w)/2),k=Qn(i,x,l).offset;if(k===_)return x;k<_?w=x+1:k>_&&(c=x-1)}return w>0?w-1:0},er=function(i,l,c,w){for(var _=i.itemCount,x=1;c<_&&Qn(i,c,l).offset<w;)c+=x,x*=2;return Uo(i,l,Math.min(c,_-1),Math.floor(c/2),w)},Wo=function(i,l){var c=i.itemCount,w=l.itemMetadataMap,_=l.estimatedItemSize,x=l.lastMeasuredIndex,k=0;if(x>=c&&(x=c-1),x>=0){var E=w[x];k=E.offset+E.size}var U=c-x-1,Z=U*_;return k+Z},Dr=fn({getItemOffset:function(i,l,c){return Qn(i,l,c).offset},getItemSize:function(i,l,c){return c.itemMetadataMap[l].size},getEstimatedTotalSize:Wo,getOffsetForIndexAndAlignment:function(i,l,c,w,_,x){var k=i.direction,E=i.height,U=i.layout,Z=i.width,$=k==="horizontal"||U==="horizontal",O=$?Z:E,ae=Qn(i,l,_),oe=Wo(i,_),P=Math.max(0,Math.min(oe-O,ae.offset)),re=Math.max(0,ae.offset-O+ae.size+x);switch(c==="smart"&&(w>=re-O&&w<=P+O?c="auto":c="center"),c){case"start":return P;case"end":return re;case"center":return Math.round(re+(P-re)/2);case"auto":default:return w>=re&&w<=P?w:w<re?re:P}},getStartIndexForOffset:function(i,l,c){return qo(i,c,l)},getStopIndexForStartIndex:function(i,l,c,w){for(var _=i.direction,x=i.height,k=i.itemCount,E=i.layout,U=i.width,Z=_==="horizontal"||E==="horizontal",$=Z?U:x,O=Qn(i,l,w),ae=c+$,oe=O.offset+O.size,P=l;P<k-1&&oe<ae;)P++,oe+=Qn(i,P,w).size;return P},initInstanceProps:function(i,l){var c=i,w=c.estimatedItemSize,_={itemMetadataMap:{},estimatedItemSize:w||Dn,lastMeasuredIndex:-1};return l.resetAfterIndex=function(x,k){k===void 0&&(k=!0),_.lastMeasuredIndex=Math.min(_.lastMeasuredIndex,x-1),l._getItemStyleCache(-1),k&&l.forceUpdate()},_},shouldResetStyleCacheOnItemSizeChange:!1,validateProps:function(i){var l=i.itemSize}}),Rr=Zt({getColumnOffset:function(i,l){var c=i.columnWidth;return l*c},getColumnWidth:function(i,l){var c=i.columnWidth;return c},getRowOffset:function(i,l){var c=i.rowHeight;return l*c},getRowHeight:function(i,l){var c=i.rowHeight;return c},getEstimatedTotalHeight:function(i){var l=i.rowCount,c=i.rowHeight;return c*l},getEstimatedTotalWidth:function(i){var l=i.columnCount,c=i.columnWidth;return c*l},getOffsetForColumnAndAlignment:function(i,l,c,w,_,x){var k=i.columnCount,E=i.columnWidth,U=i.width,Z=Math.max(0,k*E-U),$=Math.min(Z,l*E),O=Math.max(0,l*E-U+x+E);switch(c==="smart"&&(w>=O-U&&w<=$+U?c="auto":c="center"),c){case"start":return $;case"end":return O;case"center":var ae=Math.round(O+($-O)/2);return ae<Math.ceil(U/2)?0:ae>Z+Math.floor(U/2)?Z:ae;case"auto":default:return w>=O&&w<=$?w:O>$||w<O?O:$}},getOffsetForRowAndAlignment:function(i,l,c,w,_,x){var k=i.rowHeight,E=i.height,U=i.rowCount,Z=Math.max(0,U*k-E),$=Math.min(Z,l*k),O=Math.max(0,l*k-E+x+k);switch(c==="smart"&&(w>=O-E&&w<=$+E?c="auto":c="center"),c){case"start":return $;case"end":return O;case"center":var ae=Math.round(O+($-O)/2);return ae<Math.ceil(E/2)?0:ae>Z+Math.floor(E/2)?Z:ae;case"auto":default:return w>=O&&w<=$?w:O>$||w<O?O:$}},getColumnStartIndexForOffset:function(i,l){var c=i.columnWidth,w=i.columnCount;return Math.max(0,Math.min(w-1,Math.floor(l/c)))},getColumnStopIndexForStartIndex:function(i,l,c){var w=i.columnWidth,_=i.columnCount,x=i.width,k=l*w,E=Math.ceil((x+c-k)/w);return Math.max(0,Math.min(_-1,l+E-1))},getRowStartIndexForOffset:function(i,l){var c=i.rowHeight,w=i.rowCount;return Math.max(0,Math.min(w-1,Math.floor(l/c)))},getRowStopIndexForStartIndex:function(i,l,c){var w=i.rowHeight,_=i.rowCount,x=i.height,k=l*w,E=Math.ceil((x+c-k)/w);return Math.max(0,Math.min(_-1,l+E-1))},initInstanceProps:function(i){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(i){var l=i.columnWidth,c=i.rowHeight}}),tr=fn({getItemOffset:function(i,l){var c=i.itemSize;return l*c},getItemSize:function(i,l){var c=i.itemSize;return c},getEstimatedTotalSize:function(i){var l=i.itemCount,c=i.itemSize;return c*l},getOffsetForIndexAndAlignment:function(i,l,c,w,_,x){var k=i.direction,E=i.height,U=i.itemCount,Z=i.itemSize,$=i.layout,O=i.width,ae=k==="horizontal"||$==="horizontal",oe=ae?O:E,P=Math.max(0,U*Z-oe),re=Math.min(P,l*Z),le=Math.max(0,l*Z-oe+Z+x);switch(c==="smart"&&(w>=le-oe&&w<=re+oe?c="auto":c="center"),c){case"start":return re;case"end":return le;case"center":{var ge=Math.round(le+(re-le)/2);return ge<Math.ceil(oe/2)?0:ge>P+Math.floor(oe/2)?P:ge}case"auto":default:return w>=le&&w<=re?w:w<le?le:re}},getStartIndexForOffset:function(i,l){var c=i.itemCount,w=i.itemSize;return Math.max(0,Math.min(c-1,Math.floor(l/w)))},getStopIndexForStartIndex:function(i,l,c){var w=i.direction,_=i.height,x=i.itemCount,k=i.itemSize,E=i.layout,U=i.width,Z=w==="horizontal"||E==="horizontal",$=l*k,O=Z?U:_,ae=Math.ceil((O+c-$)/k);return Math.max(0,Math.min(x-1,l+ae-1))},initInstanceProps:function(i){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(i){var l=i.itemSize}});function Oo(e,i){for(var l in e)if(!(l in i))return!0;for(var c in i)if(e[c]!==i[c])return!0;return!1}var nr=null,sr=null;function or(e,i){var l=e.style,c=_objectWithoutPropertiesLoose(e,nr),w=i.style,_=_objectWithoutPropertiesLoose(i,sr);return!Oo(l,w)&&!Oo(c,_)}function Fr(e,i){return!or(this.props,e)||Oo(this.state,i)}let dn;typeof window<"u"?dn=window:typeof self<"u"?dn=self:dn=n.g;let Po=null,Do=null;const Ho=20,Ro=dn.clearTimeout,Vo=dn.setTimeout,Fo=dn.cancelAnimationFrame||dn.mozCancelAnimationFrame||dn.webkitCancelAnimationFrame,Ko=dn.requestAnimationFrame||dn.mozRequestAnimationFrame||dn.webkitRequestAnimationFrame;Fo==null||Ko==null?(Po=Ro,Do=function(i){return Vo(i,Ho)}):(Po=function(i){let[l,c]=i;Fo(l),Ro(c)},Do=function(i){const l=Ko(function(){Ro(c),i()}),c=Vo(function(){Fo(l),i()},Ho);return[l,c]});function rr(e){let i,l,c,w,_,x,k;const E=typeof document<"u"&&document.attachEvent;if(!E){x=function(le){const ge=le.__resizeTriggers__,he=ge.firstElementChild,ye=ge.lastElementChild,W=he.firstElementChild;ye.scrollLeft=ye.scrollWidth,ye.scrollTop=ye.scrollHeight,W.style.width=he.offsetWidth+1+"px",W.style.height=he.offsetHeight+1+"px",he.scrollLeft=he.scrollWidth,he.scrollTop=he.scrollHeight},_=function(le){return le.offsetWidth!==le.__resizeLast__.width||le.offsetHeight!==le.__resizeLast__.height},k=function(le){if(le.target.className&&typeof le.target.className.indexOf=="function"&&le.target.className.indexOf("contract-trigger")<0&&le.target.className.indexOf("expand-trigger")<0)return;const ge=this;x(this),this.__resizeRAF__&&Po(this.__resizeRAF__),this.__resizeRAF__=Do(function(){_(ge)&&(ge.__resizeLast__.width=ge.offsetWidth,ge.__resizeLast__.height=ge.offsetHeight,ge.__resizeListeners__.forEach(function(W){W.call(ge,le)}))})};let O=!1,ae="";c="animationstart";const oe="Webkit Moz O ms".split(" ");let P="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),re="";{const le=document.createElement("fakeelement");if(le.style.animationName!==void 0&&(O=!0),O===!1){for(let ge=0;ge<oe.length;ge++)if(le.style[oe[ge]+"AnimationName"]!==void 0){re=oe[ge],ae="-"+re.toLowerCase()+"-",c=P[ge],O=!0;break}}}l="resizeanim",i="@"+ae+"keyframes "+l+" { from { opacity: 0; } to { opacity: 0; } } ",w=ae+"animation: 1ms "+l+"; "}const U=function(O){if(!O.getElementById("detectElementResize")){const ae=(i||"")+".resize-triggers { "+(w||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',oe=O.head||O.getElementsByTagName("head")[0],P=O.createElement("style");P.id="detectElementResize",P.type="text/css",e!=null&&P.setAttribute("nonce",e),P.styleSheet?P.styleSheet.cssText=ae:P.appendChild(O.createTextNode(ae)),oe.appendChild(P)}};return{addResizeListener:function(O,ae){if(E)O.attachEvent("onresize",ae);else{if(!O.__resizeTriggers__){const oe=O.ownerDocument,P=dn.getComputedStyle(O);P&&P.position==="static"&&(O.style.position="relative"),U(oe),O.__resizeLast__={},O.__resizeListeners__=[],(O.__resizeTriggers__=oe.createElement("div")).className="resize-triggers";const re=oe.createElement("div");re.className="expand-trigger",re.appendChild(oe.createElement("div"));const le=oe.createElement("div");le.className="contract-trigger",O.__resizeTriggers__.appendChild(re),O.__resizeTriggers__.appendChild(le),O.appendChild(O.__resizeTriggers__),x(O),O.addEventListener("scroll",k,!0),c&&(O.__resizeTriggers__.__animationListener__=function(he){he.animationName===l&&x(O)},O.__resizeTriggers__.addEventListener(c,O.__resizeTriggers__.__animationListener__))}O.__resizeListeners__.push(ae)}},removeResizeListener:function(O,ae){if(E)O.detachEvent("onresize",ae);else if(O.__resizeListeners__.splice(O.__resizeListeners__.indexOf(ae),1),!O.__resizeListeners__.length){O.removeEventListener("scroll",k,!0),O.__resizeTriggers__.__animationListener__&&(O.__resizeTriggers__.removeEventListener(c,O.__resizeTriggers__.__animationListener__),O.__resizeTriggers__.__animationListener__=null);try{O.__resizeTriggers__=!O.removeChild(O.__resizeTriggers__)}catch(oe){}}}}}class ir extends s.Component{constructor(){super(...arguments),this.state={height:this.props.defaultHeight||0,width:this.props.defaultWidth||0},this._autoSizer=null,this._detectElementResize=null,this._didLogDeprecationWarning=!1,this._parentNode=null,this._resizeObserver=null,this._timeoutId=null,this._onResize=()=>{this._timeoutId=null;const{disableHeight:i,disableWidth:l,onResize:c}=this.props;if(this._parentNode){const w=window.getComputedStyle(this._parentNode)||{},_=parseFloat(w.paddingLeft||"0"),x=parseFloat(w.paddingRight||"0"),k=parseFloat(w.paddingTop||"0"),E=parseFloat(w.paddingBottom||"0"),U=this._parentNode.getBoundingClientRect(),Z=U.height-k-E,$=U.width-_-x;if(!i&&this.state.height!==Z||!l&&this.state.width!==$){this.setState({height:Z,width:$});const O=()=>{this._didLogDeprecationWarning||(this._didLogDeprecationWarning=!0,console.warn("scaledWidth and scaledHeight parameters have been deprecated; use width and height instead"))};typeof c=="function"&&c({height:Z,width:$,get scaledHeight(){return O(),Z},get scaledWidth(){return O(),$}})}}},this._setRef=i=>{this._autoSizer=i}}componentDidMount(){const{nonce:i}=this.props,l=this._autoSizer?this._autoSizer.parentNode:null;if(l!=null&&l.ownerDocument&&l.ownerDocument.defaultView&&l instanceof l.ownerDocument.defaultView.HTMLElement){this._parentNode=l;const c=l.ownerDocument.defaultView.ResizeObserver;c!=null?(this._resizeObserver=new c(()=>{this._timeoutId=setTimeout(this._onResize,0)}),this._resizeObserver.observe(l)):(this._detectElementResize=rr(i),this._detectElementResize.addResizeListener(l,this._onResize)),this._onResize()}}componentWillUnmount(){this._parentNode&&(this._detectElementResize&&this._detectElementResize.removeResizeListener(this._parentNode,this._onResize),this._timeoutId!==null&&clearTimeout(this._timeoutId),this._resizeObserver&&this._resizeObserver.disconnect())}render(){const{children:i,defaultHeight:l,defaultWidth:c,disableHeight:w=!1,disableWidth:_=!1,doNotBailOutOnEmptyChildren:x=!1,nonce:k,onResize:E,style:U={},tagName:Z="div",...$}=this.props,{height:O,width:ae}=this.state,oe={overflow:"visible"},P={};let re=!1;return w||(O===0&&(re=!0),oe.height=0,P.height=O,P.scaledHeight=O),_||(ae===0&&(re=!0),oe.width=0,P.width=ae,P.scaledWidth=ae),x&&(re=!1),(0,s.createElement)(Z,{ref:this._setRef,style:{...oe,...U},...$},!re&&i(P))}}function Br(e){return e&&e.disableHeight!==!0&&e.disableWidth!==!0}function zr(e){return e&&e.disableHeight!==!0&&e.disableWidth===!0}function Ur(e){return e&&e.disableHeight===!0&&e.disableWidth!==!0}const ar=(0,s.memo)(e=>{let{className:i,itemCount:l,itemSize:c,itemData:w,renderItem:_,onItemCountChange:x,onListRef:k}=e;const E=s.createRef();return(0,s.useEffect)(()=>{E.current&&x&&x(E)},[l,E,x]),(0,s.useEffect)(()=>{E.current&&k&&k(E)},[E,k]),(0,r.jsx)(ir,{children:U=>{let{width:Z,height:$=0}=U;return(0,r.jsx)(tr,{ref:E,className:i,itemCount:l,itemSize:c,itemData:w,height:$,width:Z,children:_})}})}),lr=e=>{const{theme:i,caption:l,captionExtra:c,className:w,permissionScope:_,memberList:x,isOnlyOneManager:k,readOnly:E,readOnlyTooltip:U,expandable:Z,defaultExpanded:$=!0,isResetPermission:O=!1,children:ae,onPermissionChange:oe,onChangeHeight:P,isMemberList:re,onlyTitle:le,isUseVirtualizedRender:ge=!1,onClickInviteButton:he,isShowInviteButton:ye=!1,currentOrg:W}=e,[xe,_e]=(0,s.useState)($),be=(0,s.useCallback)(()=>{_e(We=>!We),P&&P()},[P]),Oe=(0,s.useCallback)(We=>{var Qe;const{avatar:ot,name:at}=We;return ot&&!ot.includes("/images/avatar.png")?(0,r.jsx)("img",{className:"avatar",src:We.avatar,alt:We.name}):(0,r.jsx)("div",{className:"avatar avater-name",children:at==null||(Qe=at.slice(0,1))==null?void 0:Qe.toUpperCase()})},[]),Le=(We,Qe)=>(0,r.jsxs)("div",{className:"item",style:Qe,children:[(0,r.jsxs)("span",{className:"title",children:[Oe(We),(0,r.jsx)("span",{className:"name",children:We.name})]}),(0,r.jsx)("span",{className:"account",children:We.email||We.mobile}),(0,r.jsx)($n,{readOnly:E,readOnlyTooltip:U,member:We,isReset:O,isOnlyOneManager:k,permissionScope:_,currentOrg:W,onPermissionChange:oe})]},We.user_cid),Ze=We=>{let{data:Qe,index:ot,style:at}=We;const{memberList:lt}=Qe;return Le(lt[ot],at)};return(0,r.jsxs)(fs,{className:te()("member-list",i,w&&w,ge&&"use-virtualized-list"),"data-expanded":xe,children:[Z&&(0,r.jsxs)("h5",{className:"caption",children:[(0,r.jsx)("span",{children:l}),ye&&!(!le&&Z&&!re)&&(0,r.jsxs)("div",{className:"invite-item",onClick:he,children:[(0,r.jsx)(Re.C,{name:"sharing/invite_member"}),I18N.imockSharing.add_collaborator]}),!le&&Z&&!re&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{children:x.length>0&&"\xB7"+x.length}),(0,r.jsx)("span",{className:te()("btn-toggle-expand"),onClick:be,children:xe?I18N.BasicWidgetsNav.share_collapse:I18N.BasicWidgetsNav.share_view})]}),c]}),ae,!!x.length&&(0,r.jsx)("div",{className:"members",children:ge?(0,r.jsx)(ar,{className:"virtualized-list",itemSize:48,itemCount:x.length,itemData:{memberList:x},renderItem:Ze}):x.map(We=>Le(We))})]})},Bo=(0,s.memo)(lr),cr=L.Ay.div.withConfig({displayName:"style__StyledSearchBox",componentId:"sc-f6syk1-0"})(["flex:1;position:relative;.icon-search{position:absolute;top:12px;left:12px;width:16px;height:17px;color:#666;}.autoCompleteInput{position:absolute;width:1px;height:1px;top:-2000px;}input{width:100%;height:36px;padding:0 12px 0 12px;border-radius:4px;outline:none;color:",";border:1px solid ",";font-size:14px;&:hover,&:focus{border-color:#298df8;border:1px solid #1883FB;}}"],e=>e.theme.color_text_L1,e=>e.theme.color_bg_border_02),dr=e=>{const{value:i,onChange:l,onKeyPress:c}=e,w=x=>{l(x.currentTarget.value)},_=x=>{x.key==="Enter"&&c(x.currentTarget.value)};return(0,r.jsx)(cr,{className:"search-box",children:(0,r.jsx)("input",{className:"search-input",placeholder:I18N.imockSharing.search_member,type:"text",value:i,onChange:w,onKeyPress:_})})},ur=(0,s.memo)(dr);var pr=n(15628),Zo=n(51582);function hr(e){const{theme:i,caption:l,outerMembers:c,canInvite:w,isSharing:_,handleClickInviteMember:x}=e,k=()=>{const Z="\u54C8\u55BD\uFF0C\u7BA1\u7406\u5458\u60A8\u597D\uFF0C\u7533\u8BF7\u5C06"+(c[0].mobile||c[0].email)+"\u52A0\u5165\u300C\u58A8\u5200\u5E73\u53F0-"+l+"\u300D\uFF0C\u52A0\u5165\u540E\u6211\u5C31\u53EF\u4EE5\u548CTA\u4E00\u8D77\u534F\u4F5C\u4E86\u54E6~";(0,wn.$)(Z)},E=!c||c.length<1;return _?(0,r.jsxs)(fs,{className:"member-list sharing "+i,children:[(0,r.jsxs)("div",{className:"members invite",children:[!E&&c.map(U=>(0,r.jsxs)("div",{className:"item",children:[(0,r.jsxs)("span",{className:"title",children:[(0,r.jsx)("img",{className:"avatar",src:U.avatar,alt:U.name}),(0,r.jsxs)("span",{className:"name",children:[U.name,(0,r.jsx)("span",{className:"register",children:U.id?I18N.imockSharing.external:I18N.imockSharing.unregistered})]})]}),(0,r.jsx)("span",{className:"account",children:U.email||U.mobile}),!n.g.ENV.IS_ON_PREMISES&&(w?(0,r.jsx)("span",{className:"inviteJoin",onClick:x,children:I18N.imockSharing.invite_h}):(0,r.jsxs)("div",{className:"addOuter",children:[(0,r.jsx)("span",{className:"add",children:I18N.imockSharing.submit}),(0,r.jsx)(hs.A,{opener:(0,r.jsx)(Zo.A,{name:"question"}),mode:"dark",menuX:"right",shouldOpenOnHover:!0,hoverDelay:100,menuClassName:"add-outer-member-tip",menu:(0,r.jsxs)("p",{className:"desc",children:["\u590D\u5236\u5E10\u53F7\uFF0C\u63D0\u9192\u7BA1\u7406\u5458\u628ATA\u52A0\u5165\u4F01\u4E1A\uFF0C\u5C31\u53EF\u4EE5\u4E00\u8D77\u534F\u4F5C\u4E86\u54E6~",(0,r.jsx)("a",{className:"copy-text",onClick:k,children:I18N.imockSharing.copy})]})})]}))]},U.cid)),E&&(0,r.jsx)("div",{className:"enterTip",children:(0,r.jsx)("span",{children:n.g.ENV.IS_ON_PREMISES?(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{children:"\u60A8\u627E\u7684\u6210\u5458\u4E0D\u5728\u4F01\u4E1A\u4E2D\uFF0C"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{children:"\u8BF7\u786E\u8BA4\u300C\u90AE\u7BB1/\u624B\u673A\u53F7\u300D\u91CD\u65B0\u641C\u7D22~"})]}):(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:I18N.imockSharing.member_search_not_in_the_team}})})})]}),(0,r.jsx)(Vs,{})]}):(r.Fragment,(0,r.jsxs)(fs,{className:"member-list "+i,children:[(0,r.jsxs)("div",{className:"members invite",children:[!E&&c.map(U=>(0,r.jsxs)("div",{className:"item",children:[(0,r.jsxs)("span",{className:"title",children:[(0,r.jsx)("img",{className:"avatar",src:U.avatar,alt:U.name}),(0,r.jsxs)("span",{className:"name",children:[U.name,(0,r.jsx)("span",{className:"register",children:U.id?I18N.imockSharing.external:I18N.imockSharing.unregistered})]})]}),(0,r.jsx)("span",{className:"account",children:U.email||U.mobile}),!n.g.ENV.IS_ON_PREMISES&&(w?(0,r.jsx)("span",{className:"inviteJoin",onClick:x,children:I18N.imockSharing.invite_h}):(0,r.jsxs)("div",{className:"addOuter",children:[(0,r.jsx)("span",{className:"add",children:I18N.imockSharing.submit}),(0,r.jsx)(hs.A,{opener:(0,r.jsx)(Zo.A,{name:"question"}),mode:"dark",menuX:"right",shouldOpenOnHover:!0,hoverDelay:100,menuClassName:"add-outer-member-tip",menu:(0,r.jsxs)("p",{className:"desc",children:["\u590D\u5236\u5E10\u53F7\uFF0C\u63D0\u9192\u7BA1\u7406\u5458\u628ATA\u52A0\u5165\u4F01\u4E1A\uFF0C\u5C31\u53EF\u4EE5\u4E00\u8D77\u534F\u4F5C\u4E86\u54E6~",(0,r.jsx)("a",{className:"copy-text",onClick:k,children:I18N.imockSharing.copy})]})})]}))]},U.cid)),E&&(0,r.jsx)("div",{className:"enterTip",children:(0,r.jsx)("span",{children:n.g.ENV.IS_ON_PREMISES?(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{children:"\u60A8\u627E\u7684\u6210\u5458\u4E0D\u5728\u4F01\u4E1A\u4E2D\uFF0C"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{children:"\u8BF7\u786E\u8BA4\u300C\u90AE\u7BB1/\u624B\u673A\u53F7\u300D\u91CD\u65B0\u641C\u7D22~"})]}):(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:I18N.imockSharing.member_search_not_in_the_team}})})})]}),(0,r.jsx)(Vs,{})]}))}const mr=(0,s.memo)(hr),Go=()=>!!Number(sessionStorage.getItem("inClowdz"));function gr(e){let i="file_editarea-proto-v8-add",l="\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u6DFB\u52A0";return i=e?"file_editarea-proto-v8-input-add":"file_editarea-proto-v8-add-direct",l=e?"\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u8F93\u5165\u6846-\u6DFB\u52A0":"\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u6DFB\u52A0\u534F\u4F5C\u8005-\u6DFB\u52A0",{trackSourceId:i,trackSource:l}}function fr(e,i){i===void 0&&(i=!1);let l="file_editarea-proto-v8-invite",c="\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u9080\u8BF7\u6210\u5458";return i?(l="file_editarea-proto-v8-input-inviteTA",c="\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u8F93\u5165\u6846-\u9080\u8BF7TA"):(l=e?"file_editarea-proto-v8-input-invite":"file_editarea-proto-v8-add-invite",c=e?"\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u8F93\u5165\u6846-\u9080\u8BF7\u6210\u5458":"\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u6DFB\u52A0\u534F\u4F5C\u8005-\u9080\u8BF7\u6210\u5458"),{trackSourceId:l,trackSource:c}}function Yo(e){if(!e)return"";let i="";return e==="org_owner"?i="\u4F01\u4E1A\u6240\u6709\u8005":e==="org_manager"?i="\u8D85\u7EA7\u7BA1\u7406\u5458":e==="org_admin"||e!=null&&e.includes("manager")?i="\u7BA1\u7406\u5458":e!=null&&e.includes("member")?i="\u534F\u4F5C\u6210\u5458":e!=null&&e.includes("viewer")&&(i="\u5BA1\u9605\u6210\u5458"),i}const $o=(e,i,l)=>{try{const{source_id:c,source:w,step:_,option:x=null}=l;if(!i||!e||ENV.IS_ON_PREMISES||ENV.IS_MO)return;if((!x||(x==null?void 0:x.results)!==!1)&&c)try{(0,us.Ds)("/api/dashboard/v5/org/invitation_records",{invitation_record:{user_id:e,org_cid:i,source:c,step:_}})}catch(k){console.log(k.message)}if(_===0)(0,G.kH)("invite_members_entrance",{source:w});else if(_===2&&x){const k=Yo(x==null?void 0:x.invitor_role),E=Yo(x==null?void 0:x.invitee_role);(0,G.kH)("invite_members_via_add",{source:w,join_space_num:1,has_department:!1,invitor_role:k,invitee_role:E,results:(x==null?void 0:x.results)||!0,error_type:(x==null?void 0:x.error_type)||""})}}catch(c){console.log(c.message)}},Ks=function(e,i,l,c,w){w===void 0&&(w=null);const{trackSourceId:_,trackSource:x}=gr(l);$o(e,i,{source_id:_,source:x,step:c,option:w})},yr=function(e,i,l,c){c===void 0&&(c=!1);const{trackSourceId:w,trackSource:_}=fr(l,c),x={trackSourceId:w,trackSource:_},k="/workspace/"+i+"/admin/member?openInviteByLinkModal=true&invite_track="+(0,It._)(x);if($o(e,i,{source_id:w,source:_,step:0}),Go()){location.pathname=k;return}(0,dt.JW)(k)},xr=(e,i,l)=>{const[c]=(0,s.useState)(l),w=(0,s.useRef)(0);return(0,s.useEffect)(()=>{c&&w.current>=1||w.current>=2||(Ks(e,i,l,0),w.current+=1)},[!!l]),c||l},br=L.Ay.div.withConfig({displayName:"styles__StyledInviteMemberOverlay",componentId:"sc-six6tj-0"})(["z-index:3;height:100%;width:100%;display:flex;flex-direction:column;align-items:stretch;.overlay-content{flex:1;padding:0 4px;overflow:auto;position:relative;height:calc(100% - ","px);overflow-x:hidden;&::-webkit-scrollbar{display:block;width:4px;height:4px;}.member-list.use-virtualized-list{height:calc(100% - 10px);width:410px;.members{height:100%;.virtualized-list{&::-webkit-scrollbar{display:block;width:4px;height:4px;}}}}}.overlay-content.no-members{.member-list{height:90%;.invite{height:90%;.enterTip{height:100%;display:flex;align-items:center;justify-content:center;&:lang(en){span{display:flex;justify-content:center;div{width:80%;text-align:center;line-height:20px;}}}}}}}.collaboration-remind{position:absolute;bottom:57px;left:0;width:100%;height:29px;color:#FFFFFF;background:#298DF8;padding:0 23px;font-size:12px;line-height:17px;display:flex;justify-content:space-between;align-items:center;.collaboration-remind-close{width:16px;height:16px;cursor:pointer;path{fill:#FFFFFF;}}}.overlay-footer{display:flex;align-items:center;justify-content:space-between;height:","px;padding:0 24px;border-top:1px solid ",";.seats{color:",";font-size:12px;}}"],fe.gE,fe.gE,e=>e.theme.color_bg_border_02,e=>e.theme.color_text_L1),vr=e=>{const{theme:i,keyword:l,source:c,caption:w,permissionScope:_,assignedMembers:x,unassignedMembers:k,outerMember:E,onPermissionChange:U,hostType:Z,handleClose:$}=e,O=(0,g.d4)(Ie.wA),ae=(0,g.d4)(Ie.cb),oe=(0,g.d4)(Ie.WR),{settings:P,cid:re}=O,{seatsTaken:le,totalSeats:ge}=(0,pr.TF)(O)||{},he=xr(oe,re,l);let ye=!1;(P.invite_permission&&P.invite_permission[0]==="all"||ae.isManager)&&(ye=!0);const W=()=>{const Ze={mode:"org",orgCid:O.cid,payEntrance:"\u7F16\u8F91\u533A-v8_\u7248\u672C\u7BA1\u7406_\u4E2A\u4EBA\u5347\u4F01\u4E1A",checkoutPlace:"workspace-v8_org_mem_limit",checkoutArea:"proto"};if(Z==="proto")MB.global.popupHelper.chargeAsync(Ze),$&&$();else{const We="/workspace/"+O.cid+"/admin/order?payment_param="+(0,It._)(Ze);if(Go()){location.pathname=We;return}(0,dt.JW)(We)}},xe=(0,s.useMemo)(()=>k.filter(Ze=>Ze.name&&Ze.name.includes(l)||Ze.mobile&&Ze.mobile.includes(l)||Ze.email&&Ze.email.includes(l)),[l,k]),_e=()=>{ye&&yr(oe,re,he,!0)},be=(Ze,We)=>{U(Ze,We),Ks(oe,re,he,1),E&&Ks(oe,re,he,2,{invitor_role:ae==null?void 0:ae.roleName,invitee_role:We}),Ks(oe,re,he,3)},Oe=!xe||xe.length<1,Le=E?[E]:[];return(0,r.jsxs)(br,{className:"overlay-adding-member sharing",children:[(0,r.jsxs)("div",{className:te()("overlay-content",!E&&Oe&&"no-members"),children:[!Oe&&(0,r.jsx)(Bo,{theme:i,caption:l?null:I18N.imockSharing.unjoined,expandable:!1,className:te()(l&&"invite"),permissionScope:_,currentOrg:O,memberList:xe,isResetPermission:!0,onPermissionChange:be,isUseVirtualizedRender:!0},"filtered-unassigned-members"),Oe&&(0,r.jsx)(mr,{theme:i,caption:w,outerMembers:Le,canInvite:ye,isSharing:!0,handleClickInviteMember:_e})]}),(0,r.jsxs)("footer",{className:"overlay-footer",children:[(0,r.jsxs)("span",{className:"seats",children:["\u5DF2\u5360\u7528\u5E2D\u4F4D/\u4F01\u4E1A\u603B\u5E2D\u4F4D\u6570\uFF1A",le,"/",ge]}),!n.g.ENV.IS_ON_PREMISES&&(0,r.jsx)(un.Oc,{type:"primary",size:"tiny",corner:"soft",onClick:W,children:I18N.imockSharing.add_team_seat})]})]})},_r=(0,s.memo)(vr),wr=L.Ay.div.withConfig({displayName:"styles__StyledProjectCollaborators",componentId:"sc-th5siy-0"})(["position:relative;display:flex;flex-direction:column;width:500px;height:100%;border-radius:8px;&.sharing{width:100%;&.dark{background:#252626;&.isInviteOverlayOpen{.overlay-adding-member{background:#252626;.overlay-header{.btn-back > svg > path{stroke:rgba(255,255,255,0.9);}.caption{color:rgba(255,255,255,0.9);}}.overlay-content{.member-list > .members > .item{.title{.name{color:rgba(255,255,255,0.9);}}.account{color:rgba(255,255,255,0.9);}label{button > span{color:rgba(255,255,255,0.9);}.caret svg{border-color:transparent transparent rgba(255,255,255,0.9) rgba(255,255,255,0.9);}}.read-only-option > span > span{color:rgba(255,255,255,0.9);}}}}}.nav-header > .title-left{label > button > svg > path{fill:#B8BCBF;}}.modal-content{.collaborators{.member-list > .members > .item{.title{.name{color:rgba(255,255,255,0.9);}}.account{color:rgba(255,255,255,0.9);}label{button > span{color:rgba(255,255,255,0.9);}.caret svg{border-color:transparent transparent rgba(255,255,255,0.9) rgba(255,255,255,0.9);}}.read-only-option > span > span{color:rgba(255,255,255,0.9);}}.inherited-members{.caption > span{color:",";&:last-child{color:#1684FC;}}.members > .item{.title{.name{color:rgba(255,255,255,0.9);}}.account{color:rgba(255,255,255,0.9);}label{button > span{color:rgba(255,255,255,0.9);}.caret svg{border-color:transparent transparent rgba(255,255,255,0.9) rgba(255,255,255,0.9);}}.read-only-option > span > span{color:rgba(255,255,255,0.9);}}}}}}&.isInviteOverlayOpen{.overlay-adding-member{.overlay-header{height:50px;.btn-back{left:20px;}.caption{font-size:14px;font-weight:500;color:",";left:82px;&:lang(en){left:100px;}}}}}& > .blank{height:86px;&.noSearch{height:18px;}}.modal-content{.blank{height:55px;}.collaborators{overflow-y:overlay;.member-list{.caption{padding:10px 20px;& > span{font-weight:500;font-size:12px;}.btn-toggle-expand{color:",";&:hover{color:",";}}.btn-add-collaborator{right:20px;}}.members{margin-top:0;.item{padding:7px 20px;height:48px;.title{flex:none;width:150px;.avatar{width:28px;height:28px;}.name{width:105px;font-weight:400;font-size:14px;line-height:initial;color:#333;}}.account{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}}}}}}}.nav-header{display:flex;padding-left:10px;font-weight:500;font-size:16px;color:",";height:","px;align-items:center;border-bottom:1px solid ",";justify-content:space-between;.title-left{display:flex;align-items:center;.title-left-back{cursor:pointer;display:flex;align-items:center;}.member-manager{cursor:default;margin-left:10px;}svg{margin-top:2px;}.nav-title{color:"," !important;font-size:14px;margin-top:1px;}}.title-close{margin-right:20px;cursor:pointer;.svg-icon{color:",";&:hover{color:",";}&:active{color:",";}}}}.modal-content{display:flex;flex:1;flex-direction:column;align-items:stretch;height:calc(100% - ","px);padding-top:10px;&.modal-content-show-search{height:calc(100% - 116px);}&.in-invite-overlay{padding-top:20px;}.collaborators{padding:0 4px 20px 4px;height:100%;overflow:hidden auto;&::-webkit-scrollbar{display:block;width:4px;height:4px;}}}.search{display:flex;align-items:center;position:relative;margin:0px 20px;padding-top:20px;}"],e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_hover,e=>e.theme.color_text_L1,fe.gE,e=>e.theme.color_bg_border_02,e=>e.theme.color_text_L1,e=>e.theme.color_text_L2,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,fe.gE),Cr=e=>{const{theme:i,from:l,caption:c,targetCid:w,permissionScope:_,captionTipType:x,isFolder:k=!1,isAllowSetCollaborators:E,isShowInheritedCollaborators:U=!1,handleBackSharingHandel:Z,onChangeMembersAccess:$,handleClose:O,hostType:ae,isOnlyMemberManager:oe=!1,currentOrg:P}=e,[re,le]=(0,s.useState)(""),[ge,he]=(0,s.useState)(null),[ye,W]=(0,s.useState)(!1),{currentMembers:xe,inheritedMembers:_e,joinedMembers:be,unjoinedMembers:Oe,assignedMembers:Le,unassignedMembers:Ze,userCurrentScopePermisson:We,isOnlyOneManager:Qe}=bt(_,w),ot=xt(_,w,l),at=async(vt,on)=>{await ot(vt,on),$&&$(vt,on)};let lt=xe,St=Le,Et=Ze;const Lt=We.isMember,Pt=E&&Lt,Ht=!ye&&Lt;E&&!U&&(lt=be,St=be,Et=Oe);const gt=(0,s.useCallback)(()=>{W(!1),le(""),he(null)},[]),jt=(0,s.useCallback)(vt=>{le(vt),he(null),vt&&W(!0)},[]),Ot=(0,s.useCallback)(async vt=>{if(!n.g.ENV.IS_ON_PREMISES&&(le(vt),vt)){const on=Et.find(Yt=>Yt.email===vt.trim()||Yt.mobile===vt.trim()),Jn=St.find(Yt=>Yt.email===vt.trim()||Yt.mobile===vt.trim());if(on||Jn)return;const Xn=Yn.test(vt.trim()),vn=On.test(vt.trim());let rn=null;if(Xn?rn=await ps(vt.trim()):vn&&(rn=await ps(null,vt.trim())),rn){let Yt=null;rn.user?(Yt=rn.user,Yt.name=I18N.imockSharing.to_be_invited,Yt.cid="outer"):Yt={avatar:"/images/avatar.png",cid:vt,email:vt,isNotRegister:!0,name:I18N.imockSharing.to_be_invited},he(Yt)}}},[]),Jt=(0,s.useCallback)(()=>{if(!(oe&&!ye)){if(le(""),he(null),ye){W(!1);return}Z()}},[ye,W,Z]),ft=(0,s.useCallback)(()=>{W(!0)},[W]);return(0,r.jsxs)(wr,{className:te()("ProjectCollaborators sharing",""+i,{isInviteOverlayOpen:!0}),children:[(0,r.jsxs)("div",{className:"nav-header",children:[(0,r.jsxs)("div",{className:"title-left",children:[(0,r.jsxs)("div",{className:te()("title-left-back",{"member-manager":oe&&!ye}),onClick:Jt,children:[oe?ye&&(0,r.jsx)(Re.C,{name:"sharing/nav_back",size:32}):(0,r.jsx)(Re.C,{name:"sharing/nav_back",size:32}),(0,r.jsx)("div",{className:"nav-title",children:ye?I18N.imockSharing.add_collaborator:I18N.imockSharing.team_collaborator})]}),!ye&&(0,r.jsx)(Mo,{tipType:x,position:"left",isOnlyMemberManager:oe})]}),oe&&(0,r.jsx)("div",{className:"title-close",onClick:O,children:(0,r.jsx)(Re.C,{size:24,name:"sharing/close"})})]}),Pt&&(0,r.jsx)("div",{className:te()("search"),children:(0,r.jsx)(ur,{value:re,onChange:jt,onKeyPress:Ot})}),(0,r.jsx)("div",{className:te()("modal-content",Pt&&"modal-content-show-search"),children:ye?(0,r.jsx)(_r,{theme:i,permissionScope:_,currentOrg:P,unassignedMembers:Et,assignedMembers:St,outerMember:ge,keyword:re,caption:c,onClose:gt,onPermissionChange:at,handleClose:O,hostType:ae}):(0,r.jsxs)("div",{className:te()("collaborators",ye&&"in-invite-overlay"),children:[E&&(0,r.jsx)(Bo,{theme:i,caption:I18N.imockSharing.project_collaborators,permissionScope:_,currentOrg:P,memberList:lt,isOnlyOneManager:Qe,isMemberList:!1,expandable:!0,onlyTitle:!0,onPermissionChange:at,onClickInviteButton:ft,isShowInviteButton:Ht},"current-collaborators"),U&&(0,r.jsx)(Bo,{theme:i,caption:I18N.imockSharing.parent_folder_collaborator,expandable:!0,defaultExpanded:k,readOnly:!0,permissionScope:_,currentOrg:P,memberList:_e,onPermissionChange:at,className:"inherited-members",onClickInviteButton:ft,isShowInviteButton:Ht},"inherited-collaborators")]})}),(0,r.jsx)(Io,{})]})},Sr=(0,s.memo)(Cr);function Mr(e){const{theme:i,targetCid:l,isSharing:c,handleBackSharingHandel:w,onChangeMembersAccess:_,handleClose:x,hostType:k,isOnlyMemberManager:E,currentOrg:U}=e,$=(0,g.d4)(Ie._B).get(l),O=nt($==null?void 0:$.team_cid);return!$||O<0?null:(0,r.jsx)(Sr,{theme:i,targetCid:l,caption:$.name,isAllowSetCollaborators:!0,isShowInheritedCollaborators:O>=1,captionTipType:ke.p.Project,currentOrg:U,permissionScope:ke.W.Project,isSharing:c,handleBackSharingHandel:w,onChangeMembersAccess:_,handleClose:x,hostType:k,isOnlyMemberManager:E})}const Nr=(0,s.memo)(Mr);var Ir=n(12211);const kr=e=>{const{user:i,initData:l,currentProject:c,members:w,dispatch:_}=e,x=c==null?void 0:c.cid;return(0,s.useEffect)(()=>{(async E=>{_({type:"entry:projectAccess:initProjectData",payload:{initData:l,members:w}})})(x)},[_,l,w,x,i]),i!=null&&i.id?(0,r.jsx)(Nr,{targetCid:x,isSharing:!0,...e}):null},Er=(0,Ir.Mz)([],()=>({})),jr=(0,g.Ng)(e=>Er(e))(kr);var Tr=n(65825),Qo=n(5743),Ar=n(33236);function Lr(e){const{getIframeContentHeight:i,handleChangeMembersAccess:l,handleClose:c}=e,w=(0,g.wA)(),_=(0,g.d4)(d.query.getProject),x=(0,g.d4)(d.query.getMembers),k=(0,g.d4)(d.query.getTheme),E=(0,g.d4)(d.query.getTopPageIndex),U=(0,g.d4)(d.query.getHostType),Z=(0,g.d4)(d.query.getTabIndex),$=(0,g.d4)(d.query.getMainPage),O=(0,g.d4)(d.query.getLoading),ae=(0,g.d4)(d.query.getInitData),oe=(0,g.d4)(d.query.getOrg),P=(0,g.d4)(d.query.getUser),re=(0,g.d4)(d.query.getIsEditMode),le=(0,g.d4)(d.query.getIsOnlyMemberManager),[ge,he]=(0,s.useState)(!1);(0,s.useEffect)(()=>{const be=(0,sn.fV)();if(be){const{mdWMMktList:Oe,mtWMMktList:Le,noWMMktList:Ze}=be,We=[...Oe,...Le,...Ze];We.length&&(0,bs.q)(We).then(Qe=>{if(Qe.mdWMMktList.length>0||Qe.mtWMMktList.length>0){if(!(0,Un._5)())return;he(!0)}})}},[]);const[ye,W]=(0,s.useState)(282);(0,s.useEffect)(()=>{let be=619;$!=="access"&&(Z===0&&E==="edit"?be=212:(E==="embed"||E==="qrCode"||Z===0&&E==="setting")&&(be=380)),ge&&(be+=37),W(be),U!=="proto"&&i(be)},[U,Z,E,$,i,re,ge]);const xe=(0,s.useCallback)(()=>{w({type:d.entryKey["sharing:init"],payload:{mainPage:"share"}})},[w]);(0,s.useEffect)(()=>{w({type:d.entryKey["sharing:projectAccess:init"]})},[$,w]);const _e=(0,s.useCallback)(async(be,Oe)=>{l&&l(be,Oe)},[l]);return O?(0,r.jsx)(Qo.A,{className:te()("sharingBoxV2",""+k,"loading"),children:(0,r.jsx)(Tr.A,{})}):(0,r.jsxs)(Qo.A,{className:te()("sharingBoxV2",""+k,U==="iframe"&&"in-iframe"),style:{height:ye+"px"},id:"v8-share-page",children:[$==="share"?(0,r.jsx)(bn,{}):(0,r.jsx)(jr,{theme:k,user:P,currentProject:_,members:x,initData:ae,currentOrg:oe,handleBackSharingHandel:xe,onChangeMembersAccess:_e,hostType:U,handleClose:c,isOnlyMemberManager:le}),(0,r.jsx)(Ar.r,{})]})}var Or=n(34860);const Pr=e=>{let{theme:i,org:l=null,user:c,project:w,flatKey:_,members:x=[],hostType:k="proto",screenMetaList:E,hostSharingData:U,projShareToEdit:Z="",handleClose:$,isOnlyMemberManager:O=!1,hostCurrentScreen:ae}=e;const oe=X();let P=!0;k!=="iframe"&&(P=(0,Or.OB)());const re=Z?1:0;oe.dispatch({type:d.entryKey["sharing:init"],payload:{project:w,theme:i,user:c,org:l,flatKey:_,hostType:k,members:x,screenMetaList:E,hostSharingData:U,isEditMode:P,tabIndex:re,projShareToEdit:Z,hostCurrentScreen:ae}});const le=(0,s.useCallback)(he=>{window.top.postMessage(JSON.stringify({sharingV2Message:"sharing:height",payload:{height:he}}),"*")},[]),ge=(0,s.useCallback)((he,ye)=>{window.top.postMessage(JSON.stringify({sharingV2Message:"sharing:changeMembersAccess",payload:{member:he,value:ye}}),"*")},[]);return k==="iframe"&&O&&(oe.dispatch({type:d.entryKey["sharing:init"],payload:{mainPage:"access",isOnlyMemberManager:!0}}),le(585)),(0,s.useEffect)(()=>{if(k==="iframe"&&!O&&le(212),!w)return;const{cid:he}=w;(async()=>{if(!w)return;const{result:{result:W},statusOk:xe}=await(0,D.QC)(he);xe&&oe.dispatch({type:d.entryKey["sharing:init"],payload:{canEditByUser:W}})})()},[k,le,w,oe]),(0,r.jsx)(g.Kq,{store:oe,children:(0,r.jsx)(L.NP,{theme:ee.A[i],children:(0,r.jsx)(ne.$,{children:(0,r.jsx)(Lr,{getIframeContentHeight:le,handleChangeMembersAccess:ge,handleClose:$})})})})}},76844:(Se,ce,n)=>{"use strict";n.d(ce,{CF:()=>I,CZ:()=>u,Cj:()=>M,Rc:()=>G,Sc:()=>V,oE:()=>F,ox:()=>q,pf:()=>H,sq:()=>ie});var s=n(19481),p=n(12603);const N=function(v,C,f){return f===void 0&&(f=!1),{level:v,roleName:C,isInherited:f,isOwner:v>=s.zB.OWNER,isSuperManager:v>=s.zB.SUPERMANAGER,isManager:v>=s.zB.MANAGER,isMember:v>=s.zB.MEMBER,isViewer:v>=s.zB.VIEWER,isLimiter:v===s.zB.LIMITER,isBanned:v===s.zB.BANNED,isUnknown:v===s.zB.UNKNOWN}},u=N(s.zB.UNKNOWN),B=N(s.zB.UNJOINED),q=(v,C)=>{if(!C||C.length<=0)return null;const f=C.find(y=>{let{user_id:h,unsign_remark:b}=y;return Number(h)===Number(v)||b===v});return f||null},V=v=>v?N(s.p_[v.role],v.role):N(s.zB.UNKNOWN),R=(v,C)=>{if(!v)return[];let f=[],y=v,h=C.get(y);for(;h&&!h.root_project;)f=f.concat(h.permissions),y=h.parent_cid,h=C.get(y);return f},J=(v,C,f)=>{let y=R(v,C);return y=y.concat(f).reverse(),y=y.filter(h=>h.role!=="space_limiter"),y.map(h=>{const b=folderInheritePermissionRoleFixMap[h.role];return b?{...h,role:b,inherited:!0}:{...h,inherited:!0}})},M=(v,C,f)=>{let y=R(v,C);return y=y.concat(f).reverse(),y=y.filter(h=>h.role!=="space_limiter"),y.map(h=>{const b=s.OO[h.role];return b?{...h,role:b,inherited:!0}:{...h,inherited:!0}})},F=v=>{const{orgPermissions:C,userId:f}=v;if(!f||!C||C.length<1)return N(s.zB.UNKNOWN);const y=q(f,C);return y?N(s.p_[y.role],y.role):N(s.zB.UNKNOWN)},ie=v=>{const{spacePermissions:C,userId:f}=v;if(!f||!C||C.length<1)return N(s.zB.UNKNOWN);const y=q(f,C);return y?N(s.FU[y.role],y.role):N(s.zB.UNKNOWN)},D=v=>{const{userId:C,orgPermissions:f,spacePermissions:y,currentFolder:h,folderListMap:b}=v;if(!C||!h)return N(permissionLevels.UNKNOWN);const{org_cid:S,space_cid:A}=h;if(!S&&!A)return N(permissionLevels.OWNER,"team_owner");if(!y||y.length<1)return N(permissionLevels.UNKNOWN);const{isRoot:K,root_project:d}=h;if(K||d){const L=ie({spacePermissions:y,userId:C});return L.roleName==="space_limiter"?{...F({orgPermissions:f,userId:C}),isLimiter:!0}:L}const j=h.permissions||[],T=J(h.parent_cid,b,y),X=Object.values(Object.fromEntries([...T,...j].map(L=>[L.user_id,L]))),g=q(C,X);return g?N(folderPermissionLevelsMap[g.role],g.role,g.inherited):N(permissionLevels.UNKNOWN)},pe=v=>{const{userId:C,spacePermissions:f,currentProject:y,folderListMap:h}=v;if(!C||!y)return N(permissionLevels.UNKNOWN);const{space_cid:b}=y;if(!b)return N(permissionLevels.OWNER,"project_owner");if(!f||f.length<1)return N(permissionLevels.UNKNOWN);const S=y.permissions||[],A=M(y.team_cid,h,f),K=Object.values(Object.fromEntries([...A,...S].map(j=>[j.user_id,j]))),d=q(C,K);return d?N(projectPermissionLevelsMap[d.role],d.role,d.inherited):N(permissionLevels.UNKNOWN)},H=function(v,C,f){return f===void 0&&(f=!1),C?typeof C[v]<"u"?N(C[v],v,f):N(s.zB.UNJOINED):N(s.zB.UNKNOWN)},G=v=>!v||v.length<1?new Map:v.reduce((C,f)=>C.set(Number(f.user_id),f),new Map),I=v=>{const{permissionsMap:C,userId:f,permissionScope:y}=v;if(!f||!C||C.size<1)return N(s.zB.UNKNOWN);const{role:h,inherited:b}=C.get(Number(f))||{};if(!h)return N(s.zB.UNKNOWN);let S=null;switch(y){case p.W.Org:S=s.p_;break;case p.W.OrgSpace:S=s.FU;break;case p.W.Folder:S=s.lY;break;case p.W.Project:S=s.au;break;default:break}return H(h,S,b)}},79186:(Se,ce,n)=>{"use strict";n.d(ce,{eB:()=>g,Ay:()=>X});var s=n(38502),p=n(69623),N=n(25582),u=n.n(N),B=n(27992),q=n(28322),V=n.n(q),R=n(89775),J=n.n(R),M=n(63986),F=n.n(M),ie=n(37862),D=n.n(ie),pe=n(51582),H=n(68677),G=n(69368),I=n(35603),v=n(67787);const C=v.Ay.label.withConfig({displayName:"styles__StyledSelectLabel",componentId:"sc-grbgid-0"})(["display:inline-flex;justify-content:space-between;align-items:center;max-width:100%;min-width:3em;height:34px;color:#5b6b73;cursor:pointer;transition:all 0.2s ease-out;button{flex:1;width:calc(100% - 2em);height:100%;text-align:start;cursor:inherit;}.caret{margin:0 0.75em 0 1em;line-height:0;color:#8d9ea7;svg{display:block;width:6px;height:4px;fill:currentColor;fill-rule:evenodd;transform:rotate(180deg);transition:all 0.3s ease-out;}}&:not(.unstyled){padding-left:.5em;background-color:#f6f7f8;border:1px solid #f2f2f3;border-radius:2px;}&.small{height:22px;font-size:12px;.caret{margin-right:0.5em;}}&:not(.is-disabled):not(.readonly):not(.unstyled):hover,&:not(.unstyled).is-open{border-color:#298df8;}&.is-open{&:not(.unstyled){background-color:#fff;box-shadow:0 0 6px 0 rgba(41,141,248,.5);}.caret svg{transform:rotate(0deg);}}&.is-disabled{cursor:not-allowed;opacity:0.6;}&.readonly{cursor:default}&.CoreSelect:not(.unstyled){background-color:#fff;border:1px solid #c8cdd1;}"]),f=v.Ay.div.withConfig({displayName:"styles__StyledSelectMenuBase",componentId:"sc-grbgid-1"})(["position:fixed;pointer-events:none;z-index:1100;"]),y=v.Ay.ul.withConfig({displayName:"styles__StyledSelectMenu",componentId:"sc-grbgid-2"})(["position:absolute;margin:2px 0;padding:0;display:block;max-width:20em;min-width:100%;min-height:30px;max-height:300px;overflow-x:hidden;overflow-y:auto;pointer-events:none;font-size:12px;list-style:none;background-color:#fff;border-radius:3px;box-shadow:0 2px 10px 0 rgba(39,54,78,0.08),4px 12px 40px 0 rgba(39,54,78,0.1);color:#5b6b73;transition:0.2s ease-out;transition-property:transform,opacity;transform-origin:50% 0;&::-webkit-scrollbar{display:block;width:4px;height:4px;}&::-webkit-scrollbar-thumb{background-color:#ccc;border-radius:2px;}&::-webkit-scrollbar-track{background-color:rgba(255,255,255,0.5);}&.is-empty{width:-moz-fit-content;width:-webkit-fit-content;width:fit-content;}&:not(.is-open){opacity:0;transform:scale(0.8);}&.x-center{left:50%;transform:translateX(-50%);&:not(.is-open){transform:scale(0.8) translateX(-50%);}}&.x-right{right:0;}&.is-downward{top:100%;bottom:initial;}&.is-upward{transform-origin:50% 100%;top:initial;bottom:100%;}&.is-open{opacity:1;pointer-events:initial;}&.cant-select .SelectOption{cursor:default;}.SelectGroup{> .title{padding:0 0.75em;width:100%;height:30px;line-height:30px;font-weight:bold;}> ul{margin:0;padding:0;}}.SelectOption{display:flex;align-items:center;height:30px;line-height:30px;cursor:pointer;> .Ellipsis{padding:0 0.75em;}> .svg-icon{margin-left:auto;margin-right:0.75em;&.check path{fill:#5b6b73;}}&.is-disabled{cursor:not-allowed;opacity:0.5;}&.is-active{color:#298df8;}&:not(.empty-msg):not(.is-disabled):hover,&.is-active{background-color:#f6f7f8;}&.empty-msg{padding:0 0.5em;color:#8d9ea7;cursor:not-allowed;}}&.CheckSelectMenu{&:not(.is-empty){padding:6px 0;}.SelectOption{height:32px;.Ellipsis{padding:0 16px;}.Ellipsis + .icon{margin-right:10px;}&:hover,&.is-active:hover{color:#298df8;}&.is-active{background:none;color:inherit;}}}&.CoreSelectMenu{margin:4px 0;&:not(.is-empty){padding:8px;min-height:48px;max-height:336px;}.SelectGroup > .title,.SelectOption{height:32px;line-height:32px;border-radius:2px;}}"]);var h=n(72214);function b(ee,se,te){return(se=S(se))in ee?Object.defineProperty(ee,se,{value:te,enumerable:!0,configurable:!0,writable:!0}):ee[se]=te,ee}function S(ee){var se=A(ee,"string");return typeof se=="symbol"?se:se+""}function A(ee,se){if(typeof ee!="object"||!ee)return ee;var te=ee[Symbol.toPrimitive];if(te!==void 0){var fe=te.call(ee,se||"default");if(typeof fe!="object")return fe;throw new TypeError("@@toPrimitive must return a primitive value.")}return(se==="string"?String:Number)(ee)}const K="IBOT_SELECT_MENU_ROOT",d={"zh-CN":{select_placeholder:"\u9009\u62E9\u4E00\u4E2A...",select_empty_msg:"\u6682\u65E0\u5185\u5BB9..."},en:{select_placeholder:"Choose one\u2026",select_empty_msg:"Nothing to display\u2026"}},j=document.getElementById(K)||Object.assign(document.createElement("div"),{id:K}),T=document.body;T.contains(j)||T.appendChild(j);class X extends s.PureComponent{constructor(){super(...arguments),b(this,"state",{isOpen:!1,prevProps:this.props,value:this.props.value}),b(this,"set$select",se=>this.setState({$select:se})),b(this,"open",()=>this.setState({isOpen:!0})),b(this,"close",()=>this.setState({isOpen:!1})),b(this,"toggle",()=>this.setState({isOpen:!this.state.isOpen})),b(this,"onResizeWindow",()=>this.state.isOpen&&this.close()),b(this,"onChange",async se=>{const{onChange:te,beforeOnChange:fe}=this.props,De=()=>{this.close(),te(se)};if(!fe){this.setState({value:se},De);return}if(await fe(se)){this.setState({value:se},De);return}De()}),b(this,"onSelect",async se=>{let{currentTarget:te}=se;const{value:fe}=this.props,{canSelect:De}=this;await this.onChange(De?te.dataset.value:fe)})}static getDerivedStateFromProps(se,te){let{prevProps:fe,value:De}=te;return F()(fe,se)?null:{prevProps:se,value:se.value}}componentDidMount(){window.addEventListener("resize",this.onResizeWindow)}componentWillUnmount(){window.removeEventListener("resize",this.onResizeWindow)}get isDisabled(){const{isDisabled:se,disabled:te}=this.props;return se||te}get readOnly(){return this.props.readOnly}get canSelect(){const{isDisabled:se,readOnly:te}=this;return!se&&!te}get displayText(){const{optionList:se,placeholder:te,optionLabelProp:fe}=this.props,{value:De}=this.state,ke=(se.find(Re=>J()(Re)&&Re.slice(0).some(mt=>(0,G.o3)(mt,De)))||se).find(Re=>!J()(Re)&&(0,G.o3)(Re,De));return ke?(0,G.Oi)(ke,fe):te||d[this.props.lang].select_placeholder}render(){const{size:se,theme:te,unstyled:fe,className:De,menuX:Ye}=this.props,{isOpen:ke,$select:Re,value:mt}=this.state,{isDisabled:Pe,readOnly:Q,canSelect:Y}=this,me=(0,G.Hn)([te==="core"?"CoreSelect":"Select",se,fe&&"unstyled",De,ke&&"is-open",Pe&&"is-disabled",Q&&"readonly"]);return(0,h.jsxs)(C,{className:me,role:"listbox",ref:this.set$select,children:[(0,h.jsx)("button",{type:"button",onClick:this.toggle,disabled:Pe,children:(0,h.jsx)(H.A,{children:this.displayText})}),typeof this.props.arrowSvg=="string"?(0,h.jsx)("span",{className:"caret",dangerouslySetInnerHTML:{__html:this.props.arrowSvg}}):(0,h.jsx)("span",{className:"caret",children:this.props.arrowSvg}),(0,h.jsx)(g,{isOpen:ke,...this.props,value:mt,$select:Re,canSelect:Y,onChange:this.onSelect,onClose:this.close,menuX:Ye})]})}}b(X,"propTypes",{size:u().oneOf(["regular","small"]),theme:u().oneOf(["core","plain"]),menuTheme:u().oneOf(["core","plain","check"]),unstyled:u().bool,className:u().string,menuClassName:u().string,lang:u().string,placeholder:u().string,optionList:u().arrayOf(u().oneOfType([u().node,u().shape({label:u().node,value:u().any,isDisabled:u().bool}),u().arrayOf(u().oneOfType([u().node,u().shape({label:u().node,value:u().any,isDisabled:u().bool})]))])).isRequired,value:u().oneOfType([u().number,u().string]),isDisabled:u().bool,disabled:u().bool,readOnly:u().bool,onChange:u().func,menuX:u().oneOf(["left","center","right"]),optionLabelProp:u().string,arrowSvg:u().oneOfType([u().string,u().node])}),b(X,"defaultProps",{size:"regular",theme:"plain",menuTheme:"plain",className:"",menuClassName:"",lang:"zh-CN",optionList:[],isDisabled:!1,onChange:()=>null,menuX:"left",arrowSvg:G.t4.rH});class g extends s.PureComponent{constructor(){super(...arguments),b(this,"state",{isDownward:!0}),b(this,"portal",(0,G.ep)(j,"SelectMenuPortal")),b(this,"menuBaseRef",(0,s.createRef)()),b(this,"position",se=>{const{$select:te,menuX:fe}=this.props,{menuBaseRef:{current:De}}=this;if(se){const ke=V()(se,"target");if(ke&&D()(ke)&&ke.matches(".SelectMenu"))return}const{isDownward:Ye}=(0,I.d)({$menuBase:De,$opener:te,menuX:fe,shouldSetMaxHeight:!0});this.setState({isDownward:Ye})}),b(this,"onChange",se=>{const{onChange:te}=this.props,{isDownward:fe}=this.state,De=se.currentTarget,Ye=De.closest(".SelectMenu, .CoreSelectMenu, .CheckSelectMenu");if(!De||!Ye)return this.onlose();const{top:ke,bottom:Re}=De.getBoundingClientRect(),{top:mt,bottom:Pe}=Ye.getBoundingClientRect();return fe&&ke>=mt||!fe&&Re<=Pe?De.classList.contains("title")?void 0:te(se):this.onClose()}),b(this,"onClose",()=>{const{onClose:se}=this.props;se()}),b(this,"scrollIntoActive",()=>{const{menuBaseRef:{current:se}}=this,te=(0,G.$)("li[role=option].is-active",se);te&&te.scrollIntoView({block:"start"})}),b(this,"onClickOutside",se=>{let{target:te}=se;const{$select:fe}=this.props,De=!j.contains(te),Ye=te.closest("label"),ke=Ye&&Ye.contains(fe);De&&!ke&&this.onClose()})}componentDidMount(){const{menuBaseRef:{current:se}}=this;(0,G.sA)((0,G.$)(".SelectMenu",se))}componentDidUpdate(se){let{isOpen:te}=se;const{isOpen:fe}=this.props;!te&&fe&&(this.position(),this.scrollIntoActive())}componentWillUnmount(){this.portal&&this.portal.remove()}render(){return(0,p.createPortal)(this.menu,this.portal)}get menu(){const{isOpen:se,isDisabled:te,menuTheme:fe,menuClassName:De,menuX:Ye,optionList:ke,lang:Re,emptyMsg:mt,value:Pe,canSelect:Q}=this.props,{isDownward:Y}=this.state,me=ke.length===0,Ne=(0,G.Hn)([fe==="core"?"CoreSelectMenu":fe==="check"?"CheckSelectMenu":"SelectMenu",De,"x-"+Ye,se&&"is-open",Y?"is-downward":"is-upward",te&&"is-disabled",me&&"is-empty",Q?"can-select":"cant-select"]);return(0,h.jsx)(f,{ref:this.menuBaseRef,className:"SelectMenuBase",children:(0,h.jsxs)(y,{className:Ne,onTransitionEnd:this.onTransitionEnd,children:[me?(0,h.jsx)("li",{className:"SelectOption empty-msg",children:mt||d[Re].select_empty_msg}):ke.map((je,Ge)=>J()(je)?(0,h.jsx)(L,{menuTheme:fe,optionList:je,value:Pe,onChange:this.onChange},Ge):(0,h.jsx)(ne,{menuTheme:fe,isActive:(0,G.o3)(je,Pe),option:je,isDisabled:je.isDisabled,onChange:this.onChange},Ge)),se&&(0,h.jsx)(B.A,{target:document,onClick:(0,B.t)(this.onClickOutside,{capture:!0})}),se&&(0,h.jsx)(B.A,{target:document,onScroll:(0,B.t)(this.position,{capture:!0})})]})})}}b(g,"propTypes",{...X.propTypes,isOpen:u().bool,canSelect:u().bool,onChange:u().func,onClose:u().func,$select:u().instanceOf(Element)}),b(g,"defaultProps",{isOpen:!1});function L(ee){let{value:se,optionList:[te,...fe],menuTheme:De,onChange:Ye}=ee;return(0,h.jsxs)("li",{className:"SelectGroup",children:[(0,h.jsx)(H.A,{className:"title",onClick:Ye,children:te}),(0,h.jsx)("ul",{children:fe.map((ke,Re)=>(0,h.jsx)(ne,{menuTheme:De,option:ke,isActive:(0,G.o3)(ke,se),isDisabled:ke.isDisabled,onChange:Ye},Re))})]})}L.propTypes={optionList:u().array,onChange:u().func,menuTheme:u().string,value:u().string};function ne(ee){let{option:se,isActive:te,isDisabled:fe,menuTheme:De,onChange:Ye}=ee;const ke=(0,G.Hn)(["SelectOption",te&&"is-active",fe&&"is-disabled"]),Re=(0,G.Oi)(se),mt=(0,G.nE)(se);return(0,h.jsxs)("li",{role:"option","data-value":mt,className:ke,onClick:fe?void 0:Ye,children:[(0,h.jsx)(H.A,{children:Re}),De==="check"&&te&&(0,h.jsx)(pe.A,{name:"check"})]})}ne.propTypes={option:u().oneOfType([u().node,u().object]),isActive:u().bool,isDisabled:u().bool,menuTheme:u().string,onChange:u().func}},79458:(Se,ce,n)=>{"use strict";n.d(ce,{Os:()=>M,Tw:()=>F,ft:()=>ie});var s=n(38502),p=n(72214);const N="/mb-static/2308/loading.gif",u="/mb-static/2308/loading-mo.gif",B=(D,pe)=>pe?"."+D:D,q=D=>(0,p.jsx)("img",{className:D.className,alt:"Loading...",src:B(D.isMockitt?u:N,D.isHtmlZip)}),V=D=>(0,p.jsx)("img",{className:D.className,alt:"Loading...",src:B(ENV.IS_WONDER_SHARE?u:N,D.isHtmlZip)}),R=D=>(0,p.jsx)("img",{className:D.className,alt:"Loading...",src:B(u,D.isHtmlZip)});var J=n(68818);const M=n.j!=15?(0,s.memo)(D=>(0,p.jsx)(q,{...D,isHtmlZip:(0,J.FM)()})):null,F=n.j!=15?(0,s.memo)(D=>(0,p.jsx)(V,{...D,isHtmlZip:(0,J.FM)()})):null,ie=(0,s.memo)(D=>(0,p.jsx)(R,{...D,isHtmlZip:(0,J.FM)()}))},83414:(Se,ce,n)=>{"use strict";n.d(ce,{gE:()=>p,nM:()=>s,nQ:()=>N});const s="local_sharing_copy_url_v8_1",p=50,N={public:"share_anyone_view",restricted:"org_members_only"}},83557:(Se,ce,n)=>{"use strict";n.d(ce,{GT:()=>B,Nc:()=>N,lq:()=>u});var s=n(62732),p=n(55338);const N=async(q,V,R)=>{const[J,M]=await(0,s.Vz)(p.r,q),F="/flpak/w-paknewft?"+new URLSearchParams({upperType:"project-basic",type:"proto2",teamCid:V,treLen:String(M.byteLength),treRawLen:String(J)}),ie=await fetch(F,{method:"POST",body:M,headers:{"mb-client-opt":encodeURIComponent(JSON.stringify(R))}});if(!ie.ok)throw Object.assign(new Error("failed to create new flpak: "+F),{message:await ie.text(),status:ie.status});return ie.json()},u=async(q,V,R)=>{const[J,M]=await(0,s.Vz)(p.r,q),F="/flpak/w-pakfcgmkt?"+new URLSearchParams({upperType:"combo-group",upperAction:"create-reviewable-project",type:"proto2",teamCid:V,treLen:String(M.byteLength),treRawLen:String(J)}),ie=await fetch(F,{method:"POST",body:M,headers:{"mb-client-opt":encodeURIComponent(JSON.stringify(R))}});if(!ie.ok)throw Object.assign(new Error("failed to create new fcgmkt: "+F),{message:await ie.text(),status:ie.status});return ie.json()},B=async(q,V,R)=>{const[J,M]=await(0,s.Vz)(p.r,q),F="/flpak/w-paksclibft?"+new URLSearchParams({upperType:"project-basic",type:"proto2",teamCid:V,upperCid:R,treLen:String(M.byteLength),treRawLen:String(J)}),ie=await fetch(F,{method:"POST",body:M,headers:{"mb-client-opt":encodeURIComponent(JSON.stringify({}))}});if(!ie.ok)throw Object.assign(new Error("failed to create new sclib: "+F),{message:await ie.text(),status:ie.status});return ie.json()}},84934:(Se,ce,n)=>{var s=n(24469),p=n(59696),N=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,u="\\u0300-\\u036f",B="\\ufe20-\\ufe2f",q="\\u20d0-\\u20ff",V=u+B+q,R="["+V+"]",J=RegExp(R,"g");function M(F){return F=p(F),F&&F.replace(N,s).replace(J,"")}Se.exports=M},84957:(Se,ce,n)=>{"use strict";n.d(ce,{_:()=>s});const s=N=>encodeURIComponent(JSON.stringify(N)),p=N=>JSON.parse(decodeURIComponent(N))},85378:(Se,ce,n)=>{var s=n(45308),p=n(62783),N=n(59483);function u(B){return N(p(B,void 0,s),B+"")}Se.exports=u},88856:(Se,ce,n)=>{"use strict";n.d(ce,{eY:()=>h,At:()=>f,pU:()=>y,DV:()=>b,Ay:()=>C});var s=n(95549),p=n(38502);if(n.j!=15)var N=n(37342);var u=n(18381),B=n(67787);const q=(0,B.DU)(["div.notice-tips-container{z-index:9999;.notice-tips{min-height:38px;p{color:",";}:not(.pure-svg-icon).svg-icon{fill:",";}&.success{.tip-svg{path:nth-child(1){fill:",";}}}&.warning{.tip-svg{path:nth-child(1){fill:",";}}}&.error{.tip-svg{circle{fill:",";}}.button-a{cursor:pointer;}.button-p{margin-left:0;}}&.super{background-color:",";p{margin:0 16px 0 4px;}.times-icon{path{fill:",";}}}}}"],S=>S.theme.color_text_btn,S=>S.theme.color_text_btn,S=>S.theme.color_success,S=>S.theme.color_warning,S=>S.theme.color_error,S=>S.theme.color_error,S=>S.theme.color_text_btn);var V=n(58315),R=n(51582),J=n(29523);const M=B.Ay.div.withConfig({displayName:"styles__StyledNoticeTips",componentId:"sc-1ehf3rt-0"})([".notice-tips{width:100%;height:100%;min-height:38px;padding:8px 16px;display:flex;align-items:center;font-size:14px;color:",";box-shadow:0 2px 8px 0 rgba(0,0,0,0.05),0 4px 12px 0 rgba(0,0,0,0.15);border-radius:6px;p{margin:0 0 0 4px;}&.info{background-color:",";p{margin:0 4px;}a{margin-left:12px;color:#80BCFF;&:hover{color:#A6D2FF;}}}&.success{background:",";box-shadow:0 3px 6px -4px rgba(0,0,0,0.12),0 6px 16px rgba(0,0,0,0.08),0 9px 28px 8px rgba(0,0,0,0.05);border-radius:6px;}&.warning{background-color:",";}&.error{background-color:",";}&.super{background-color:",";}&.loading{background-color:",";.tip-svg{animation:design-ani-spinning 1s infinite linear;fill:rgb(69,70,71);}}@keyframes design-ani-spinning{0%{transform:rotate(0deg);}100%{transform:rotate(359deg);}}.times-icon{width:20px;display:flex;flex-shrink:0;opacity:1;cursor:pointer;transition:opacity 0.15s ease-out;margin-left:16px;align-self:center;&:hover{opacity:1;}}.tip-svg{width:20px;height:20px;}}a{text-decoration:underline;color:",";&:hover{color:",";}}.fade{&-enter{transform:translateY(-50%);opacity:0;}&-enter-active{opacity:1;transform:translateY(0);transition:all 0.15s ease-out;}&-exit{opacity:1;}&-exit-active{opacity:0;transition:opacity 0.25s ease-in-out;}&-exit-done{opacity:0;}}"],J.f.color_bg_white.value_light,J.f.color_btn_secondary_active.value_dark,J.f.color_btn_secondary_active.value_dark,J.f.color_btn_secondary_active.value_dark,J.f.color_btn_secondary_active.value_dark,J.f.color_error.value_light,J.f.color_btn_secondary_active.value_dark,J.f.color_text_link_normal.value_dark,J.f.color_text_link_hover.value_dark);var F=n(72214);const ie={success:"design/toast/success",error:"design/toast/error",warning:"design/toast/warning",super:"design/toast/offline",loading:"design/toast/loading"};class D extends p.PureComponent{render(){const{isShow:A,type:K,text:d,showClose:j,isReactDom:T,CustomChildComponent:X,svgName:g,onDisappear:L}=this.props;return(0,F.jsx)(M,{children:(0,F.jsx)(V.A,{in:A,timeout:2500,classNames:"fade",unmountOnExit:!0,children:(0,F.jsxs)("div",{className:"notice-tips "+K,children:[g&&(0,F.jsx)(R.C,{name:g,className:"tip-svg",isColorPure:!0}),!g&&K&&ie[K]&&(0,F.jsx)(R.C,{name:ie[K],className:"tip-svg",isColorPure:!0}),(0,F.jsxs)(p.Fragment,{children:[T?d:(0,F.jsxs)(F.Fragment,{children:[(0,F.jsx)("p",{dangerouslySetInnerHTML:{__html:d}}),(typeof X=="function"||typeof X=="object")&&(0,F.jsx)(X,{})]}),j&&(0,F.jsx)(R.C,{className:"times-icon",name:"general/times",onClick:L})]})]})})})}}(0,s.A)(D,"defaultProps",{isShow:!1,type:"info",showClose:!1,isReactDom:!1,svgName:""});const pe=B.Ay.div.withConfig({displayName:"styles__StyledNoticeTipsContainer",componentId:"sc-1ewb7bf-0"})(["position:fixed;left:50%;top:56px;max-width:480px;transform:translateX(-50%);z-index:1052;"]);var H=n(69623);class G extends p.PureComponent{constructor(A){super(A),(0,s.A)(this,"timerId",null),(0,s.A)(this,"handleDisappear",()=>{typeof this.props.closeCallback=="function"&&this.props.closeCallback()}),this.el=document.createElement("div")}componentDidMount(){document.body.appendChild(this.el),this.props.isShow&&this.updateTimer()}componentDidUpdate(A,K,d){const{isShow:j,id:T}=this.props,{isShow:X,id:g}=A;(T!==g&&j||j&&j!==X)&&this.updateTimer()}updateTimer(){this.timerId&&clearTimeout(this.timerId);const{duration:A}=this.props;this.timerId=A&&setTimeout(this.handleDisappear,A)}componentWillUnmount(){this.el&&document.body.contains(this.el)&&document.body.removeChild(this.el),this.timerId&&clearTimeout(this.timerId)}render(){const{isShow:A,type:K,text:d,showClose:j,isReactDom:T,CustomChildComponent:X,svgName:g,className:L}=this.props;return(0,H.createPortal)(A&&(0,F.jsx)(pe,{className:L,children:(0,F.jsx)(D,{isShow:A,type:K,showClose:j,onDisappear:this.handleDisappear,isReactDom:T,text:d,CustomChildComponent:X,svgName:g})}),this.el)}}(0,s.A)(G,"defaultProps",{text:"",isShow:!1,type:"info",showClose:!1,isReactDom:!1,CustomChildComponent:void 0,svgName:"",id:"",duration:2e3,closeCallback:()=>null});const I=G,v={isShow:!1,text:"",type:"info",showClose:!1,isReactDom:!1,CustomChildComponent:void 0,svgName:"",duration:2e3,id:""};class C extends p.PureComponent{constructor(A){super(A),(0,s.A)(this,"closeCallback",null),(0,s.A)(this,"handleDisappear",()=>{this.setState({isShow:!1}),typeof this.closeCallback=="function"&&this.closeCallback(),this.priority=0}),this.state=v,this.priority=0}componentDidMount(){MB.notice&&(this.prevNotice=MB.notice),MB.notice=A=>{let{text:K,type:d="info",CustomChildComponent:j=void 0,duration:T=2e3,showClose:X=!1,isReactDom:g=!1,closeCallback:L=null,priority:ne=0,svgName:ee="",id:se=Math.random().toString(36).slice(2),isShow:te=!0}=A;this.priority>ne||(clearTimeout(this.timer),this.setState({isShow:te,text:K,type:d,showClose:X,id:se,isReactDom:g,CustomChildComponent:j,svgName:ee,duration:T}),this.closeCallback=L,this.priority=ne)}}componentWillUnmount(){this.setState({isShow:!1}),this.prevNotice&&(MB.notice=this.prevNotice)}render(){const{isShow:A,type:K,duration:d,text:j,showClose:T,id:X,isReactDom:g,CustomChildComponent:L,svgName:ne}=this.state;return(0,F.jsxs)(F.Fragment,{children:[(0,F.jsx)(I,{className:"notice-tips-container",isShow:A,type:K,text:j,id:X,duration:d,showClose:T,isReactDom:g,CustomChildComponent:L,svgName:ne,closeCallback:this.handleDisappear}),(0,F.jsx)(q,{})]})}}const f=S=>(0,p.memo)(()=>{const A=()=>(0,N.JW)("/hc/articles/393","_blank","noreferrer");return(0,F.jsx)("a",{style:{cursor:"pointer"},onClick:A,children:S})}),y=n.j!=15?(0,p.memo)(()=>{const S=(0,u.wA)(),A=()=>S({type:"toolbar:sharingManager:open"});return(0,F.jsx)("a",{onClick:A,children:I18N.ScreenPanel.share_panel})}):null,h=S=>(0,p.memo)(()=>{const A=()=>(0,N.JW)(I18N.link.link_article_business_font_state,"_blank","noreferrer");return(0,F.jsx)("a",{className:"button-a",onClick:A,children:S})}),b=S=>{let{onClick:A}=S;return(0,p.memo)(()=>{const K=(0,u.wA)(),d=()=>{A&&A(K)};return(0,F.jsxs)(F.Fragment,{children:[(0,F.jsx)("a",{className:"button-a",onClick:d,children:I18N.dModule.network_api_error_2}),(0,F.jsx)("p",{className:"button-p",children:I18N.dModule.network_api_error_3})]})})}},89022:function(Se){(function(ce,n){Se.exports=n()})(this,function(){"use strict";return function(ce,n,s){n.prototype.isBetween=function(p,N,u,B){var q=s(p),V=s(N),R=(B=B||"()")[0]==="(",J=B[1]===")";return(R?this.isAfter(q,u):!this.isBefore(q,u))&&(J?this.isBefore(V,u):!this.isAfter(V,u))||(R?this.isBefore(q,u):!this.isAfter(q,u))&&(J?this.isAfter(V,u):!this.isBefore(V,u))}}})},89208:(Se,ce,n)=>{"use strict";n.d(ce,{n:()=>F,A:()=>ie});var s=n(38502),p=n(53732),N=n.n(p),u=n(17307),B=n(83199),q=n(71272),V=n(67787);const R=V.Ay.ol.withConfig({displayName:"styles__StyledNewSlideNav",componentId:"sc-17e9w47-0"})(["display:flex;height:48px;.nav-label{position:relative;height:100%;padding:0 3px;display:flex;align-items:center;justify-content:center;white-space:nowrap;}.nav-item{position:relative;display:flex;flex:none;justify-content:center;align-items:center;color:",";padding:0 3px;cursor:pointer;.nav-underline{position:absolute;display:none;bottom:0;width:70%;border-bottom:2px solid ",";}&:lang(ja){font-size:10px;}&.active{color:",";&:not(:first-child:nth-last-child(1)){.nav-underline{display:inline;}}&.background_active{background-color:transparent;}&.line_active::after{content:'';position:absolute;bottom:0;width:30px;height:2px;background:",";}}&.disabled{color:",";cursor:not-allowed;}&:not(:first-child){}.tabNewIcon{position:absolute;width:30px;top:0;right:3px;path:first-child{fill:#1684fc;}path:last-child{fill:#fff;}}}.nav-item-small:lang(en){font-size:10px;}min-height:48px;padding:0 6px;.nav-item{font-size:13px;&.active{font-weight:bold;}}"],D=>D.theme.color_text_L3,D=>D.theme.color_text_L1,D=>D.theme.color_text_L1,D=>D.theme.color_text_L1,D=>D.theme.color_text_disabled01);var J=n(72214);const M=(0,s.memo)(D=>{let{activeIndex:pe=0,className:H,children:G,onTabChange:I}=D;return(0,J.jsx)(R,{className:N()(H),children:s.Children.map(G,(v,C)=>{const f={index:C,active:C===pe,onClick:I};return s.cloneElement(v,f)})})}),F=(0,s.memo)(D=>{let{className:pe,index:H,active:G,onClick:I,label:v,tooltip:C,isNew:f,disabled:y,tempDisabled:h,canMouseEnterChange:b=!1}=D;const S=(0,s.useRef)(null),A=()=>{if(y)return h&&MB.notice({text:I18N.Common.feature_is_not_available,type:"warning"}),null;I(H)},K=()=>{b&&(S.current&&clearTimeout(S.current),S.current=setTimeout(()=>{A==null||A()},q.w))},d=()=>{b&&S.current&&(clearTimeout(S.current),S.current=null)},j=(0,J.jsxs)("li",{className:N()("nav-item",{active:G},{disabled:y},pe),onClick:A,onMouseEnter:K,onMouseLeave:d,children:[v&&(0,J.jsxs)("span",{className:"nav-label",children:[v,C&&C]}),f&&(0,J.jsx)(u.C,{name:"design/tab/new",className:"tabNewIcon"})]});return y?(0,J.jsx)(B.A,{content:"\u6682\u4E0D\u5F00\u653E",direction:"down",children:j}):j}),ie=M},90108:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>v});var s=n(38502),p=n(67787),N=n(25582),u=n.n(N),B=n(63986),q=n.n(B),V=n(69368);const R=p.Ay.label.withConfig({displayName:"styled__StyledLabel",componentId:"sc-1pwp3tx-0"})(["position:relative;display:inline-flex;align-items:baseline;line-height:1.5;cursor:pointer;color:#5b6b73;> input[type=radio]{position:absolute;opacity:0;}&.regular{font-size:14px;}&.small{font-size:12px;.Check-state{transform:translateY(2px);}}&.readonly{cursor:default;}&.is-disabled{cursor:not-allowed;&::after{content:'';position:absolute;left:0;top:0;width:100%;height:100%;background-color:rgba(255,255,255,0.5);}}.Check-state{position:relative;flex:0 0 1em;margin-right:0.33333em;width:1em;height:1em;transform:translateY(1px);font-size:12px;background-color:#f6f7f8;border:1px solid #8d9ea7;border-radius:2px;color:#fff;.svg-icon.check{position:absolute;top:-1px;left:-1px;opacity:0;}}&.small .Check-state{transform:translateY(2px);}&.CoreRadio,&.Radio{&.is-checked{.Check-state{background-color:#298df8;border-color:transparent;color:#fff;.svg-icon.check{transform:scale(0.833);}}}}&:not(.is-checked) .Check-state .icon{speak:none;opacity:0;}&.CoreRadio{.Check-state{background-color:#fff;border-color:#bacdd6;}&.is-checked .Check-state{background-color:#eb5648;}}"]),J=(0,p.Ay)(R).withConfig({displayName:"styled__StyledRadio",componentId:"sc-1pwp3tx-1"})([".Check-state{padding:1px;border-radius:50%;&:after{content:'';position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);display:block;width:4px;height:4px;background-color:#fff;border-radius:50%;}}.is-checked .Check-state{position:relative;}"]),M=p.Ay.span.withConfig({displayName:"styled__StyledRadioGroup",componentId:"sc-1pwp3tx-2"})(["position:relative;display:flex;flex-wrap:wrap;&.is-disabled{cursor:not-allowed;}.Radio,.CoreRadio{margin-right:.5em;min-height:2em;}"]);var F=n(72214);function ie(C,f,y){return(f=D(f))in C?Object.defineProperty(C,f,{value:y,enumerable:!0,configurable:!0,writable:!0}):C[f]=y,C}function D(C){var f=pe(C,"string");return typeof f=="symbol"?f:f+""}function pe(C,f){if(typeof C!="object"||!C)return C;var y=C[Symbol.toPrimitive];if(y!==void 0){var h=y.call(C,f||"default");if(typeof h!="object")return h;throw new TypeError("@@toPrimitive must return a primitive value.")}return(f==="string"?String:Number)(C)}class H extends s.PureComponent{constructor(){super(...arguments),ie(this,"state",{prevProps:this.props,isChecked:this.props.isChecked}),ie(this,"onToggle",()=>{const{name:f,value:y,label:h,onToggle:b,onChange:S}=this.props,{isChecked:A}=this.state,{canToggle:K}=this,d=K?!0:A;this.setState({isChecked:d}),b(d,f,y||h),S(f,y||h,d)})}static getDerivedStateFromProps(f,y){let{prevProps:h,isChecked:b}=y;return q()(h,f)?null:{prevProps:f,isChecked:f.isChecked}}get isDisabled(){const{isDisabled:f,disabled:y}=this.props;return f||y}get readOnly(){return this.props.readOnly}get canToggle(){const{isDisabled:f,readOnly:y}=this;return!f&&!y}render(){const{size:f,theme:y,className:h,label:b,name:S}=this.props,{isChecked:A}=this.state,{isDisabled:K,readOnly:d}=this;return(0,F.jsxs)(J,{className:(0,V.Hn)([y==="core"?"CoreRadio":"Radio",f,h,A&&"is-checked",K&&"is-disabled",d&&"readonly"]),children:[(0,F.jsx)("input",{type:"radio",defaultChecked:A,disabled:K,name:S,onClick:this.onToggle}),(0,F.jsx)("span",{className:"Check-state"}),(0,F.jsx)("span",{className:"Check-label",children:b})]})}}ie(H,"propTypes",{size:u().oneOf(["regular","small"]),theme:u().oneOf(["core","plain"]),className:u().string,label:u().any,name:u().string,value:u().any,isChecked:u().bool,isDisabled:u().bool,disabled:u().bool,readOnly:u().bool,onChange:u().func.isRequired,onToggle:u().func.isRequired}),ie(H,"defaultProps",{size:"regular",theme:"plain",isChecked:!1,label:"",className:"",onChange:()=>null,onToggle:()=>null});class G extends s.PureComponent{constructor(){super(...arguments),ie(this,"name",this.props.name||Math.random().toString(36).substring(2,15)),ie(this,"state",{prevProps:this.props,value:this.props.value}),ie(this,"createOnChangeHandler",(f,y,h)=>()=>{const{onToggle:b,onChange:S}=this.props,{value:A}=this.state,{canToggle:K}=this,d=K?y:A;this.setState({value:d}),b(d,f),S({name:f,value:d,idx:h})})}static getDerivedStateFromProps(f,y){let{prevProps:h,value:b}=y;return q()(h,f)?null:{prevProps:f,value:f.value}}get isDisabled(){const{isDisabled:f,disabled:y}=this.props;return f||y}get readOnly(){return this.props.readOnly}get canToggle(){const{isDisabled:f,readOnly:y}=this;return!f&&!y}render(){const{size:f,theme:y,className:h,optionList:b}=this.props,{value:S}=this.state,{name:A,isDisabled:K,readOnly:d}=this,j=(0,V.Hn)([y==="core"?"CoreRadioGroup":"RadioGroup",f,h,K&&"is-disabled",d&&"readonly"]);return(0,F.jsx)(M,{className:j,children:b.map((T,X)=>T&&(0,F.jsx)(H,{name:A,size:f,theme:y,label:(0,V.Oi)(T),type:"radio",isChecked:(0,V.o3)(T,S),isDisabled:K||T.isDisabled,readOnly:d,onChange:K||T.isDisabled?void 0:this.createOnChangeHandler(A,(0,V.nE)(T),X)},X))})}}ie(G,"propTypes",{size:u().oneOf(["regular","small"]),theme:u().oneOf(["core","plain"]),className:u().string,name:u().string,optionList:u().arrayOf(u().oneOfType([u().string,u().number,u().shape({label:u().any,value:u().any,isDisabled:u().bool})])).isRequired,value:u().oneOfType([u().number,u().string]),isDisabled:u().bool,disabled:u().bool,readOnly:u().bool,onChange:u().func.isRequired,onToggle:u().func.isRequired}),ie(G,"defaultProps",{size:"regular",theme:"plain",className:"",optionList:[],onChange:()=>null,onToggle:()=>null});const I=(0,p.Ay)(G).withConfig({displayName:"styles__StyledRadioGroup",componentId:"sc-1x96wso-0"})([".Radio{color:",';input[type="radio"]{cursor:pointer;}&.is-checked{color:',";.Check-state{background:",";&::after{background:#fff;}}}&:not(.is-checked){.Check-state{background:",";border:1px solid ",";&::after{background:",";}}}&.is-disabled::after{background-color:",";opacity:0.5;}}"],C=>C.theme.color_text_L2,C=>C.theme.color_text_L1,C=>C.theme.color_proto,C=>C.theme.color_bg_white,C=>C.theme.color_text_disabled01,C=>C.theme.color_bg_white,C=>C.theme.color_bg_white);class v extends s.PureComponent{render(){return(0,F.jsx)(I,{...this.props})}}},91752:(Se,ce,n)=>{"use strict";n.d(ce,{Ay:()=>H});var s=n(38502),p=n(25582),N=n.n(p),u=n(45525),B=n.n(u),q=n(51582),V=n(69368),R=n(67787);const J=R.Ay.button.withConfig({displayName:"styled__StyledButton",componentId:"sc-1p06iev-0"})(["&.RegularButton,&.PrimaryButton,&.PrimaryCoreButton,&.RegularCoreButton,&.SecondaryCoreButton,&.TertiaryCoreButton{padding:0 0.5em;height:2.125rem;font-size:0.875rem;border:1px solid;border-radius:2px;cursor:pointer;transition:all 0.2s ease-out;&:not(button){display:inline-flex;justify-content:center;align-items:center;}&:disabled{cursor:not-allowed;opacity:0.6;}.icon{margin-right:0.125em;}&.small{height:2em;font-size:0.75rem;}.svg-icon.loading{margin-right:0.5em;vertical-align:-.15em;animation:ibot-ani-spinning 1.5s infinite ease-out;}}&.PrimaryCoreButton,&.RegularCoreButton,&.SecondaryCoreButton,&.TertiaryCoreButton{padding:0 1em;height:2.375rem;&.small{height:2em;}&:disabled{background-color:#f6f7f8;border-color:#dedee4;color:#c8cdd1;opacity:1;}}&.PrimaryCoreButton{background-color:#eb5648;&,&:link,&:visited{border-color:transparent;color:#fff;}&:enabled:hover,a&:hover{background-color:#ef776c;color:#fff;}&:enabled:active,a&:active{background-color:#bc4439;color:#e4b4b0;}}&.RegularCoreButton,&.SecondaryCoreButton{background-color:#fff;&,&:link,&:visited{color:#eb5648;}&:enabled:hover,a&:hover{border-color:#ffa39e;color:#ef776c;}&:enabled:active,a&:active{border-color:#e84030;color:#bc4439;}}&.TertiaryCoreButton{background-color:#fff;border-color:#c8cdd1;&,&:link,&:visited{color:#415058;}&:enabled:hover,a&:hover{background-color:#8d9ea7;border-color:#7d8694;color:#fff;}&:enabled:active,a&:active{background-color:#5b6b73;border-color:#415058;}}&.RegularButton{&,&:link,&:visited{color:#8d9ea7;}&:enabled:hover,a&:hover{color:#5b6b73;}&:disabled{opacity:0.6;}}&.PrimaryButton{background-color:#8d9ea7;border-color:transparent;&,&:link,&:visited{color:#fff;}&:enabled:hover,a&:hover{background-color:#5b6b73;color:#fff;}&:enabled:active,a&:active{color:rgba(255,255,255,0.6);}}&.TextButton,&.TextCoreButton{transition:all 0.1s ease-out;&,&:link,&:visited{color:#298df8;}&:disabled{cursor:not-allowed;opacity:0.6;}&:enabled:hover,a&:hover{color:#0d7ef7;text-decoration:underline;}.svg-icon.loading{margin-right:0.25em;vertical-align:-.15em;animation:ibot-ani-spinning 1.5s infinite ease-out;}.icon{margin-right:0.125em;}&.small{height:2em;font-size:0.75rem;}}&.TextCoreButton{&,&:link,&:visited{color:#eb5648;}&:enabled:hover,a&:hover{color:#ef776c;text-decoration:none;}&:enabled:active,a&:active{color:#bc4439;}}"]);var M=n(72214);function F(C,f,y){return(f=ie(f))in C?Object.defineProperty(C,f,{value:y,enumerable:!0,configurable:!0,writable:!0}):C[f]=y,C}function ie(C){var f=D(C,"string");return typeof f=="symbol"?f:f+""}function D(C,f){if(typeof C!="object"||!C)return C;var y=C[Symbol.toPrimitive];if(y!==void 0){var h=y.call(C,f||"default");if(typeof h!="object")return h;throw new TypeError("@@toPrimitive must return a primitive value.")}return(f==="string"?String:Number)(C)}const pe={primary:"Primary",regular:"Regular",secondary:"Regular",tertiary:"Tertiary",text:"Text"};class H extends s.PureComponent{get className(){const{type:f,theme:y,size:h,className:b}=this.props,{isDisabled:S,isLoading:A}=this;return(0,V.Hn)(["Button",""+pe[f]+(y==="core"?"CoreButton":"Button"),h!=="regular"&&h,A&&"is-loading",S&&"is-disabled",b])}get isDisabled(){const{isDisabled:f,disabled:y}=this.props;return f||y}get isLoading(){const{isLoading:f,loading:y}=this.props;return f||y}render(){const{className:f,isLoading:y,isDisabled:h}=this,{iconType:b,icon:S,children:A,...K}=this.props;return(0,M.jsx)(J,{className:f,disabled:h,onClick:d=>h&&d.preventDefault(),type:"button",...B()(K,["className","type","theme","isDisabled","disabled","isLoading","loading"]),children:(0,M.jsxs)(M.Fragment,{children:[y&&(0,M.jsx)(q.A,{name:"loading"}),S&&b==="svg"&&(0,M.jsx)(q.A,{name:S}),A]})})}}F(H,"propTypes",{type:N().oneOf(["primary","regular","secondary","tertiary","text"]),size:N().oneOf(["regular","small"]),theme:N().oneOf(["core","plain"]),iconType:N().oneOf(["svg","dora","mb","icon","fa","md"]),icon:N().string,className:N().string,isDisabled:N().bool,disabled:N().bool,isLoading:N().bool,loading:N().bool,children:N().any,html:N().string}),F(H,"defaultProps",{type:"regular",size:"regular",theme:"plain",icon:"",className:"",isDisabled:!1});function G(C){return _jsx(H,{...C,theme:"core"})}function I(C){return _jsx(G,{...C,type:"primary"})}function v(C){return _jsx(G,{...C,type:"tertiary"})}},92114:(Se,ce,n)=>{"use strict";n.d(ce,{A:()=>H});var s=n(38502),p=n(67787),N=n(25582),u=n.n(N),B=n(63986),q=n.n(B),V=n(69368);const R=p.Ay.label.withConfig({displayName:"styled__StyledSwitch",componentId:"sc-16h4tgy-0"})(["position:relative;display:inline-block;margin:0 10px;width:28px;height:10px;vertical-align:text-bottom;background:rgba(22,132,252,0.4);border-radius:100px;cursor:pointer;transform:matrix(-1,0,0,1,0,0);transition:all 0.2s ease-in;transition-delay:0.15s;&.readonly{cursor:default;}> button{position:absolute;top:-20%;left:0;display:flex;justify-content:center;align-items:center;width:14px;height:14px;font-size:14px;border-radius:50%;background:#1684FC;box-shadow:0px 1px 1px rgba(0,0,0,0.15),0px 0px 2px rgba(0,0,0,0.12);transform:matrix(-1,0,0,1,0,0);cursor:inherit;transition:all 0.2s cubic-bezier(0.55,0.06,0.68,0.19);}&.small{width:28px;height:10px;vertical-align:baseline;> button{width:14px;height:14px;border-width:1px;}}&.is-checked{background:#dbdbdb;> button{transform:translate(14px,0%);cursor:inherit;background-color:#fff;box-shadow:0px 1px 1px rgba(0,0,0,0.15),0px 0px 2px rgba(0,0,0,0.12);}&.small > button{transform:translate(14px,0);background-color:#999;}&.small{background-color:#59515c;}}&.is-disabled{background-color:#dedee4;cursor:not-allowed;&.is-checked{background-color:rgba(22,132,252,0.4);}button{background-color:#f6f7f8;}}&.Switch-IOS{background:#1684fc;transform:matrix(1,0,0,1,0,0);transition:all 0.2s ease-in;> button{top:1px;left:1px;background:#fff;transform:matrix(1,0,0,1,0,0);}&.small{width:24px;height:12px;border-radius:6px;> button{width:10px;height:10px;border-width:0;}}&.is-checked{&.small > button{transform:translate(12px,0);background-color:#fff;}&.small{background-color:#ccc;}}}&.Switch-IOS-NoTransform{background:#1684fc;transition:all 0.2s ease-in;> button{top:1px;left:1px;background:#fff;transform:matrix(1,0,0,1,0,0);}&.small{width:24px;height:12px;border-radius:6px;> button{width:10px;height:10px;border-width:0;}}&.is-checked{&.small > button{transform:translate(12px,0);background-color:#fff;}&.small{background-color:#ccc;}}}"]);var J=n(72214);function M(G,I,v){return(I=F(I))in G?Object.defineProperty(G,I,{value:v,enumerable:!0,configurable:!0,writable:!0}):G[I]=v,G}function F(G){var I=ie(G,"string");return typeof I=="symbol"?I:I+""}function ie(G,I){if(typeof G!="object"||!G)return G;var v=G[Symbol.toPrimitive];if(v!==void 0){var C=v.call(G,I||"default");if(typeof C!="object")return C;throw new TypeError("@@toPrimitive must return a primitive value.")}return(I==="string"?String:Number)(G)}class D extends s.PureComponent{constructor(){super(...arguments),M(this,"state",{prevProps:this.props,isChecked:this.props.isChecked}),M(this,"toggle",I=>{let{target:v}=I;const{onChange:C}=this.props,{isChecked:f}=this.state,{isDisabled:y,canToggle:h}=this,b=h?!f:f;return v.blur(),this.setState({isChecked:b}),!y&&C(b)})}static getDerivedStateFromProps(I,v){let{prevProps:C}=v;return q()(C,I)?null:{prevProps:I,isChecked:I.isChecked}}get isDisabled(){const{isDisabled:I,disabled:v}=this.props;return I||v}get readOnly(){return this.props.readOnly}get canToggle(){const{isDisabled:I,readOnly:v}=this;return!I&&!v}render(){const{size:I,readOnly:v,className:C,children:f,isIOS:y,isIOSReverse:h}=this.props,{isChecked:b}=this.state,{isDisabled:S}=this;return(0,J.jsxs)(R,{className:(0,V.Hn)(["Switch",y&&"Switch-IOS",h&&"Switch-IOS-NoTransform",I,b?"is-checked":"isnt-checked",S&&"is-disabled",v&&"readonly",C]),children:[(0,J.jsx)("button",{type:"button",disabled:S,onClick:this.toggle}),f]})}}M(D,"propTypes",{className:u().string,size:u().oneOf(["regular","small"]),isChecked:u().bool,isDisabled:u().bool,disabled:u().bool,readOnly:u().bool,isIOS:u().bool,isIOSReverse:u().bool,onChange:u().func,children:u().any}),M(D,"defaultProps",{className:"",size:"regular",isChecked:!1,isDisabled:!1,disabled:!1,readOnly:!1,isIOS:!1,isIOSReverse:!1,onChange:()=>null});const pe=(0,p.Ay)(D).withConfig({displayName:"styles__StyledSwitch",componentId:"sc-1wltq4q-0"})(["&.Switch-IOS{background:",";transform:matrix(1,0,0,-1,0,0);margin:0;button{border:unset;background:",";}&.regular{width:30px;height:14px;button{height:12px;width:12px;}}&.is-checked{&.small,&.regular{background:",";button{background:",";}}&.regular  button{transform:translate(15px,0);}}}"],G=>G.theme.color_text_disabled01,G=>G.theme.color_text_btn,G=>G.theme.color_proto,G=>G.theme.color_text_btn);class H extends s.PureComponent{render(){return(0,J.jsx)(pe,{...this.props})}}},96411:Se=>{var ce="\\ud800-\\udfff",n="\\u0300-\\u036f",s="\\ufe20-\\ufe2f",p="\\u20d0-\\u20ff",N=n+s+p,u="\\u2700-\\u27bf",B="a-z\\xdf-\\xf6\\xf8-\\xff",q="\\xac\\xb1\\xd7\\xf7",V="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",R="\\u2000-\\u206f",J=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",M="A-Z\\xc0-\\xd6\\xd8-\\xde",F="\\ufe0e\\ufe0f",ie=q+V+R+J,D="['\u2019]",pe="["+ie+"]",H="["+N+"]",G="\\d+",I="["+u+"]",v="["+B+"]",C="[^"+ce+ie+G+u+B+M+"]",f="\\ud83c[\\udffb-\\udfff]",y="(?:"+H+"|"+f+")",h="[^"+ce+"]",b="(?:\\ud83c[\\udde6-\\uddff]){2}",S="[\\ud800-\\udbff][\\udc00-\\udfff]",A="["+M+"]",K="\\u200d",d="(?:"+v+"|"+C+")",j="(?:"+A+"|"+C+")",T="(?:"+D+"(?:d|ll|m|re|s|t|ve))?",X="(?:"+D+"(?:D|LL|M|RE|S|T|VE))?",g=y+"?",L="["+F+"]?",ne="(?:"+K+"(?:"+[h,b,S].join("|")+")"+L+g+")*",ee="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",se="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",te=L+g+ne,fe="(?:"+[I,b,S].join("|")+")"+te,De=RegExp([A+"?"+v+"+"+T+"(?="+[pe,A,"$"].join("|")+")",j+"+"+X+"(?="+[pe,A+d,"$"].join("|")+")",A+"?"+d+"+"+T,A+"+"+X,se,ee,G,fe].join("|"),"g");function Ye(ke){return ke.match(De)||[]}Se.exports=Ye},96582:(Se,ce,n)=>{"use strict";n.d(ce,{Ay:()=>H});var s=n(8509),p=n(18941),N=n(38502),u=n(69623);const B={disabled:!1},q=N.createContext(null);var V=n(99129),R="unmounted",J="exited",M="entering",F="entered",ie="exiting",D=function(G){(0,p.A)(I,G);function I(C,f){var y;y=G.call(this,C,f)||this;var h=f,b=h&&!h.isMounting?C.enter:C.appear,S;return y.appearStatus=null,C.in?b?(S=J,y.appearStatus=M):S=F:C.unmountOnExit||C.mountOnEnter?S=R:S=J,y.state={status:S},y.nextCallback=null,y}I.getDerivedStateFromProps=function(f,y){var h=f.in;return h&&y.status===R?{status:J}:null};var v=I.prototype;return v.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},v.componentDidUpdate=function(f){var y=null;if(f!==this.props){var h=this.state.status;this.props.in?h!==M&&h!==F&&(y=M):(h===M||h===F)&&(y=ie)}this.updateStatus(!1,y)},v.componentWillUnmount=function(){this.cancelNextCallback()},v.getTimeouts=function(){var f=this.props.timeout,y,h,b;return y=h=b=f,f!=null&&typeof f!="number"&&(y=f.exit,h=f.enter,b=f.appear!==void 0?f.appear:h),{exit:y,enter:h,appear:b}},v.updateStatus=function(f,y){if(f===void 0&&(f=!1),y!==null)if(this.cancelNextCallback(),y===M){if(this.props.unmountOnExit||this.props.mountOnEnter){var h=this.props.nodeRef?this.props.nodeRef.current:u.findDOMNode(this);h&&(0,V.F)(h)}this.performEnter(f)}else this.performExit();else this.props.unmountOnExit&&this.state.status===J&&this.setState({status:R})},v.performEnter=function(f){var y=this,h=this.props.enter,b=this.context?this.context.isMounting:f,S=this.props.nodeRef?[b]:[u.findDOMNode(this),b],A=S[0],K=S[1],d=this.getTimeouts(),j=b?d.appear:d.enter;if(!f&&!h||B.disabled){this.safeSetState({status:F},function(){y.props.onEntered(A)});return}this.props.onEnter(A,K),this.safeSetState({status:M},function(){y.props.onEntering(A,K),y.onTransitionEnd(j,function(){y.safeSetState({status:F},function(){y.props.onEntered(A,K)})})})},v.performExit=function(){var f=this,y=this.props.exit,h=this.getTimeouts(),b=this.props.nodeRef?void 0:u.findDOMNode(this);if(!y||B.disabled){this.safeSetState({status:J},function(){f.props.onExited(b)});return}this.props.onExit(b),this.safeSetState({status:ie},function(){f.props.onExiting(b),f.onTransitionEnd(h.exit,function(){f.safeSetState({status:J},function(){f.props.onExited(b)})})})},v.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},v.safeSetState=function(f,y){y=this.setNextCallback(y),this.setState(f,y)},v.setNextCallback=function(f){var y=this,h=!0;return this.nextCallback=function(b){h&&(h=!1,y.nextCallback=null,f(b))},this.nextCallback.cancel=function(){h=!1},this.nextCallback},v.onTransitionEnd=function(f,y){this.setNextCallback(y);var h=this.props.nodeRef?this.props.nodeRef.current:u.findDOMNode(this),b=f==null&&!this.props.addEndListener;if(!h||b){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var S=this.props.nodeRef?[this.nextCallback]:[h,this.nextCallback],A=S[0],K=S[1];this.props.addEndListener(A,K)}f!=null&&setTimeout(this.nextCallback,f)},v.render=function(){var f=this.state.status;if(f===R)return null;var y=this.props,h=y.children,b=y.in,S=y.mountOnEnter,A=y.unmountOnExit,K=y.appear,d=y.enter,j=y.exit,T=y.timeout,X=y.addEndListener,g=y.onEnter,L=y.onEntering,ne=y.onEntered,ee=y.onExit,se=y.onExiting,te=y.onExited,fe=y.nodeRef,De=(0,s.A)(y,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return N.createElement(q.Provider,{value:null},typeof h=="function"?h(f,De):N.cloneElement(N.Children.only(h),De))},I}(N.Component);D.contextType=q,D.propTypes={};function pe(){}D.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:pe,onEntering:pe,onEntered:pe,onExit:pe,onExiting:pe,onExited:pe},D.UNMOUNTED=R,D.EXITED=J,D.ENTERING=M,D.ENTERED=F,D.EXITING=ie;const H=D},97974:(Se,ce,n)=>{"use strict";n.d(ce,{in:()=>pe,Yr:()=>H,hE:()=>G});var s=n(67787),p=n(65609),N=n(95549),u=n(38502),B=n(53732),q=n.n(B),V=n(72214);function R(I){let{className:v,renderIconList:C}=I;return(0,V.jsx)("span",{className:q()("btn-icon",v),children:C.map((f,y)=>(0,V.jsx)("span",{className:"btn-icon-container",children:(0,V.jsx)(V.Fragment,{children:f})},y))})}const J=R;var M=n(22460);class F extends u.PureComponent{constructor(v){super(v),(0,N.A)(this,"getClassName",()=>{const{className:C,type:f,size:y,corner:h,disabled:b,icon:S,children:A}=this.props;return q()(C,"button-root",{["type-"+f]:f,["size-"+y]:y,["corner-"+h]:h,"is-disabled":b,"has-icon":S&&A,"only-icon":S&&A===void 0})}),(0,N.A)(this,"onButtonClick",C=>{if(this.props.disabled){C.preventDefault();return}const{onClick:f}=this.props;f&&f(C)}),(0,N.A)(this,"getIconNode",()=>(0,V.jsx)(J,{renderIconList:this.state.iconList,className:this.props.className})),(0,N.A)(this,"getChildrenNode",()=>(0,V.jsx)("span",{className:"btn-text",children:this.props.children})),this.state={iconList:this.props.icon?[this.props.icon]:[]}}componentDidUpdate(v){let{icon:C}=v;const{icon:f}=this.props;C!==f&&this.setState({iconList:f?[this.props.icon]:[]})}render(){const v=this.getClassName(),C=this.getIconNode(),f=this.getChildrenNode();return(0,V.jsx)(M.O,{className:v,onClick:this.onButtonClick,children:(0,V.jsxs)("div",{className:q()("btn-icon-text-container"),children:[C,f]})})}}(0,N.A)(F,"defaultProps",{type:"linear",disabled:!1,size:"common",corner:"smooth"});var ie=n(33233);const D=(I,v)=>{const C=I.forceTheme;return C?ie.fm[v]["value_"+C]:I.theme[v]},pe=(0,s.AH)(["line-height:18px;&[class*='type-primary']{color:",";background:",";border:1px solid transparent;&:hover:not([class*='is-disabled']){background:",";border:1px solid transparent;}&:active:not([class*='is-disabled']){background:",";border:1px solid transparent;}&[class*='is-disabled']{color:",";background:",";border:1px solid transparent;}}&[class*='type-secondary']{color:",";background:",";border:1px solid ",";&:hover:not([class*='is-disabled']){background:",";border:1px solid ",";}&:active:not([class*='is-disabled']){background:",";border:1px solid ",";}&[class*='is-disabled']{color:",";background:",";border:1px solid ",";}}&[class*='type-danger']{color:",";background:",";border:1px solid transparent;&:hover:not([class*='is-disabled']){background:",";border:1px solid transparent;}&:active:not([class*='is-disabled']){background:",";border:1px solid transparent;}&[class*='is-disabled']{color:",";background:",";border:1px solid transparent;}}&[class*='type-text']{color:",";background:transparent;border:1px solid transparent;&:hover:not([class*='is-disabled']){background:",";border:1px solid transparent;}&:active:not([class*='is-disabled']){background:",";border:1px solid transparent;}&[class*='is-disabled']{color:",";background:transparent;border:1px solid transparent;}}&[class*='size-tiny']{max-height:28px;font-size:12px;line-height:14px;padding:6px 8px;.btn-icon-text-container{min-width:46px;}}&[class*='size-common']{max-height:32px;font-size:13px;padding:6px 10px;.btn-icon-text-container{min-width:58px;}}&[class*='size-medium']{max-height:32px;font-size:13px;padding:6px 12px;.btn-icon-text-container{min-width:70px;}}&[class*='is-disabled']{opacity:1;}"],I=>D(I,"color_text_btn"),I=>D(I,"color_btn_primary_normal"),I=>D(I,"color_btn_primary_hover"),I=>D(I,"color_btn_primary_clicked"),I=>D(I,"color_text_disabled02"),I=>D(I,"color_btn_primary_disabled"),I=>D(I,"color_text_L1"),I=>D(I,"color_bg_white"),I=>D(I,"color_bg_border_02"),I=>D(I,"color_btn_secondary_hover"),I=>D(I,"color_bg_border_02"),I=>D(I,"color_btn_secondary_active"),I=>D(I,"color_bg_border_02"),I=>D(I,"color_text_disabled01"),I=>D(I,"color_bg_white"),I=>D(I,"color_bg_border_02"),I=>D(I,"color_text_btn"),I=>D(I,"color_btn_danger_normal"),I=>D(I,"color_btn_danger_hover"),I=>D(I,"color_btn_danger_clicked"),I=>D(I,"color_text_disabled02"),I=>D(I,"color_btn_danger_disabled"),I=>D(I,"color_text_L1"),I=>D(I,"color_btn_secondary_hover"),I=>D(I,"color_btn_secondary_active"),I=>D(I,"color_text_disabled01")),H=(0,s.Ay)(p.A).withConfig({displayName:"styles__StyledProtoButton",componentId:"sc-1pmgkeq-0"})(["",";"],pe),G=(0,s.Ay)(F).withConfig({displayName:"styles__StyledProtoIconButton",componentId:"sc-1pmgkeq-1"})(["",";"],pe)},99129:(Se,ce,n)=>{"use strict";n.d(ce,{F:()=>s});var s=function(N){return N.scrollTop}}}]);

//# sourceMappingURL=4.n9fxu-vendor-030207b253056b24344b.js.map