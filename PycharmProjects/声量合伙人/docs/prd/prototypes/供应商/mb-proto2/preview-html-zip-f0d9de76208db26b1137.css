 {

  /* @media screen and (-webkit-min-device-pixel-ratio: 2), screen and (min-resolution: 2dppx) {
    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  } */
}
  * {
    margin: 0;
    padding: 0;
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none;
  }
  *, *::before, *::after {
    box-sizing: border-box;
  }
  html {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    --color-prettylights-syntax-comment: #6e7781;
    --color-prettylights-syntax-constant: #0550ae;
    --color-prettylights-syntax-entity: #8250df;
    --color-prettylights-syntax-storage-modifier-import: #24292f;
    --color-prettylights-syntax-entity-tag: #116329;
    --color-prettylights-syntax-keyword: #cf222e;
    --color-prettylights-syntax-string: #0a3069;
    --color-prettylights-syntax-variable: #953800;
    --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
    --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
    --color-prettylights-syntax-invalid-illegal-bg: #82071e;
    --color-prettylights-syntax-carriage-return-text: #f6f8fa;
    --color-prettylights-syntax-carriage-return-bg: #cf222e;
    --color-prettylights-syntax-string-regexp: #116329;
    --color-prettylights-syntax-markup-list: #3b2300;
    --color-prettylights-syntax-markup-heading: #0550ae;
    --color-prettylights-syntax-markup-italic: #24292f;
    --color-prettylights-syntax-markup-bold: #24292f;
    --color-prettylights-syntax-markup-deleted-text: #82071e;
    --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
    --color-prettylights-syntax-markup-inserted-text: #116329;
    --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
    --color-prettylights-syntax-markup-changed-text: #953800;
    --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
    --color-prettylights-syntax-markup-ignored-text: #eaeef2;
    --color-prettylights-syntax-markup-ignored-bg: #0550ae;
    --color-prettylights-syntax-meta-diff-range: #8250df;
    --color-prettylights-syntax-brackethighlighter-angle: #57606a;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
    --color-prettylights-syntax-constant-other-reference-link: #0a3069;
    --color-fg-default: #333333;
    --color-fg-muted: #57606a;
    --color-fg-subtle: #6e7781;
    --color-canvas-default: transparent;
    --color-canvas-subtle: #f6f8fa;
    --color-border-default: #d0d7de;
    --color-border-muted: hsla(210, 18%, 87%, 1);
    --color-neutral-muted: rgba(175, 184, 193, 0.2);
    --color-accent-fg: #0969da;
    --color-accent-emphasis: #0969da;
    --color-attention-subtle: #fff8c5;
    --color-danger-fg: #cf222e;
    --color_bg_border_01: rgba(18, 17, 42, 0.07);
  }
  input, button, select, textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }
  html, body {
    font-family: -apple-system, "SF UI Text", "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Zen Hei", sans-serif;
    font-weight: normal;
    line-height: 18px;
    font-size: 16px;
    height: 100%;
    overflow: hidden;
  }
  body {
    font-size: 12px;
  }

body {
    color: #525e71;
  }

    body.new-embedded #IBOT_MODAL_ROOT .ModalPortal {
        min-width: 0;
      }

    @media only screen and (max-height: 511px) {
          body.new-embedded #workspace>div>.logo {
            margin: 0px auto 20px;
            height: 150px;
          }

            body.new-embedded #workspace>div>.logo .svg-icon {
              width: 300px;
            }

          body.new-embedded #workspace>div>.content {
              margin: 20px auto;
          }

            body.new-embedded #workspace>div>.content .proposal {
              margin: 16px auto 35px;
            }

          body.new-embedded #workspace>div>.btn-list .btn {
              width: 115px;
              height: 40px;
              font-size: 14px;
              line-height: 40px;
          }
        }
  @font-face {
    font-family: Inter;
    src: url('/mb-workspace/fonts/Inter-Regular.ttf'), url('/mb-workspace/fonts/Inter-SemiBold.ttf');
  }
  html.wonder-share body {
    font-family: Inter, -apple-system, "SF UI Text", "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Zen Hei", sans-serif;
  }
  .spinner {
    width: 30px;
    height: 30px;
    border-width: 3px;
    border-style: solid;
    border-color: rgba(51, 54, 58, 0.6) transparent;
    border-radius: 100%;
    transform-origin: 50% 50% 0;
    animation: mb-spin .8s infinite linear;
    display: inline-block;
  }
  @keyframes mb-spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(359deg);
    }
  }
  kbd {
    font-family: Inter;
  }
  #loading {
    width: 100%;
    height: 100%;
    z-index: 2;
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  #loading .logo {
      width: 100px;
      height: 100px;
    }
  #loading span {
      margin-top: 15px;
      font-size: 14px;
      line-height: 28px;
      color: #F77D77;
    }
  html.wonder-share body #loading .logo {
        width: 86px;
        height: 86px;
        background: url('/mb-workspace/images/MockittLoading.gif');
        background-repeat: no-repeat;
      }
  html.wonder-share body #loading span {
        color: #4257ff;
      }

/* reset */
  button {
    background: none;
    border: 0;
    outline: 0;
    border-radius: 0;
    color: inherit;
    cursor: pointer;
  }
  input {
    background: none;
    border: 0;
    outline: 0;
  }
  /* ellipsis */
  .quote {
    display: inline-block;
  }
  .user-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 10em;
  }
  .project-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 30em;
  }
  .widget-name {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 12em;
  }
  /* form */
  input.regular:not([type="radio"]):not([type="checkbox"]):not([readonly]) {
      padding: .25em .5em;
      background: #fff;
      border: 0;
      border-bottom: 1px solid #d7d7d7;
      border-radius: 0;
      color: #27364e;
      transition: all .2s ease-out;
    }
  input.regular:not([type="radio"]):not([type="checkbox"]):not([readonly])::-moz-placeholder {
        color: #a9afb8;
      }
  input.regular:not([type="radio"]):not([type="checkbox"]):not([readonly])::placeholder {
        color: #a9afb8;
      }
  input.regular:not([type="radio"]):not([type="checkbox"]):not([readonly]):hover, input.regular:not([type="radio"]):not([type="checkbox"]):not([readonly]):focus {
        border-color: #eb5648;
      }
  input.regular[readonly] {
      padding: .25em .5em;
      background-color: #f6f6f6;
      border: 0;
      color: rgba(#27364e, .4);
    }
  textarea.regular:focus {
      outline: 0;
    }
  textarea.regular[readonly] {
      padding: .25em .5em;
      background-color: #f6f6f6;
      border: 0;
      color: rgba(#27364e, .4);
    }
  button.regular, button.primary {
    padding: .25em;
    border: 1px solid #eb5648;
    border-radius: 1px;
    color: #eb5648;
    cursor: pointer;
    transition: all .2s ease-out;
  }
  button.regular:not(button), button.primary:not(button) {
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }
  button.regular:hover:not([disabled]), button.primary:hover:not([disabled]) {
      background-color: #eb5648;
      border: 1px solid #eb5648;
      color: #fff;
    }
  button.regular[disabled], button.primary[disabled] {
      opacity: .6;
    }
  button.primary {
    background-color: #eb5648;
    color: #fff;
  }
  button.primary:hover:not([disabled]) {
      background-color: #ff7c75;
      border-color: #ff7c75;
      color: #fff;
    }

.has-watermark::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 2147483646;
    pointer-events: none;
  }
  .has-watermark.wm_s::before {
    background: url('./images/mktWM/watermark_s.svg') center center / 80px 80px repeat;
  }
  .has-watermark.wm_m::before {
    background: url('./images/mktWM/watermark_m.svg') center center / 180px 152px repeat;
  }
  .has-watermark .has-watermark::before {
    display: none;
  }
  .has-watermark>.wrap-watermark::before {
    display: block;
  }
  .has-watermark .has-watermark>.has-watermark::before {
    display: none;
  }

/* NOTE: this file is script generated, change should be made at 'script/generate/HostedFont.js' */
@font-face {
  font-style: normal;
  font-family: 'PingFangSC';
  font-weight: 100;
  src: url('/mb-sigma/fonts/zh-CN/PingFangSC/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'PingFangSC';
  font-weight: 200;
  src: url('/mb-sigma/fonts/zh-CN/PingFangSC/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'PingFangSC';
  font-weight: 300;
  src: url('/mb-sigma/fonts/zh-CN/PingFangSC/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'PingFangSC';
  font-weight: 400;
  src: local('PingFangSC-Regular'), local('PingFang SC Regular'), local('.PingFangSC-Regular'), local('.PingFang SC Regular'), local('PingFangSC'), local('PingFang SC'), url('/mb-sigma/fonts/zh-CN/PingFangSC/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'PingFangSC';
  font-weight: 500;
  src: url('/mb-sigma/fonts/zh-CN/PingFangSC/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'PingFangSC';
  font-weight: 600;
  src: local('PingFangSC-Semibold'), local('PingFang SC Semibold'), local('.PingFangSC-Semibold'), local('.PingFang SC Semibold'), url('/mb-sigma/fonts/zh-CN/PingFangSC/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSansSC';
  font-weight: 200;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSansSC/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSansSC';
  font-weight: 300;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSansSC/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSansSC';
  font-weight: 400;
  src: local('SourceHanSansSC-Regular'), local('Source Han Sans SC Regular'), local('Source Han Sans SC'), local('Noto Sans CJK SC Regular'), local('Noto Sans CJK SC'), url('/mb-sigma/fonts/zh-CN/SourceHanSansSC/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSansSC';
  font-weight: 500;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSansSC/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSansSC';
  font-weight: 700;
  src: local('SourceHanSansSC-Bold'), local('Source Han Sans SC Bold'), local('Noto Sans CJK SC Bold'), url('/mb-sigma/fonts/zh-CN/SourceHanSansSC/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSansSC';
  font-weight: 900;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSansSC/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SFUIText';
  font-weight: 400;
  src: local('SFUIText-Regular'), local('.SFUIText-Regular'), local('SFProText-Regular'), local('SFProText'), url('/fonts/sf-ui/Text-Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SFUIText';
  font-weight: 700;
  src: local('SFUIText-Bold'), local('.SFUIText-Bold'), local('SFProText-Bold'), url('/fonts/sf-ui/Text-Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Roboto';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/Roboto/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Roboto';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Roboto/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Roboto';
  font-weight: 400;
  src: local('Roboto'), url('/mb-sigma/fonts/en/Roboto/Regular.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Roboto';
  font-weight: 400;
  src: local('Roboto-Italic'), local('Roboto Italic'), url('/mb-sigma/fonts/en/Roboto/Italic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Roboto';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Roboto/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Roboto';
  font-weight: 700;
  src: local('Roboto-Bold'), local('Roboto Bold'), url('/mb-sigma/fonts/en/Roboto/Bold.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Roboto';
  font-weight: 700;
  src: local('Roboto-BoldItalic'), local('Roboto Bold Italic'), url('/mb-sigma/fonts/en/Roboto/BoldItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Roboto';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/Roboto/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'AlibabaPuHui';
  font-weight: 300;
  src: url('/mb-sigma/fonts/zh-CN/AlibabaPuHui/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'AlibabaPuHui';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/AlibabaPuHui/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'AlibabaPuHui';
  font-weight: 500;
  src: url('/mb-sigma/fonts/zh-CN/AlibabaPuHui/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'AlibabaPuHui';
  font-weight: 700;
  src: url('/mb-sigma/fonts/zh-CN/AlibabaPuHui/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'AlibabaPuHui';
  font-weight: 900;
  src: url('/mb-sigma/fonts/zh-CN/AlibabaPuHui/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'OPPOSans';
  font-weight: 300;
  src: url('/mb-sigma/fonts/zh-CN/OPPOSans/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'OPPOSans';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/OPPOSans/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'OPPOSans';
  font-weight: 500;
  src: url('/mb-sigma/fonts/zh-CN/OPPOSans/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'OPPOSans';
  font-weight: 700;
  src: url('/mb-sigma/fonts/zh-CN/OPPOSans/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'OPPOSans';
  font-weight: 900;
  src: url('/mb-sigma/fonts/zh-CN/OPPOSans/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 100;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/Thin.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 100;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/ThinItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 300;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/Light.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 300;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/LightItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/Regular.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/RegularItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 500;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/Medium.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 500;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/MediumItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 700;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/Bold.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 700;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/BoldItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 900;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/Black.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'HarmonyOS_Sans_Condensed';
  font-weight: 900;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_Condensed/BlackItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_SC';
  font-weight: 100;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_SC/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_SC';
  font-weight: 300;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_SC/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_SC';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_SC/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_SC';
  font-weight: 500;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_SC/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_SC';
  font-weight: 700;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_SC/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_SC';
  font-weight: 900;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_SC/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_TC';
  font-weight: 100;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_TC/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_TC';
  font-weight: 300;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_TC/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_TC';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_TC/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_TC';
  font-weight: 500;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_TC/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_TC';
  font-weight: 700;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_TC/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'HarmonyOS_Sans_TC';
  font-weight: 900;
  src: url('/mb-sigma/fonts/zh-CN/HarmonyOS_Sans_TC/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'PMZDBiaoTi';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/PMZDBiaoTi/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'PMZDCuShuSong';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/PMZDCuShuSong/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'PMZDQingSong';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/PMZDQingSong/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSerif';
  font-weight: 200;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSerif/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSerif';
  font-weight: 300;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSerif/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSerif';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSerif/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSerif';
  font-weight: 500;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSerif/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSerif';
  font-weight: 600;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSerif/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSerif';
  font-weight: 700;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSerif/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceHanSerif';
  font-weight: 900;
  src: url('/mb-sigma/fonts/zh-CN/SourceHanSerif/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'TaipeiHei';
  font-weight: 300;
  src: url('/mb-sigma/fonts/zh-CN/TaipeiHei/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'TaipeiHei';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/TaipeiHei/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'TaipeiHei';
  font-weight: 700;
  src: url('/mb-sigma/fonts/zh-CN/TaipeiHei/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'YSBiaoTiHei';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/YSBiaoTiHei/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'YSHaoShenTi';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/YSHaoShenTi/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'ZKGaoDuanHei';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/ZKGaoDuanHei/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'ZKKuHei';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/ZKKuHei/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'ZKKuaiLeTi';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/ZKKuaiLeTi/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'ZKQingKeHuangYou';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/ZKQingKeHuangYou/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'ZKWenYi';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/ZKWenYi/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'ZKXiaoWeiLogo';
  font-weight: 400;
  src: url('/mb-sigma/fonts/zh-CN/ZKXiaoWeiLogo/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'AlibabaSans';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/AlibabaSans/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'AlibabaSans';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/AlibabaSans/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'AlibabaSans';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/AlibabaSans/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'AlibabaSans';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/AlibabaSans/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'AlibabaSans';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/AlibabaSans/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Inter';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/Inter/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Inter';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/Inter/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Inter';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Inter/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Inter';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Inter/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Inter';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Inter/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Inter';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/Inter/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Inter';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Inter/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Inter';
  font-weight: 800;
  src: url('/mb-sigma/fonts/en/Inter/ExtraBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Inter';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/Inter/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Lato';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/Lato/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Lato';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Lato/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Lato';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Lato/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Lato';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Lato/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Lato';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/Lato/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Lobster';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Lobster/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Lora';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Lora/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Lora';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Lora/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Lora';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/Lora/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Lora';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Lora/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Montserrat';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/Montserrat/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Montserrat';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/Montserrat/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Montserrat';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Montserrat/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Montserrat';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Montserrat/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Montserrat';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Montserrat/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Montserrat';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/Montserrat/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Montserrat';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Montserrat/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Montserrat';
  font-weight: 800;
  src: url('/mb-sigma/fonts/en/Montserrat/ExtraBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Montserrat';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/Montserrat/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'MontserratAlternates';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/MontserratAlternates/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'MontserratAlternates';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/MontserratAlternates/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'MontserratAlternates';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/MontserratAlternates/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'MontserratAlternates';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/MontserratAlternates/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'MontserratAlternates';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/MontserratAlternates/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'MontserratAlternates';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/MontserratAlternates/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'MontserratAlternates';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/MontserratAlternates/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'MontserratAlternates';
  font-weight: 800;
  src: url('/mb-sigma/fonts/en/MontserratAlternates/ExtraBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'MontserratAlternates';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/MontserratAlternates/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'NotoSans';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/NotoSans/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'NotoSans';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/NotoSans/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'OpenSans';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/OpenSans/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'OpenSans';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/OpenSans/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'OpenSans';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/OpenSans/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'OpenSans';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/OpenSans/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'OpenSans';
  font-weight: 800;
  src: url('/mb-sigma/fonts/en/OpenSans/ExtraBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Oswald';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/Oswald/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Oswald';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Oswald/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Oswald';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Oswald/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Oswald';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Oswald/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Oswald';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/Oswald/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Oswald';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Oswald/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Playfair Display';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Playfair Display/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Playfair Display';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Playfair Display/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Playfair Display';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/Playfair Display/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Playfair Display';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Playfair Display/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Playfair Display';
  font-weight: 800;
  src: url('/mb-sigma/fonts/en/Playfair Display/ExtraBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Playfair Display';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/Playfair Display/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Poppins';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/Poppins/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Poppins';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/Poppins/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Poppins';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Poppins/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Poppins';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Poppins/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Poppins';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Poppins/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Poppins';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/Poppins/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Poppins';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Poppins/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Poppins';
  font-weight: 800;
  src: url('/mb-sigma/fonts/en/Poppins/ExtraBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Poppins';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/Poppins/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Raleway';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/Raleway/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Raleway';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/Raleway/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Raleway';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Raleway/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Raleway';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Raleway/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Raleway';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Raleway/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Raleway';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/Raleway/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Raleway';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Raleway/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Raleway';
  font-weight: 800;
  src: url('/mb-sigma/fonts/en/Raleway/ExtraBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Raleway';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/Raleway/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Display';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/SF Pro Display/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Display';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/SF Pro Display/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Display';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/SF Pro Display/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Display';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/SF Pro Display/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Display';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/SF Pro Display/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Display';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/SF Pro Display/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Display';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/SF Pro Display/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Display';
  font-weight: 800;
  src: url('/mb-sigma/fonts/en/SF Pro Display/ExtraBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Display';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/SF Pro Display/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Text';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/SF Pro Text/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Text';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/SF Pro Text/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Text';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/SF Pro Text/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Text';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/SF Pro Text/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Text';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/SF Pro Text/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Text';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/SF Pro Text/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Text';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/SF Pro Text/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Text';
  font-weight: 800;
  src: url('/mb-sigma/fonts/en/SF Pro Text/ExtraBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SF Pro Text';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/SF Pro Text/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceSans Pro';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/SourceSans Pro/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceSans Pro';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/SourceSans Pro/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceSans Pro';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/SourceSans Pro/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceSans Pro';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/SourceSans Pro/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceSans Pro';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/SourceSans Pro/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'SourceSans Pro';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/SourceSans Pro/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Spartan';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/Spartan/Thin.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Spartan';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/Spartan/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Spartan';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Spartan/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Spartan';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Spartan/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Spartan';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Spartan/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Spartan';
  font-weight: 600;
  src: url('/mb-sigma/fonts/en/Spartan/SemiBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Spartan';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Spartan/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Spartan';
  font-weight: 800;
  src: url('/mb-sigma/fonts/en/Spartan/ExtraBold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Spartan';
  font-weight: 900;
  src: url('/mb-sigma/fonts/en/Spartan/Black.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Ubuntu';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Ubuntu/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Ubuntu';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Ubuntu/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Ubuntu';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Ubuntu/Medium.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Ubuntu';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Ubuntu/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'FZLanTingHei';
  font-weight: 300;
  src: local('FZLanTingHeiS-L-GB'), url('/mb-sigma/fonts/zh-CN/FZLanTingHei/Light.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'FZLanTingHei';
  font-weight: 400;
  src: local('FZLanTingHeiS-R-GB'), url('/mb-sigma/fonts/zh-CN/FZLanTingHei/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'FZYuan';
  font-weight: 400;
  src: local('FZZhunYuan-M02S'), url('/mb-sigma/fonts/zh-CN/FZYuan/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'FZYuan';
  font-weight: 700;
  src: url('/mb-sigma/fonts/zh-CN/FZYuan/Bold.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'FZXinSong';
  font-weight: 400;
  src: local('FZNewShuSong-Z10S'), url('/mb-sigma/fonts/zh-CN/FZXinSong/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'FZKai';
  font-weight: 400;
  src: local('FZKai-Z03S'), url('/mb-sigma/fonts/zh-CN/FZKai/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'FZGongYeHei';
  font-weight: 400;
  src: local('FZGongYHS-R-GB'), url('/mb-sigma/fonts/zh-CN/FZGongYeHei/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'FZHanZhenGuangBiao';
  font-weight: 400;
  src: local('FZHanZhenGuangBiaoS-GB'), url('/mb-sigma/fonts/zh-CN/FZHanZhenGuangBiao/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'FZZongYi';
  font-weight: 400;
  src: local('FZZongYi-M05S'), url('/mb-sigma/fonts/zh-CN/FZZongYi/Regular.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Helvetica';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Helvetica/Light.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Helvetica';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Helvetica/LightItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Helvetica';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Helvetica/Regular.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Helvetica';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Helvetica/Italic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Helvetica';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Helvetica/Bold.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Helvetica';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Helvetica/BoldItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Helvetica Neue';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/Thin.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Helvetica Neue';
  font-weight: 100;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/ThinItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Helvetica Neue';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/ExtraLight.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Helvetica Neue';
  font-weight: 200;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/ExtraLightItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Helvetica Neue';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/Light.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Helvetica Neue';
  font-weight: 300;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/LightItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Helvetica Neue';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/Regular.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Helvetica Neue';
  font-weight: 400;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/Italic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Helvetica Neue';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/Medium.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Helvetica Neue';
  font-weight: 500;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/MediumItalic.woff2') format('woff2');
}
@font-face {
  font-style: normal;
  font-family: 'Helvetica Neue';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/Bold.woff2') format('woff2');
}
@font-face {
  font-style: italic;
  font-family: 'Helvetica Neue';
  font-weight: 700;
  src: url('/mb-sigma/fonts/en/Helvetica Neue/BoldItalic.woff2') format('woff2');
}
/*@import 'rails-lib/stylesheet/widget/widget.css';*/
/*@import 'rails-lib/stylesheet/widget/panel.css';*/
/*@import 'rails-lib/stylesheet/widget/android.css';*/
.rdw-option-wrapper{border:1px solid #f1f1f1;padding:5px;min-width:25px;height:20px;border-radius:2px;margin:0 4px;display:flex;justify-content:center;align-items:center;cursor:pointer;background:white;text-transform:capitalize}
.rdw-option-wrapper:hover{box-shadow:1px 1px 0 #bfbdbd}
.rdw-option-wrapper:active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-option-active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-option-disabled{opacity:.3;cursor:default}
.rdw-dropdown-wrapper{height:30px;background:white;cursor:pointer;border:1px solid #f1f1f1;border-radius:2px;margin:0 3px;text-transform:capitalize;background:white}
.rdw-dropdown-wrapper:focus{outline:0}
.rdw-dropdown-wrapper:hover{box-shadow:1px 1px 0 #bfbdbd;background-color:#fff}
.rdw-dropdown-wrapper:active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-dropdown-carettoopen{height:0;width:0;position:absolute;top:35%;right:10%;border-top:6px solid black;border-left:5px solid transparent;border-right:5px solid transparent}
.rdw-dropdown-carettoclose{height:0;width:0;position:absolute;top:35%;right:10%;border-bottom:6px solid black;border-left:5px solid transparent;border-right:5px solid transparent}
.rdw-dropdown-selectedtext{display:flex;position:relative;height:100%;align-items:center;padding:0 5px}
.rdw-dropdown-optionwrapper{z-index:100;position:relative;border:1px solid #f1f1f1;width:98%;background:white;border-radius:2px;margin:0;padding:0;max-height:250px;overflow-y:scroll}
.rdw-dropdown-optionwrapper:hover{box-shadow:1px 1px 0 #bfbdbd;background-color:#fff}
.rdw-dropdownoption-default{min-height:25px;display:flex;align-items:center;padding:0 5px}
.rdw-dropdownoption-highlighted{background:#f1f1f1}
.rdw-dropdownoption-active{background:#f5f5f5}
.rdw-dropdownoption-disabled{opacity:.3;cursor:default}
.rdw-inline-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-inline-dropdown{width:50px}
.rdw-inline-dropdownoption{height:40px;display:flex;justify-content:center}
.rdw-block-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-block-dropdown{width:110px}
.rdw-fontsize-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-fontsize-dropdown{min-width:40px}
.rdw-fontsize-option{display:flex;justify-content:center}
.rdw-fontfamily-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-fontfamily-dropdown{width:115px}
.rdw-fontfamily-placeholder{white-space:nowrap;max-width:90px;overflow:hidden;text-overflow:ellipsis}
.rdw-fontfamily-optionwrapper{width:140px}
.rdw-list-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-list-dropdown{width:50px;z-index:90}
.rdw-list-dropdownOption{height:40px;display:flex;justify-content:center}
.rdw-text-align-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-text-align-dropdown{width:50px;z-index:90}
.rdw-text-align-dropdownOption{height:40px;display:flex;justify-content:center}
.rdw-right-aligned-block{text-align:right}
.rdw-left-aligned-block{text-align:left !important}
.rdw-center-aligned-block{text-align:center !important}
.rdw-justify-aligned-block{text-align:justify !important}
.rdw-right-aligned-block>div{display:inline-block;min-width:1px}
.rdw-left-aligned-block>div{display:inline-block;min-width:1px}
.rdw-center-aligned-block>div{display:inline-block;min-width:1px}
.rdw-justify-aligned-block>div{display:inline-block;min-width:1px}
.rdw-colorpicker-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-colorpicker-modal{position:absolute;top:35px;left:5px;display:flex;flex-direction:column;width:175px;height:175px;border:1px solid #f1f1f1;padding:15px;border-radius:2px;z-index:100;background:white;box-shadow:3px 3px 5px #bfbdbd}
.rdw-colorpicker-modal-header{display:flex;padding-bottom:5px}
.rdw-colorpicker-modal-style-label{font-size:15px;width:50%;text-align:center;cursor:pointer;padding:0 10px 5px}
.rdw-colorpicker-modal-style-label-active{border-bottom:2px solid #0a66b7}
.rdw-colorpicker-modal-options{margin:5px auto;display:flex;width:100%;height:100%;flex-wrap:wrap;overflow:scroll}
.rdw-colorpicker-cube{width:22px;height:22px;border:1px solid #f1f1f1}
.rdw-colorpicker-option{margin:3px;padding:0;min-height:20px;border:0;width:22px;height:22px;min-width:22px;box-shadow:1px 2px 1px #bfbdbd inset}
.rdw-colorpicker-option:hover{box-shadow:1px 2px 1px #bfbdbd}
.rdw-colorpicker-option:active{box-shadow:-1px -2px 1px #bfbdbd}
.rdw-colorpicker-option-active{box-shadow:0 0 2px 2px #bfbdbd}
.rdw-link-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-link-dropdown{width:50px}
.rdw-link-dropdownOption{height:40px;display:flex;justify-content:center}
.rdw-link-dropdownPlaceholder{margin-left:8px}
.rdw-link-modal{position:absolute;top:35px;left:5px;display:flex;flex-direction:column;width:235px;height:205px;border:1px solid #f1f1f1;padding:15px;border-radius:2px;z-index:100;background:white;box-shadow:3px 3px 5px #bfbdbd}
.rdw-link-modal-label{font-size:15px}
.rdw-link-modal-input{margin-top:5px;border-radius:2px;border:1px solid #f1f1f1;height:25px;margin-bottom:15px;padding:0 5px}
.rdw-link-modal-input:focus{outline:0}
.rdw-link-modal-buttonsection{margin:0 auto}
.rdw-link-modal-target-option{margin-bottom:20px}
.rdw-link-modal-target-option>span{margin-left:5px}
.rdw-link-modal-btn{margin-left:10px;width:75px;height:30px;border:1px solid #f1f1f1;border-radius:2px;cursor:pointer;background:white;text-transform:capitalize}
.rdw-link-modal-btn:hover{box-shadow:1px 1px 0 #bfbdbd}
.rdw-link-modal-btn:active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-link-modal-btn:focus{outline:none !important}
.rdw-link-modal-btn:disabled{background:#ece9e9}
.rdw-link-dropdownoption{height:40px;display:flex;justify-content:center}
.rdw-history-dropdown{width:50px}
.rdw-embedded-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-embedded-modal{position:absolute;top:35px;left:5px;display:flex;flex-direction:column;width:235px;height:180px;border:1px solid #f1f1f1;padding:15px;border-radius:2px;z-index:100;background:white;justify-content:space-between;box-shadow:3px 3px 5px #bfbdbd}
.rdw-embedded-modal-header{font-size:15px;display:flex}
.rdw-embedded-modal-header-option{width:50%;cursor:pointer;display:flex;justify-content:center;align-items:center;flex-direction:column}
.rdw-embedded-modal-header-label{width:95px;border:1px solid #f1f1f1;margin-top:5px;background:#6eb8d4;border-bottom:2px solid #0a66b7}
.rdw-embedded-modal-link-section{display:flex;flex-direction:column}
.rdw-embedded-modal-link-input{width:88%;height:35px;margin:10px 0;border:1px solid #f1f1f1;border-radius:2px;font-size:15px;padding:0 5px}
.rdw-embedded-modal-link-input-wrapper{display:flex;align-items:center}
.rdw-embedded-modal-link-input:focus{outline:0}
.rdw-embedded-modal-btn-section{display:flex;justify-content:center}
.rdw-embedded-modal-btn{margin:0 3px;width:75px;height:30px;border:1px solid #f1f1f1;border-radius:2px;cursor:pointer;background:white;text-transform:capitalize}
.rdw-embedded-modal-btn:hover{box-shadow:1px 1px 0 #bfbdbd}
.rdw-embedded-modal-btn:active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-embedded-modal-btn:focus{outline:none !important}
.rdw-embedded-modal-btn:disabled{background:#ece9e9}
.rdw-embedded-modal-size{align-items:center;display:flex;margin:8px 0;justify-content:space-between}
.rdw-embedded-modal-size-input{width:80%;height:20px;border:1px solid #f1f1f1;border-radius:2px;font-size:12px}
.rdw-embedded-modal-size-input:focus{outline:0}
.rdw-emoji-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-emoji-modal{overflow:auto;position:absolute;top:35px;left:5px;display:flex;flex-wrap:wrap;width:235px;height:180px;border:1px solid #f1f1f1;padding:15px;border-radius:2px;z-index:100;background:white;box-shadow:3px 3px 5px #bfbdbd}
.rdw-emoji-icon{margin:2.5px;height:24px;width:24px;cursor:pointer;font-size:22px;display:flex;justify-content:center;align-items:center}
.rdw-spinner{display:flex;align-items:center;justify-content:center;height:100%;width:100%}
.rdw-spinner>div{width:12px;height:12px;background-color:#333;border-radius:100%;display:inline-block;animation:sk-bouncedelay 1.4s infinite ease-in-out both}
.rdw-spinner .rdw-bounce1{animation-delay:-0.32s}
.rdw-spinner .rdw-bounce2{animation-delay:-0.16s}
@keyframes ZdD58bXGNcNoQjiPeXLu{0%,    80%,    100%{transform:scale(0)}40%{transform:scale(1.0)}}
.rdw-image-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-image-modal{position:absolute;top:35px;left:5px;display:flex;flex-direction:column;width:235px;border:1px solid #f1f1f1;padding:15px;border-radius:2px;z-index:100;background:white;box-shadow:3px 3px 5px #bfbdbd}
.rdw-image-modal-header{font-size:15px;margin:10px 0;display:flex}
.rdw-image-modal-header-option{width:50%;cursor:pointer;display:flex;justify-content:center;align-items:center;flex-direction:column}
.rdw-image-modal-header-label{width:80px;background:#f1f1f1;border:1px solid #f1f1f1;margin-top:5px}
.rdw-image-modal-header-label-highlighted{background:#6eb8d4;border-bottom:2px solid #0a66b7}
.rdw-image-modal-upload-option{width:100%;color:gray;cursor:pointer;display:flex;border:0;font-size:15px;align-items:center;justify-content:center;background-color:#f1f1f1;outline:2px dashed gray;outline-offset:-10px;margin:10px 0;padding:9px 0}
.rdw-image-modal-upload-option-highlighted{outline:2px dashed #0a66b7}
.rdw-image-modal-upload-option-label{cursor:pointer;height:100%;width:100%;display:flex;justify-content:center;align-items:center;padding:15px}
.rdw-image-modal-upload-option-label span{padding:0 20px}
.rdw-image-modal-upload-option-image-preview{max-width:100%;max-height:200px}
.rdw-image-modal-upload-option-input{width:.1px;height:.1px;opacity:0;overflow:hidden;position:absolute;z-index:-1}
.rdw-image-modal-url-section{display:flex;align-items:center}
.rdw-image-modal-url-input{width:90%;height:35px;margin:15px 0 12px;border:1px solid #f1f1f1;border-radius:2px;font-size:15px;padding:0 5px}
.rdw-image-modal-btn-section{margin:10px auto 0}
.rdw-image-modal-url-input:focus{outline:0}
.rdw-image-modal-btn{margin:0 5px;width:75px;height:30px;border:1px solid #f1f1f1;border-radius:2px;cursor:pointer;background:white;text-transform:capitalize}
.rdw-image-modal-btn:hover{box-shadow:1px 1px 0 #bfbdbd}
.rdw-image-modal-btn:active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-image-modal-btn:focus{outline:none !important}
.rdw-image-modal-btn:disabled{background:#ece9e9}
.rdw-image-modal-spinner{position:absolute;top:-3px;left:0;width:100%;height:100%;opacity:.5}
.rdw-image-modal-alt-input{width:70%;height:20px;border:1px solid #f1f1f1;border-radius:2px;font-size:12px;margin-left:5px}
.rdw-image-modal-alt-input:focus{outline:0}
.rdw-image-modal-alt-lbl{font-size:12px}
.rdw-image-modal-size{align-items:center;display:flex;margin:8px 0;justify-content:space-between}
.rdw-image-modal-size-input{width:40%;height:20px;border:1px solid #f1f1f1;border-radius:2px;font-size:12px}
.rdw-image-modal-size-input:focus{outline:0}
.rdw-image-mandatory-sign{color:red;margin-left:3px;margin-right:3px}
.rdw-remove-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-history-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-history-dropdownoption{height:40px;display:flex;justify-content:center}
.rdw-history-dropdown{width:50px}
.rdw-link-decorator-wrapper{position:relative}
.rdw-link-decorator-icon{position:absolute;left:40%;top:0;cursor:pointer;background-color:white}
.rdw-mention-link{text-decoration:none;color:#1236ff;background-color:#f0fbff;padding:1px 2px;border-radius:2px}
.rdw-suggestion-wrapper{position:relative}
.rdw-suggestion-dropdown{position:absolute;display:flex;flex-direction:column;border:1px solid #f1f1f1;min-width:100px;max-height:150px;overflow:auto;background:white;z-index:100}
.rdw-suggestion-option{padding:7px 5px;border-bottom:1px solid #f1f1f1}
.rdw-suggestion-option-active{background-color:#f1f1f1}
.rdw-hashtag-link{text-decoration:none;color:#1236ff;background-color:#f0fbff;padding:1px 2px;border-radius:2px}
.rdw-image-alignment-options-popup{position:absolute;background:white;display:flex;padding:5px 2px;border-radius:2px;border:1px solid #f1f1f1;width:105px;cursor:pointer;z-index:100}
.rdw-alignment-option-left{justify-content:flex-start}
.rdw-image-alignment-option{height:15px;width:15px;min-width:15px}
.rdw-image-alignment{position:relative}
.rdw-image-imagewrapper{position:relative}
.rdw-image-center{display:flex;justify-content:center}
.rdw-image-left{display:flex}
.rdw-image-right{display:flex;justify-content:flex-end}
.rdw-image-alignment-options-popup-right{right:0}
.rdw-editor-main{height:100%;box-sizing:border-box}
.rdw-editor-toolbar{padding:6px 5px 0;border-radius:2px;border:1px solid #f1f1f1;display:flex;justify-content:flex-start;background:white;flex-wrap:wrap;font-size:15px;margin-bottom:5px;-webkit-user-select:none;-moz-user-select:none;user-select:none}
.public-DraftStyleDefault-block{margin:1em 0}
.rdw-editor-wrapper:focus{outline:0}
.rdw-editor-wrapper{box-sizing:content-box}
.rdw-editor-main blockquote{border-left:5px solid #f1f1f1;padding-left:5px}
.rdw-editor-main pre{background:#f1f1f1;border-radius:3px;padding:1px 10px}
.DraftEditor-editorContainer, .DraftEditor-root, .public-DraftEditor-content{height:inherit;text-align:initial}
.public-DraftEditor-content[contenteditable=true]{-webkit-user-modify:read-write-plaintext-only}
.DraftEditor-root{position:relative}
.DraftEditor-editorContainer{background-color:rgba(255, 255, 255, 0);border-left:.1px solid transparent;position:relative;z-index:1}
.public-DraftEditor-block{position:relative}
.DraftEditor-alignLeft .public-DraftStyleDefault-block{text-align:left}
.DraftEditor-alignLeft .public-DraftEditorPlaceholder-root{left:0;text-align:left}
.DraftEditor-alignCenter .public-DraftStyleDefault-block{text-align:center}
.DraftEditor-alignCenter .public-DraftEditorPlaceholder-root{margin:0 auto;text-align:center;width:100%}
.DraftEditor-alignRight .public-DraftStyleDefault-block{text-align:right}
.DraftEditor-alignRight .public-DraftEditorPlaceholder-root{right:0;text-align:right}
.public-DraftEditorPlaceholder-root{color:#9197a3;position:absolute;z-index:0}
.public-DraftEditorPlaceholder-hasFocus{color:#bdc1c9}
.DraftEditorPlaceholder-hidden{display:none}
.public-DraftStyleDefault-block{position:relative;white-space:pre-wrap}
.public-DraftStyleDefault-ltr{direction:ltr;text-align:left}
.public-DraftStyleDefault-rtl{direction:rtl;text-align:right}
.public-DraftStyleDefault-listLTR{direction:ltr}
.public-DraftStyleDefault-listRTL{direction:rtl}
input, textarea, .editable, .editable * {
    -webkit-user-select: text;
       -moz-user-select: text;
            user-select: text;
  }
a {
    text-decoration: none;
    color: #979797;
    cursor: pointer;
  }
a.active-link {
      color: #eb5648;
    }
.svg-icon:not(.pure-svg-icon) > * {
      fill: currentColor;
      /* patch firefox path not inherit "fill: currentColor" */
    }
.svg-icon:not(.pure-svg-icon).disabled > * {
      fill: rgba(0, 0, 0, 0.3);
    }
.svg-icon:not(.pure-svg-icon).fill [fill] {
        fill: currentColor;
        stroke: none;
      }
.svg-icon:not(.pure-svg-icon).fill [stroke] {
        stroke: currentColor;
        fill: none;
      }
.svg-icon:not(.pure-svg-icon).fill [fill='none'] {
        fill: none
      }
.svg-icon:not(.pure-svg-icon).fill [fill='none'][stroke] {
        stroke: currentColor;
      }
.svg-icon:not(.pure-svg-icon).fill [stroke = 'none'] {
        stroke: none;
      }
.svg-icon:not(.pure-svg-icon).fill [stroke = 'none'][fill] {
        fill: currentColor;
      }
.svg-icon:not(.pure-svg-icon).fill:not([fill]), .svg-icon:not(.pure-svg-icon).fill:not([stroke]) {
        fill: currentColor;
      }
.SelectMenu .SelectOption, .SelectGroup, .CoreSelectMenu .SelectOption {
    transform: translate(0, 0);
  }

/*@import 'rails-lib/stylesheet/widget/widget.css';*/
/*@import 'rails-lib/stylesheet/widget/panel.css';*/
/*@import 'rails-lib/stylesheet/widget/android.css';*/
.rdw-option-wrapper{border:1px solid #f1f1f1;padding:5px;min-width:25px;height:20px;border-radius:2px;margin:0 4px;display:flex;justify-content:center;align-items:center;cursor:pointer;background:white;text-transform:capitalize}
.rdw-option-wrapper:hover{box-shadow:1px 1px 0 #bfbdbd}
.rdw-option-wrapper:active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-option-active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-option-disabled{opacity:.3;cursor:default}
.rdw-dropdown-wrapper{height:30px;background:white;cursor:pointer;border:1px solid #f1f1f1;border-radius:2px;margin:0 3px;text-transform:capitalize;background:white}
.rdw-dropdown-wrapper:focus{outline:0}
.rdw-dropdown-wrapper:hover{box-shadow:1px 1px 0 #bfbdbd;background-color:#fff}
.rdw-dropdown-wrapper:active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-dropdown-carettoopen{height:0;width:0;position:absolute;top:35%;right:10%;border-top:6px solid black;border-left:5px solid transparent;border-right:5px solid transparent}
.rdw-dropdown-carettoclose{height:0;width:0;position:absolute;top:35%;right:10%;border-bottom:6px solid black;border-left:5px solid transparent;border-right:5px solid transparent}
.rdw-dropdown-selectedtext{display:flex;position:relative;height:100%;align-items:center;padding:0 5px}
.rdw-dropdown-optionwrapper{z-index:100;position:relative;border:1px solid #f1f1f1;width:98%;background:white;border-radius:2px;margin:0;padding:0;max-height:250px;overflow-y:scroll}
.rdw-dropdown-optionwrapper:hover{box-shadow:1px 1px 0 #bfbdbd;background-color:#fff}
.rdw-dropdownoption-default{min-height:25px;display:flex;align-items:center;padding:0 5px}
.rdw-dropdownoption-highlighted{background:#f1f1f1}
.rdw-dropdownoption-active{background:#f5f5f5}
.rdw-dropdownoption-disabled{opacity:.3;cursor:default}
.rdw-inline-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-inline-dropdown{width:50px}
.rdw-inline-dropdownoption{height:40px;display:flex;justify-content:center}
.rdw-block-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-block-dropdown{width:110px}
.rdw-fontsize-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-fontsize-dropdown{min-width:40px}
.rdw-fontsize-option{display:flex;justify-content:center}
.rdw-fontfamily-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-fontfamily-dropdown{width:115px}
.rdw-fontfamily-placeholder{white-space:nowrap;max-width:90px;overflow:hidden;text-overflow:ellipsis}
.rdw-fontfamily-optionwrapper{width:140px}
.rdw-list-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-list-dropdown{width:50px;z-index:90}
.rdw-list-dropdownOption{height:40px;display:flex;justify-content:center}
.rdw-text-align-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-text-align-dropdown{width:50px;z-index:90}
.rdw-text-align-dropdownOption{height:40px;display:flex;justify-content:center}
.rdw-right-aligned-block{text-align:right}
.rdw-left-aligned-block{text-align:left !important}
.rdw-center-aligned-block{text-align:center !important}
.rdw-justify-aligned-block{text-align:justify !important}
.rdw-right-aligned-block>div{display:inline-block;min-width:1px}
.rdw-left-aligned-block>div{display:inline-block;min-width:1px}
.rdw-center-aligned-block>div{display:inline-block;min-width:1px}
.rdw-justify-aligned-block>div{display:inline-block;min-width:1px}
.rdw-colorpicker-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-colorpicker-modal{position:absolute;top:35px;left:5px;display:flex;flex-direction:column;width:175px;height:175px;border:1px solid #f1f1f1;padding:15px;border-radius:2px;z-index:100;background:white;box-shadow:3px 3px 5px #bfbdbd}
.rdw-colorpicker-modal-header{display:flex;padding-bottom:5px}
.rdw-colorpicker-modal-style-label{font-size:15px;width:50%;text-align:center;cursor:pointer;padding:0 10px 5px}
.rdw-colorpicker-modal-style-label-active{border-bottom:2px solid #0a66b7}
.rdw-colorpicker-modal-options{margin:5px auto;display:flex;width:100%;height:100%;flex-wrap:wrap;overflow:scroll}
.rdw-colorpicker-cube{width:22px;height:22px;border:1px solid #f1f1f1}
.rdw-colorpicker-option{margin:3px;padding:0;min-height:20px;border:0;width:22px;height:22px;min-width:22px;box-shadow:1px 2px 1px #bfbdbd inset}
.rdw-colorpicker-option:hover{box-shadow:1px 2px 1px #bfbdbd}
.rdw-colorpicker-option:active{box-shadow:-1px -2px 1px #bfbdbd}
.rdw-colorpicker-option-active{box-shadow:0 0 2px 2px #bfbdbd}
.rdw-link-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-link-dropdown{width:50px}
.rdw-link-dropdownOption{height:40px;display:flex;justify-content:center}
.rdw-link-dropdownPlaceholder{margin-left:8px}
.rdw-link-modal{position:absolute;top:35px;left:5px;display:flex;flex-direction:column;width:235px;height:205px;border:1px solid #f1f1f1;padding:15px;border-radius:2px;z-index:100;background:white;box-shadow:3px 3px 5px #bfbdbd}
.rdw-link-modal-label{font-size:15px}
.rdw-link-modal-input{margin-top:5px;border-radius:2px;border:1px solid #f1f1f1;height:25px;margin-bottom:15px;padding:0 5px}
.rdw-link-modal-input:focus{outline:0}
.rdw-link-modal-buttonsection{margin:0 auto}
.rdw-link-modal-target-option{margin-bottom:20px}
.rdw-link-modal-target-option>span{margin-left:5px}
.rdw-link-modal-btn{margin-left:10px;width:75px;height:30px;border:1px solid #f1f1f1;border-radius:2px;cursor:pointer;background:white;text-transform:capitalize}
.rdw-link-modal-btn:hover{box-shadow:1px 1px 0 #bfbdbd}
.rdw-link-modal-btn:active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-link-modal-btn:focus{outline:none !important}
.rdw-link-modal-btn:disabled{background:#ece9e9}
.rdw-link-dropdownoption{height:40px;display:flex;justify-content:center}
.rdw-history-dropdown{width:50px}
.rdw-embedded-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-embedded-modal{position:absolute;top:35px;left:5px;display:flex;flex-direction:column;width:235px;height:180px;border:1px solid #f1f1f1;padding:15px;border-radius:2px;z-index:100;background:white;justify-content:space-between;box-shadow:3px 3px 5px #bfbdbd}
.rdw-embedded-modal-header{font-size:15px;display:flex}
.rdw-embedded-modal-header-option{width:50%;cursor:pointer;display:flex;justify-content:center;align-items:center;flex-direction:column}
.rdw-embedded-modal-header-label{width:95px;border:1px solid #f1f1f1;margin-top:5px;background:#6eb8d4;border-bottom:2px solid #0a66b7}
.rdw-embedded-modal-link-section{display:flex;flex-direction:column}
.rdw-embedded-modal-link-input{width:88%;height:35px;margin:10px 0;border:1px solid #f1f1f1;border-radius:2px;font-size:15px;padding:0 5px}
.rdw-embedded-modal-link-input-wrapper{display:flex;align-items:center}
.rdw-embedded-modal-link-input:focus{outline:0}
.rdw-embedded-modal-btn-section{display:flex;justify-content:center}
.rdw-embedded-modal-btn{margin:0 3px;width:75px;height:30px;border:1px solid #f1f1f1;border-radius:2px;cursor:pointer;background:white;text-transform:capitalize}
.rdw-embedded-modal-btn:hover{box-shadow:1px 1px 0 #bfbdbd}
.rdw-embedded-modal-btn:active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-embedded-modal-btn:focus{outline:none !important}
.rdw-embedded-modal-btn:disabled{background:#ece9e9}
.rdw-embedded-modal-size{align-items:center;display:flex;margin:8px 0;justify-content:space-between}
.rdw-embedded-modal-size-input{width:80%;height:20px;border:1px solid #f1f1f1;border-radius:2px;font-size:12px}
.rdw-embedded-modal-size-input:focus{outline:0}
.rdw-emoji-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-emoji-modal{overflow:auto;position:absolute;top:35px;left:5px;display:flex;flex-wrap:wrap;width:235px;height:180px;border:1px solid #f1f1f1;padding:15px;border-radius:2px;z-index:100;background:white;box-shadow:3px 3px 5px #bfbdbd}
.rdw-emoji-icon{margin:2.5px;height:24px;width:24px;cursor:pointer;font-size:22px;display:flex;justify-content:center;align-items:center}
.rdw-spinner{display:flex;align-items:center;justify-content:center;height:100%;width:100%}
.rdw-spinner>div{width:12px;height:12px;background-color:#333;border-radius:100%;display:inline-block;animation:sk-bouncedelay 1.4s infinite ease-in-out both}
.rdw-spinner .rdw-bounce1{animation-delay:-0.32s}
.rdw-spinner .rdw-bounce2{animation-delay:-0.16s}
@keyframes ZBrU7Yes6CevGkk6hArA{0%,    80%,    100%{transform:scale(0)}40%{transform:scale(1.0)}}
.rdw-image-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-image-modal{position:absolute;top:35px;left:5px;display:flex;flex-direction:column;width:235px;border:1px solid #f1f1f1;padding:15px;border-radius:2px;z-index:100;background:white;box-shadow:3px 3px 5px #bfbdbd}
.rdw-image-modal-header{font-size:15px;margin:10px 0;display:flex}
.rdw-image-modal-header-option{width:50%;cursor:pointer;display:flex;justify-content:center;align-items:center;flex-direction:column}
.rdw-image-modal-header-label{width:80px;background:#f1f1f1;border:1px solid #f1f1f1;margin-top:5px}
.rdw-image-modal-header-label-highlighted{background:#6eb8d4;border-bottom:2px solid #0a66b7}
.rdw-image-modal-upload-option{width:100%;color:gray;cursor:pointer;display:flex;border:0;font-size:15px;align-items:center;justify-content:center;background-color:#f1f1f1;outline:2px dashed gray;outline-offset:-10px;margin:10px 0;padding:9px 0}
.rdw-image-modal-upload-option-highlighted{outline:2px dashed #0a66b7}
.rdw-image-modal-upload-option-label{cursor:pointer;height:100%;width:100%;display:flex;justify-content:center;align-items:center;padding:15px}
.rdw-image-modal-upload-option-label span{padding:0 20px}
.rdw-image-modal-upload-option-image-preview{max-width:100%;max-height:200px}
.rdw-image-modal-upload-option-input{width:.1px;height:.1px;opacity:0;overflow:hidden;position:absolute;z-index:-1}
.rdw-image-modal-url-section{display:flex;align-items:center}
.rdw-image-modal-url-input{width:90%;height:35px;margin:15px 0 12px;border:1px solid #f1f1f1;border-radius:2px;font-size:15px;padding:0 5px}
.rdw-image-modal-btn-section{margin:10px auto 0}
.rdw-image-modal-url-input:focus{outline:0}
.rdw-image-modal-btn{margin:0 5px;width:75px;height:30px;border:1px solid #f1f1f1;border-radius:2px;cursor:pointer;background:white;text-transform:capitalize}
.rdw-image-modal-btn:hover{box-shadow:1px 1px 0 #bfbdbd}
.rdw-image-modal-btn:active{box-shadow:1px 1px 0 #bfbdbd inset}
.rdw-image-modal-btn:focus{outline:none !important}
.rdw-image-modal-btn:disabled{background:#ece9e9}
.rdw-image-modal-spinner{position:absolute;top:-3px;left:0;width:100%;height:100%;opacity:.5}
.rdw-image-modal-alt-input{width:70%;height:20px;border:1px solid #f1f1f1;border-radius:2px;font-size:12px;margin-left:5px}
.rdw-image-modal-alt-input:focus{outline:0}
.rdw-image-modal-alt-lbl{font-size:12px}
.rdw-image-modal-size{align-items:center;display:flex;margin:8px 0;justify-content:space-between}
.rdw-image-modal-size-input{width:40%;height:20px;border:1px solid #f1f1f1;border-radius:2px;font-size:12px}
.rdw-image-modal-size-input:focus{outline:0}
.rdw-image-mandatory-sign{color:red;margin-left:3px;margin-right:3px}
.rdw-remove-wrapper{display:flex;align-items:center;margin-bottom:6px;position:relative;flex-wrap:wrap}
.rdw-history-wrapper{display:flex;align-items:center;margin-bottom:6px;flex-wrap:wrap}
.rdw-history-dropdownoption{height:40px;display:flex;justify-content:center}
.rdw-history-dropdown{width:50px}
.rdw-link-decorator-wrapper{position:relative}
.rdw-link-decorator-icon{position:absolute;left:40%;top:0;cursor:pointer;background-color:white}
.rdw-mention-link{text-decoration:none;color:#1236ff;background-color:#f0fbff;padding:1px 2px;border-radius:2px}
.rdw-suggestion-wrapper{position:relative}
.rdw-suggestion-dropdown{position:absolute;display:flex;flex-direction:column;border:1px solid #f1f1f1;min-width:100px;max-height:150px;overflow:auto;background:white;z-index:100}
.rdw-suggestion-option{padding:7px 5px;border-bottom:1px solid #f1f1f1}
.rdw-suggestion-option-active{background-color:#f1f1f1}
.rdw-hashtag-link{text-decoration:none;color:#1236ff;background-color:#f0fbff;padding:1px 2px;border-radius:2px}
.rdw-image-alignment-options-popup{position:absolute;background:white;display:flex;padding:5px 2px;border-radius:2px;border:1px solid #f1f1f1;width:105px;cursor:pointer;z-index:100}
.rdw-alignment-option-left{justify-content:flex-start}
.rdw-image-alignment-option{height:15px;width:15px;min-width:15px}
.rdw-image-alignment{position:relative}
.rdw-image-imagewrapper{position:relative}
.rdw-image-center{display:flex;justify-content:center}
.rdw-image-left{display:flex}
.rdw-image-right{display:flex;justify-content:flex-end}
.rdw-image-alignment-options-popup-right{right:0}
.rdw-editor-main{height:100%;box-sizing:border-box}
.rdw-editor-toolbar{padding:6px 5px 0;border-radius:2px;border:1px solid #f1f1f1;display:flex;justify-content:flex-start;background:white;flex-wrap:wrap;font-size:15px;margin-bottom:5px;-webkit-user-select:none;-moz-user-select:none;user-select:none}
.public-DraftStyleDefault-block{margin:1em 0}
.rdw-editor-wrapper:focus{outline:0}
.rdw-editor-wrapper{box-sizing:content-box}
.rdw-editor-main blockquote{border-left:5px solid #f1f1f1;padding-left:5px}
.rdw-editor-main pre{background:#f1f1f1;border-radius:3px;padding:1px 10px}
.DraftEditor-editorContainer, .DraftEditor-root, .public-DraftEditor-content{height:inherit;text-align:initial}
.public-DraftEditor-content[contenteditable=true]{-webkit-user-modify:read-write-plaintext-only}
.DraftEditor-root{position:relative}
.DraftEditor-editorContainer{background-color:rgba(255, 255, 255, 0);border-left:.1px solid transparent;position:relative;z-index:1}
.public-DraftEditor-block{position:relative}
.DraftEditor-alignLeft .public-DraftStyleDefault-block{text-align:left}
.DraftEditor-alignLeft .public-DraftEditorPlaceholder-root{left:0;text-align:left}
.DraftEditor-alignCenter .public-DraftStyleDefault-block{text-align:center}
.DraftEditor-alignCenter .public-DraftEditorPlaceholder-root{margin:0 auto;text-align:center;width:100%}
.DraftEditor-alignRight .public-DraftStyleDefault-block{text-align:right}
.DraftEditor-alignRight .public-DraftEditorPlaceholder-root{right:0;text-align:right}
.public-DraftEditorPlaceholder-root{color:#9197a3;position:absolute;z-index:0}
.public-DraftEditorPlaceholder-hasFocus{color:#bdc1c9}
.DraftEditorPlaceholder-hidden{display:none}
.public-DraftStyleDefault-block{position:relative;white-space:pre-wrap}
.public-DraftStyleDefault-ltr{direction:ltr;text-align:left}
.public-DraftStyleDefault-rtl{direction:rtl;text-align:right}
.public-DraftStyleDefault-listLTR{direction:ltr}
.public-DraftStyleDefault-listRTL{direction:rtl}
input, textarea, .sticky {
    -webkit-user-select: text;
       -moz-user-select: text;
            user-select: text;
  }
.svg-icon > * {
      fill: currentColor; /* patch firefox path not inherit "fill: currentColor" */
    }
.svg-icon.fill [fill] {
        fill: currentColor;
        stroke: none;
      }
.svg-icon.fill [stroke] {
        stroke: currentColor;
        fill: none;
      }
.svg-icon.fill [fill='none'] {
        fill: none
      }
.svg-icon.fill [fill='none'][stroke] {
        stroke: currentColor;
      }
.svg-icon.fill [stroke = 'none'] {
        stroke: none;
      }
.svg-icon.fill [stroke = 'none'][fill] {
        fill: currentColor;
      }
.svg-icon.fill:not([fill]), .svg-icon.fill:not([stroke]) {
         fill: currentColor;
      }
#workspace {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
#workspace.isMobile {
      background: black;
    }

/**
 * (c) jExcel v3.6.3
 *
 * Author: Paul Hodel <<EMAIL>>
 * Website: https://bossanova.uk/jexcel/
 * Description: Create amazing web based spreadsheets.
 *
 * This software is distribute under MIT License
 */
:root {
    --jexcel-border-color:#000;
  }
* {
    box-sizing: border-box;
  }
.jexcel_container {
    display:inline-block;
    padding-right:2px;
    box-sizing: border-box;
    overscroll-behavior: contain;
  }
.jexcel_content {
    display:inline-block;
    box-sizing: border-box;
    padding-right:2px;
    position:relative;
  }
.jexcel {
    border-collapse:separate;
    table-layout:fixed;
    white-space: nowrap;
    empty-cells:show;
    border:0px;
    background-color:rgba(0, 0, 0, 0);
    width:0;

    border-top:1px solid transparent;
    border-left:1px solid transparent;
    border-right:1px solid #ccc;
    border-bottom:1px solid #ccc;
  }
.jexcel > thead > tr > td
  {
    border-top:1px solid #ccc;
    border-left:1px solid #ccc;
    border-right:1px solid transparent;
    border-bottom:1px solid transparent;
    background-color:#f3f3f3;
    padding:2px;
    cursor:pointer;
    box-sizing: border-box;
    overflow: hidden;
    position: sticky;
    top: 0;
    z-index:2000;
  }
.jexcel .highlight-selected
  {
    background-color:rgba(0,0,0,0.0);
  }
.jexcel .selection
  {
    background-color:rgba(0,0,0,0.05);
  }
.jexcel .selection-left
  {
    border-left:1px dotted #000;
  }
.jexcel .selection-right
  {
    border-right:1px dotted #000;
  }
.jexcel .selection-top
  {
    border-top:1px dotted #000;
  }
.jexcel .selection-bottom
  {
    border-bottom:1px dotted #000;
  }
.jexcel_corner
  {
    position:absolute;
    background-color: rgb(0, 0, 0);
    height: 1px;
    width: 1px;
    border: 1px solid rgb(255, 255, 255);
    top:-2000px;
    left:-2000px;
    cursor:crosshair;
    box-sizing: initial;
    z-index:7000;
    padding: 2px;
  }
.jexcel, .jexcel td, .jexcel_corner
  {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
  }

:lang(en) .leftdrawer .name, :lang(en) .rightdrawer .name {
        transform: translateX(-50%) scale(0.83);
      }
  .li7V5EPbUFjQxQ3UBo2z {
    position: relative;
    height: 53px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 9px;
    margin-right: 16px;
    cursor: pointer;
  }
  .li7V5EPbUFjQxQ3UBo2z:nth-child(4n) {
      margin-right: 0;
    }
  .li7V5EPbUFjQxQ3UBo2z .pages {
      position: relative;
      height: 34px;
      width: 34px;
      line-height: 28px;
      border: 1px solid #c8cdd0;
      border-radius: 2px;
      overflow: hidden;
      transition: all 0.15s ease-in-out;
    }
  .li7V5EPbUFjQxQ3UBo2z .name {
      position: absolute;
      white-space: nowrap;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }
  .li7V5EPbUFjQxQ3UBo2z .page1, .li7V5EPbUFjQxQ3UBo2z .page2 {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      transition: all 1s ease-in-out;
      font-size: 17px;
      line-height: 34px;
      text-align: center;
      color: #fff;
    }
  .li7V5EPbUFjQxQ3UBo2z .page1 {
      z-index: 2;
      background: #ebebeb;
      color: #415058;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  .li7V5EPbUFjQxQ3UBo2z .page2 {
      background: #298df8;
    }
  .li7V5EPbUFjQxQ3UBo2z .page1:before, .li7V5EPbUFjQxQ3UBo2z .page2:before {
      display: none;
    }
  .li7V5EPbUFjQxQ3UBo2z .page2.icon-arrow-bottom, .li7V5EPbUFjQxQ3UBo2z .page2.icon-arrow-top, .li7V5EPbUFjQxQ3UBo2z .page2.icon-arrow-left, .li7V5EPbUFjQxQ3UBo2z .page2.icon-arrow-right {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  .li7V5EPbUFjQxQ3UBo2z:hover .pages {
      border: 1px solid #3299ff;
    }
  .li7V5EPbUFjQxQ3UBo2z.current .pages {
      border: 1px solid #3299ff;
      box-shadow: 0 0 4px 0 rgba(34, 162, 237, 0.66);
    }
  /* 默认 */
  .li7V5EPbUFjQxQ3UBo2z.none .page1 {
        top: 5px;
        left: 5px;
        width: 19px;
        height: 19px;
      }
  /* 左菜单 */
  .li7V5EPbUFjQxQ3UBo2z.leftmenu .page2 {
        transform: translateX(-100%);
      }
  .li7V5EPbUFjQxQ3UBo2z.leftmenu:hover .page1 {
          box-shadow: -1px 0 2px 0 rgba(0, 0, 0, 0.1);
          animation: leftmenu1 1s linear infinite;
        }
  .li7V5EPbUFjQxQ3UBo2z.leftmenu:hover .page2 {
          animation: leftmenu2 1s linear infinite;
        }
  @keyframes leftmenu1 {
      from {
        transform: translateX(0);
      }

      20% {
        transform: translateX(0);
      }

      80% {
        transform: translateX(70%);
      }

      100% {
        transform: translateX(70%);
      }
    }
  @keyframes leftmenu2 {
      from {
        transform: translateX(-100%);
      }

      20% {
        transform: translateX(-100%);
      }

      80% {
        transform: translateX(-30%);
      }

      100% {
        transform: translateX(-30%);
      }
    }
  /* 右菜单 */
  .li7V5EPbUFjQxQ3UBo2z.rightmenu .page2 {
        transform: translateX(100%);
      }
  .li7V5EPbUFjQxQ3UBo2z.rightmenu:hover .page1 {
          animation: rightmenu1 1s linear infinite;
          box-shadow: -1px 0 2px 0 rgba(0, 0, 0, 0.1);
        }
  .li7V5EPbUFjQxQ3UBo2z.rightmenu:hover .page2 {
          animation: rightmenu2 1s linear infinite;
        }
  @keyframes rightmenu1 {
      from {
        transform: translateX(0);
      }

      20% {
        transform: translateX(0);
      }

      80% {
        transform: translateX(-70%);
      }

      100% {
        transform: translateX(-70%);
      }
    }
  @keyframes rightmenu2 {
      from {
        transform: translateX(100%);
      }

      20% {
        transform: translateX(100%);
      }

      80% {
        transform: translateX(30%);
      }

      100% {
        transform: translateX(30%);
      }
    }
  /* 左抽屉 */
  .li7V5EPbUFjQxQ3UBo2z.leftdrawer .page2 {
        left: -100%;
        z-index: 2;
      }
  .li7V5EPbUFjQxQ3UBo2z.leftdrawer:hover .page2 {
          animation: leftdrawer 1s linear infinite;
          box-shadow: -1px 0 2px 0 rgba(0, 0, 0, 0.1);
        }
  @keyframes leftdrawer {
      from {
        transform: translateX(0);
      }

      20% {
        transform: translateX(0);
      }

      80% {
        transform: translateX(70%);
      }

      100% {
        transform: translateX(70%);
      }
    }
  /* 右抽屉 */
  .li7V5EPbUFjQxQ3UBo2z.rightdrawer .page2 {
        left: 100%;
        z-index: 2;
      }
  .li7V5EPbUFjQxQ3UBo2z.rightdrawer:hover .page2 {
          animation: rightdrawer 1s linear infinite;
          box-shadow: -1px 0 2px 0 rgba(0, 0, 0, 0.1);
        }
  @keyframes rightdrawer {
      from {
        transform: translateX(0);
      }

      20% {
        transform: translateX(0);
      }

      80% {
        transform: translateX(-70%);
      }

      100% {
        transform: translateX(-70%);
      }
    }
  /* 左移入 */
  .li7V5EPbUFjQxQ3UBo2z.slideright .page2 {
        left: -100%;
      }
  .li7V5EPbUFjQxQ3UBo2z.slideright:hover .page1, .li7V5EPbUFjQxQ3UBo2z.slideright:hover .page2 {
          animation: slideright 1s linear infinite;
        }
  @keyframes slideright {
      from {
        transform: translateX(0);
      }

      20% {
        transform: translateX(0);
      }

      80% {
        transform: translateX(100%);
      }

      100% {
        transform: translateX(100%);
      }
    }
  /* 右移入 */
  .li7V5EPbUFjQxQ3UBo2z.slideleft .page2 {
        left: 100%;
      }
  .li7V5EPbUFjQxQ3UBo2z.slideleft:hover .page1, .li7V5EPbUFjQxQ3UBo2z.slideleft:hover .page2 {
          animation: slideleft 1s linear infinite;
        }
  @keyframes slideleft {
      from {
        transform: translateX(0);
      }

      20% {
        transform: translateX(0);
      }

      80% {
        transform: translateX(-100%);
      }

      100% {
        transform: translateX(-100%);
      }
    }
  /* 上移入 */
  .li7V5EPbUFjQxQ3UBo2z.slidetop .page2 {
        top: -100%;
      }
  .li7V5EPbUFjQxQ3UBo2z.slidetop:hover .page1, .li7V5EPbUFjQxQ3UBo2z.slidetop:hover .page2 {
          animation: slidetop 1s linear infinite;
        }
  @keyframes slidetop {
      from {
        transform: translateY(0);
      }

      20% {
        transform: translateY(0);
      }

      80% {
        transform: translateY(100%);
      }

      100% {
        transform: translateY(100%);
      }
    }
  /* 下移入 */
  .li7V5EPbUFjQxQ3UBo2z.slidebottom .page2 {
        top: 100%;
      }
  .li7V5EPbUFjQxQ3UBo2z.slidebottom:hover .page1, .li7V5EPbUFjQxQ3UBo2z.slidebottom:hover .page2 {
          animation: slidebottom 1s linear infinite;
        }
  @keyframes slidebottom {
      from {
        transform: translateY(0);
      }

      20% {
        transform: translateY(0);
      }

      80% {
        transform: translateY(-100%);
      }

      100% {
        transform: translateY(-100%);
      }
    }
  /* 左弹入 */
  .li7V5EPbUFjQxQ3UBo2z.slideleft2 .page2 {
        left: -100%;
        z-index: 3;
      }
  .li7V5EPbUFjQxQ3UBo2z.slideleft2:hover .page2 {
          animation: slideleft2 1s linear infinite;
        }
  @keyframes slideleft2 {
      from {
        transform: translateX(0);
      }

      20% {
        transform: translateX(0);
      }

      80% {
        transform: translateX(100%);
      }

      100% {
        transform: translateX(100%);
      }
    }
  /* 右弹入 */
  .li7V5EPbUFjQxQ3UBo2z.slideright2 .page2 {
        left: 100%;
        z-index: 2;
      }
  .li7V5EPbUFjQxQ3UBo2z.slideright2:hover .page2 {
          animation: slideright2 1s linear infinite;
        }
  @keyframes slideright2 {
      from {
        transform: translateX(0);
      }

      20% {
        transform: translateX(0);
      }

      80% {
        transform: translateX(-100%);
      }

      100% {
        transform: translateX(-100%);
      }
    }
  /* 上弹入 */
  .li7V5EPbUFjQxQ3UBo2z.topin .page2 {
        top: -100%;
        z-index: 3;
      }
  .li7V5EPbUFjQxQ3UBo2z.topin:hover .page2 {
          animation: topin 1s linear infinite;
        }
  @keyframes topin {
      from {
        transform: translateY(0);
      }

      20% {
        transform: translateY(0);
      }

      80% {
        transform: translateY(100%);
      }

      100% {
        transform: translateY(100%);
      }
    }
  /* 下弹入 */
  .li7V5EPbUFjQxQ3UBo2z.popin .page2 {
        top: 100%;
        z-index: 3;
      }
  .li7V5EPbUFjQxQ3UBo2z.popin:hover .page2 {
          animation: popin 1s linear infinite;
        }
  @keyframes popin {
      from {
        transform: translateY(0);
      }

      20% {
        transform: translateY(0);
      }

      80% {
        transform: translateY(-100%);
      }

      100% {
        transform: translateY(-100%);
      }
    }
  /* 左弹出 */
  .li7V5EPbUFjQxQ3UBo2z.leftout .page2 {
        transform: translateX(100%);
        transition: none;
      }
  .li7V5EPbUFjQxQ3UBo2z.leftout:hover .page2 {
        transform: translateX(0);
      }
  .li7V5EPbUFjQxQ3UBo2z.leftout:hover .page1 {
        animation: leftout 1s linear infinite;
      }
  @keyframes leftout {
      from {
        transform: translateX(0);
      }

      20% {
        transform: translateX(0);
      }

      80% {
        transform: translateX(-100%);
      }

      100% {
        transform: translateX(-100%);
      }
    }
  /* 右弹出 */
  .li7V5EPbUFjQxQ3UBo2z.rightout .page2 {
        transform: translateX(-100%);
        transition: none;
      }
  .li7V5EPbUFjQxQ3UBo2z.rightout:hover .page2 {
        transform: translateX(0);
      }
  .li7V5EPbUFjQxQ3UBo2z.rightout:hover .page1 {
        animation: rightout 1s ease-in-out infinite;
      }
  @keyframes rightout {
      from {
        transform: translateX(0);
      }

      20% {
        transform: translateX(0);
      }

      80% {
        transform: translateX(100%);
      }

      100% {
        transform: translateX(100%);
      }
    }
  /* 上弹出 */
  .li7V5EPbUFjQxQ3UBo2z.topout .page2 {
        transform: translateY(100%);
        transition: none;
      }
  .li7V5EPbUFjQxQ3UBo2z.topout:hover .page2 {
        transform: translateX(0);
      }
  .li7V5EPbUFjQxQ3UBo2z.topout:hover .page1 {
        animation: topout 1s ease-in-out infinite;
      }
  @keyframes topout {
      from {
        transform: translateY(0);
      }

      20% {
        transform: translateY(0);
      }

      80% {
        transform: translateY(-100%);
      }

      100% {
        transform: translateY(-100%);
      }
    }
  /* 下弹出 */
  .li7V5EPbUFjQxQ3UBo2z.popout .page2 {
        transform: translateY(-100%);
        transition: none;
      }
  .li7V5EPbUFjQxQ3UBo2z.popout:hover .page2 {
        transform: translateX(0);
      }
  .li7V5EPbUFjQxQ3UBo2z.popout:hover .page1 {
        animation: popout 1s ease-in-out infinite;
      }
  @keyframes popout {
      from {
        transform: translateY(0);
      }

      20% {
        transform: translateY(0);
      }

      80% {
        transform: translateY(100%);
      }

      100% {
        transform: translateY(100%);
      }
    }
  .li7V5EPbUFjQxQ3UBo2z.transition-view-icon .pages {
        background-color: unset !important;
        background: unset !important;
      }
  .li7V5EPbUFjQxQ3UBo2z.transition-view-icon .pages:hover .page1 {
            background-color: #ebebeb !important;
          }
  .li7V5EPbUFjQxQ3UBo2z.transition-view-icon .page1 {
        transition: unset;
        background-color: unset !important;
        color: inherit;
      }

.RrZKprPGiEqkxEELaP57 {
    display: none;
    position: absolute;
    pointer-events: none;
    z-index: 11;
    background-image: none;
  }

    .RrZKprPGiEqkxEELaP57 .tab_bar, .RrZKprPGiEqkxEELaP57 .keyboard {
      top: 0 !important;
    }

    .RrZKprPGiEqkxEELaP57 .pg {
      width: 40px !important;
      height: 40px !important;
    }

    .RrZKprPGiEqkxEELaP57[data-do-not-create=true] .carousel, .RrZKprPGiEqkxEELaP57[data-do-not-create=true] .map_view {
      width: 200px !important;
      height: 100px !important;
    }

    .RrZKprPGiEqkxEELaP57[data-do-not-create=true] .image_view, .RrZKprPGiEqkxEELaP57[data-do-not-create=true] .image_view .wrapper {
      max-width: 109px;
      max-height: 109px;
    }

    .RrZKprPGiEqkxEELaP57[data-do-not-create=true] .image_view img {
      width: auto !important;
      height: auto !important;
      max-width: 109px;
      max-height: 109px;
    }
  .WVRqDWvrnmzMpK3ZflPA {
    position: absolute;
    pointer-events: none;
    background: white;
    z-index: 11;
    border: 1px dashed black;
    opacity: 0.5;
    display: none;
  }
.nT4U2_NOIS7CAZHovWxY {
  position: absolute;
  left: 0;
  top: 100vh;
  width: 100%;
  height: 100%;
  margin: 1px 0 0;
  border: none;
  visibility: hidden;
  pointer-events: none;
}

.ReactCrop {
  position: relative;
  display: inline-block;
  cursor: crosshair;
  overflow: hidden;
  max-width: 100%;
}
.ReactCrop:focus {
  outline: none;
}
.ReactCrop--disabled, .ReactCrop--locked {
  cursor: inherit;
}
.ReactCrop__image {
  display: block;
  max-width: 100%;
  touch-action: none;
}
.ReactCrop__crop-selection {
  position: absolute;
  top: 0;
  left: 0;
  transform: translate3d(0, 0, 0);
  box-sizing: border-box;
  cursor: move;
  box-shadow: 0 0 0 9999em rgba(0, 0, 0, 0.5);
  touch-action: none;
  border: 1px solid;
  border-image-source: url(data:image/gif;base64,R0lGODlhCgAKAJECAAAAAP///////wAAACH/C05FVFNDQVBFMi4wAwEAAAAh/wtYTVAgRGF0YVhNUDw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6OEI5RDc5MTFDNkE2MTFFM0JCMDZEODI2QTI4MzJBOTIiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6OEI5RDc5MTBDNkE2MTFFM0JCMDZEODI2QTI4MzJBOTIiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoTWFjaW50b3NoKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuZGlkOjAyODAxMTc0MDcyMDY4MTE4MDgzQzNDMjA5MzREQ0ZDIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjAyODAxMTc0MDcyMDY4MTE4MDgzQzNDMjA5MzREQ0ZDIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEBQoAAgAsAAAAAAoACgAAAhWEERkn7W3ei7KlagMWF/dKgYeyGAUAIfkEBQoAAgAsAAAAAAoACgAAAg+UYwLJ7RnQm7QmsCyVKhUAIfkEBQoAAgAsAAAAAAoACgAAAhCUYgLJHdiinNSAVfOEKoUCACH5BAUKAAIALAAAAAAKAAoAAAIRVISAdusPo3RAzYtjaMIaUQAAIfkEBQoAAgAsAAAAAAoACgAAAg+MDiem7Q8bSLFaG5il6xQAIfkEBQoAAgAsAAAAAAoACgAAAg+UYRLJ7QnQm7SmsCyVKhUAIfkEBQoAAgAsAAAAAAoACgAAAhCUYBLJDdiinNSEVfOEKoECACH5BAUKAAIALAAAAAAKAAoAAAIRFISBdusPo3RBzYsjaMIaUQAAOw==);
  border-image-slice: 1;
  border-image-repeat: repeat;
}
.ReactCrop--disabled .ReactCrop__crop-selection {
  cursor: inherit;
}
.ReactCrop--circular-crop .ReactCrop__crop-selection {
  border-radius: 50%;
  box-shadow: 0px 0px 1px 1px white, 0 0 0 9999em rgba(0, 0, 0, 0.5);
}
.ReactCrop--invisible-crop .ReactCrop__crop-selection {
  display: none;
}
.ReactCrop__rule-of-thirds-vt::before, .ReactCrop__rule-of-thirds-vt::after, .ReactCrop__rule-of-thirds-hz::before, .ReactCrop__rule-of-thirds-hz::after {
  content: "";
  display: block;
  position: absolute;
  background-color: rgba(255, 255, 255, 0.4);
}
.ReactCrop__rule-of-thirds-vt::before, .ReactCrop__rule-of-thirds-vt::after {
  width: 1px;
  height: 100%;
}
.ReactCrop__rule-of-thirds-vt::before {
  left: 33.3333%;
  left: calc(100% / 3);
}
.ReactCrop__rule-of-thirds-vt::after {
  left: 66.6666%;
  left: calc(100% / 3 * 2);
}
.ReactCrop__rule-of-thirds-hz::before, .ReactCrop__rule-of-thirds-hz::after {
  width: 100%;
  height: 1px;
}
.ReactCrop__rule-of-thirds-hz::before {
  top: 33.3333%;
  top: calc(100% / 3);
}
.ReactCrop__rule-of-thirds-hz::after {
  top: 66.6666%;
  top: calc(100% / 3 * 2);
}
.ReactCrop__drag-handle {
  position: absolute;
}
.ReactCrop__drag-handle::after {
  position: absolute;
  content: "";
  display: block;
  width: 10px;
  height: 10px;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.7);
  box-sizing: border-box;
  outline: 1px solid transparent;
}
.ReactCrop .ord-nw {
  top: 0;
  left: 0;
  margin-top: -5px;
  margin-left: -5px;
  cursor: nw-resize;
}
.ReactCrop .ord-nw::after {
  top: 0;
  left: 0;
}
.ReactCrop .ord-n {
  top: 0;
  left: 50%;
  margin-top: -5px;
  margin-left: -5px;
  cursor: n-resize;
}
.ReactCrop .ord-n::after {
  top: 0;
}
.ReactCrop .ord-ne {
  top: 0;
  right: 0;
  margin-top: -5px;
  margin-right: -5px;
  cursor: ne-resize;
}
.ReactCrop .ord-ne::after {
  top: 0;
  right: 0;
}
.ReactCrop .ord-e {
  top: 50%;
  right: 0;
  margin-top: -5px;
  margin-right: -5px;
  cursor: e-resize;
}
.ReactCrop .ord-e::after {
  right: 0;
}
.ReactCrop .ord-se {
  bottom: 0;
  right: 0;
  margin-bottom: -5px;
  margin-right: -5px;
  cursor: se-resize;
}
.ReactCrop .ord-se::after {
  bottom: 0;
  right: 0;
}
.ReactCrop .ord-s {
  bottom: 0;
  left: 50%;
  margin-bottom: -5px;
  margin-left: -5px;
  cursor: s-resize;
}
.ReactCrop .ord-s::after {
  bottom: 0;
}
.ReactCrop .ord-sw {
  bottom: 0;
  left: 0;
  margin-bottom: -5px;
  margin-left: -5px;
  cursor: sw-resize;
}
.ReactCrop .ord-sw::after {
  bottom: 0;
  left: 0;
}
.ReactCrop .ord-w {
  top: 50%;
  left: 0;
  margin-top: -5px;
  margin-left: -5px;
  cursor: w-resize;
}
.ReactCrop .ord-w::after {
  left: 0;
}
.ReactCrop__disabled .ReactCrop__drag-handle {
  cursor: inherit;
}
.ReactCrop__drag-bar {
  position: absolute;
}
.ReactCrop__drag-bar.ord-n {
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  margin-top: -3px;
}
.ReactCrop__drag-bar.ord-e {
  right: 0;
  top: 0;
  width: 6px;
  height: 100%;
  margin-right: -3px;
}
.ReactCrop__drag-bar.ord-s {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6px;
  margin-bottom: -3px;
}
.ReactCrop__drag-bar.ord-w {
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  margin-left: -3px;
}
.ReactCrop--new-crop .ReactCrop__drag-bar, .ReactCrop--new-crop .ReactCrop__drag-handle, .ReactCrop--fixed-aspect .ReactCrop__drag-bar {
  display: none;
}
.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-n, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-e, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-s, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-w {
  display: none;
}
@media (pointer: coarse) {
  .ReactCrop .ord-n,
.ReactCrop .ord-e,
.ReactCrop .ord-s,
.ReactCrop .ord-w {
    display: none;
  }
  .ReactCrop__drag-handle {
    width: 24px;
    height: 24px;
  }
}

/*# sourceMappingURL=ReactCrop.css.map */


span.data-grid-container, span.data-grid-container:focus {
  outline: none;
}

.data-grid-container .data-grid {
  table-layout: fixed;
  border-collapse: collapse;
} 

.data-grid-container .data-grid .cell.updated {
    background-color: rgba(0, 145, 253, 0.16);
    transition : background-color 0ms ease ;
}
.data-grid-container .data-grid .cell {
  height: 17px;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  cursor: cell;
  background-color: unset;
  transition : background-color 500ms ease;
  vertical-align: middle;
  text-align: right;
  border: 1px solid #DDD;
  padding: 0;
}
.data-grid-container .data-grid .cell.selected {
  border: 1px double rgb(33, 133, 208);
  transition: none;
  box-shadow: inset 0 -100px 0 rgba(33, 133, 208, 0.15);
}

.data-grid-container .data-grid .cell.read-only {
  background: whitesmoke;
  color: #999;
  text-align: center;
}

.data-grid-container .data-grid .cell > .text {
  padding: 2px 5px;
  text-overflow: ellipsis;
  overflow: hidden;
}


.data-grid-container .data-grid .cell > input {
  outline: none !important;
  border: 2px solid rgb(33, 133, 208);
  text-align:right;
  width: calc(100% - 6px);
  height: 11px;
  background: none;
  display: block;
}


.data-grid-container .data-grid .cell {
  vertical-align: bottom;
}

.data-grid-container .data-grid .cell,
.data-grid-container .data-grid.wrap .cell,
.data-grid-container .data-grid.wrap .cell.wrap,
.data-grid-container .data-grid .cell.wrap,
.data-grid-container .data-grid.nowrap .cell.wrap,
.data-grid-container .data-grid.clip .cell.wrap {
  white-space: normal;
}

.data-grid-container .data-grid.nowrap .cell,
.data-grid-container .data-grid.nowrap .cell.nowrap,
.data-grid-container .data-grid .cell.nowrap,
.data-grid-container .data-grid.wrap .cell.nowrap,
.data-grid-container .data-grid.clip .cell.nowrap {
  white-space: nowrap;
  overflow-x: visible;
}

.data-grid-container .data-grid.clip .cell,
.data-grid-container .data-grid.clip .cell.clip,
.data-grid-container .data-grid .cell.clip,
.data-grid-container .data-grid.wrap .cell.clip,
.data-grid-container .data-grid.nowrap .cell.clip {
  white-space: nowrap;
  overflow-x: hidden;
}

.data-grid-container .data-grid .cell .value-viewer, .data-grid-container .data-grid .cell .data-editor {
  display: block;
}

/*

github.com style (c) Vasily Polovnyov <<EMAIL>>

*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  color: #333;
  background: #f8f8f8;
}

.hljs-comment,
.hljs-quote {
  color: #998;
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: #333;
  font-weight: bold;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
  color: #008080;
}

.hljs-string,
.hljs-doctag {
  color: #d14;
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: #900;
  font-weight: bold;
}

.hljs-subst {
  font-weight: normal;
}

.hljs-type,
.hljs-class .hljs-title {
  color: #458;
  font-weight: bold;
}

.hljs-tag,
.hljs-name,
.hljs-attribute {
  color: #000080;
  font-weight: normal;
}

.hljs-regexp,
.hljs-link {
  color: #009926;
}

.hljs-symbol,
.hljs-bullet {
  color: #990073;
}

.hljs-built_in,
.hljs-builtin-name {
  color: #0086b3;
}

.hljs-meta {
  color: #999;
  font-weight: bold;
}

.hljs-deletion {
  background: #fdd;
}

.hljs-addition {
  background: #dfd;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}


/*# sourceMappingURL=preview-html-zip-f0d9de76208db26b1137.css.map*/