try{let N=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},$=new N.Error().stack;$&&(N._sentryDebugIds=N._sentryDebugIds||{},N._sentryDebugIds[$]="66c61e27-18c4-4199-a520-ac0fa378c17d",N._sentryDebugIdIdentifier="sentry-dbid-66c61e27-18c4-4199-a520-ac0fa378c17d")}catch(N){}{let N=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};N.SENTRY_RELEASE={id:"22.0.4"}}(()=>{var N={9820:h=>{"use strict";h.exports=html2canvas},16700:h=>{"use strict";h.exports=canvg},18183:(h,S,o)=>{"use strict";o.d(S,{Dw:()=>a,H:()=>g,QQ:()=>e,X9:()=>t,o$:()=>y});const y="/proto",a=c=>y+"/"+c,b=c=>{const i=/^\/proto\/design\/(?:([^/]+?))$/i.exec(c);return i?i[1]:new Error("project cid error")},g=c=>{let{search:i,pathname:r}=c;if(r.includes("/sharing"))return r.includes("dashboard")||r.includes("htmlzip")?r.split("/").at(-3):r.includes("embed/v2")?location.pathname.split("/").at(-4):r.split("/").at(-2);{const l=new URLSearchParams(i).get("token");if(l)return l;const d=/^\/proto\/([^/]+?)(?:\/forum|\/uichina|\/embed|\/embed\/v2|\/htmlzip|\/sharing|\/ro)?\/?$/i.exec(r);return d?d[1]:""}},B=c=>{let{pathname:i}=c;const r=/^\/proto\/design\/([^/]+)\/?$/i.exec(i);return r?r[1]:""},t=c=>{let{search:i,pathname:r}=c;return g({search:i,pathname:r})||B({pathname:r})},e="/p2mkt_view",s=c=>e+"/"+c,n=c=>/^\/p2mkt_view\/(?:([^/]+?))$/i.exec(c)[1]},28217:()=>{/*! iScroll v5.2.0-snapshot ~ (c) 2008-2017 Matteo Spinelli ~ http://cubiq.org/license */(function(h,S,o){var y=h.requestAnimationFrame||h.webkitRequestAnimationFrame||h.mozRequestAnimationFrame||h.oRequestAnimationFrame||h.msRequestAnimationFrame||function(t){h.setTimeout(t,16.666666666666668)},a=function(){var t={},e=S.createElement("div").style,s=function(){for(var i=["t","webkitT","MozT","msT","OT"],r,l=0,d=i.length;l<d;l++)if(r=i[l]+"ransform",r in e)return i[l].substr(0,i[l].length-1);return!1}();function n(i){return s===!1?!1:s===""?i:s+i.charAt(0).toUpperCase()+i.substr(1)}t.getTime=Date.now||function(){return new Date().getTime()},t.extend=function(i,r){for(var l in r)i[l]=r[l]},t.addEvent=function(i,r,l,d){i.addEventListener(r,l,!!d)},t.removeEvent=function(i,r,l,d){i.removeEventListener(r,l,!!d)},t.prefixPointerEvent=function(i){return h.MSPointerEvent?"MSPointer"+i.charAt(7).toUpperCase()+i.substr(8):i},t.momentum=function(i,r,l,d,P,_){var v=i-r,L=o.abs(v)/l,p,m;return _=_===void 0?6e-4:_,p=i+L*L/(2*_)*(v<0?-1:1),m=L/_,p<d?(p=P?d-P/2.5*(L/8):d,v=o.abs(p-i),m=v/L):p>0&&(p=P?P/2.5*(L/8):0,v=o.abs(i)+p,m=v/L),{destination:o.round(p),duration:m}};var c=n("transform");return t.extend(t,{hasTransform:c!==!1,hasPerspective:n("perspective")in e,hasTouch:"ontouchstart"in h,hasPointer:!!(h.PointerEvent||h.MSPointerEvent),hasTransition:n("transition")in e}),t.isBadAndroid=function(){var i=h.navigator.appVersion;if(/Android/.test(i)&&!/Chrome\/\d/.test(i)){var r=i.match(/Safari\/(\d+.\d)/);return r&&typeof r=="object"&&r.length>=2?parseFloat(r[1])<535.19:!0}else return!1}(),t.extend(t.style={},{transform:c,transitionTimingFunction:n("transitionTimingFunction"),transitionDuration:n("transitionDuration"),transitionDelay:n("transitionDelay"),transformOrigin:n("transformOrigin"),touchAction:n("touchAction")}),t.hasClass=function(i,r){var l=new RegExp("(^|\\s)"+r+"(\\s|$)");return l.test(i.className)},t.addClass=function(i,r){if(!t.hasClass(i,r)){var l=i.className.split(" ");l.push(r),i.className=l.join(" ")}},t.removeClass=function(i,r){if(t.hasClass(i,r)){var l=new RegExp("(^|\\s)"+r+"(\\s|$)","g");i.className=i.className.replace(l," ")}},t.offset=function(i){for(var r=-i.offsetLeft,l=-i.offsetTop;i=i.offsetParent;)r-=i.offsetLeft,l-=i.offsetTop;return{left:r,top:l}},t.preventDefaultException=function(i,r){for(var l in r)if(r[l].test(i[l]))return!0;return!1},t.extend(t.eventType={},{touchstart:1,touchmove:1,touchend:1,mousedown:2,mousemove:2,mouseup:2,pointerdown:3,pointermove:3,pointerup:3,MSPointerDown:3,MSPointerMove:3,MSPointerUp:3}),t.extend(t.ease={},{quadratic:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(i){return i*(2-i)}},circular:{style:"cubic-bezier(0.1, 0.57, 0.1, 1)",fn:function(i){return o.sqrt(1- --i*i)}},back:{style:"cubic-bezier(0.175, 0.885, 0.32, 1.275)",fn:function(i){var r=4;return(i=i-1)*i*((r+1)*i+r)+1}},bounce:{style:"",fn:function(i){return(i/=1)<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375}},elastic:{style:"",fn:function(i){var r=.22,l=.4;return i===0?0:i==1?1:l*o.pow(2,-10*i)*o.sin((i-r/4)*(2*o.PI)/r)+1}}}),t.tap=function(i,r){var l=S.createEvent("Event");l.initEvent(r,!0,!0),l.pageX=i.pageX,l.pageY=i.pageY,i.target.dispatchEvent(l)},t.click=function(i){var r=i.target,l;/(SELECT|INPUT|TEXTAREA)/i.test(r.tagName)||(l=S.createEvent(h.MouseEvent?"MouseEvents":"Event"),l.initEvent("click",!0,!0),l.view=i.view||h,l.detail=1,l.screenX=r.screenX||0,l.screenY=r.screenY||0,l.clientX=r.clientX||0,l.clientY=r.clientY||0,l.ctrlKey=!!i.ctrlKey,l.altKey=!!i.altKey,l.shiftKey=!!i.shiftKey,l.metaKey=!!i.metaKey,l.button=0,l.relatedTarget=null,l._constructed=!0,r.dispatchEvent(l))},t.getTouchAction=function(i,r){var l="none";return i==="vertical"?l="pan-y":i==="horizontal"&&(l="pan-x"),r&&l!="none"&&(l+=" pinch-zoom"),l},t.getRect=function(i){if(i instanceof SVGElement){var r=i.getBoundingClientRect();return{top:r.top,left:r.left,width:r.width,height:r.height}}else return{top:i.offsetTop,left:i.offsetLeft,width:i.offsetWidth,height:i.offsetHeight}},t}();function b(t,e){this.wrapper=typeof t=="string"?S.querySelector(t):t,this.scroller=this.wrapper.children[0],this.scrollerStyle=this.scroller.style,this.options={resizeScrollbars:!0,mouseWheelSpeed:20,snapThreshold:.334,disablePointer:!a.hasPointer,disableTouch:a.hasPointer||!a.hasTouch,disableMouse:a.hasPointer||a.hasTouch,startX:0,startY:0,scrollY:!0,directionLockThreshold:5,momentum:!0,bounce:!0,bounceTime:600,bounceEasing:"",preventDefault:!0,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT)$/},HWCompositing:!0,useTransition:!0,useTransform:!0,bindToWrapper:typeof h.onmousedown>"u"};for(var s in e)this.options[s]=e[s];this.translateZ=this.options.HWCompositing&&a.hasPerspective?" translateZ(0)":"",this.options.useTransition=a.hasTransition&&this.options.useTransition,this.options.useTransform=a.hasTransform&&this.options.useTransform,this.options.eventPassthrough=this.options.eventPassthrough===!0?"vertical":this.options.eventPassthrough,this.options.preventDefault=!this.options.eventPassthrough&&this.options.preventDefault,this.options.scrollY=this.options.eventPassthrough=="vertical"?!1:this.options.scrollY,this.options.scrollX=this.options.eventPassthrough=="horizontal"?!1:this.options.scrollX,this.options.freeScroll=this.options.freeScroll&&!this.options.eventPassthrough,this.options.directionLockThreshold=this.options.eventPassthrough?0:this.options.directionLockThreshold,this.options.bounceEasing=typeof this.options.bounceEasing=="string"?a.ease[this.options.bounceEasing]||a.ease.circular:this.options.bounceEasing,this.options.resizePolling=this.options.resizePolling===void 0?60:this.options.resizePolling,this.options.tap===!0&&(this.options.tap="tap"),!this.options.useTransition&&!this.options.useTransform&&(/relative|absolute/i.test(this.scrollerStyle.position)||(this.scrollerStyle.position="relative")),this.options.shrinkScrollbars=="scale"&&(this.options.useTransition=!1),this.options.invertWheelDirection=this.options.invertWheelDirection?-1:1,this.options.probeType==3&&(this.options.useTransition=!1),this.x=0,this.y=0,this.directionX=0,this.directionY=0,this._events={},this._init(),this.refresh(),this.scrollTo(this.options.startX,this.options.startY),this.enable()}b.prototype={version:"5.2.0-snapshot",_init:function(){this._initEvents(),(this.options.scrollbars||this.options.indicators)&&this._initIndicators(),this.options.mouseWheel&&this._initWheel(),this.options.snap&&this._initSnap(),this.options.keyBindings&&this._initKeys()},destroy:function(){this._initEvents(!0),clearTimeout(this.resizeTimeout),this.resizeTimeout=null,this._execEvent("destroy")},_transitionEnd:function(t){t.target!=this.scroller||!this.isInTransition||(this._transitionTime(),this.resetPosition(this.options.bounceTime)||(this.isInTransition=!1,this._execEvent("scrollEnd")))},_start:function(t){if(a.eventType[t.type]!=1){var e;if(t.which?e=t.button:e=t.button<2?0:t.button==4?1:2,e!==0)return}if(!(!this.enabled||this.initiated&&a.eventType[t.type]!==this.initiated)&&!(this.options.ignoreEventException&&this.options.ignoreEventException(t))){this.options.preventDefault&&!a.isBadAndroid&&!a.preventDefaultException(t.target,this.options.preventDefaultException)&&t.preventDefault();var s=t.touches?t.touches[0]:t,n;this.initiated=a.eventType[t.type],this.moved=!1,this.distX=0,this.distY=0,this.directionX=0,this.directionY=0,this.directionLocked=0,this.startTime=a.getTime(),this.options.useTransition&&this.isInTransition?(this._transitionTime(),this.isInTransition=!1,n=this.getComputedPosition(),this._translate(o.round(n.x),o.round(n.y)),this._execEvent("scrollEnd")):!this.options.useTransition&&this.isAnimating&&(this.isAnimating=!1,this._execEvent("scrollEnd")),this.startX=this.x,this.startY=this.y,this.absStartX=this.x,this.absStartY=this.y,this.pointX=s.pageX,this.pointY=s.pageY,this._execEvent("beforeScrollStart")}},_move:function(t){if(!(!this.enabled||a.eventType[t.type]!==this.initiated)&&!(this.options.ignoreEventException&&this.options.ignoreEventException(t))){this.options.preventDefault&&t.preventDefault();var e=t.touches?t.touches[0]:t,s=e.pageX-this.pointX,n=e.pageY-this.pointY,c=a.getTime(),i,r,l,d;if(this.pointX=e.pageX,this.pointY=e.pageY,this.distX+=s,this.distY+=n,l=o.abs(this.distX),d=o.abs(this.distY),!(c-this.endTime>300&&l<10&&d<10)){if(!this.directionLocked&&!this.options.freeScroll&&(l>d+this.options.directionLockThreshold?this.directionLocked="h":d>=l+this.options.directionLockThreshold?this.directionLocked="v":this.directionLocked="n"),this.directionLocked=="h"){if(this.options.eventPassthrough=="vertical")t.preventDefault();else if(this.options.eventPassthrough=="horizontal"){this.initiated=!1;return}n=0}else if(this.directionLocked=="v"){if(this.options.eventPassthrough=="horizontal")t.preventDefault();else if(this.options.eventPassthrough=="vertical"){this.initiated=!1;return}s=0}s=this.hasHorizontalScroll?s:0,n=this.hasVerticalScroll?n:0,i=this.x+s,r=this.y+n,(i>0||i<this.maxScrollX)&&(i=this.options.bounce?this.x+s/3:i>0?0:this.maxScrollX),(r>0||r<this.maxScrollY)&&(r=this.options.bounce?this.y+n/3:r>0?0:this.maxScrollY),this.directionX=s>0?-1:s<0?1:0,this.directionY=n>0?-1:n<0?1:0,this.moved||this._execEvent("scrollStart"),this.moved=!0,this._translate(i,r),c-this.startTime>300&&(this.startTime=c,this.startX=this.x,this.startY=this.y,this.options.probeType==1&&this._execEvent("scroll")),this.options.probeType>1&&this._execEvent("scroll")}}},_end:function(t){if(!(!this.enabled||a.eventType[t.type]!==this.initiated)){this.options.preventDefault&&!a.preventDefaultException(t.target,this.options.preventDefaultException)&&t.preventDefault();var e=t.changedTouches?t.changedTouches[0]:t,s,n,c=a.getTime()-this.startTime,i=o.round(this.x),r=o.round(this.y),l=o.abs(i-this.startX),d=o.abs(r-this.startY),P=0,_="";if(this.isInTransition=0,this.initiated=0,this.endTime=a.getTime(),!this.resetPosition(this.options.bounceTime)){if(this.scrollTo(i,r),!this.moved){this.options.tap&&a.tap(t,this.options.tap),this.options.click&&a.click(t),this._execEvent("scrollCancel");return}if(this._events.flick&&c<200&&l<100&&d<100){this._execEvent("flick");return}if(this.options.momentum&&c<300&&(s=this.hasHorizontalScroll?a.momentum(this.x,this.startX,c,this.maxScrollX,this.options.bounce?this.wrapperWidth:0,this.options.deceleration):{destination:i,duration:0},n=this.hasVerticalScroll?a.momentum(this.y,this.startY,c,this.maxScrollY,this.options.bounce?this.wrapperHeight:0,this.options.deceleration):{destination:r,duration:0},i=s.destination,r=n.destination,P=o.max(s.duration,n.duration),this.isInTransition=1),this.options.snap){var v=this._nearestSnap(i,r);this.currentPage=v,P=this.options.snapSpeed||o.max(o.max(o.min(o.abs(i-v.x),1e3),o.min(o.abs(r-v.y),1e3)),300),i=v.x,r=v.y,this.directionX=0,this.directionY=0,_=this.options.bounceEasing}if(i!=this.x||r!=this.y){(i>0||i<this.maxScrollX||r>0||r<this.maxScrollY)&&(_=a.ease.quadratic),this.scrollTo(i,r,P,_);return}this._execEvent("scrollEnd")}}},_resize:function(){var t=this;clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(function(){t.refresh()},this.options.resizePolling)},resetPosition:function(t){var e=this.x,s=this.y;return t=t||0,!this.hasHorizontalScroll||this.x>0?e=0:this.x<this.maxScrollX&&(e=this.maxScrollX),!this.hasVerticalScroll||this.y>0?s=0:this.y<this.maxScrollY&&(s=this.maxScrollY),e==this.x&&s==this.y?!1:(this.scrollTo(e,s,t,this.options.bounceEasing),!0)},disable:function(){this.enabled=!1},enable:function(){this.enabled=!0},refresh:function(){a.getRect(this.wrapper),this.wrapperWidth=this.wrapper.clientWidth,this.wrapperHeight=this.wrapper.clientHeight;var t=a.getRect(this.scroller);this.scrollerWidth=t.width,this.scrollerHeight=t.height,this.maxScrollX=this.wrapperWidth-this.scrollerWidth,this.maxScrollY=this.wrapperHeight-this.scrollerHeight,this.hasHorizontalScroll=this.options.scrollX&&this.maxScrollX<0,this.hasVerticalScroll=this.options.scrollY&&this.maxScrollY<0,this.hasHorizontalScroll||(this.maxScrollX=0,this.scrollerWidth=this.wrapperWidth),this.hasVerticalScroll||(this.maxScrollY=0,this.scrollerHeight=this.wrapperHeight),this.endTime=0,this.directionX=0,this.directionY=0,a.hasPointer&&!this.options.disablePointer&&(this.wrapper.style[a.style.touchAction]=a.getTouchAction(this.options.eventPassthrough,!0),this.wrapper.style[a.style.touchAction]||(this.wrapper.style[a.style.touchAction]=a.getTouchAction(this.options.eventPassthrough,!1))),this.wrapperOffset=a.offset(this.wrapper),this._execEvent("refresh"),this.resetPosition()},on:function(t,e){this._events[t]||(this._events[t]=[]),this._events[t].push(e)},off:function(t,e){if(this._events[t]){var s=this._events[t].indexOf(e);s>-1&&this._events[t].splice(s,1)}},_execEvent:function(t){if(this._events[t]){var e=0,s=this._events[t].length;if(s)for(;e<s;e++)this._events[t][e].apply(this,[].slice.call(arguments,1))}},scrollBy:function(t,e,s,n){t=this.x+t,e=this.y+e,s=s||0,this.scrollTo(t,e,s,n)},scrollTo:function(t,e,s,n){n=n||a.ease.circular,this.isInTransition=this.options.useTransition&&s>0;var c=this.options.useTransition&&n.style;!s||c?(c&&(this._transitionTimingFunction(n.style),this._transitionTime(s)),this._translate(t,e)):this._animate(t,e,s,n.fn)},scrollToElement:function(t,e,s,n,c){if(t=t.nodeType?t:this.scroller.querySelector(t),!!t){var i=a.offset(t);i.left-=this.wrapperOffset.left,i.top-=this.wrapperOffset.top;var r=a.getRect(t),l=a.getRect(this.wrapper);s===!0&&(s=o.round(r.width/2-l.width/2)),n===!0&&(n=o.round(r.height/2-l.height/2)),i.left-=s||0,i.top-=n||0,i.left=i.left>0?0:i.left<this.maxScrollX?this.maxScrollX:i.left,i.top=i.top>0?0:i.top<this.maxScrollY?this.maxScrollY:i.top,e=e==null||e==="auto"?o.max(o.abs(this.x-i.left),o.abs(this.y-i.top)):e,this.scrollTo(i.left,i.top,e,c)}},_transitionTime:function(t){if(this.options.useTransition){t=t||0;var e=a.style.transitionDuration;if(e){if(this.scrollerStyle[e]=t+"ms",!t&&a.isBadAndroid){this.scrollerStyle[e]="0.0001ms";var s=this;y(function(){s.scrollerStyle[e]==="0.0001ms"&&(s.scrollerStyle[e]="0s")})}if(this.indicators)for(var n=this.indicators.length;n--;)this.indicators[n].transitionTime(t)}}},_transitionTimingFunction:function(t){if(this.scrollerStyle[a.style.transitionTimingFunction]=t,this.indicators)for(var e=this.indicators.length;e--;)this.indicators[e].transitionTimingFunction(t)},_translate:function(t,e){if(this.options.useTransform?this.scrollerStyle[a.style.transform]="translate("+t+"px,"+e+"px)"+this.translateZ:(t=o.round(t),e=o.round(e),this.scrollerStyle.left=t+"px",this.scrollerStyle.top=e+"px"),this.x=t,this.y=e,this.indicators)for(var s=this.indicators.length;s--;)this.indicators[s].updatePosition()},_initEvents:function(t){var e=t?a.removeEvent:a.addEvent,s=this.options.bindToWrapper?this.wrapper:h;e(h,"orientationchange",this),e(h,"resize",this),this.options.click&&e(this.wrapper,"click",this,!0),this.options.disableMouse||(e(this.wrapper,"mousedown",this),e(s,"mousemove",this),e(s,"mousecancel",this),e(s,"mouseup",this)),a.hasPointer&&!this.options.disablePointer&&(e(this.wrapper,a.prefixPointerEvent("pointerdown"),this),e(s,a.prefixPointerEvent("pointermove"),this),e(s,a.prefixPointerEvent("pointercancel"),this),e(s,a.prefixPointerEvent("pointerup"),this)),a.hasTouch&&!this.options.disableTouch&&(e(this.wrapper,"touchstart",this),e(s,"touchmove",this),e(s,"touchcancel",this),e(s,"touchend",this)),e(this.scroller,"transitionend",this),e(this.scroller,"webkitTransitionEnd",this),e(this.scroller,"oTransitionEnd",this),e(this.scroller,"MSTransitionEnd",this)},getComputedPosition:function(){var t=h.getComputedStyle(this.scroller,null),e,s;return this.options.useTransform?(t=t[a.style.transform].split(")")[0].split(", "),e=+(t[12]||t[4]),s=+(t[13]||t[5])):(e=+t.left.replace(/[^-\d.]/g,""),s=+t.top.replace(/[^-\d.]/g,"")),{x:e,y:s}},_initIndicators:function(){var t=this.options.interactiveScrollbars,e=typeof this.options.scrollbars!="string",s=[],n,c=this;this.indicators=[],this.options.scrollbars&&(this.options.scrollY&&(n={el:g("v",t,this.options.scrollbars),interactive:t,defaultScrollbars:!0,customStyle:e,resize:this.options.resizeScrollbars,shrink:this.options.shrinkScrollbars,fade:this.options.fadeScrollbars,listenX:!1},this.wrapper.appendChild(n.el),s.push(n)),this.options.scrollX&&(n={el:g("h",t,this.options.scrollbars),interactive:t,defaultScrollbars:!0,customStyle:e,resize:this.options.resizeScrollbars,shrink:this.options.shrinkScrollbars,fade:this.options.fadeScrollbars,listenY:!1},this.wrapper.appendChild(n.el),s.push(n))),this.options.indicators&&(s=s.concat(this.options.indicators));for(var i=s.length;i--;)this.indicators.push(new B(this,s[i]));function r(l){if(c.indicators)for(var d=c.indicators.length;d--;)l.call(c.indicators[d])}this.options.fadeScrollbars&&(this.on("scrollEnd",function(){r(function(){this.fade()})}),this.on("scrollCancel",function(){r(function(){this.fade()})}),this.on("scrollStart",function(){r(function(){this.fade(1)})}),this.on("beforeScrollStart",function(){r(function(){this.fade(1,!0)})})),this.on("refresh",function(){r(function(){this.refresh()})}),this.on("destroy",function(){r(function(){this.destroy()}),delete this.indicators})},_initWheel:function(){a.addEvent(this.wrapper,"wheel",this),a.addEvent(this.wrapper,"mousewheel",this),a.addEvent(this.wrapper,"DOMMouseScroll",this),this.on("destroy",function(){clearTimeout(this.wheelTimeout),this.wheelTimeout=null,a.removeEvent(this.wrapper,"wheel",this),a.removeEvent(this.wrapper,"mousewheel",this),a.removeEvent(this.wrapper,"DOMMouseScroll",this)})},_wheel:function(t){if(this.enabled){if(t.metaKey||t.ctrlKey||t.buttons===4)return!0;t.preventDefault();var e,s,n,c,i=this;if(this.wheelTimeout===void 0&&i._execEvent("scrollStart"),clearTimeout(this.wheelTimeout),this.wheelTimeout=setTimeout(function(){i.options.snap||i._execEvent("scrollEnd"),i.wheelTimeout=void 0},400),"deltaX"in t)t.deltaMode===1?(e=-t.deltaX*this.options.mouseWheelSpeed,s=-t.deltaY*this.options.mouseWheelSpeed):(e=-t.deltaX,s=-t.deltaY);else if("wheelDeltaX"in t)e=t.wheelDeltaX/120*this.options.mouseWheelSpeed,s=t.wheelDeltaY/120*this.options.mouseWheelSpeed;else if("wheelDelta"in t)e=s=t.wheelDelta/120*this.options.mouseWheelSpeed;else if("detail"in t)e=s=-t.detail/3*this.options.mouseWheelSpeed;else return;if(e*=this.options.invertWheelDirection,s*=this.options.invertWheelDirection,this.options.snap){n=this.currentPage.pageX,c=this.currentPage.pageY,e>0?n--:e<0&&n++,s>0?c--:s<0&&c++,this.goToPage(n,c);return}n=this.x+o.round(this.hasHorizontalScroll?e:0),c=this.y+o.round(this.hasVerticalScroll?s:0),this.directionX=e>0?-1:e<0?1:0,this.directionY=s>0?-1:s<0?1:0,n>0?n=0:n<this.maxScrollX&&(n=this.maxScrollX),c>0?c=0:c<this.maxScrollY&&(c=this.maxScrollY),this.scrollTo(n,c,0),this.options.probeType>1&&this._execEvent("scroll")}},_initSnap:function(){this.currentPage={},typeof this.options.snap=="string"&&(this.options.snap=this.scroller.querySelectorAll(this.options.snap)),this.on("refresh",function(){var t=0,e,s=0,n,c,i,r=0,l,d=this.options.snapStepX||this.wrapperWidth,P=this.options.snapStepY||this.wrapperHeight,_,v;if(this.pages=[],!(!this.wrapperWidth||!this.wrapperHeight||!this.scrollerWidth||!this.scrollerHeight)){if(this.options.snap===!0)for(c=o.round(d/2),i=o.round(P/2);r>-this.scrollerWidth;){for(this.pages[t]=[],e=0,l=0;l>-this.scrollerHeight;)this.pages[t][e]={x:o.max(r,this.maxScrollX),y:o.max(l,this.maxScrollY),width:d,height:P,cx:r-c,cy:l-i},l-=P,e++;r-=d,t++}else for(_=this.options.snap,e=_.length,n=-1;t<e;t++)v=a.getRect(_[t]),(t===0||v.left<=a.getRect(_[t-1]).left)&&(s=0,n++),this.pages[s]||(this.pages[s]=[]),r=o.max(-v.left,this.maxScrollX),l=o.max(-v.top,this.maxScrollY),c=r-o.round(v.width/2),i=l-o.round(v.height/2),this.pages[s][n]={x:r,y:l,width:v.width,height:v.height,cx:c,cy:i},r>this.maxScrollX&&s++;this.goToPage(this.currentPage.pageX||0,this.currentPage.pageY||0,0),this.options.snapThreshold%1===0?(this.snapThresholdX=this.options.snapThreshold,this.snapThresholdY=this.options.snapThreshold):(this.snapThresholdX=o.round(this.pages[this.currentPage.pageX][this.currentPage.pageY].width*this.options.snapThreshold),this.snapThresholdY=o.round(this.pages[this.currentPage.pageX][this.currentPage.pageY].height*this.options.snapThreshold))}}),this.on("flick",function(){var t=this.options.snapSpeed||o.max(o.max(o.min(o.abs(this.x-this.startX),1e3),o.min(o.abs(this.y-this.startY),1e3)),300);this.goToPage(this.currentPage.pageX+this.directionX,this.currentPage.pageY+this.directionY,t)})},_nearestSnap:function(t,e){if(!this.pages.length)return{x:0,y:0,pageX:0,pageY:0};var s=0,n=this.pages.length,c=0;if(o.abs(t-this.absStartX)<this.snapThresholdX&&o.abs(e-this.absStartY)<this.snapThresholdY)return this.currentPage;for(t>0?t=0:t<this.maxScrollX&&(t=this.maxScrollX),e>0?e=0:e<this.maxScrollY&&(e=this.maxScrollY);s<n;s++)if(t>=this.pages[s][0].cx){t=this.pages[s][0].x;break}for(n=this.pages[s].length;c<n;c++)if(e>=this.pages[0][c].cy){e=this.pages[0][c].y;break}return s==this.currentPage.pageX&&(s+=this.directionX,s<0?s=0:s>=this.pages.length&&(s=this.pages.length-1),t=this.pages[s][0].x),c==this.currentPage.pageY&&(c+=this.directionY,c<0?c=0:c>=this.pages[0].length&&(c=this.pages[0].length-1),e=this.pages[0][c].y),{x:t,y:e,pageX:s,pageY:c}},goToPage:function(t,e,s,n){n=n||this.options.bounceEasing,t>=this.pages.length?t=this.pages.length-1:t<0&&(t=0),e>=this.pages[t].length?e=this.pages[t].length-1:e<0&&(e=0);var c=this.pages[t][e].x,i=this.pages[t][e].y;s=s===void 0?this.options.snapSpeed||o.max(o.max(o.min(o.abs(c-this.x),1e3),o.min(o.abs(i-this.y),1e3)),300):s,this.currentPage={x:c,y:i,pageX:t,pageY:e},this.scrollTo(c,i,s,n)},next:function(t,e){var s=this.currentPage.pageX,n=this.currentPage.pageY;s++,s>=this.pages.length&&this.hasVerticalScroll&&(s=0,n++),this.goToPage(s,n,t,e)},prev:function(t,e){var s=this.currentPage.pageX,n=this.currentPage.pageY;s--,s<0&&this.hasVerticalScroll&&(s=0,n--),this.goToPage(s,n,t,e)},_initKeys:function(t){var e={pageUp:33,pageDown:34,end:35,home:36,left:37,up:38,right:39,down:40},s;if(typeof this.options.keyBindings=="object")for(s in this.options.keyBindings)typeof this.options.keyBindings[s]=="string"&&(this.options.keyBindings[s]=this.options.keyBindings[s].toUpperCase().charCodeAt(0));else this.options.keyBindings={};for(s in e)this.options.keyBindings[s]=this.options.keyBindings[s]||e[s];a.addEvent(h,"keydown",this),this.on("destroy",function(){a.removeEvent(h,"keydown",this)})},_key:function(t){if(this.enabled){var e=this.options.snap,s=e?this.currentPage.pageX:this.x,n=e?this.currentPage.pageY:this.y,c=a.getTime(),i=this.keyTime||0,r=.25,l;switch(this.options.useTransition&&this.isInTransition&&(l=this.getComputedPosition(),this._translate(o.round(l.x),o.round(l.y)),this.isInTransition=!1),this.keyAcceleration=c-i<200?o.min(this.keyAcceleration+r,50):0,t.keyCode){case this.options.keyBindings.pageUp:this.hasHorizontalScroll&&!this.hasVerticalScroll?s+=e?1:this.wrapperWidth:n+=e?1:this.wrapperHeight;break;case this.options.keyBindings.pageDown:this.hasHorizontalScroll&&!this.hasVerticalScroll?s-=e?1:this.wrapperWidth:n-=e?1:this.wrapperHeight;break;case this.options.keyBindings.end:s=e?this.pages.length-1:this.maxScrollX,n=e?this.pages[0].length-1:this.maxScrollY;break;case this.options.keyBindings.home:s=0,n=0;break;case this.options.keyBindings.left:s+=e?-1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.up:n+=e?1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.right:s-=e?-1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.down:n-=e?1:5+this.keyAcceleration>>0;break;default:return}if(e){this.goToPage(s,n);return}s>0?(s=0,this.keyAcceleration=0):s<this.maxScrollX&&(s=this.maxScrollX,this.keyAcceleration=0),n>0?(n=0,this.keyAcceleration=0):n<this.maxScrollY&&(n=this.maxScrollY,this.keyAcceleration=0),this.scrollTo(s,n,0),this.keyTime=c}},_animate:function(t,e,s,n){var c=this,i=this.x,r=this.y,l=a.getTime(),d=l+s;function P(){var _=a.getTime(),v,L,p;if(_>=d){c.isAnimating=!1,c._translate(t,e),c.resetPosition(c.options.bounceTime)||c._execEvent("scrollEnd");return}_=(_-l)/s,p=n(_),v=(t-i)*p+i,L=(e-r)*p+r,c._translate(v,L),c.isAnimating&&y(P),c.options.probeType==3&&c._execEvent("scroll")}this.isAnimating=!0,P()},handleEvent:function(t){switch(t.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(t);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(t);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(t);break;case"orientationchange":case"resize":this._resize();break;case"transitionend":case"webkitTransitionEnd":case"oTransitionEnd":case"MSTransitionEnd":this._transitionEnd(t);break;case"wheel":case"DOMMouseScroll":case"mousewheel":this._wheel(t);break;case"keydown":this._key(t);break;case"click":this.enabled&&!t._constructed&&(t.preventDefault(),t.stopPropagation());break}}};function g(t,e,s){var n=S.createElement("div"),c=S.createElement("div");return s===!0&&(n.style.cssText="position:absolute;z-index:9999",c.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:absolute;background:rgba(0,0,0,0.5);border:1px solid rgba(255,255,255,0.9);border-radius:3px"),c.className="iScrollIndicator",t=="h"?(s===!0&&(n.style.cssText+=";height:7px;left:2px;right:2px;bottom:0",c.style.height="100%"),n.className="iScrollHorizontalScrollbar"):(s===!0&&(n.style.cssText+=";width:7px;bottom:2px;top:2px;right:1px",c.style.width="100%"),n.className="iScrollVerticalScrollbar"),n.style.cssText+=";overflow:hidden",e||(n.style.pointerEvents="none"),n.appendChild(c),n}function B(t,e){this.wrapper=typeof e.el=="string"?S.querySelector(e.el):e.el,this.wrapperStyle=this.wrapper.style,this.indicator=this.wrapper.children[0],this.indicatorStyle=this.indicator.style,this.scroller=t,this.options={listenX:!0,listenY:!0,interactive:!1,resize:!0,defaultScrollbars:!1,shrink:!1,fade:!1,speedRatioX:0,speedRatioY:0};for(var s in e)this.options[s]=e[s];if(this.sizeRatioX=1,this.sizeRatioY=1,this.maxPosX=0,this.maxPosY=0,this.options.interactive&&(this.options.disableTouch||(a.addEvent(this.indicator,"touchstart",this),a.addEvent(h,"touchend",this)),this.options.disablePointer||(a.addEvent(this.indicator,a.prefixPointerEvent("pointerdown"),this),a.addEvent(h,a.prefixPointerEvent("pointerup"),this)),this.options.disableMouse||(a.addEvent(this.indicator,"mousedown",this),a.addEvent(h,"mouseup",this))),this.options.fade){this.wrapperStyle[a.style.transform]=this.scroller.translateZ;var n=a.style.transitionDuration;if(!n)return;this.wrapperStyle[n]=a.isBadAndroid?"0.0001ms":"0ms";var c=this;a.isBadAndroid&&y(function(){c.wrapperStyle[n]==="0.0001ms"&&(c.wrapperStyle[n]="0s")}),this.wrapperStyle.opacity="0"}}B.prototype={handleEvent:function(t){switch(t.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(t);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(t);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(t);break}},destroy:function(){this.options.fadeScrollbars&&(clearTimeout(this.fadeTimeout),this.fadeTimeout=null),this.options.interactive&&(a.removeEvent(this.indicator,"touchstart",this),a.removeEvent(this.indicator,a.prefixPointerEvent("pointerdown"),this),a.removeEvent(this.indicator,"mousedown",this),a.removeEvent(h,"touchmove",this),a.removeEvent(h,a.prefixPointerEvent("pointermove"),this),a.removeEvent(h,"mousemove",this),a.removeEvent(h,"touchend",this),a.removeEvent(h,a.prefixPointerEvent("pointerup"),this),a.removeEvent(h,"mouseup",this)),this.options.defaultScrollbars&&this.wrapper.parentNode&&this.wrapper.parentNode.removeChild(this.wrapper)},_start:function(t){var e=t.touches?t.touches[0]:t;t.preventDefault(),t.stopPropagation(),this.transitionTime(),this.initiated=!0,this.moved=!1,this.lastPointX=e.pageX,this.lastPointY=e.pageY,this.startTime=a.getTime(),this.options.disableTouch||a.addEvent(h,"touchmove",this),this.options.disablePointer||a.addEvent(h,a.prefixPointerEvent("pointermove"),this),this.options.disableMouse||a.addEvent(h,"mousemove",this),this.scroller._execEvent("beforeScrollStart")},_move:function(t){var e=t.touches?t.touches[0]:t,s,n,c,i,r=a.getTime();this.moved||this.scroller._execEvent("scrollStart"),this.moved=!0,s=e.pageX-this.lastPointX,this.lastPointX=e.pageX,n=e.pageY-this.lastPointY,this.lastPointY=e.pageY,c=this.x+s,i=this.y+n,this._pos(c,i),this.scroller.options.probeType==1&&r-this.startTime>300?(this.startTime=r,this.scroller._execEvent("scroll")):this.scroller.options.probeType>1&&this.scroller._execEvent("scroll"),t.preventDefault(),t.stopPropagation()},_end:function(t){if(this.initiated){if(this.initiated=!1,t.preventDefault(),t.stopPropagation(),a.removeEvent(h,"touchmove",this),a.removeEvent(h,a.prefixPointerEvent("pointermove"),this),a.removeEvent(h,"mousemove",this),this.scroller.options.snap){var e=this.scroller._nearestSnap(this.scroller.x,this.scroller.y),s=this.options.snapSpeed||o.max(o.max(o.min(o.abs(this.scroller.x-e.x),1e3),o.min(o.abs(this.scroller.y-e.y),1e3)),300);(this.scroller.x!=e.x||this.scroller.y!=e.y)&&(this.scroller.directionX=0,this.scroller.directionY=0,this.scroller.currentPage=e,this.scroller.scrollTo(e.x,e.y,s,this.scroller.options.bounceEasing))}this.moved&&this.scroller._execEvent("scrollEnd")}},transitionTime:function(t){t=t||0;var e=a.style.transitionDuration;if(e&&(this.indicatorStyle[e]=t+"ms",!t&&a.isBadAndroid)){this.indicatorStyle[e]="0.0001ms";var s=this;y(function(){s.indicatorStyle[e]==="0.0001ms"&&(s.indicatorStyle[e]="0s")})}},transitionTimingFunction:function(t){this.indicatorStyle[a.style.transitionTimingFunction]=t},refresh:function(){this.transitionTime(),this.options.listenX&&!this.options.listenY?this.indicatorStyle.display=this.scroller.hasHorizontalScroll?"block":"none":this.options.listenY&&!this.options.listenX?this.indicatorStyle.display=this.scroller.hasVerticalScroll?"block":"none":this.indicatorStyle.display=this.scroller.hasHorizontalScroll||this.scroller.hasVerticalScroll?"block":"none",this.scroller.hasHorizontalScroll&&this.scroller.hasVerticalScroll?(a.addClass(this.wrapper,"iScrollBothScrollbars"),a.removeClass(this.wrapper,"iScrollLoneScrollbar"),this.options.defaultScrollbars&&this.options.customStyle&&(this.options.listenX?this.wrapper.style.right="8px":this.wrapper.style.bottom="8px")):(a.removeClass(this.wrapper,"iScrollBothScrollbars"),a.addClass(this.wrapper,"iScrollLoneScrollbar"),this.options.defaultScrollbars&&this.options.customStyle&&(this.options.listenX?this.wrapper.style.right="2px":this.wrapper.style.bottom="2px")),a.getRect(this.wrapper),this.options.listenX&&(this.wrapperWidth=this.wrapper.clientWidth,this.options.resize?(this.indicatorWidth=o.max(o.round(this.wrapperWidth*this.wrapperWidth/(this.scroller.scrollerWidth||this.wrapperWidth||1)),8),this.indicatorStyle.width=this.indicatorWidth+"px"):this.indicatorWidth=this.indicator.clientWidth,this.maxPosX=this.wrapperWidth-this.indicatorWidth,this.options.shrink=="clip"?(this.minBoundaryX=-this.indicatorWidth+8,this.maxBoundaryX=this.wrapperWidth-8):(this.minBoundaryX=0,this.maxBoundaryX=this.maxPosX),this.sizeRatioX=this.options.speedRatioX||this.scroller.maxScrollX&&this.maxPosX/this.scroller.maxScrollX),this.options.listenY&&(this.wrapperHeight=this.wrapper.clientHeight,this.options.resize?(this.indicatorHeight=o.max(o.round(this.wrapperHeight*this.wrapperHeight/(this.scroller.scrollerHeight||this.wrapperHeight||1)),8),this.indicatorStyle.height=this.indicatorHeight+"px"):this.indicatorHeight=this.indicator.clientHeight,this.maxPosY=this.wrapperHeight-this.indicatorHeight,this.options.shrink=="clip"?(this.minBoundaryY=-this.indicatorHeight+8,this.maxBoundaryY=this.wrapperHeight-8):(this.minBoundaryY=0,this.maxBoundaryY=this.maxPosY),this.maxPosY=this.wrapperHeight-this.indicatorHeight,this.sizeRatioY=this.options.speedRatioY||this.scroller.maxScrollY&&this.maxPosY/this.scroller.maxScrollY),this.updatePosition()},updatePosition:function(){var t=this.options.listenX&&o.round(this.sizeRatioX*this.scroller.x)||0,e=this.options.listenY&&o.round(this.sizeRatioY*this.scroller.y)||0;this.options.ignoreBoundaries||(t<this.minBoundaryX?(this.options.shrink=="scale"&&(this.width=o.max(this.indicatorWidth+t,8),this.indicatorStyle.width=this.width+"px"),t=this.minBoundaryX):t>this.maxBoundaryX?this.options.shrink=="scale"?(this.width=o.max(this.indicatorWidth-(t-this.maxPosX),8),this.indicatorStyle.width=this.width+"px",t=this.maxPosX+this.indicatorWidth-this.width):t=this.maxBoundaryX:this.options.shrink=="scale"&&this.width!=this.indicatorWidth&&(this.width=this.indicatorWidth,this.indicatorStyle.width=this.width+"px"),e<this.minBoundaryY?(this.options.shrink=="scale"&&(this.height=o.max(this.indicatorHeight+e*3,8),this.indicatorStyle.height=this.height+"px"),e=this.minBoundaryY):e>this.maxBoundaryY?this.options.shrink=="scale"?(this.height=o.max(this.indicatorHeight-(e-this.maxPosY)*3,8),this.indicatorStyle.height=this.height+"px",e=this.maxPosY+this.indicatorHeight-this.height):e=this.maxBoundaryY:this.options.shrink=="scale"&&this.height!=this.indicatorHeight&&(this.height=this.indicatorHeight,this.indicatorStyle.height=this.height+"px")),this.x=t,this.y=e,this.scroller.options.useTransform?this.indicatorStyle[a.style.transform]="translate("+t+"px,"+e+"px)"+this.scroller.translateZ:(this.indicatorStyle.left=t+"px",this.indicatorStyle.top=e+"px")},_pos:function(t,e){t<0?t=0:t>this.maxPosX&&(t=this.maxPosX),e<0?e=0:e>this.maxPosY&&(e=this.maxPosY),t=this.options.listenX?o.round(t/this.sizeRatioX):this.scroller.x,e=this.options.listenY?o.round(e/this.sizeRatioY):this.scroller.y,this.scroller.scrollTo(t,e)},fade:function(t,e){if(!(e&&!this.visible)){clearTimeout(this.fadeTimeout),this.fadeTimeout=null;var s=t?250:500,n=t?0:300;t=t?"1":"0",this.wrapperStyle[a.style.transitionDuration]=s+"ms",this.fadeTimeout=setTimeout((function(c){this.wrapperStyle.opacity=c,this.visible=+c}).bind(this,t),n)}}},b.utils=a,h.IScroll=b})(window,document,Math)},28501:(h,S,o)=>{"use strict";var y=o(70768),a=o(68818),b=o(55769),g=o(39096);const B={"sk-bouncedelay":"ZdD58bXGNcNoQjiPeXLu"},t={"sk-bouncedelay":"ZBrU7Yes6CevGkk6hArA"};o(17730),o(28217),o(43435);var e=o(95549),s=o(38502),n=o(79458),c=o(68418),i=o(18381),r=o(21838),l=o(9338),d=o(29767),P=o(24437),_=o(28731),v=o(72214);class L extends s.PureComponent{constructor(f){super(f),(0,e.A)(this,"handleGetViewMode",()=>{var C;const{store:R,isHtmlZip:z}=this.props,G=new URLSearchParams(location.search).get("view_mode");return z?R==null||(C=R.getState())==null||(C=C.editor)==null||(C=C.state)==null||(C=C.mode)==null?void 0:C.previewViewMode:G});const{store:M,projectBasic:T,projectMeta:k,isHtmlZip:A}=f,X=new URLSearchParams(location.search);let j="",u="";const w=X.get("view_mode");if(["read_only","inspect"].includes(w)){var W;j=X.get("screen")||(M==null||(W=M.getState())==null||(W=W.current.currentScreen)==null?void 0:W.cid)||"",u=X.get("canvasId")||""}this.state={visibleIsEmpty:!1};try{const C=(0,P.rV)(j);M.dispatch({type:"entry:root-project:readOnly:activate",payload:{projectBasic:T,projectMeta:k,screenCid:C,canvasCid:u,isHtmlZip:A}})}catch(C){this.state={visibleIsEmpty:!0}}}componentDidMount(){this.props.onComponentDidMount(),window.addEventListener("message",_.a,!1),this.handleGetViewMode()==="inspect"&&MB.__store__.dispatch({type:"entry:handle-tool:toggle:inspect-mode",payload:{mode:!0}})}componentWillUnmount(){window.removeEventListener("message",_.a,!1)}render(){const{store:f}=this.props;return this.state.visibleIsEmpty?(0,d.V)():(0,v.jsx)(i.Kq,{store:f,children:(0,v.jsx)(l.A,{children:(0,v.jsx)(r.A,{})})})}}var p=o(79388);const m=async D=>{let{projectMetaCid:f}=D;await(0,p.F2)({userId:void 0,flpakKey:f,onTransferError:p.z0,onReadOnlyError:p.sH,isDummyCmt:!0,__fetchFlpakAsync:a.BD},void 0)};var E=o(22215);const Y=function(D,f,M){const T=f.getGuidesData()||"[]",k=new Set(JSON.parse(T)),A=new Set,X=new Map,j={checkGuideHasShown:u=>ENV.IS_ON_PREMISES||k.has(u),checkGuideToBeShown:u=>A.has(u),getState:()=>({commonGuidesHasShown:Array.from(k),commonGuidesToBeShown:Array.from(A),mountGuidesMap:X}),tryToShowGuide:u=>{var w;if(k.has(u)||!((w=MB)!=null&&(w=w.user)!=null&&w.id)||ENV.IS_ON_PREMISES)return;const W=document.getElementById("MD_"+u);if(W){W.style.display="block";return}A.add(u),(0,E.o)(u,D,M)},markGuideAsRead:u=>{k.add(u),f.setGuidesData(JSON.stringify(Array.from(k)))},batchMarkGuideAsRead:u=>{u.forEach(w=>k.add(w)),f.setGuidesData(JSON.stringify(Array.from(k)))},closeAndMarkGuideAsRead:u=>{j.checkGuideHasShown(u)||(j.closeGuide(u),j.markGuideAsRead(u))},closeGuide:u=>{const w=document.getElementById("MD_"+u);w&&w.parentNode&&w.parentNode.removeChild(w)},closeAllGuide:()=>{const u=document.getElementById("GUIDES");u&&u.parentNode&&u.parentNode.removeChild(u)},hideAllGuides:()=>{const u=document.getElementById("GUIDES");u&&Array.from(u.children).forEach(w=>w.style.visibility="hidden")},restoreAllGuides:()=>{const u=document.getElementById("GUIDES");u&&Array.from(u.children).forEach(w=>w.style.visibility="visible")},removeGuide:u=>{k.delete(u),f.setGuidesData(JSON.stringify(Array.from(k)))},tryToContinueMountGuide:(u,w)=>{const W=X.get(u);W&&typeof(W==null?void 0:W.handleContinue)=="function"&&W.handleContinue(w)},tryToResizeMountGuide:u=>{const w=X.get(u);w&&typeof(w==null?void 0:w.handleResize)=="function"&&w.handleResize()},tryToCloseMountGuide:u=>{const w=X.get(u);w&&typeof(w==null?void 0:w.handleClose)=="function"&&w.handleClose()},registerMountGuideCallback:(u,w)=>{const W=X.get(u)||{};X.set(u,{...W,...w})},removeMountGuideCallback:(u,w)=>{if(!w){X.delete(u);return}const W=X.get(u)||{};X.set(u,{...W,[w]:void 0})}};return j},K=D=>{let{store:f,commonGuidesData:M,isDesign:T,isPreview:k}=D;MB.commonGuideHelper=Y(f,{getGuidesData:()=>M,setGuidesData:updateUserCommonGuidesData},{isDesign:T,isPreview:k})},q=D=>{let{store:f,isDesign:M,isPreview:T}=D;const k={guide:{dashboard_guides:"",workspace_guides:"",square_guides:null,workspace_guides_version:"",default_team_cid:"",root_project_team_cid:"",workspace_guides_2203:"",workspace_guides_2203_version:"",aboard_guides:""},guide_project_template_cid:"",guide_project_template_2203_cid:"",guide_flow_template_cid:"",guide_design_template_cid:"",guide_prototype_template_cid:""},{guide:{workspace_guides:A}}=k;MB.commonGuideHelper=Y(f,{getGuidesData:()=>A,setGuidesData:()=>{}},{isDesign:M,isPreview:T})};var O=o(46407),I=o(10629),F=o(44327),tt=o(55244),zt=o(15399),ct=o(47163),ht=o(98033);const pt=D=>{let{initialData:f,designOptions:M,global:T,onUpdateSharing:k}=D;const{user:A,org:X,team:j,preference:u,orgList:w,user_fcg:W,org_fcg:C,space:R,canEditByCurrentUser:z}=f,{currentUser:H,currentOrg:G}=(0,ht.n)({user:A,org:X});(0,tt.kZ)((0,ct.w)()),Object.assign(MB,{user:H||{},orgList:w,currentOrg:G,canEditByCurrentUser:z,global:{...T,popupHelper:(0,I.L)(),experienceLoginModalHelper:O.F}}),MB.action("current:update:state",{currentOrg:G,currentTeam:j,currentUser:H,orgList:w,user_fcg:W,org_fcg:C,currentSpace:R,personalOrg:X}),MB.action("update:design:options",{designOptions:M}),A!=null&&A.id&&MB.action("entry:init:preference",{preference:u}),k&&k()},Ht=async D=>{let{designStore:f}=D,M="[]";try{const{guide:T}=await requestUserCommonGuidesData();M=T==null?void 0:T.workspace_guides}catch(T){console.error("\u83B7\u53D6 guide_data \u5931\u8D25")}await initCommonGuide({store:f,commonGuidesData:M,isDesign:!0,isPreview:!1})},Ft=async D=>{let{type:f,projectBasicCid:M}=D;switch(f){case DesignType.Design:return requestProjectFullDataFlat(M);case DesignType.Experience:return requestExperienceFullDataFlat(M);default:throw new Error(f+" not found in known design types for "+M)}};var dt=o(72671),ut=o(97109),ft=o(62556),mt=o(16810),U=o(56202);const gt=D=>{const{isEmbedV1:f=!1}=D||{};return{isDesktop:!((0,U.Fr)()||f),isInApp:!1,isMultiLink:!1,isMobile:(0,U.Fr)(),isIOSClient:(0,U.Hv)(),isStandAlone:(0,U.Gd)(),isEmbedV1:f,isEmbedV2:!1,isHTMLZip:!1,isUIChina:(0,U.II)(),isForum:(0,U.uf)(),isElectron:(0,U.b8)(),isSquare:!1,isFeishu:(0,U.Fl)(),isWechat:(0,U.vq)(),...D}},vt=D=>{const{isMobile:f,isEmbedV2:M,isSquare:T}=D;return f?ft.A:M||T?ut.A:mt.A};var yt=o(88334);const wt=D=>{let{designOptions:f,previewOptions:M,initialData:T,WMListConfig:k,onComponentDidMount:A,sharing:X}=D,j=!1;const u=(0,c.s)(MB);pt({initialData:T,designOptions:f,global:{designOptions:f,previewOptions:M}}),(0,yt.LN)(k);const{isIOSClient:w,isStandAlone:W}=M;(W||w)&&(document.documentElement.style.height="100vh");const{runnerController:C,messageBucket:R,webpackInterface:z,PreviewApp:H}=(0,dt._)(vt(M));return Object.assign(MB,{runnerController:C,messageBucket:R,webpackInterface:z,global:{...MB.global||{},popupHelper:(0,I.L)(),designOptions:f,previewOptions:M}}),MB.webpackInterface.store.dispatch({type:"preview:update:state",payload:{previewOptions:M}}),{requestData:async G=>{const{user:Z,org:Q,preference:J,project_basic:it,project_meta:lt}=T;return q({store:u,isDesign:!0,isPreview:!1}),Object.assign(MB,{tag:it==null?void 0:it.version}),MB.action("update:design:options",{designOptions:MB.global.designOptions}),await m({projectMetaCid:lt.cid}),MB.webpackInterface.store.dispatch({type:"preview:set:current-projectShare",payload:{projectShare:X}}),MB.webpackInterface.store.dispatch({type:"entry:init:preview:preference",payload:{preference:J||{}}}),MB.action("entry:handle-tool:preview:view_mode",{previewViewMode:G}),Object.assign(MB,{user:Z||{},canEditByCurrentUser:!1,org:Q||void 0}),MB.webpackInterface.store.dispatch({type:"entry:state:init"}),j=!0,{projectAlike:it,projectMeta:lt,projectShare:X}},renderDesign:G=>{let{projectAlike:Z,projectMeta:Q}=G;return j?(0,v.jsx)(L,{store:u,projectBasic:Z,projectMeta:Q,onComponentDidMount:A,isHtmlZip:!0}):null},renderPreview:G=>{let{projectAlike:Z,projectMeta:Q,projectShare:J}=G;return j?((0,d.X)(J),(0,v.jsx)(H,{store:z.store,previewMode:"preview",projectAlike:Z,projectMeta:Q,projectShare:J})):null}}};var ot=o(67787);const Gt=ot.Ay.div.withConfig({displayName:"styles__StyledExampleApp",componentId:"sc-1r36j4k-0"})(["&{position:absolute;top:0;left:0;bottom:0;right:0;transition:all 0.2s ease-in-out;.loading-container{position:absolute;top:50%;left:50%;.mb-loading{width:80px;height:80px;margin-top:-40px;margin-left:-40px;}.chris-icon{position:absolute;left:68%;top:-8%;}}}.mb-design-page{height:100%;&,#mb-toolbar{transition:all 0.2s ease-in-out;}&.is-collapse{opacity:0;z-index:0;#mb-toolbar{transform:translateY(-100%);}.mb-right-panel{transform:translateX(100%);}}}.mb-preview-page{position:absolute;top:0;left:0;width:100%;bottom:0;font-size:12px;transition:all 0.2s ease-in-out;&.is-collapse{opacity:0;z-index:0;}}"]),St=ot.Ay.div.withConfig({displayName:"styles__StyledROExampleApp",componentId:"sc-1r36j4k-1"})(["&{position:absolute;top:0;left:0;bottom:0;right:0;transition:all 0.2s ease-in-out;.loading-container{position:absolute;top:50%;left:50%;.mb-loading{width:80px;height:80px;margin-top:-40px;margin-left:-40px;}.chris-icon{position:absolute;left:68%;top:-8%;}}}.mb-design-page{height:100%;position:relative;z-index:1;&.is-collapse{opacity:0;z-index:0;}}.mb-preview-page{position:absolute;top:0;left:0;width:100%;bottom:0;font-size:12px;&.is-collapse{opacity:0;z-index:0;}}"]);var nt=o(7198),bt=o(82738),Mt=o(53732),rt=o.n(Mt);class xt extends s.PureComponent{render(){const{isCollapse:f,renderReadOnly:M,projectAlike:T,projectMeta:k}=this.props;return(0,v.jsx)("div",{ref:A=>this.$element=A,className:rt()("mb-design-page",{"is-collapse":f}),children:M({projectAlike:T,projectMeta:k})})}}class Pt extends s.PureComponent{constructor(f){super(f),(0,e.A)(this,"setElementRef",M=>this.$element=M),MB.f.inPreview=!f.isCollapse}componentDidMount(){this.$element.addEventListener("wheel",this.handleWheel,{passive:!1})}componentWillUnmount(){MB.f.inPreview=!1,this.$element.removeEventListener("wheel",this.handleWheel)}handleWheel(f){(f.ctrlKey||f.metaKey)&&f.preventDefault()}render(){const{isCollapse:f,renderPreview:M,projectAlike:T,projectMeta:k,projectShare:A}=this.props;return(0,v.jsx)("div",{ref:this.setElementRef,className:rt()("mb-preview-page",{"is-collapse":f}),children:M({projectAlike:T,projectMeta:k,projectShare:A})})}}var Et=o(81070),Tt=o(11227),_t=o(15297),et=o(98267),kt=o(30986),Bt=o(86769),Ct=o(3862),V=o(78915),at=o(22835);const At=(D,f)=>{const{current:{currentProject:M},ui:{toolbar:{isShowSticky:T},leftLayout:{leftPanel:{show:k,width:A},directory:{screen:{currentPage:X},bottom:{height:j},screen:{screenCollapseKeySet:u}}},rightLayout:{rightPanel:{show:w,resizedWidth:W}},fixedLayout:{modals:{previewCanvasModal:C,previewModal:R}}},editor:{state:{mode:{fullScreenMode:z}}}}=MB.__store__.getState(),H=()=>{MB.__store__.dispatch({type:"entry:handle-tool:preview:view_mode",payload:{previewViewMode:"device"}});const{pageKey:G,canvasKey:Z}=Bt.A.validateAndTryCorrectPGAndCVKey(X.key,D,null,M.category===Ct.t_);MB.webpackInterface.store.dispatch({type:"entry:runner:activate:canvas",payload:{pageKey:G,canvasKey:Z,saveHistory:!1,forceInit:!0,keepScale:!1}}),MB.webpackInterface.store.dispatch({type:"preview:set:leftPaneWidth",payload:{leftPaneWidth:A!=null?A:250}}),MB.webpackInterface.store.dispatch({type:"preview:update:state",payload:{hasChangedScaleManually:!1}}),MB.webpackInterface.store.dispatch({type:"preview:panel:set:height",payload:{panelHeight:j}}),MB.webpackInterface.store.dispatch({type:"preview:set:rightPaneWidth",payload:{rightPaneWidth:W,projectCid:M.cid}}),z&&MB.webpackInterface.store.dispatch({type:"reducer:preview-toolbar:fullscreen",payload:{isFullScreenMode:!0}}),MB.webpackInterface.store.dispatch({type:"ST:update",payload:{STMode:T}}),MB.webpackInterface.store.dispatch({type:"entry:preview-setting:toggle:isMinimized",payload:{isMinimized:!k,isMemorize:!z}}),MB.webpackInterface.store.dispatch({type:"entry:preview-setting:toggle:rightPane",payload:{isShowRightPane:w,isMemorize:!z}}),MB.webpackInterface.store.dispatch({type:"preview:set:collapsedRBPageKeySet",payload:{collapsedRBPageKeySet:u}}),MB.webpackInterface.store.dispatch({type:"entry:preview:toggle:isHiddenToolBar",payload:{value:!1}}),MB.action("modal:reset:state"),MB.action("entry:exit:edit:basket"),MB.__store__.dispatch({type:"entry:comment:disabled",payload:{noWigglingCommentCid:!0}}),MB.__store__.dispatch({type:"versions:update",payload:{isVersionManagementPaneShow:!1}}),MB.__store__.dispatch(V.GO.toggleInspectMode(!1)),MB.__store__.dispatch(V.GO.toggleHandMode(!1)),MB.__store__.dispatch(V.GO.select([])),MB.__store__.dispatch({type:"elbow:update",payload:{isInElbowMode:!1}}),MB.__store__.dispatch({type:"entry:ST:STMode:disable"}),MB.__store__.dispatch({type:"ST:clear"})};C!=null&&C.isOpen||R!=null&&R.isOpen?(MB.action("modal:reset:preview:modal:state"),setTimeout(()=>{H(),f==null||f()},600)):(H(),f==null||f())},Dt=(D,f,M)=>{const{container:{current:{rootProject:T},common:{leftPaneWidth:k,panelHeight:A,rightPaneWidth:X,collapsedRBPageKeySet:j},runner:{activePageKey:u},previewSetting:{isFullScreenMode:w,isMinimized:W,isShowRightPane:C}},ST:{STMode:R}}=MB.webpackInterface.store.getState(),{ui:{leftLayout:{directory:{screen:{currentPage:z}}}},editor:{state:{mode:{isHtmlZipPreview:H}}}}=MB.__store__.getState();M==="inspect"?(MB.__store__.dispatch({type:"entry:comment:disabled"}),MB.__store__.dispatch(V.GO.toggleInspectMode(!1)),MB.__store__.dispatch(V.GO.toggleHandMode(!1)),MB.__store__.dispatch(V.GO.select([]))):(z.key!==u&&MB.__store__.dispatch({type:"entry:root-project:readOnly:activate",payload:{projectBasic:D,projectMeta:f,screenCid:u,isHtmlZip:H}}),MB.__store__.dispatch({type:"screen:add:collapseKeySet",payload:{refs:j,projectCid:T.cid}}),w&&MB.__store__.dispatch(V.GO.toggleFullScreenMode(!0)),MB.webpackInterface.store.dispatch({type:"entry:runner:cleanup:audio"}),MB.webpackInterface.store.dispatch({type:"runner:reset:state:except:history"}),MB.webpackInterface.store.dispatch({type:"container:comment:reset:state"}),MB.webpackInterface.store.dispatch({type:"preview:previewPanel:navindex",payload:{previewPanelNavIndex:0}}),MB.__store__.dispatch({type:"left-panel:set:width",payload:{width:k,projectCid:T.cid}}),MB.__store__.dispatch({type:"directory-panel:bottom:set:height",payload:{height:A}}),MB.__store__.dispatch({type:"right-side-panel:update:resize:width",payload:{width:X,projectCid:T.cid}}),MB.__store__.dispatch({type:"right-side-panel:collapse:panel",payload:{show:C,isMemorize:!w}}),MB.__store__.dispatch({type:"left-panel:collapse:panel:show",payload:{show:!W,isMemorize:!w}}),MB.__store__.dispatch({type:"entry:observe:dom:size:update-viewport-rect"}),MB.__store__.dispatch({type:"entry:isShowSticky:toggle",payload:{isShowSticky:R}}),requestAnimationFrame(()=>MB.__store__.dispatch({type:"entry:update:viewport:rect"})),MB.webpackInterface.store.dispatch({type:"entry:comment:disabled"}),(0,at.RF)(u))},Xt=(D,f,M)=>{if(M!=="read_only"){const{container:{current:{rootProject:T},common:{leftPaneWidth:k,panelHeight:A,rightPaneWidth:X,collapsedRBPageKeySet:j},runner:{activePageKey:u},previewSetting:{isFullScreenMode:w,isMinimized:W,isShowRightPane:C}},ST:{STMode:R}}=MB.webpackInterface.store.getState(),{ui:{leftLayout:{directory:{screen:{currentPage:z}}}},editor:{state:{mode:{isHtmlZipPreview:H}}}}=MB.__store__.getState();z.key!==u&&MB.__store__.dispatch({type:"entry:root-project:readOnly:activate",payload:{projectBasic:D,projectMeta:f,screenCid:u,isHtmlZip:H}}),MB.__store__.dispatch({type:"screen:add:collapseKeySet",payload:{refs:j,projectCid:T.cid}}),w&&MB.__store__.dispatch(V.GO.toggleFullScreenMode(!0)),MB.webpackInterface.store.dispatch({type:"entry:runner:cleanup:audio"}),MB.webpackInterface.store.dispatch({type:"runner:reset:state:except:history"}),MB.webpackInterface.store.dispatch({type:"container:comment:reset:state"}),MB.webpackInterface.store.dispatch({type:"preview:previewPanel:navindex",payload:{previewPanelNavIndex:0}}),MB.__store__.dispatch({type:"left-panel:set:width",payload:{width:k,projectCid:T.cid}}),MB.__store__.dispatch({type:"directory-panel:bottom:set:height",payload:{height:A}}),MB.__store__.dispatch({type:"right-side-panel:update:resize:width",payload:{width:X,projectCid:T.cid}}),MB.__store__.dispatch({type:"right-side-panel:collapse:panel",payload:{show:C,isMemorize:!w}}),MB.__store__.dispatch({type:"left-panel:collapse:panel:show",payload:{show:!W,isMemorize:!w}}),MB.__store__.dispatch({type:"entry:observe:dom:size:update-viewport-rect"}),MB.__store__.dispatch({type:"entry:isShowSticky:toggle",payload:{isShowSticky:R}}),requestAnimationFrame(()=>{MB.__store__.dispatch({type:"entry:update:viewport:rect"})}),(0,at.RF)(u)}MB.__store__.dispatch({type:"entry:handle-tool:toggle:inspect-mode",payload:{mode:!0}})};class Wt extends s.PureComponent{constructor(f){super(f),(0,e.A)(this,"handleWheelChanged",C=>{if(C.deltaX===0)return;const R=document.getElementById("workspace"),z=R.scrollLeft+R.offsetWidth===R.scrollWidth&&C.deltaX>0,H=R.scrollLeft===0&&C.deltaX<0;(z||H)&&C.preventDefault()}),(0,e.A)(this,"handleDesignDidMount",()=>{MB.reduxEntry.setEntryMap(Et.T),this.setState({isLoading:!1})}),(0,e.A)(this,"keyboardManager",new nt.Rr);const{viewMode:M,designOptions:T,previewOptions:k,initialData:A,WMListConfig:X,sharing:j}=f;MB.switchToPreview=C=>{this.state.viewMode!=="device"&&((0,et.N8)("device"),At(C),this.setState({viewMode:"device"}))},MB.switchToReadOnly=()=>{if(this.state.viewMode==="read_only")return;const{projectAlike:C,projectMeta:R,viewMode:z}=this.state;(0,et.N8)("read_only"),Dt(C,R,z),this.setState({viewMode:"read_only"})},MB.switchToInspect=()=>{if(this.state.viewMode==="inspect")return;const{projectAlike:C,projectMeta:R,viewMode:z}=this.state;(0,et.N8)("inspect"),Xt(C,R,z),this.setState({viewMode:"inspect"})},this.state={isLoading:!0,isDataLoaded:!1,viewMode:M,projectAlike:null,projectMeta:null,projectShare:void 0};const{requestData:u,renderDesign:w,renderPreview:W}=wt({initialData:A,WMListConfig:X,designOptions:T,previewOptions:k,onComponentDidMount:this.handleDesignDidMount,sharing:j});this.requestData=u,this.renderDesign=w,this.renderPreview=W}componentDidMount(){Promise.all([this.requestData(this.state.viewMode)]).then(f=>{let[{projectAlike:M,projectMeta:T,projectShare:k}]=f;this.setState({projectAlike:M,projectMeta:T,projectShare:k},()=>{this.setState({isDataLoaded:!0})});const A=document.getElementById("workspace");A&&A.addEventListener("wheel",this.handleWheelChanged)})}componentWillUnmount(){const f=document.getElementById("workspace");f&&f.removeEventListener("wheel",this.handleWheelChanged,{capture:!0})}render(){const{isLoading:f,isDataLoaded:M,viewMode:T,projectAlike:k,projectMeta:A,projectShare:X}=this.state,{renderDesign:j,renderPreview:u}=this;return(0,v.jsx)(kt.G.Provider,{value:T,children:(0,v.jsx)(nt.M.Provider,{value:this.keyboardManager,children:(0,v.jsx)(bt.$,{children:(0,v.jsxs)(St,{className:"example-app",children:[M&&(0,v.jsx)(xt,{isCollapse:!["read_only","inspect"].includes(T),projectAlike:k,projectMeta:A,renderReadOnly:j}),M&&(0,v.jsx)(Pt,{isCollapse:T!=="device",projectAlike:k,projectMeta:A,projectShare:X,renderPreview:u}),f&&(0,v.jsxs)("div",{className:"loading-container",children:[(0,v.jsx)(n.Tw,{className:"mb-loading"}),(0,_t.X)()&&(0,v.jsx)(Tt.n,{className:"chris-icon"})]})]})})})})}}var Yt=o(16615),Lt=o(78401),jt=o(54844),Ot=o(28894);o.p="./mb-proto2/";const Rt=document.getElementById("workspace"),It=async()=>{var D,f;const M=gt({isHTMLZip:!0});(0,U.Fr)()&&((D=document.querySelector("body"))==null||D.classList.add("mobile"));let T=await(0,a.Rz)();const{projectUpper:k,projectMeta:A}=T;T={...T,projectName:k.name,project_basic:k,project_meta:A};const{sharing:X}=await(0,jt.J_)({project:k}),j=await(0,a.CS)(),{generateLang:u}=await Ot.qu.loadLLG();await u(),document.title=""+(0,Lt.qk)(k.name||k.title),!ENV.IS_ON_PREMISES&&(0,Yt.I2)();const w=(f=new URLSearchParams(location.search))==null?void 0:f.get("view_mode"),W=window.HZv5_PREVIEW_MODE||"",C=w||W,R=["device","read_only","inspect"].includes(C)?C:"read_only",z={isDesign:!0,isExperience:!1,isLoggedExperience:!1};(0,y.createRoot)(Rt).render((0,v.jsx)(Wt,{initialData:T,WMListConfig:j,viewMode:R,designOptions:z,previewOptions:M,sharing:X}))};document.addEventListener("DOMContentLoaded",async()=>{await It()})},42459:(h,S,o)=>{"use strict";o.d(S,{$G:()=>P,Op:()=>l,Uj:()=>i,XB:()=>a,be:()=>g,fQ:()=>c,nK:()=>b,u5:()=>B});const y=(p,m,E)=>{if(p[m]===E)return p;const Y=[...p];return Y[m]=E,Y},a=(p,m)=>m>=0&&m<=p.length-1?[...p.slice(0,m),...p.slice(m+1)]:p,b=(p,m,E)=>[...p.slice(0,m),E,...p.slice(m)],g=(p,m,E)=>E===m?p:E<m?[...p.slice(0,E),...p.slice(E+1,m+1),p[E],...p.slice(m+1)]:[...p.slice(0,m),p[E],...p.slice(m,E),...p.slice(E+1)],B=(p,m)=>[...p,m],t=(p,m)=>[m,...p],e=p=>{if(p.length===0)return p;const m=[...p];return m.pop(),m},s=p=>{if(p.length===0)return p;const m=[...p];return m.shift(),m},n=(p,m)=>m&&m.length?[...p,...m]:p,c=(p,m)=>p.includes(m)?p:[...p,m],i=(p,m)=>{const E=p.indexOf(m);return~E?[...p.slice(0,E),...p.slice(E+1)]:p},r=(p,m,E)=>{const Y=p.indexOf(E);return~Y?g(p,m,Y):p},l=(p,m)=>{const E=p.findIndex(m);return~E?[...p.slice(0,E),...p.slice(E+1)]:p},d=(p,m,E)=>{const Y=p.findIndex(m);return~Y?g(p,E,Y):p},P=(p,m,E)=>{const Y=p.findIndex(m);if(!~Y||p[Y]===E)return p;const K=[...p];return K[Y]=E,K},_=(p,m,E)=>{const Y=p.findIndex(m);if(!~Y)return[...p,E];if(p[Y]===E)return p;const K=[...p];return K[Y]=E,K},v=(p,m,E)=>p.find(m)===void 0?[...p,E]:p,L=(p,m)=>{const E=[];for(let Y=0,K=p.length;Y<K;Y+=m)E.push(p.slice(Y,Y+m));return E}},52563:(h,S,o)=>{"use strict";o.d(S,{FE:()=>t,Pj:()=>g,Vo:()=>e});var y=o(79371),a=o.n(y);const b=function(s,n,c){c===void 0&&(c=1e3);const i=(r,l)=>{(r==null?void 0:r.size)>n[0]&&console.error("[Oversize "+s+"]",r.size),(l==null?void 0:l.size)>n[1]&&console.error("[Oversize "+s+" effecSet]",l.size)};return a()(i,c)},g=s=>{const n=new Map;return{_map:n,addRef:(d,P)=>{n.has(d)?n.get(d).add(P):n.set(d,new Set([P])),s==null||s(n,n.get(d))},delRef:(d,P)=>{const _=n.get(d);_&&(_.delete(P),_.size||n.delete(d))},getRef:d=>n.get(d),hasRef:(d,P)=>{var _;return(_=n.get(d))==null?void 0:_.has(P)}}},B=s=>{const n=s.hotAttr.interactions;if(!(n!=null&&n.length))return null;const c=new Set;return n.forEach(i=>{const r=i.targetResCanvasKey||i.targetBasketKey||i.targetWidgetKey||i.targetOverlayKey;r&&c.add(r)}),c},t=()=>{const s=b("ItaRefMap",[2e3,200]),{_map:n,addRef:c,delRef:i}=g(s);return{update:(r,l)=>{const{key:d}=r,P=l?B(l):null,_=B(r);_&&[..._].forEach(v=>{c(v,d),P==null||P.delete(v)}),P!=null&&P.size&&[...P].forEach(v=>i(v,d))},delete:r=>{const l=r?B(r):null;l!=null&&l.size&&[...l].forEach(d=>i(d,r.key))},getEffectKeys:r=>n.has(r)?Array.from(n.get(r)):[],clear:()=>{s.flush(),n.clear()},_getMap:()=>n}},e=()=>{const s=b("MktCidRefMap",[2e3,2e3]),{_map:n,addRef:c,delRef:i,hasRef:r}=g(s),l=d=>{if(!d)return;const{mktCid:P,mktUnlock:_}=d.hotAttr;return P&&!_?P:void 0};return{update:(d,P)=>{const{key:_}=d,v=l(P),L=l(d);L&&L!==v&&(L&&!sdkStore.findUpHotItemList(_).some(p=>r(L,p.key))&&c(L,_),v&&i(L,_))},delete:d=>{if(d){const P=l(d);P&&i(P,d.key)}},getEffectKeys:d=>n.has(d)?Array.from(n.get(d)):[],clear:()=>{s.flush(),n.clear()},isEmpty:()=>!n.size,_getMap:()=>n}}},56202:(h,S,o)=>{"use strict";o.d(S,{Bd:()=>m,Fl:()=>t,Fr:()=>i,Gd:()=>_,H8:()=>s,Hv:()=>P,II:()=>v,Md:()=>p,VK:()=>q,b8:()=>b,cX:()=>g,gm:()=>n,lg:()=>E,m0:()=>r,nr:()=>c,uF:()=>B,uf:()=>L,un:()=>d,vq:()=>e});var y,a;const b=()=>window.MB_DESKTOP_VERSION||window.isElectron||/Electron/i.test(navigator.userAgent),g=()=>/(Macintosh)/i.test(navigator.userAgent),B=()=>/(Windows)/i.test(navigator.userAgent),t=()=>/(Lark)/i.test(navigator.userAgent),e=()=>/MicroMessenger/i.test(navigator.userAgent),s=()=>/Chrome/i.test(navigator.userAgent),n=()=>/Firefox/i.test(navigator.userAgent),c=()=>/Safari/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent),i=()=>{const O=new URLSearchParams(location.search);return O&&O.get("inapp")==="1"&&(o.g.isInAPP=!0),/(iPod|iPhone|iPad|Android|MobileClient)/.test(navigator.userAgent)||window.isInAPP},r=()=>/Android/i.test(navigator.userAgent),l=()=>/(iPad)/.test(navigator.userAgent),d=()=>/(iPod|iPhone|iPad)/.test(navigator.userAgent),P=()=>!!(window.webkit&&window.webkit.messageHandlers),_=()=>navigator.standalone,v=()=>/\/uichina$/.test(location.pathname),L=()=>/\/forum$/.test(location.pathname),p=()=>/(MobileClient)/.test(navigator.userAgent),m=!!(typeof window<"u"&&window.document&&window.document.createElement),E=/(mac|iphone|ipod|ipad)/i.test(typeof navigator<"u"?((y=navigator)==null?void 0:y.platform)||((a=navigator)==null||(a=a.userAgentData)==null?void 0:a.platform):""),Y=()=>{var O;if(!((O=navigator)!=null&&O.plugins))return!1;for(const I in navigator.plugins)if(navigator.plugins[I]&&navigator.plugins[I].filename==="np-mswmp.dll")return!0;return!1},K=O=>{O=O.toLowerCase();const I=O.includes("qihu"),F=O.includes("360se"),tt=O.includes("360ee");return I&&(F||tt)},q=()=>{let O,I;const F=navigator.userAgent;if(I=void 0,O=F.match(/(opera|chrome|qq|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i)||[],/trident/i.test(O[1]))return I=/\brv[ :]+(\d+)/g.exec(F)||[],{name:"IE",version:parseFloat(I[1]||"")};if(O[1]==="Chrome"){if(K(F)||Y())return{name:"360Browser",version:"unknown"};if(/Edg/i.test(F))return I=F.match(/Edg\/(\d+)/i),{name:"Edge",version:I?parseFloat(I[1]):"unknown"};if(I=F.match(/\b(OPR|Edge)\/(\d+)/),I!==null)return{name:I[1].replace("OPR","Opera"),version:parseFloat(I[2])}}return/QQBrowser/i.test(F)?(I=F.match(/QQBrowser\/(\d+)/i),{name:"QQBrowser",version:I?parseFloat(I[1]):"unknown"}):(O=O[2]?[O[1],O[2]]:[navigator.appName,navigator.appVersion,"-?"],(I=F.match(/version\/(\d+)/i))!==null&&O.splice(1,1,I[1]),{name:O[0],version:parseFloat(O[1])})}},58156:(h,S,o)=>{"use strict";o.d(S,{Z:()=>b,j:()=>g});const y=new Map;let a;const b=B=>{const{key:t,hotAttr:e,sub:s}=B;e.type==="rResBunch"&&e.bunch==="rbPage"&&(a=t),a&&y.set(a,g(a)+1)},g=B=>y.get(B)||0},59128:(h,S,o)=>{"use strict";o.d(S,{sI:()=>a});const y="_cmt",a=n=>n.endsWith(y),b=n=>{if(a(n))throw new Error("bad datKey: "+n);return""+n+y},g=n=>{if(!a(n))throw new Error("bad cmtKey: "+n);return n.slice(0,-y.length)},B="_cur",t=n=>n.endsWith(B),e=n=>{if(t(n))throw new Error("bad datKey: "+n);return""+n+B},s=n=>{if(!t(n))throw new Error("bad curKey: "+n);return n.slice(0,-B.length)}},88334:(h,S,o)=>{"use strict";o.d(S,{LN:()=>c,uT:()=>e});var y=o(85449),a=o(67771),b=o(79371),g=o.n(b);const B=i=>{var r,l;const d={type:"reducer:watermark:update-state",payload:i};(r=MB.__store__)==null||r.dispatch(d),(l=MB.webpackInterface)==null||(l=l.store)==null||l.dispatch(d)},t=function(i,r){r===void 0&&(r=sdkStore);const l=new Set;return r.walkHotItemSubtree2(i,d=>{d.hotAttr.mktCid&&l.add(d.hotAttr.mktCid)}),[...l]},e=g()(async i=>{let r=(0,y.fV)();if(!r)return{mdWMMktList:new Set,mtWMMktList:new Set,noWMMktList:new Set};const{mdWMMktList:l,mtWMMktList:d,noWMMktList:P}=r,_=i.filter(E=>!l.has(E)&&!d.has(E)&&!P.has(E)),v=await(0,a.E5)(_);r=(0,y.fV)();const L=new Set([...r.mdWMMktList].concat(v.mdWMMktList)),p=new Set([...r.mtWMMktList].concat(v.mtWMMktList)),m=new Set([...r.noWMMktList].concat(v.noWMMktList));return B({mdWMMktList:L,mtWMMktList:p,noWMMktList:m}),{mdWMMktList:L,mtWMMktList:p,noWMMktList:m}},1e3),s=async i=>{const r=await splitMktListByCid(i),l=new Set(r.mdWMMktList),d=new Set(r.mtWMMktList),P=new Set(r.noWMMktList);return B({mdWMMktList:l,mtWMMktList:d,noWMMktList:P}),{mdWMMktList:l,mtWMMktList:d,noWMMktList:P}},n=i=>{const r=new Set,l=new Set,d=new Set(i);B({mdWMMktList:r,mtWMMktList:l,noWMMktList:d})},c=i=>{const r=new Set((i==null?void 0:i.md_vip_mkt_list)||[]),l=new Set((i==null?void 0:i.mt_vip_mkt_list)||[]),d=new Set((i==null?void 0:i.no_wm_mkt_list)||[]);B({mdWMMktList:r,mtWMMktList:l,noWMMktList:d})}},94994:h=>{"use strict";h.exports=dompurify}},$={};function x(h){var S=$[h];if(S!==void 0)return S.exports;var o=$[h]={id:h,loaded:!1,exports:{}};return N[h].call(o.exports,o,o.exports,x),o.loaded=!0,o.exports}x.m=N,x.amdO={},(()=>{var h=[];x.O=(S,o,y,a)=>{if(o){a=a||0;for(var b=h.length;b>0&&h[b-1][2]>a;b--)h[b]=h[b-1];h[b]=[o,y,a];return}for(var g=1/0,b=0;b<h.length;b++){for(var[o,y,a]=h[b],B=!0,t=0;t<o.length;t++)(a&!1||g>=a)&&Object.keys(x.O).every(r=>x.O[r](o[t]))?o.splice(t--,1):(B=!1,a<g&&(g=a));if(B){h.splice(b--,1);var e=y();e!==void 0&&(S=e)}}return S}})(),x.n=h=>{var S=h&&h.__esModule?()=>h.default:()=>h;return x.d(S,{a:S}),S},(()=>{var h=Object.getPrototypeOf?o=>Object.getPrototypeOf(o):o=>o.__proto__,S;x.t=function(o,y){if(y&1&&(o=this(o)),y&8||typeof o=="object"&&o&&(y&4&&o.__esModule||y&16&&typeof o.then=="function"))return o;var a=Object.create(null);x.r(a);var b={};S=S||[null,h({}),h([]),h(h)];for(var g=y&2&&o;(typeof g=="object"||typeof g=="function")&&!~S.indexOf(g);g=h(g))Object.getOwnPropertyNames(g).forEach(B=>b[B]=()=>o[B]);return b.default=()=>o,x.d(a,b),a}})(),x.d=(h,S)=>{for(var o in S)x.o(S,o)&&!x.o(h,o)&&Object.defineProperty(h,o,{enumerable:!0,get:S[o]})},x.f={},x.e=h=>Promise.all(Object.keys(x.f).reduce((S,o)=>(x.f[o](h,S),S),[])),x.u=h=>""+{214:"lazy-lib-paper",437:"lazy-lib-fontkit",703:"lazy-lib-rehype",956:"lazy-lib-i18n",976:"lazy-lib-dl"}[h]+"-"+{214:"5a9908939d850d4cae67",437:"94d1920236ee8aff3185",703:"013772de7c49f3c667fa",956:"5041cfa2a12106f1676b",976:"ef4a8eb58075f96842b2"}[h]+".js",x.miniCssF=h=>{},x.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch(h){if(typeof window=="object")return window}}(),x.o=(h,S)=>Object.prototype.hasOwnProperty.call(h,S),(()=>{var h={},S="@mb2024/mb-proto:";x.l=(o,y,a,b)=>{if(h[o]){h[o].push(y);return}var g,B;if(a!==void 0)for(var t=document.getElementsByTagName("script"),e=0;e<t.length;e++){var s=t[e];if(s.getAttribute("src")==o||s.getAttribute("data-webpack")==S+a){g=s;break}}g||(B=!0,g=document.createElement("script"),g.charset="utf-8",g.timeout=120,x.nc&&g.setAttribute("nonce",x.nc),g.setAttribute("data-webpack",S+a),g.src=o),h[o]=[y];var n=(i,r)=>{g.onerror=g.onload=null,clearTimeout(c);var l=h[o];if(delete h[o],g.parentNode&&g.parentNode.removeChild(g),l&&l.forEach(d=>d(r)),i)return i(r)},c=setTimeout(n.bind(null,void 0,{type:"timeout",target:g}),12e4);g.onerror=n.bind(null,g.onerror),g.onload=n.bind(null,g.onload),B&&document.head.appendChild(g)}})(),x.r=h=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(h,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(h,"__esModule",{value:!0})},x.nmd=h=>(h.paths=[],h.children||(h.children=[]),h),x.j=699,x.p="/mb-proto2/",(()=>{var h={699:0};x.f.j=(y,a)=>{var b=x.o(h,y)?h[y]:void 0;if(b!==0)if(b)a.push(b[2]);else{var g=new Promise((s,n)=>b=h[y]=[s,n]);a.push(b[2]=g);var B=x.p+x.u(y),t=new Error,e=s=>{if(x.o(h,y)&&(b=h[y],b!==0&&(h[y]=void 0),b)){var n=s&&(s.type==="load"?"missing":s.type),c=s&&s.target&&s.target.src;t.message="Loading chunk "+y+" failed.\n("+n+": "+c+")",t.name="ChunkLoadError",t.type=n,t.request=c,b[1](t)}};x.l(B,e,"chunk-"+y,y)}},x.O.j=y=>h[y]===0;var S=(y,a)=>{var[b,g,B]=a,t,e,s=0;if(b.some(c=>h[c]!==0)){for(t in g)x.o(g,t)&&(x.m[t]=g[t]);if(B)var n=B(x)}for(y&&y(a);s<b.length;s++)e=b[s],x.o(h,e)&&h[e]&&h[e][0](),h[e]=0;return x.O(n)},o=self.webpackChunk_mb2024_mb_proto=self.webpackChunk_mb2024_mb_proto||[];o.forEach(S.bind(null,0)),o.push=S.bind(null,o.push.bind(o))})(),x.nc=void 0,x.O(void 0,[31,788,908,347],()=>x(97935));var st=x.O(void 0,[31,788,908,347],()=>x(28501));st=x.O(st)})();

//# sourceMappingURL=preview-html-zip-343d9981d3417f6af735.js.map