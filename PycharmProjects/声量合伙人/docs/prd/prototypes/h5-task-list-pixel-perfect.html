<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>任务列表 - 声量合伙人H5</title>
    <link href="https://unpkg.com/vant@4.8.1/lib/index.css" rel="stylesheet">
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vant@4.8.1/lib/vant.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 基于项目实际的CSS变量和样式 */
        :root {
            /* Vant4 主题变量覆盖 - 基于项目实际配置 */
            --van-primary-color: #3366cc;
            --van-button-large-height: 48px;
            --van-button-default-height: 36px;
            --van-cell-font-size: 12px;
            --van-cell-value-font-size: 14px;
            --van-nav-bar-height: 46px;
            --van-nav-bar-background: var(--van-primary-color);
            --van-nav-bar-title-text-color: #fff;
            --van-nav-bar-icon-color: #fff;
            --van-tabs-line-height: 2px;
            --van-tabs-bottom-bar-color: var(--van-primary-color);
            --van-tag-primary-color: var(--van-primary-color);
            --van-card-background: #fff;
            --van-card-padding: 16px;
            --van-card-border-radius: 8px;
            --van-text-color: rgba(0, 0, 0, 0.85);
            --van-text-color-2: rgba(0, 0, 0, 0.65);
            --van-text-color-3: rgba(0, 0, 0, 0.45);
            --van-border-color: #ebedf0;
            --van-background-color: #f5f5f5;
            --van-background-color-light: #fafafa;
        }

        /* 基础样式重置 - 基于项目Tailwindcss配置 */
        * {
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
            background-color: var(--van-background-color);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 页面容器 */
        .page-container {
            min-height: 100vh;
            background-color: var(--van-background-color);
        }

        /* 任务卡片样式 - 基于项目实际卡片设计 */
        .task-card {
            background: var(--van-card-background);
            margin: 8px 16px;
            border-radius: var(--van-card-border-radius);
            padding: var(--van-card-padding);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .task-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        .task-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .task-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--van-text-color);
            line-height: 1.4;
            flex: 1;
            margin-right: 12px;
        }

        .task-reward {
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .task-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            font-size: 12px;
            color: var(--van-text-color-2);
        }

        .task-desc {
            font-size: 14px;
            color: var(--van-text-color-2);
            line-height: 1.4;
            margin-bottom: 12px;
        }

        .task-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .task-source {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: var(--van-text-color-3);
        }

        .task-action {
            background: var(--van-primary-color);
            color: white;
            padding: 6px 16px;
            border-radius: 16px;
            font-size: 14px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .task-action:active {
            background: #2952a3;
            transform: scale(0.95);
        }

        .task-action:disabled {
            background: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);
        }

        /* 推送来源标识 - 新增功能 */
        .source-icon {
            width: 18px;
            height: 18px;
            border-radius: 3px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            font-weight: bold;
        }

        .source-org {
            background: #1890ff;
        }

        .source-crowd {
            background: #52c41a;
        }

        .source-mixed {
            background: #fa8c16;
        }

        /* 状态标签 */
        .status-tag {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
        }

        .status-active {
            background: #f6ffed;
            color: #52c41a;
        }

        .status-warning {
            background: #fff7e6;
            color: #fa8c16;
        }

        .status-completed {
            background: #e6f7ff;
            color: #1890ff;
        }

        .status-expired {
            background: #fff2f0;
            color: #ff4d4f;
        }

        /* 原型标注样式 */
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -4px;
            border: 4px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.05);
            border: 2px dashed #ff4444;
            border-radius: 8px;
            position: relative;
        }

        /* 底部说明区域 */
        .info-panel {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 16px;
            font-size: 12px;
            line-height: 1.4;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .info-panel.show {
            transform: translateY(0);
        }

        .info-panel .close-btn {
            position: absolute;
            top: 8px;
            right: 12px;
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }

        /* 适配安全区域 */
        @supports (padding: max(0px)) {
            .page-container {
                padding-bottom: max(16px, env(safe-area-inset-bottom));
            }
        }
    </style>
</head>
<body>
    <div id="app" class="page-container">
        <!-- 导航栏 -->
        <van-nav-bar 
            title="任务列表" 
            left-text="返回" 
            left-arrow 
            @click-left="goBack"
        />

        <!-- 标签页 -->
        <van-tabs v-model:active="activeTab" @change="onTabChange">
            <van-tab title="全部任务" name="all"></van-tab>
            <van-tab title="进行中" name="active"></van-tab>
            <van-tab title="已完成" name="completed"></van-tab>
            <van-tab title="已过期" name="expired"></van-tab>
        </van-tabs>

        <!-- 任务列表 -->
        <div style="padding-bottom: 16px;">
            <!-- 人群推送任务 -->
            <div class="task-card highlight-new" @click="goToDetail(taskList[0])">
                <div class="annotation" style="top: -25px; left: 16px;">
                    新增：推送来源标识
                </div>
                <div class="task-header">
                    <div class="task-title">VIP客户专享活动推广</div>
                    <div class="task-reward">+50积分</div>
                </div>
                <div class="task-meta">
                    <span><i class="fas fa-clock" style="margin-right: 4px;"></i>2025-02-01 ~ 2025-02-15</span>
                    <span class="status-tag status-active">进行中</span>
                </div>
                <div class="task-desc">
                    针对VIP客户群体的专享活动推广，完成注册和分享即可获得积分奖励
                </div>
                <div class="task-footer">
                    <div class="task-source">
                        <div class="source-icon source-crowd">群</div>
                        <span>人群推送 · VIP客户群体</span>
                    </div>
                    <button class="task-action" @click.stop="participateTask(taskList[0])">立即参与</button>
                </div>
            </div>

            <!-- 组织推送任务 -->
            <div class="task-card" @click="goToDetail(taskList[1])">
                <div class="task-header">
                    <div class="task-title">品牌宣传视频分享</div>
                    <div class="task-reward">+30积分</div>
                </div>
                <div class="task-meta">
                    <span><i class="fas fa-clock" style="margin-right: 4px;"></i>2025-01-25 ~ 2025-02-10</span>
                    <span class="status-tag status-active">进行中</span>
                </div>
                <div class="task-desc">
                    分享品牌宣传视频到朋友圈，并邀请好友点赞评论
                </div>
                <div class="task-footer">
                    <div class="task-source">
                        <div class="source-icon source-org">部</div>
                        <span>组织推送 · 市场部</span>
                    </div>
                    <button class="task-action" @click.stop="participateTask(taskList[1])">立即参与</button>
                </div>
            </div>

            <!-- 混合推送任务 -->
            <div class="task-card highlight-new" @click="goToDetail(taskList[2])">
                <div class="annotation" style="top: -25px; left: 16px;">
                    新增：混合推送标识
                </div>
                <div class="task-header">
                    <div class="task-title">新春活动问卷调研</div>
                    <div class="task-reward">+20积分</div>
                </div>
                <div class="task-meta">
                    <span><i class="fas fa-clock" style="margin-right: 4px;"></i>2025-01-20 ~ 2025-02-05</span>
                    <span class="status-tag status-warning">即将截止</span>
                </div>
                <div class="task-desc">
                    参与新春活动问卷调研，帮助我们了解用户需求
                </div>
                <div class="task-footer">
                    <div class="task-source">
                        <div class="source-icon source-mixed">混</div>
                        <span>混合推送 · 销售部+新用户群</span>
                    </div>
                    <button class="task-action" @click.stop="participateTask(taskList[2])">立即参与</button>
                </div>
            </div>

            <!-- 已完成任务 -->
            <div class="task-card" @click="goToDetail(taskList[3])" style="opacity: 0.8;">
                <div class="task-header">
                    <div class="task-title">产品体验反馈</div>
                    <div class="task-reward">+40积分</div>
                </div>
                <div class="task-meta">
                    <span><i class="fas fa-clock" style="margin-right: 4px;"></i>2025-01-15 ~ 2025-01-30</span>
                    <span class="status-tag status-completed">已完成</span>
                </div>
                <div class="task-desc">
                    体验新产品功能并提交使用反馈
                </div>
                <div class="task-footer">
                    <div class="task-source">
                        <div class="source-icon source-crowd">群</div>
                        <span>人群推送 · VIP客户群体</span>
                    </div>
                    <button class="task-action" disabled>已完成</button>
                </div>
            </div>

            <!-- 已过期任务 -->
            <div class="task-card" @click="goToDetail(taskList[4])" style="opacity: 0.6;">
                <div class="task-header">
                    <div class="task-title">年终总结分享</div>
                    <div class="task-reward">+25积分</div>
                </div>
                <div class="task-meta">
                    <span><i class="fas fa-clock" style="margin-right: 4px;"></i>2024-12-20 ~ 2025-01-10</span>
                    <span class="status-tag status-expired">已过期</span>
                </div>
                <div class="task-desc">
                    分享个人年终总结，展示工作成果
                </div>
                <div class="task-footer">
                    <div class="task-source">
                        <div class="source-icon source-org">部</div>
                        <span>组织推送 · 全公司</span>
                    </div>
                    <button class="task-action" disabled>已过期</button>
                </div>
            </div>
        </div>

        <!-- 功能说明面板 -->
        <div class="info-panel" :class="{ show: showInfoPanel }">
            <button class="close-btn" @click="showInfoPanel = false">×</button>
            <div style="margin-right: 30px;">
                <div style="font-weight: 500; margin-bottom: 8px;">
                    <i class="fas fa-info-circle" style="margin-right: 6px;"></i>功能说明
                </div>
                <div style="margin-bottom: 4px;"><strong>推送来源：</strong>显示任务的推送来源，包括组织推送、人群推送、混合推送</div>
                <div style="margin-bottom: 4px;"><strong>来源标识：</strong>通过不同颜色的图标区分推送类型，便于用户识别</div>
                <div><strong>详细信息：</strong>显示具体的推送部门或人群名称</div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        
        createApp({
            setup() {
                const activeTab = ref('all');
                const showInfoPanel = ref(false);
                
                const taskList = reactive([
                    {
                        id: 1,
                        title: 'VIP客户专享活动推广',
                        reward: 50,
                        status: 'active',
                        sourceType: 'crowd',
                        sourceName: 'VIP客户群体'
                    },
                    {
                        id: 2,
                        title: '品牌宣传视频分享',
                        reward: 30,
                        status: 'active',
                        sourceType: 'org',
                        sourceName: '市场部'
                    },
                    {
                        id: 3,
                        title: '新春活动问卷调研',
                        reward: 20,
                        status: 'warning',
                        sourceType: 'mixed',
                        sourceName: '销售部+新用户群'
                    },
                    {
                        id: 4,
                        title: '产品体验反馈',
                        reward: 40,
                        status: 'completed',
                        sourceType: 'crowd',
                        sourceName: 'VIP客户群体'
                    },
                    {
                        id: 5,
                        title: '年终总结分享',
                        reward: 25,
                        status: 'expired',
                        sourceType: 'org',
                        sourceName: '全公司'
                    }
                ]);
                
                const goBack = () => {
                    window.location.href = './index.html';
                };
                
                const onTabChange = (name) => {
                    console.log('切换标签页:', name);
                };
                
                const goToDetail = (task) => {
                    window.location.href = './h5-task-detail.html';
                };
                
                const participateTask = (task) => {
                    vant.showToast('跳转到任务执行页面');
                };
                
                // 5秒后显示功能说明
                onMounted(() => {
                    setTimeout(() => {
                        showInfoPanel.value = true;
                    }, 2000);
                    
                    // 10秒后自动隐藏
                    setTimeout(() => {
                        showInfoPanel.value = false;
                    }, 10000);
                });
                
                return {
                    activeTab,
                    showInfoPanel,
                    taskList,
                    goBack,
                    onTabChange,
                    goToDetail,
                    participateTask
                };
            }
        }).use(vant).mount('#app');
    </script>
</body>
</html>
