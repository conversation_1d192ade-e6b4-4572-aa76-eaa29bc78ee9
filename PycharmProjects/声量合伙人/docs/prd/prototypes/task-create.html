<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务发布页 - 声量合伙人</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --el-color-primary: #409eff;
            --el-color-success: #67c23a;
            --el-color-warning: #e6a23c;
            --el-border-color: #dcdfe6;
            --el-border-color-light: #e4e7ed;
            --el-text-color-primary: #303133;
            --el-text-color-regular: #606266;
            --el-bg-color: #ffffff;
            --el-bg-color-page: #f2f3f5;
        }
        
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            background-color: var(--el-bg-color-page);
        }
        
        .el-card {
            background: var(--el-bg-color);
            border: 1px solid var(--el-border-color-light);
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
        }
        
        .el-form-item__label {
            font-weight: 700;
            color: var(--el-text-color-regular);
            width: 120px;
            text-align: right;
            padding-right: 12px;
        }
        
        .el-input__inner, .el-textarea__inner {
            width: 100%;
            padding: 0 15px;
            border: 1px solid var(--el-border-color);
            border-radius: 4px;
            color: var(--el-text-color-regular);
            background: var(--el-bg-color);
            height: 32px;
            line-height: 32px;
        }
        
        .el-textarea__inner {
            height: 80px;
            line-height: 1.5;
            padding: 8px 15px;
            resize: vertical;
        }
        
        .el-button {
            padding: 8px 15px;
            border-radius: 4px;
            border: 1px solid var(--el-border-color);
            background: var(--el-bg-color);
            color: var(--el-text-color-regular);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .el-button--primary {
            background: var(--el-color-primary);
            border-color: var(--el-color-primary);
            color: white;
        }
        
        .el-radio {
            margin-right: 20px;
        }
        
        .el-radio__input {
            margin-right: 8px;
        }
        
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border: 5px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.1);
            border: 2px dashed #ff4444;
            border-radius: 4px;
            position: relative;
        }
        
        .highlight-modified {
            background: rgba(54, 162, 235, 0.1);
            border: 2px dashed #36a2eb;
            border-radius: 4px;
            position: relative;
        }
        
        .crowd-selector {
            border: 1px solid var(--el-border-color);
            border-radius: 4px;
            padding: 12px;
            background: #fafafa;
        }
        
        .crowd-item {
            display: flex;
            align-items: center;
            justify-content: between;
            padding: 8px 12px;
            border: 1px solid var(--el-border-color-light);
            border-radius: 4px;
            background: white;
            margin-bottom: 8px;
        }
        
        .crowd-item:last-child {
            margin-bottom: 0;
        }
        
        .target-preview {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            border-radius: 4px;
            padding: 16px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-4">
        <div class="px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="./index.html" class="text-blue-500 hover:text-blue-700 mr-4">
                        <i class="fas fa-arrow-left"></i> 返回导航
                    </a>
                    <h1 class="text-lg font-semibold text-gray-900">发布任务</h1>
                </div>
                <div class="text-sm text-gray-500">管理后台 > 任务管理 > 发布任务</div>
            </div>
        </div>
    </div>

    <div class="p-6 max-w-4xl mx-auto">
        <!-- 任务基础信息 -->
        <div class="el-card mb-6">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">任务基础信息</h3>
            </div>
            <div class="p-6">
                <form class="space-y-6">
                    <div class="flex items-start">
                        <label class="el-form-item__label">任务名称 *</label>
                        <div class="flex-1">
                            <input type="text" class="el-input__inner" placeholder="请输入任务名称" value="VIP客户专享活动推广">
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <label class="el-form-item__label">任务类型 *</label>
                        <div class="flex-1">
                            <select class="el-input__inner">
                                <option value="1">推广任务</option>
                                <option value="2">调研任务</option>
                                <option value="3">活动任务</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <label class="el-form-item__label">任务描述 *</label>
                        <div class="flex-1">
                            <textarea class="el-textarea__inner" placeholder="请输入任务描述" rows="4">针对VIP客户群体的专享活动推广，提升客户满意度和品牌忠诚度</textarea>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <label class="el-form-item__label">任务奖励 *</label>
                        <div class="flex-1 flex items-center gap-4">
                            <input type="number" class="el-input__inner w-32" placeholder="奖励积分" value="50">
                            <span class="text-gray-600">积分</span>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 推送目标设置 -->
        <div class="el-card mb-6 highlight-modified">
            <div class="annotation" style="top: -35px; left: 20px; background: #36a2eb;">
                改造：新增人群推送选项
            </div>
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">推送目标设置</h3>
            </div>
            <div class="p-6">
                <!-- 推送方式选择 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">推送方式 *</label>
                    <div class="space-y-3">
                        <label class="el-radio flex items-center">
                            <input type="radio" name="pushType" value="1" class="el-radio__input">
                            <span class="ml-2">按组织推送</span>
                            <span class="ml-2 text-sm text-gray-500">（原有功能）</span>
                        </label>
                        <label class="el-radio flex items-center">
                            <input type="radio" name="pushType" value="2" checked class="el-radio__input">
                            <span class="ml-2">按人群推送</span>
                            <span class="ml-2 text-sm text-red-600">（新增功能）</span>
                        </label>
                        <label class="el-radio flex items-center">
                            <input type="radio" name="pushType" value="3" class="el-radio__input">
                            <span class="ml-2">组织+人群推送</span>
                            <span class="ml-2 text-sm text-red-600">（混合模式）</span>
                        </label>
                    </div>
                </div>

                <!-- 人群选择器 -->
                <div class="mb-6 highlight-new">
                    <div class="annotation" style="top: -35px; left: 20px;">
                        新增：人群选择器
                    </div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">选择目标人群 *</label>
                    <div class="crowd-selector">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-2">
                                <input type="text" class="el-input__inner w-64" placeholder="搜索人群名称">
                                <button class="el-button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <button class="el-button el-button--primary">
                                <i class="fas fa-plus mr-1"></i>添加人群
                            </button>
                        </div>
                        
                        <!-- 已选择的人群 -->
                        <div class="space-y-2">
                            <div class="crowd-item">
                                <div class="flex items-center flex-1">
                                    <input type="checkbox" checked class="mr-3">
                                    <div class="flex-1">
                                        <div class="font-medium">VIP客户群体</div>
                                        <div class="text-sm text-gray-500">1,180个有效用户 | 匹配率94.4%</div>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">正常</span>
                                    <button class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="crowd-item">
                                <div class="flex items-center flex-1">
                                    <input type="checkbox" checked class="mr-3">
                                    <div class="flex-1">
                                        <div class="font-medium">新用户推广群</div>
                                        <div class="text-sm text-gray-500">2,650个有效用户 | 匹配率94.6%</div>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">正常</span>
                                    <button class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 推送目标预览 -->
                <div class="target-preview highlight-new">
                    <div class="annotation" style="top: -35px; left: 20px;">
                        新增：推送目标统计
                    </div>
                    <h4 class="font-medium text-blue-800 mb-3">
                        <i class="fas fa-users mr-2"></i>推送目标预览
                    </h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="text-xl font-bold text-blue-600">2</div>
                            <div class="text-sm text-blue-600">选中人群</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xl font-bold text-green-600">3,830</div>
                            <div class="text-sm text-green-600">总用户数</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xl font-bold text-orange-600">150</div>
                            <div class="text-sm text-orange-600">重复用户</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xl font-bold text-purple-600">3,680</div>
                            <div class="text-sm text-purple-600">实际推送</div>
                        </div>
                    </div>
                    <div class="mt-4 p-3 bg-white rounded border">
                        <div class="text-sm text-gray-600">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            系统将自动去除重复用户，确保每个用户只接收一次任务推送
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务设置 -->
        <div class="el-card mb-6">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">任务设置</h3>
            </div>
            <div class="p-6">
                <form class="space-y-6">
                    <div class="flex items-start">
                        <label class="el-form-item__label">开始时间 *</label>
                        <div class="flex-1">
                            <input type="datetime-local" class="el-input__inner" value="2025-02-01T09:00">
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <label class="el-form-item__label">结束时间 *</label>
                        <div class="flex-1">
                            <input type="datetime-local" class="el-input__inner" value="2025-02-15T18:00">
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <label class="el-form-item__label">任务链接</label>
                        <div class="flex-1">
                            <input type="url" class="el-input__inner" placeholder="请输入任务链接" value="https://activity.example.com/vip-promotion">
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <label class="el-form-item__label">完成要求</label>
                        <div class="flex-1">
                            <textarea class="el-textarea__inner" placeholder="请输入完成要求" rows="3">1. 访问活动页面并注册
2. 分享活动到朋友圈
3. 邀请至少2位好友参与</textarea>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center justify-between">
            <button class="el-button">
                <i class="fas fa-save mr-2"></i>保存草稿
            </button>
            <div class="flex gap-3">
                <button class="el-button">取消</button>
                <button class="el-button">预览</button>
                <button class="el-button el-button--primary">
                    <i class="fas fa-paper-plane mr-2"></i>发布任务
                </button>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="font-medium text-blue-800 mb-2">
                <i class="fas fa-info-circle mr-2"></i>功能说明
            </h3>
            <div class="text-sm text-blue-700 space-y-1">
                <p><strong>1. 推送方式：</strong>新增人群推送和混合推送模式，支持精准营销</p>
                <p><strong>2. 人群选择：</strong>支持多人群选择，实时显示用户数量和匹配率</p>
                <p><strong>3. 自动去重：</strong>系统自动识别和去除重复用户，避免重复推送</p>
                <p><strong>4. 目标预览：</strong>实时显示推送目标统计，便于确认推送范围</p>
                <p><strong>5. 兼容性：</strong>保持原有组织推送功能完全兼容</p>
            </div>
        </div>

        <!-- 开发备注 -->
        <div class="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h3 class="font-medium text-orange-800 mb-2">
                <i class="fas fa-code mr-2"></i>开发备注
            </h3>
            <div class="text-sm text-orange-700 space-y-1">
                <p><strong>人群选择器：</strong>需要实现搜索、多选、实时统计功能</p>
                <p><strong>用户去重：</strong>后端需要实现用户去重算法</p>
                <p><strong>推送逻辑：</strong>需要改造现有推送逻辑，支持人群推送</p>
                <p><strong>数据统计：</strong>需要记录推送来源，便于后续统计分析</p>
                <p><strong>权限控制：</strong>需要验证用户对选中人群的访问权限</p>
            </div>
        </div>
    </div>
</body>
</html>
