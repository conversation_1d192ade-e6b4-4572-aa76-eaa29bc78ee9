<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务详情 - 声量合伙人H5</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --van-primary-color: #3366cc;
            --van-text-color: rgba(0, 0, 0, 0.85);
            --van-text-color-2: rgba(0, 0, 0, 0.65);
            --van-text-color-3: rgba(0, 0, 0, 0.45);
            --van-border-color: #ebedf0;
            --van-background-color: #f5f5f5;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, <PERSON><PERSON>, <PERSON><PERSON>, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
            background-color: var(--van-background-color);
            margin: 0;
            padding: 0;
        }
        
        .van-nav-bar {
            background: var(--van-primary-color);
            color: white;
            height: 46px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-size: 16px;
            font-weight: 500;
        }
        
        .van-nav-bar__left {
            position: absolute;
            left: 16px;
            color: white;
        }
        
        .content-card {
            background: white;
            margin: 8px 16px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        
        .task-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid var(--van-border-color);
            margin-bottom: 20px;
        }
        
        .task-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--van-text-color);
            margin-bottom: 8px;
            line-height: 1.4;
        }
        
        .task-reward {
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 12px;
        }
        
        .task-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .info-section {
            margin-bottom: 20px;
        }
        
        .info-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--van-text-color);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .info-title i {
            margin-right: 8px;
            color: var(--van-primary-color);
        }
        
        .info-content {
            font-size: 14px;
            color: var(--van-text-color-2);
            line-height: 1.6;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            width: 80px;
            font-size: 14px;
            color: var(--van-text-color-3);
        }
        
        .info-value {
            flex: 1;
            font-size: 14px;
            color: var(--van-text-color);
        }
        
        .source-info {
            background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
            border: 1px solid #b3d8ff;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
        }
        
        .source-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .source-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .source-crowd {
            background: #52c41a;
        }
        
        .source-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--van-primary-color);
        }
        
        .source-desc {
            font-size: 12px;
            color: var(--van-text-color-2);
            line-height: 1.4;
        }
        
        .action-button {
            background: var(--van-primary-color);
            color: white;
            border: none;
            border-radius: 24px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            margin-top: 20px;
            cursor: pointer;
        }
        
        .action-button:disabled {
            background: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);
        }
        
        .progress-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .progress-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--van-text-color);
        }
        
        .progress-value {
            font-size: 14px;
            color: var(--van-primary-color);
            font-weight: 500;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--van-primary-color), #79bbff);
            transition: width 0.3s ease;
        }
        
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -4px;
            border: 4px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.1);
            border: 2px dashed #ff4444;
            border-radius: 8px;
            position: relative;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="van-nav-bar">
        <div class="van-nav-bar__left">
            <a href="./h5-task-list.html" class="text-white">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        任务详情
    </div>

    <!-- 任务头部信息 -->
    <div class="content-card">
        <div class="task-header">
            <div class="task-title">VIP客户专享活动推广</div>
            <div class="task-reward">+50积分</div>
            <div class="task-status status-active">进行中</div>
        </div>

        <!-- 推送来源信息 -->
        <div class="source-info highlight-new">
            <div class="annotation" style="top: -25px; left: 16px;">
                新增：推送来源详情
            </div>
            <div class="source-header">
                <div class="source-icon source-crowd">群</div>
                <div class="source-title">人群推送任务</div>
            </div>
            <div class="source-desc">
                您因为属于"VIP客户群体"而收到此任务。该人群共有1,180名用户，您是其中的重要成员。
            </div>
        </div>

        <!-- 任务基本信息 -->
        <div class="info-section">
            <div class="info-title">
                <i class="fas fa-info-circle"></i>
                任务信息
            </div>
            <div class="info-item">
                <div class="info-label">任务类型</div>
                <div class="info-value">推广任务</div>
            </div>
            <div class="info-item">
                <div class="info-label">开始时间</div>
                <div class="info-value">2025-02-01 09:00</div>
            </div>
            <div class="info-item">
                <div class="info-label">结束时间</div>
                <div class="info-value">2025-02-15 18:00</div>
            </div>
            <div class="info-item">
                <div class="info-label">剩余时间</div>
                <div class="info-value text-orange-500">13天 5小时</div>
            </div>
        </div>

        <!-- 任务描述 -->
        <div class="info-section">
            <div class="info-title">
                <i class="fas fa-file-text"></i>
                任务描述
            </div>
            <div class="info-content">
                针对VIP客户群体的专享活动推广，提升客户满意度和品牌忠诚度。完成以下步骤即可获得积分奖励：
            </div>
        </div>

        <!-- 完成要求 -->
        <div class="info-section">
            <div class="info-title">
                <i class="fas fa-tasks"></i>
                完成要求
            </div>
            <div class="info-content">
                <div class="space-y-2">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>访问活动页面并注册</span>
                    </div>
                    <div class="flex items-center">
                        <i class="far fa-circle text-gray-400 mr-2"></i>
                        <span>分享活动到朋友圈</span>
                    </div>
                    <div class="flex items-center">
                        <i class="far fa-circle text-gray-400 mr-2"></i>
                        <span>邀请至少2位好友参与</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务进度 -->
        <div class="progress-section highlight-new">
            <div class="annotation" style="top: -25px; left: 16px;">
                新增：人群任务进度
            </div>
            <div class="progress-header">
                <div class="progress-title">人群完成进度</div>
                <div class="progress-value">68.5% (808/1180)</div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 68.5%"></div>
            </div>
            <div class="text-xs text-gray-500 mt-2">
                您所在的VIP客户群体中，已有808人完成了此任务
            </div>
        </div>

        <!-- 个人进度 -->
        <div class="progress-section">
            <div class="progress-header">
                <div class="progress-title">我的完成进度</div>
                <div class="progress-value">33.3% (1/3)</div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 33.3%"></div>
            </div>
            <div class="text-xs text-gray-500 mt-2">
                您已完成1个步骤，还需完成2个步骤
            </div>
        </div>

        <!-- 操作按钮 -->
        <button class="action-button">
            继续完成任务
        </button>
    </div>

    <!-- 相关信息 -->
    <div class="content-card">
        <div class="info-section">
            <div class="info-title">
                <i class="fas fa-users"></i>
                人群信息
            </div>
            <div class="info-item">
                <div class="info-label">人群名称</div>
                <div class="info-value">VIP客户群体</div>
            </div>
            <div class="info-item">
                <div class="info-label">人群规模</div>
                <div class="info-value">1,180人</div>
            </div>
            <div class="info-item">
                <div class="info-label">匹配方式</div>
                <div class="info-value">手机号匹配</div>
            </div>
            <div class="info-item">
                <div class="info-label">加入时间</div>
                <div class="info-value">2025-01-28 14:35</div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">
                <i class="fas fa-chart-bar"></i>
                任务统计
            </div>
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-lg font-bold text-blue-600">1,180</div>
                    <div class="text-xs text-gray-500">推送总数</div>
                </div>
                <div>
                    <div class="text-lg font-bold text-green-600">808</div>
                    <div class="text-xs text-gray-500">已完成</div>
                </div>
                <div>
                    <div class="text-lg font-bold text-orange-600">372</div>
                    <div class="text-xs text-gray-500">进行中</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能说明浮层 -->
    <div class="fixed bottom-4 left-4 right-4 bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm">
        <div class="font-medium text-blue-800 mb-2">
            <i class="fas fa-info-circle mr-2"></i>功能说明
        </div>
        <div class="text-blue-700 space-y-1">
            <p><strong>推送来源：</strong>显示任务来自哪个人群推送，说明用户为什么会收到此任务</p>
            <p><strong>人群进度：</strong>显示同一人群中其他用户的完成情况，增加参与动力</p>
            <p><strong>人群信息：</strong>展示用户所属人群的详细信息和统计数据</p>
        </div>
    </div>

    <!-- 开发备注浮层 -->
    <div class="fixed bottom-20 left-4 right-4 bg-orange-50 border border-orange-200 rounded-lg p-3 text-sm">
        <div class="font-medium text-orange-800 mb-2">
            <i class="fas fa-code mr-2"></i>开发备注
        </div>
        <div class="text-orange-700 space-y-1">
            <p><strong>数据来源：</strong>需要从任务关联的人群中获取统计数据</p>
            <p><strong>实时更新：</strong>人群完成进度需要实时或定时更新</p>
            <p><strong>权限控制：</strong>只有人群成员才能看到人群相关信息</p>
            <p><strong>性能优化：</strong>人群统计数据可以适当缓存</p>
        </div>
    </div>

    <script>
        // 操作按钮点击
        document.querySelector('.action-button').addEventListener('click', () => {
            alert('跳转到任务执行页面');
        });

        // 隐藏说明浮层
        setTimeout(() => {
            document.querySelectorAll('.fixed').forEach(el => {
                el.style.display = 'none';
            });
        }, 10000);
    </script>
</body>
</html>
