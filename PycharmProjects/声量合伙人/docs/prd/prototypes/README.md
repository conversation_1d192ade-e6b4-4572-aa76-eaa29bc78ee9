# 人群功能产品原型文档

## 📋 原型概述

本原型文档包含了人群功能的完整页面设计，基于现有系统的设计风格，高度还原真实页面效果。所有原型页面都包含详细的功能说明和开发备注，便于研发人员理解需求和进行开发。

### 🎯 设计目标
- **高度还原**：基于现有系统设计风格，保持视觉一致性
- **功能完整**：覆盖PRD中设计的所有页面和功能点
- **交互清晰**：明确标注新增功能和改造功能
- **开发友好**：提供详细的开发备注和技术说明

## 🗂️ 原型页面清单

### 管理后台页面 (5个)

#### 1. [人群列表页](./crowd-list.html)
**功能描述**：人群管理的主入口页面
- ✅ 人群基础信息展示（名称、编码、用户数、匹配率等）
- ✅ 搜索筛选功能（按名称、状态、类型筛选）
- ✅ 批量操作（新建、导出、批量导入）
- ✅ 实时状态显示（导入进度、处理状态）
- ✅ 分页和排序功能

**设计亮点**：
- 实时显示Excel导入进度
- 匹配率可视化展示
- 状态标签清晰区分

#### 2. [人群创建页](./crowd-create.html)
**功能描述**：创建人群和Excel导入功能
- ✅ 分步骤创建流程（基础信息 → 导入用户 → 匹配结果）
- ✅ Excel文件上传（支持拖拽上传）
- ✅ 用户匹配结果预览
- ✅ 匹配统计和详情展示
- ✅ 模板下载功能

**设计亮点**：
- 步骤指示器引导用户操作
- 拖拽上传体验友好
- 匹配结果实时预览

#### 3. [人群详情页](./crowd-detail.html)
**功能描述**：查看人群详细信息和用户列表
- ✅ 人群基础信息展示
- ✅ 统计数据可视化（总数、有效数、匹配率）
- ✅ 用户列表（支持筛选、分页）
- ✅ 匹配状态管理
- ✅ 数据导出功能

**设计亮点**：
- 统计卡片直观展示关键数据
- 用户列表支持匹配状态筛选
- 匹配失败用户支持手动匹配

#### 4. [任务发布页（改造）](./task-create.html)
**功能描述**：支持人群推送的任务发布
- ✅ 推送方式选择（组织/人群/混合）
- ✅ 人群选择器（搜索、多选、预览）
- ✅ 推送目标统计（自动去重）
- ✅ 兼容原有组织推送功能
- ✅ 推送范围预览

**设计亮点**：
- 清晰标注新增功能区域
- 人群选择器支持实时搜索
- 推送目标统计自动计算去重

#### 5. [任务统计页（改造）](./task-statistics.html)
**功能描述**：多维度任务统计分析
- ✅ 统计维度选择（综合/组织/人群）
- ✅ 推送方式效果对比
- ✅ 人群效果排行榜
- ✅ 趋势分析图表
- ✅ 数据导出功能

**设计亮点**：
- 推送方式效果对比图表
- 人群排行榜展示最佳实践
- 交互式趋势分析图表

### H5移动端页面 (3个)

#### 6. [H5任务列表页（改造）](./h5-task-list.html)
**功能描述**：显示任务来源标识的移动端列表
- ✅ 推送来源标识（组织/人群/混合）
- ✅ 来源详情显示
- ✅ 任务卡片优化设计
- ✅ 标签页筛选功能
- ✅ 兼容原有功能

**设计亮点**：
- 彩色图标区分推送类型
- 来源信息清晰展示
- 移动端友好的卡片设计

#### 7. [H5任务详情页（改造）](./h5-task-detail.html)
**功能描述**：显示推送信息的任务详情
- ✅ 推送来源详细说明
- ✅ 人群完成进度展示
- ✅ 个人进度对比
- ✅ 人群信息展示
- ✅ 任务统计数据

**设计亮点**：
- 推送来源信息突出显示
- 人群进度条增加参与动力
- 详细的人群统计信息

#### 8. [H5个人中心页（改造）](./h5-profile.html)
**功能描述**：显示人群标签信息的个人中心
- ✅ 我的人群信息展示
- ✅ 人群任务统计
- ✅ 人群vs组织任务对比
- ✅ 最近活动记录
- ✅ 个人统计数据

**设计亮点**：
- 人群卡片设计精美
- 任务统计对比清晰
- 活动时间线展示

## 🎨 设计规范

### 管理后台设计规范
- **UI框架**：基于Element Plus设计语言
- **主色调**：#409eff（蓝色）
- **布局方式**：卡片式布局，圆角4px
- **表格设计**：行高适中，支持排序筛选
- **表单设计**：标签宽度150px，字体加粗
- **按钮设计**：间距10px，图标+文字组合

### H5移动端设计规范
- **UI框架**：基于Vant4设计语言
- **主色调**：#3366cc（深蓝色）
- **圆角设计**：6px（默认）、12px（大）
- **按钮高度**：48px（大）、36px（默认）
- **间距规范**：使用4的倍数（4px、8px、16px）
- **文字层级**：主要、次要、说明、占位

### 标注规范
- **红色虚线框**：新增功能区域
- **蓝色虚线框**：改造功能区域
- **红色标注**：新增功能说明
- **蓝色标注**：改造功能说明
- **绿色标注**：数据展示区域

## 💡 交互设计亮点

### 1. 渐进式引导
- **人群创建**：分步骤引导，降低操作复杂度
- **Excel导入**：模板下载 → 文件上传 → 结果预览
- **任务发布**：推送方式选择 → 目标选择 → 预览确认

### 2. 实时反馈
- **导入进度**：实时显示Excel处理进度
- **匹配结果**：即时展示用户匹配统计
- **推送统计**：自动计算去重后的推送数量

### 3. 数据可视化
- **统计卡片**：关键数据突出展示
- **进度条**：匹配率、完成率可视化
- **图表分析**：趋势分析和效果对比

### 4. 移动端优化
- **卡片设计**：适合移动端的信息展示
- **图标标识**：快速识别推送类型
- **触摸友好**：按钮大小符合移动端规范

## 🔧 开发指导

### 前端开发要点
1. **组件复用**：人群选择器、统计卡片等可复用组件
2. **状态管理**：人群数据、导入进度等状态管理
3. **异步处理**：Excel导入、数据统计等异步操作
4. **响应式设计**：管理后台适配不同屏幕尺寸
5. **性能优化**：大数据量列表的虚拟滚动

### 后端开发要点
1. **API设计**：RESTful风格，统一响应格式
2. **异步处理**：Excel导入使用队列处理
3. **数据缓存**：统计数据适当缓存
4. **权限控制**：基于角色的访问控制
5. **性能优化**：数据库查询优化

### 数据库设计要点
1. **表结构**：4张新表 + 2张表改造
2. **索引优化**：高频查询字段建立索引
3. **分页查询**：大数据量列表分页处理
4. **数据一致性**：人群数据与用户数据同步
5. **备份策略**：重要数据定期备份

## 📊 原型验证

### 功能完整性验证
- ✅ 覆盖PRD中的所有功能点
- ✅ 新增功能清晰标注
- ✅ 改造功能保持兼容
- ✅ 交互流程完整闭环

### 用户体验验证
- ✅ 操作流程简单直观
- ✅ 信息展示层次清晰
- ✅ 错误提示友好
- ✅ 移动端适配良好

### 技术可行性验证
- ✅ 基于现有技术栈
- ✅ 组件设计合理
- ✅ 性能要求可达
- ✅ 开发工作量合理

## 🚀 使用说明

### 查看原型
1. 打开 [index.html](./index.html) 查看原型导航
2. 点击对应页面链接查看具体原型
3. 每个页面都包含功能说明和开发备注

### 需求讲解
1. **产品经理**：使用原型进行需求讲解和评审
2. **设计师**：参考原型进行UI设计优化
3. **开发人员**：根据原型理解功能需求
4. **测试人员**：基于原型编写测试用例

### 迭代更新
1. 根据评审反馈更新原型
2. 保持原型与PRD文档同步
3. 及时更新开发备注信息
4. 记录原型变更历史

---
*人群功能产品原型 v1.0 - 为高质量开发提供可视化指导*
