<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人群列表页 - 声量合伙人（布局修复版）</title>
    <link href="https://unpkg.com/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 基于项目实际的CSS变量 */
        :root {
            --tableHeaderBg: #f8f8f9;
            --tableHeaderTextColor: #515a6e;
            --brder-color: #e8e8e8;
        }

        /* 基础样式重置 */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, <PERSON><PERSON>, sans-serif;
            background-color: #f2f3f5;
            -webkit-font-smoothing: antialiased;
        }

        /* RuoYi 样式类 */
        .app-container { padding: 20px; }
        .mb10 { margin-bottom: 10px; }
        .mr10 { margin-right: 10px; }
        .ml10 { margin-left: 10px; }

        /* 面包屑 */
        .breadcrumb {
            margin-bottom: 10px;
            color: #909399;
            font-size: 12px;
        }
        .breadcrumb a {
            color: #409eff;
            text-decoration: none;
        }

        /* 卡片样式 */
        .el-card {
            background: #ffffff;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
            overflow: hidden;
        }

        .el-card__header {
            padding: 14px 15px;
            border-bottom: 1px solid #ebeef5;
            background: #ffffff;
        }

        .el-card__body {
            padding: 20px;
        }

        /* 表单样式 */
        .search-form {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 20px;
        }

        .form-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-item label {
            font-weight: 700;
            color: #606266;
            font-size: 14px;
            min-width: 80px;
        }

        .el-input, .el-select {
            width: 200px;
            height: 32px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 0 11px;
            font-size: 14px;
            color: #606266;
        }

        .el-select {
            width: 120px;
            cursor: pointer;
            background: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="%23C0C4CC" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.6 30.592 30.592 0 0 0-42.752 0z"/></svg>') no-repeat right 8px center;
            background-size: 14px;
        }

        /* 按钮样式 */
        .el-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            padding: 8px 15px;
            font-size: 14px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            background: #ffffff;
            color: #606266;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
        }

        .el-button:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
        }

        .el-button--primary {
            color: #ffffff;
            background-color: #409eff;
            border-color: #409eff;
        }

        .el-button--primary:hover {
            background: #79bbff;
            border-color: #79bbff;
        }

        .el-button--success {
            color: #ffffff;
            background-color: #67c23a;
            border-color: #67c23a;
        }

        .el-button--warning {
            color: #ffffff;
            background-color: #e6a23c;
            border-color: #e6a23c;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background-color: var(--tableHeaderBg);
            color: var(--tableHeaderTextColor);
            font-weight: 500;
            padding: 12px 10px;
            text-align: left;
            border-bottom: 1px solid var(--brder-color);
            font-size: 13px;
            height: 40px;
        }

        .data-table td {
            padding: 12px 10px;
            border-bottom: 1px solid var(--brder-color);
            vertical-align: middle;
        }

        .data-table tr:hover td {
            background-color: #f5f7fa;
        }

        /* 标签样式 */
        .el-tag {
            display: inline-block;
            height: 24px;
            padding: 0 8px;
            line-height: 22px;
            font-size: 12px;
            border-radius: 4px;
            border: 1px solid;
            white-space: nowrap;
        }

        .el-tag--success {
            background-color: #f0f9ff;
            border-color: #b3d8ff;
            color: #67c23a;
        }

        .el-tag--warning {
            background-color: #fdf6ec;
            border-color: #f5dab1;
            color: #e6a23c;
        }

        .el-tag--info {
            background-color: #f4f4f5;
            border-color: #e9e9eb;
            color: #909399;
        }

        /* 操作按钮 */
        .action-btn {
            color: #409eff;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0 8px;
            font-size: 12px;
        }

        .action-btn:hover {
            color: #79bbff;
        }

        .action-btn--danger {
            color: #f56c6c;
        }

        .action-btn--danger:hover {
            color: #f78989;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
            padding: 10px 20px;
        }

        /* 原型标注 */
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border: 5px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.05);
            border: 2px dashed #ff4444;
            border-radius: 4px;
            position: relative;
        }

        /* 工具栏 */
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .toolbar-left {
            display: flex;
            gap: 10px;
        }

        .toolbar-right {
            display: flex;
            gap: 10px;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
                align-items: stretch;
            }
            
            .form-item {
                flex-direction: column;
                align-items: stretch;
            }
            
            .form-item label {
                min-width: auto;
            }
            
            .el-input, .el-select {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="./index.html">
                <i class="fas fa-arrow-left" style="margin-right: 5px;"></i>返回导航
            </a>
            <span style="margin: 0 8px;">/</span>
            人群管理
            <span style="margin: 0 8px;">/</span>
            人群列表
        </div>

        <!-- 搜索表单 -->
        <div class="el-card mb10">
            <div class="el-card__body">
                <form class="search-form">
                    <div class="form-item">
                        <label>人群名称</label>
                        <input type="text" class="el-input" placeholder="请输入人群名称">
                    </div>
                    <div class="form-item">
                        <label>人群状态</label>
                        <select class="el-select">
                            <option value="">全部状态</option>
                            <option value="1">正常</option>
                            <option value="2">停用</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <label>人群类型</label>
                        <select class="el-select">
                            <option value="">全部类型</option>
                            <option value="1">Excel导入</option>
                            <option value="2">条件筛选</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <button type="button" class="el-button el-button--primary mr10" onclick="handleQuery()">
                            <i class="fas fa-search" style="margin-right: 5px;"></i>查询
                        </button>
                        <button type="button" class="el-button" onclick="resetQuery()">
                            <i class="fas fa-refresh" style="margin-right: 5px;"></i>重置
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 表格区域 -->
        <div class="el-card highlight-new">
            <!-- 新增功能标注 -->
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：人群管理功能
            </div>
            
            <div class="el-card__header">
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="el-button el-button--primary" onclick="handleAdd()">
                            <i class="fas fa-plus" style="margin-right: 5px;"></i>新建人群
                        </button>
                        <button class="el-button el-button--success" onclick="handleExport()">
                            <i class="fas fa-download" style="margin-right: 5px;"></i>导出
                        </button>
                        <button class="el-button el-button--warning" onclick="handleImport()">
                            <i class="fas fa-upload" style="margin-right: 5px;"></i>批量导入
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <button class="el-button" title="刷新" onclick="handleQuery()">
                            <i class="fas fa-refresh"></i>
                        </button>
                        <button class="el-button" title="列设置" onclick="handleColumnSetting()">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="el-card__body" style="padding: 0;">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th style="width: 50px; text-align: center;">
                                    <input type="checkbox">
                                </th>
                                <th style="width: 80px; text-align: center;">ID</th>
                                <th style="min-width: 200px;">人群名称</th>
                                <th style="width: 150px; text-align: center;">人群编码</th>
                                <th style="width: 100px; text-align: center;">人群类型</th>
                                <th style="width: 100px; text-align: center;">总用户数</th>
                                <th style="width: 100px; text-align: center;">有效用户数</th>
                                <th style="width: 80px; text-align: center;">匹配率</th>
                                <th style="width: 80px; text-align: center;">状态</th>
                                <th style="width: 150px; text-align: center;">创建时间</th>
                                <th style="width: 200px; text-align: center;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="text-align: center;"><input type="checkbox"></td>
                                <td style="text-align: center;">1001</td>
                                <td>
                                    <div style="font-weight: 500;">VIP客户群体</div>
                                    <div style="font-size: 12px; color: #909399; margin-top: 2px;">高价值客户精准营销</div>
                                </td>
                                <td style="text-align: center;">VIP_CUSTOMERS_001</td>
                                <td style="text-align: center;">
                                    <span class="el-tag el-tag--success">Excel导入</span>
                                </td>
                                <td style="text-align: center;">1,250</td>
                                <td style="text-align: center; color: #67c23a; font-weight: 500;">1,180</td>
                                <td style="text-align: center; color: #67c23a; font-weight: 500;">94.4%</td>
                                <td style="text-align: center;">
                                    <span class="el-tag el-tag--success">正常</span>
                                </td>
                                <td style="text-align: center;">2025-01-28 14:30</td>
                                <td style="text-align: center;">
                                    <button class="action-btn" onclick="handleView()">查看</button>
                                    <button class="action-btn" onclick="handleEdit()">编辑</button>
                                    <button class="action-btn" onclick="handleStats()">统计</button>
                                    <button class="action-btn action-btn--danger" onclick="handleDelete()">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: center;"><input type="checkbox"></td>
                                <td style="text-align: center;">1002</td>
                                <td>
                                    <div style="font-weight: 500;">新用户推广群</div>
                                    <div style="font-size: 12px; color: #909399; margin-top: 2px;">新注册用户引导转化</div>
                                </td>
                                <td style="text-align: center;">NEW_USERS_002</td>
                                <td style="text-align: center;">
                                    <span class="el-tag el-tag--success">Excel导入</span>
                                </td>
                                <td style="text-align: center;">2,800</td>
                                <td style="text-align: center; color: #67c23a; font-weight: 500;">2,650</td>
                                <td style="text-align: center; color: #67c23a; font-weight: 500;">94.6%</td>
                                <td style="text-align: center;">
                                    <span class="el-tag el-tag--success">正常</span>
                                </td>
                                <td style="text-align: center;">2025-01-27 09:15</td>
                                <td style="text-align: center;">
                                    <button class="action-btn" onclick="handleView()">查看</button>
                                    <button class="action-btn" onclick="handleEdit()">编辑</button>
                                    <button class="action-btn" onclick="handleStats()">统计</button>
                                    <button class="action-btn action-btn--danger" onclick="handleDelete()">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: center;"><input type="checkbox"></td>
                                <td style="text-align: center;">1003</td>
                                <td>
                                    <div style="font-weight: 500;">销售部门推广</div>
                                    <div style="font-size: 12px; color: #909399; margin-top: 2px;">销售团队内部推广</div>
                                </td>
                                <td style="text-align: center;">SALES_TEAM_003</td>
                                <td style="text-align: center;">
                                    <span class="el-tag el-tag--success">Excel导入</span>
                                </td>
                                <td style="text-align: center;">450</td>
                                <td style="text-align: center; color: #67c23a; font-weight: 500;">380</td>
                                <td style="text-align: center; color: #67c23a; font-weight: 500;">84.4%</td>
                                <td style="text-align: center;">
                                    <span class="el-tag el-tag--warning">停用</span>
                                </td>
                                <td style="text-align: center;">2025-01-25 16:45</td>
                                <td style="text-align: center;">
                                    <button class="action-btn" onclick="handleView()">查看</button>
                                    <button class="action-btn" onclick="handleEdit()">编辑</button>
                                    <button class="action-btn" onclick="handleStats()">统计</button>
                                    <button class="action-btn action-btn--danger" onclick="handleDelete()">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: center;"><input type="checkbox"></td>
                                <td style="text-align: center;">1004</td>
                                <td>
                                    <div style="font-weight: 500;">测试人群数据</div>
                                    <div style="font-size: 12px; color: #909399; margin-top: 2px;">导入处理中...</div>
                                </td>
                                <td style="text-align: center;">TEST_CROWD_004</td>
                                <td style="text-align: center;">
                                    <span class="el-tag el-tag--success">Excel导入</span>
                                </td>
                                <td style="text-align: center;">1,000</td>
                                <td style="text-align: center; color: #909399;">0</td>
                                <td style="text-align: center; color: #909399;">0%</td>
                                <td style="text-align: center;">
                                    <span class="el-tag el-tag--info">处理中</span>
                                </td>
                                <td style="text-align: center;">2025-01-30 10:20</td>
                                <td style="text-align: center;">
                                    <button class="action-btn" onclick="handleView()">查看</button>
                                    <button class="action-btn" onclick="handleEdit()" disabled style="color: #c0c4cc;">编辑</button>
                                    <button class="action-btn" onclick="handleStats()" disabled style="color: #c0c4cc;">统计</button>
                                    <button class="action-btn action-btn--danger" onclick="handleDelete()">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <span>共 4 条</span>
                    <select style="width: 100px; height: 28px; border: 1px solid #dcdfe6; border-radius: 4px;">
                        <option>10 条/页</option>
                        <option>20 条/页</option>
                        <option>50 条/页</option>
                        <option>100 条/页</option>
                    </select>
                    <button class="el-button" onclick="handleCurrentChange()">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button class="el-button" style="background: #ecf5ff; color: #409eff; border-color: #c6e2ff;">1</button>
                    <button class="el-button" onclick="handleCurrentChange()">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <span>前往</span>
                    <input type="number" min="1" max="1" style="width: 50px; height: 28px; text-align: center; border: 1px solid #dcdfe6; border-radius: 4px;">
                    <span>页</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 基础交互功能
        function handleQuery() { alert('查询功能演示'); }
        function resetQuery() { alert('重置查询'); }
        function handleAdd() { window.open('./crowd-create.html', '_blank'); }
        function handleExport() { alert('导出功能演示'); }
        function handleImport() { alert('批量导入功能演示'); }
        function handleColumnSetting() { alert('列设置功能演示'); }
        function handleView() { window.open('./crowd-detail.html', '_blank'); }
        function handleEdit() { alert('编辑功能演示'); }
        function handleStats() { alert('统计功能演示'); }
        function handleDelete() { if (confirm('确定删除该人群吗？')) alert('删除成功'); }
        function handleCurrentChange() { console.log('分页切换'); }
    </script>
</body>
</html>
