<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人群列表页 - 声量合伙人</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Element Plus 完整CSS变量 - 基于RuoYi-Vue-Plus */
        :root {
            --el-color-white: #ffffff;
            --el-color-black: #000000;
            --el-color-primary: #409eff;
            --el-color-primary-light-3: #79bbff;
            --el-color-primary-light-5: #a0cfff;
            --el-color-primary-light-7: #c6e2ff;
            --el-color-primary-light-8: #d9ecff;
            --el-color-primary-light-9: #ecf5ff;
            --el-color-primary-dark-2: #337ecc;
            --el-color-success: #67c23a;
            --el-color-success-light-3: #85ce61;
            --el-color-success-light-5: #a4da89;
            --el-color-success-light-7: #c2e7b0;
            --el-color-success-light-8: #d1edc4;
            --el-color-success-light-9: #f0f9ff;
            --el-color-warning: #e6a23c;
            --el-color-warning-light-3: #ebb563;
            --el-color-warning-light-5: #f0c78a;
            --el-color-warning-light-7: #f5dab1;
            --el-color-warning-light-8: #f8e3c5;
            --el-color-warning-light-9: #fdf6ec;
            --el-color-danger: #f56c6c;
            --el-color-danger-light-3: #f78989;
            --el-color-danger-light-5: #fab6b6;
            --el-color-danger-light-7: #fcd3d3;
            --el-color-danger-light-8: #fde2e2;
            --el-color-danger-light-9: #fef0f0;
            --el-color-error: #f56c6c;
            --el-color-error-light-3: #f78989;
            --el-color-error-light-5: #fab6b6;
            --el-color-error-light-7: #fcd3d3;
            --el-color-error-light-8: #fde2e2;
            --el-color-error-light-9: #fef0f0;
            --el-color-info: #909399;
            --el-color-info-light-3: #a6a9ad;
            --el-color-info-light-5: #b1b3b8;
            --el-color-info-light-7: #c8c9cc;
            --el-color-info-light-8: #dedfe0;
            --el-color-info-light-9: #f4f4f5;
            --el-text-color-primary: #303133;
            --el-text-color-regular: #606266;
            --el-text-color-secondary: #909399;
            --el-text-color-placeholder: #a8abb2;
            --el-text-color-disabled: #c0c4cc;
            --el-border-color: #dcdfe6;
            --el-border-color-light: #e4e7ed;
            --el-border-color-lighter: #ebeef5;
            --el-border-color-extra-light: #f2f6fc;
            --el-border-color-dark: #d4d7de;
            --el-border-color-darker: #cdd0d6;
            --el-fill-color: #f0f2f5;
            --el-fill-color-light: #f5f7fa;
            --el-fill-color-lighter: #fafafa;
            --el-fill-color-extra-light: #fafcff;
            --el-fill-color-dark: #ebedf0;
            --el-fill-color-darker: #e6e8eb;
            --el-fill-color-blank: #ffffff;
            --el-bg-color: #ffffff;
            --el-bg-color-page: #f2f3f5;
            --el-bg-color-overlay: #ffffff;
            --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
            --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.12);
            --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.12);
            --el-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.08), 0px 12px 32px rgba(0, 0, 0, 0.12), 0px 8px 16px -8px rgba(0, 0, 0, 0.16);
            --el-disabled-bg-color: var(--el-fill-color-light);
            --el-disabled-text-color: var(--el-text-color-placeholder);
            --el-disabled-border-color: var(--el-border-color-light);
            --el-overlay-color: rgba(0, 0, 0, 0.8);
            --el-overlay-color-light: rgba(0, 0, 0, 0.7);
            --el-overlay-color-lighter: rgba(0, 0, 0, 0.5);
            --el-mask-color: rgba(255, 255, 255, 0.9);
            --el-mask-color-extra-light: rgba(255, 255, 255, 0.3);
            --el-border-width: 1px;
            --el-border-style: solid;
            --el-border-color-hover: var(--el-text-color-disabled);
            --el-border: var(--el-border-width) var(--el-border-style) var(--el-border-color);
            --el-border-radius-base: 4px;
            --el-border-radius-small: 2px;
            --el-border-radius-round: 20px;
            --el-border-radius-circle: 100%;
            --el-transition-duration: 0.3s;
            --el-transition-duration-fast: 0.2s;
            --el-transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
            --el-transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);
            --el-transition-all: all var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier);
            --el-transition-fade: opacity var(--el-transition-duration) var(--el-transition-function-fast-bezier);
            --el-transition-md-fade: transform var(--el-transition-duration) var(--el-transition-function-fast-bezier), opacity var(--el-transition-duration) var(--el-transition-function-fast-bezier);
            --el-transition-fade-linear: opacity var(--el-transition-duration-fast) linear;
            --el-transition-border: border-color var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
            --el-transition-box-shadow: box-shadow var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
            --el-transition-color: color var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
            --el-component-size-large: 40px;
            --el-component-size: 32px;
            --el-component-size-small: 24px;
            --el-font-size-extra-large: 20px;
            --el-font-size-large: 18px;
            --el-font-size-medium: 16px;
            --el-font-size-base: 14px;
            --el-font-size-small: 13px;
            --el-font-size-extra-small: 12px;
            --el-font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            --el-font-weight-primary: 500;
            --el-font-line-height-primary: 24px;
            --el-index-normal: 1;
            --el-index-top: 1000;
            --el-index-popper: 2000;
            --el-svg-monochrome-grey: var(--el-border-color);
        }

        /* 基础样式重置 */
        * {
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: var(--el-font-family);
            font-size: var(--el-font-size-base);
            color: var(--el-text-color-primary);
            background-color: var(--el-bg-color-page);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* RuoYi 应用容器 */
        .app-container {
            padding: 20px;
        }

        /* RuoYi 通用间距类 */
        .p-2 { padding: 8px; }
        .mb-10 { margin-bottom: 10px; }
        .mb5 { margin-bottom: 5px; }
        .mr10 { margin-right: 10px; }
        .ml10 { margin-left: 10px; }

        /* Element Plus 卡片组件 */
        .el-card {
            border-radius: var(--el-border-radius-base);
            border: 1px solid var(--el-border-color-light);
            background-color: var(--el-bg-color-overlay);
            overflow: hidden;
            color: var(--el-text-color-primary);
            transition: var(--el-transition-duration);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
        }

        .el-card.is-always-shadow, .el-card.is-hover-shadow:focus, .el-card.is-hover-shadow:hover {
            box-shadow: var(--el-box-shadow-light);
        }

        .el-card__header {
            padding: 18px 20px;
            border-bottom: 1px solid var(--el-border-color-lighter);
            box-sizing: border-box;
        }

        .el-card__body {
            padding: 20px;
        }

        /* Element Plus 表单组件 */
        .el-form {
            margin: 0;
            padding: 0;
        }

        .el-form--inline .el-form-item {
            display: inline-block;
            margin-right: 10px;
            vertical-align: top;
        }

        .el-form-item {
            margin-bottom: 22px;
        }

        .el-form-item__label {
            text-align: right;
            vertical-align: middle;
            float: left;
            font-size: var(--el-font-size-base);
            color: var(--el-text-color-regular);
            line-height: 32px;
            padding: 0 12px 0 0;
            box-sizing: border-box;
        }

        .el-form-item__content {
            line-height: 32px;
            position: relative;
            font-size: var(--el-font-size-base);
        }

        /* Element Plus 输入框组件 */
        .el-input {
            position: relative;
            font-size: var(--el-font-size-base);
            display: inline-block;
            width: 100%;
        }

        .el-input__wrapper {
            display: inline-flex;
            flex-grow: 1;
            align-items: center;
            justify-content: center;
            padding: 1px 11px;
            background-color: var(--el-fill-color-blank);
            background-image: none;
            border-radius: var(--el-border-radius-base);
            cursor: text;
            transition: var(--el-transition-box-shadow);
            transform: translate3d(0, 0, 0);
            box-shadow: 0 0 0 1px var(--el-border-color) inset;
        }

        .el-input__wrapper:hover {
            box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
        }

        .el-input.is-focus .el-input__wrapper {
            box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }

        .el-input__inner {
            width: 100%;
            flex-grow: 1;
            -webkit-appearance: none;
            color: var(--el-text-color-regular);
            font-size: inherit;
            height: 30px;
            line-height: 30px;
            padding: 0;
            outline: none;
            border: none;
            background: none;
            box-sizing: border-box;
        }

        .el-input__inner::placeholder {
            color: var(--el-text-color-placeholder);
        }

        .el-input__suffix {
            display: inline-flex;
            white-space: nowrap;
            flex-shrink: 0;
            flex-wrap: nowrap;
            height: 100%;
            text-align: center;
            color: var(--el-text-color-placeholder);
            transition: all var(--el-transition-duration);
            pointer-events: none;
        }

        .el-input__icon {
            height: inherit;
            line-height: inherit;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all var(--el-transition-duration);
            margin-left: 8px;
        }

        /* Element Plus 选择器组件 */
        .el-select {
            display: inline-block;
            position: relative;
        }

        .el-select .el-input__inner {
            cursor: pointer;
        }

        .el-select__caret {
            color: var(--el-text-color-placeholder);
            font-size: var(--el-font-size-base);
            transition: var(--el-transition-duration);
            transform: rotateZ(180deg);
            cursor: pointer;
        }

        .el-select__caret.is-reverse {
            transform: rotateZ(0deg);
        }

        /* Element Plus 按钮组件 */
        .el-button {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            line-height: 1;
            height: 32px;
            white-space: nowrap;
            cursor: pointer;
            color: var(--el-text-color-regular);
            text-align: center;
            box-sizing: border-box;
            outline: none;
            transition: 0.1s;
            font-weight: var(--el-font-weight-primary);
            user-select: none;
            vertical-align: middle;
            -webkit-appearance: none;
            background-color: var(--el-bg-color);
            border: var(--el-border);
            border-color: var(--el-border-color);
            border-radius: var(--el-border-radius-base);
            padding: 8px 15px;
            font-size: var(--el-font-size-base);
        }

        .el-button:hover, .el-button:focus {
            color: var(--el-color-primary);
            border-color: var(--el-color-primary-light-7);
            background-color: var(--el-color-primary-light-9);
        }

        .el-button--primary {
            color: var(--el-color-white);
            background-color: var(--el-color-primary);
            border-color: var(--el-color-primary);
        }

        .el-button--primary:hover, .el-button--primary:focus {
            background: var(--el-color-primary-light-3);
            border-color: var(--el-color-primary-light-3);
            color: var(--el-color-white);
        }

        .el-button--success {
            color: var(--el-color-white);
            background-color: var(--el-color-success);
            border-color: var(--el-color-success);
        }

        .el-button--warning {
            color: var(--el-color-white);
            background-color: var(--el-color-warning);
            border-color: var(--el-color-warning);
        }

        .el-button--danger {
            color: var(--el-color-white);
            background-color: var(--el-color-danger);
            border-color: var(--el-color-danger);
        }

        /* 原型标注样式 */
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border: 5px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.05);
            border: 2px dashed #ff4444;
            border-radius: 4px;
            position: relative;
        }
    </style>
</head>
<body>
    <!-- RuoYi 标准页面容器 -->
    <div class="app-container">
        <!-- 面包屑导航 -->
        <div class="mb-10">
            <span style="color: var(--el-text-color-secondary); font-size: 12px;">
                <a href="./index.html" style="color: var(--el-color-primary); text-decoration: none;">
                    <i class="fas fa-arrow-left" style="margin-right: 5px;"></i>返回导航
                </a>
                <span style="margin: 0 8px;">/</span>
                人群管理
                <span style="margin: 0 8px;">/</span>
                人群列表
            </span>
        </div>

        <!-- 搜索表单 -->
        <div class="el-card mb-10">
            <div class="el-card__body">
                <form class="el-form el-form--inline">
                    <div class="el-form-item">
                        <label class="el-form-item__label" style="width: 80px;">人群名称</label>
                        <div class="el-form-item__content">
                            <div class="el-input" style="width: 200px;">
                                <div class="el-input__wrapper">
                                    <input class="el-input__inner" type="text" placeholder="请输入人群名称">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label" style="width: 80px;">人群状态</label>
                        <div class="el-form-item__content">
                            <div class="el-select" style="width: 120px;">
                                <div class="el-input el-input--suffix">
                                    <div class="el-input__wrapper">
                                        <input class="el-input__inner" readonly placeholder="全部状态" style="cursor: pointer;">
                                        <span class="el-input__suffix">
                                            <i class="el-select__caret el-input__icon fas fa-angle-down"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="el-form-item">
                        <label class="el-form-item__label" style="width: 80px;">人群类型</label>
                        <div class="el-form-item__content">
                            <div class="el-select" style="width: 120px;">
                                <div class="el-input el-input--suffix">
                                    <div class="el-input__wrapper">
                                        <input class="el-input__inner" readonly placeholder="全部类型" style="cursor: pointer;">
                                        <span class="el-input__suffix">
                                            <i class="el-select__caret el-input__icon fas fa-angle-down"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="el-form-item">
                        <div class="el-form-item__content">
                            <button type="button" class="el-button el-button--primary mr10">
                                <i class="fas fa-search" style="margin-right: 5px;"></i>查询
                            </button>
                            <button type="button" class="el-button">
                                <i class="fas fa-refresh" style="margin-right: 5px;"></i>重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 表格操作区域 -->
        <div class="el-card highlight-new">
            <!-- 新增功能标注 -->
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：人群管理功能
            </div>
            
            <div class="el-card__header">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <button class="el-button el-button--primary mr10">
                            <i class="fas fa-plus" style="margin-right: 5px;"></i>新建人群
                        </button>
                        <button class="el-button el-button--success mr10">
                            <i class="fas fa-download" style="margin-right: 5px;"></i>导出
                        </button>
                        <button class="el-button el-button--warning mr10">
                            <i class="fas fa-upload" style="margin-right: 5px;"></i>批量导入
                        </button>
                    </div>
                    <div>
                        <button class="el-button" title="刷新">
                            <i class="fas fa-refresh"></i>
                        </button>
                        <button class="el-button ml10" title="列设置">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="el-card__body" style="padding: 0;">
                <!-- Element Plus 表格 -->
                <div class="el-table" style="width: 100%;">
                    <div class="el-table__header-wrapper" style="overflow: hidden;">
                        <table cellspacing="0" cellpadding="0" border="0" class="el-table__header" style="width: 100%; border-collapse: separate; border-spacing: 0;">
                            <colgroup>
                                <col name="el-table_1_column_1" width="50">
                                <col name="el-table_1_column_2" width="80">
                                <col name="el-table_1_column_3" width="200">
                                <col name="el-table_1_column_4" width="150">
                                <col name="el-table_1_column_5" width="100">
                                <col name="el-table_1_column_6" width="100">
                                <col name="el-table_1_column_7" width="100">
                                <col name="el-table_1_column_8" width="80">
                                <col name="el-table_1_column_9" width="80">
                                <col name="el-table_1_column_10" width="150">
                                <col name="el-table_1_column_11" width="200">
                            </colgroup>
                            <thead>
                                <tr>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: center;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">
                                            <label class="el-checkbox">
                                                <span class="el-checkbox__input">
                                                    <span class="el-checkbox__inner" style="background-color: #fff; border: 1px solid #dcdfe6; border-radius: 2px; width: 14px; height: 14px; position: relative; cursor: pointer; display: inline-block; box-sizing: border-box;"></span>
                                                    <input type="checkbox" class="el-checkbox__original" style="opacity: 0; outline: none; position: absolute; margin: 0; width: 0; height: 0; z-index: -1;">
                                                </span>
                                            </label>
                                        </div>
                                    </th>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: center;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">ID</div>
                                    </th>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: left;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">人群名称</div>
                                    </th>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: center;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">人群编码</div>
                                    </th>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: center;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">人群类型</div>
                                    </th>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: center;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">总用户数</div>
                                    </th>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: center;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">有效用户数</div>
                                    </th>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: center;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">匹配率</div>
                                    </th>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: center;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">状态</div>
                                    </th>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: center;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">创建时间</div>
                                    </th>
                                    <th class="el-table__cell" style="background-color: #f8f8f9; color: #515a6e; font-weight: 500; border-bottom: 1px solid #e8e8e8; padding: 12px 0; text-align: center;">
                                        <div class="cell" style="padding-left: 10px; padding-right: 10px;">操作</div>
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
