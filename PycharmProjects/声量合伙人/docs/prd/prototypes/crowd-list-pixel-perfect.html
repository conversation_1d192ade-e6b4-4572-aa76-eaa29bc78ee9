<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人群列表页 - 声量合伙人</title>
    <link href="https://unpkg.com/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 完全基于项目实际样式的CSS变量 */
        :root {
            --menuBg: #304156;
            --menuColor: #bfcbd9;
            --menuActiveText: #f4f4f5;
            --menuHover: #263445;
            --subMenuBg: #1f2d3d;
            --subMenuActiveText: #f4f4f5;
            --subMenuHover: #001528;
            --subMenuTitleHover: #293444;
            --fixedHeaderBg: #ffffff;
            --tableHeaderBg: #f8f8f9;
            --tableHeaderTextColor: #515a6e;
            --brder-color: #e8e8e8;
            --tags-view-active-bg: var(--el-color-primary);
            --tags-view-active-border-color: var(--el-color-primary);
        }

        /* 基础样式 - 完全按照项目index.scss */
        body {
            height: 100%;
            margin: 0;
            -moz-osx-font-smoothing: grayscale;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
        }

        html {
            height: 100%;
            box-sizing: border-box;
        }

        *,
        *:before,
        *:after {
            box-sizing: inherit;
        }

        label {
            font-weight: 700;
        }

        /* RuoYi 通用样式类 - 完全按照ruoyi.scss */
        .pt5 { padding-top: 5px; }
        .pr5 { padding-right: 5px; }
        .pb5 { padding-bottom: 5px; }
        .mt5 { margin-top: 5px; }
        .mr5 { margin-right: 5px; }
        .mb5 { margin-bottom: 5px; }
        .mb8 { margin-bottom: 8px; }
        .ml5 { margin-left: 5px; }
        .mt10 { margin-top: 10px; }
        .mr10 { margin-right: 10px; }
        .mb10 { margin-bottom: 10px; }
        .ml10 { margin-left: 10px; }
        .mt20 { margin-top: 20px; }
        .mr20 { margin-right: 20px; }
        .mb20 { margin-bottom: 20px; }
        .ml20 { margin-left: 20px; }

        /* 主容器样式 - 按照index.scss */
        .app-container {
            padding: 20px;
        }

        /* 搜索面板样式 - 按照index.scss */
        .panel,
        .search {
            margin-bottom: 0.75rem;
            border-radius: 0.25rem;
            border: 1px solid var(--el-border-color-light);
            background-color: var(--el-bg-color-overlay);
            padding: 0.75rem;
            transition: all ease 0.3s;
        }

        .panel:hover,
        .search:hover {
            box-shadow: 0 2px 12px #0000001a;
            transition: all ease 0.3s;
        }

        /* Element Plus 表格样式覆盖 - 按照ruoyi.scss */
        .el-table {
            position: relative;
            overflow: hidden;
            box-sizing: border-box;
            flex: 1;
            width: 100%;
            max-width: 100%;
            background-color: var(--el-bg-color);
            font-size: 14px;
            color: var(--el-text-color-regular);
        }

        .el-table__header-wrapper {
            overflow: hidden;
        }

        .el-table__header {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border: 0;
        }

        .el-table__cell {
            padding: 12px 0;
            min-width: 0;
            box-sizing: border-box;
            text-overflow: ellipsis;
            vertical-align: middle;
            position: relative;
            text-align: left;
            border-bottom: 1px solid var(--brder-color);
        }

        .el-table__header .el-table__cell {
            background-color: var(--tableHeaderBg) !important;
            color: var(--tableHeaderTextColor);
            height: 40px !important;
            font-size: 13px;
            font-weight: 500;
            word-break: break-word;
        }

        .el-table__body .el-table__cell {
            border-bottom: 1px solid var(--brder-color);
        }

        .el-table__body tr:hover > .el-table__cell {
            background-color: var(--el-fill-color-light);
        }

        .el-table__cell .cell {
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: normal;
            word-break: break-all;
            line-height: 23px;
            padding-left: 10px;
            padding-right: 10px;
        }

        /* Element Plus 标签样式 */
        .el-tag {
            background-color: var(--el-color-info-light-9);
            border-color: var(--el-color-info-light-8);
            color: var(--el-color-info);
            display: inline-block;
            height: 24px;
            padding: 0 8px;
            line-height: 22px;
            font-size: 12px;
            border-radius: 4px;
            box-sizing: border-box;
            border: 1px solid;
            white-space: nowrap;
        }

        .el-tag--success.el-tag--light {
            background-color: var(--el-color-success-light-9);
            border-color: var(--el-color-success-light-8);
            color: var(--el-color-success);
        }

        .el-tag--warning.el-tag--light {
            background-color: var(--el-color-warning-light-9);
            border-color: var(--el-color-warning-light-8);
            color: var(--el-color-warning);
        }

        .el-tag--info.el-tag--light {
            background-color: var(--el-color-info-light-9);
            border-color: var(--el-color-info-light-8);
            color: var(--el-color-info);
        }

        /* Element Plus 按钮文本样式 */
        .el-button--text {
            border-color: transparent;
            background: transparent;
            color: var(--el-color-primary);
            padding-left: 0;
            padding-right: 0;
        }

        .el-button--text:hover,
        .el-button--text:focus {
            color: var(--el-color-primary-light-3);
            border-color: transparent;
            background-color: transparent;
        }

        .el-button--text.is-type-danger {
            color: var(--el-color-danger);
        }

        .el-button--text.is-type-danger:hover,
        .el-button--text.is-type-danger:focus {
            color: var(--el-color-danger-light-3);
        }

        .el-button--small {
            height: 24px;
            padding: 5px 11px;
            font-size: 12px;
            border-radius: 3px;
        }

        .el-button--text.el-button--small {
            padding-left: 8px;
            padding-right: 8px;
        }

        /* Element Plus 复选框样式 */
        .el-checkbox {
            color: var(--el-text-color-regular);
            font-weight: 500;
            font-size: 14px;
            position: relative;
            cursor: pointer;
            display: inline-block;
            white-space: nowrap;
            user-select: none;
            margin-right: 30px;
        }

        .el-checkbox__input {
            white-space: nowrap;
            cursor: pointer;
            outline: none;
            display: inline-block;
            line-height: 1;
            position: relative;
            vertical-align: middle;
        }

        .el-checkbox__inner {
            display: inline-block;
            position: relative;
            border: 1px solid var(--el-border-color);
            border-radius: 2px;
            box-sizing: border-box;
            width: 14px;
            height: 14px;
            background-color: var(--el-bg-color);
            z-index: 1;
            transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46), background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
        }

        .el-checkbox__original {
            opacity: 0;
            outline: none;
            position: absolute;
            margin: 0;
            width: 0;
            height: 0;
            z-index: -1;
        }

        /* Element Plus 卡片样式覆盖 - 按照ruoyi.scss */
        .el-card__header {
            padding: 14px 15px 14px !important;
            min-height: 40px;
        }

        .el-card__body {
            padding: 20px 20px 20px 20px !important;
        }

        /* Element Plus 表单样式覆盖 - 按照ruoyi.scss */
        .el-form .el-form-item__label {
            font-weight: 700;
        }

        /* 分页容器样式 - 按照ruoyi.scss */
        .pagination-container {
            height: 25px;
            margin-bottom: 10px;
            margin-top: 15px;
            padding: 10px 20px !important;
        }

        /* 表格右侧工具栏样式 - 按照ruoyi.scss */
        .top-right-btn {
            margin-left: auto;
        }

        /* 原型标注样式 */
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border: 5px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.05);
            border: 2px dashed #ff4444;
            border-radius: 4px;
            position: relative;
        }

        /* 面包屑样式 */
        .breadcrumb {
            margin-bottom: 10px;
            color: var(--el-text-color-secondary);
            font-size: 12px;
        }

        .breadcrumb a {
            color: var(--el-color-primary);
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: var(--el-color-primary-light-3);
        }
    </style>
</head>
<body>
    <!-- RuoYi 标准页面容器 -->
    <div class="app-container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="./index.html">
                <i class="fas fa-arrow-left" style="margin-right: 5px;"></i>返回导航
            </a>
            <span style="margin: 0 8px;">/</span>
            人群管理
            <span style="margin: 0 8px;">/</span>
            人群列表
        </div>

        <!-- 搜索表单 -->
        <div class="el-card search mb10">
            <div class="el-card__body">
                <form class="el-form el-form--inline">
                    <div class="el-form-item" style="margin-right: 20px;">
                        <label class="el-form-item__label" style="width: 80px;">人群名称</label>
                        <div class="el-form-item__content">
                            <div class="el-input" style="width: 200px;">
                                <div class="el-input__wrapper">
                                    <input class="el-input__inner" type="text" placeholder="请输入人群名称">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="el-form-item" style="margin-right: 20px;">
                        <label class="el-form-item__label" style="width: 80px;">人群状态</label>
                        <div class="el-form-item__content">
                            <div class="el-select" style="width: 120px;">
                                <div class="el-input el-input--suffix">
                                    <div class="el-input__wrapper">
                                        <input class="el-input__inner" readonly placeholder="全部状态" style="cursor: pointer;">
                                        <span class="el-input__suffix">
                                            <i class="el-select__caret el-input__icon fas fa-angle-down"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="el-form-item" style="margin-right: 20px;">
                        <label class="el-form-item__label" style="width: 80px;">人群类型</label>
                        <div class="el-form-item__content">
                            <div class="el-select" style="width: 120px;">
                                <div class="el-input el-input--suffix">
                                    <div class="el-input__wrapper">
                                        <input class="el-input__inner" readonly placeholder="全部类型" style="cursor: pointer;">
                                        <span class="el-input__suffix">
                                            <i class="el-select__caret el-input__icon fas fa-angle-down"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="el-form-item">
                        <div class="el-form-item__content">
                            <button type="button" class="el-button el-button--primary mr10" onclick="handleQuery()">
                                <i class="fas fa-search" style="margin-right: 5px;"></i>查询
                            </button>
                            <button type="button" class="el-button" onclick="resetQuery()">
                                <i class="fas fa-refresh" style="margin-right: 5px;"></i>重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 表格区域 -->
        <div class="el-card highlight-new">
            <!-- 新增功能标注 -->
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：人群管理功能
            </div>

            <div class="el-card__header">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <button class="el-button el-button--primary mr10" onclick="handleAdd()">
                            <i class="fas fa-plus" style="margin-right: 5px;"></i>新建人群
                        </button>
                        <button class="el-button el-button--success mr10" onclick="handleExport()">
                            <i class="fas fa-download" style="margin-right: 5px;"></i>导出
                        </button>
                        <button class="el-button el-button--warning mr10" onclick="handleImport()">
                            <i class="fas fa-upload" style="margin-right: 5px;"></i>批量导入
                        </button>
                    </div>
                    <div class="top-right-btn">
                        <button class="el-button" title="刷新" onclick="handleQuery()">
                            <i class="fas fa-refresh"></i>
                        </button>
                        <button class="el-button ml10" title="列设置" onclick="handleColumnSetting()">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 使用原生HTML表格，完全按照项目实际样式 -->
            <div class="el-table el-table--fit el-table--striped el-table--enable-row-hover el-table--enable-row-transition" style="width: 100%;">
                <div class="el-table__header-wrapper" style="overflow: hidden;">
                    <table cellspacing="0" cellpadding="0" border="0" class="el-table__header" style="width: 100%;">
                        <colgroup>
                            <col name="el-table_column_1" width="50">
                            <col name="el-table_column_2" width="80">
                            <col name="el-table_column_3" width="200">
                            <col name="el-table_column_4" width="150">
                            <col name="el-table_column_5" width="100">
                            <col name="el-table_column_6" width="100">
                            <col name="el-table_column_7" width="100">
                            <col name="el-table_column_8" width="80">
                            <col name="el-table_column_9" width="80">
                            <col name="el-table_column_10" width="150">
                            <col name="el-table_column_11" width="200">
                        </colgroup>
                        <thead>
                            <tr>
                                <th class="el-table__cell is-leaf" style="text-align: center;">
                                    <div class="cell">
                                        <label class="el-checkbox">
                                            <span class="el-checkbox__input">
                                                <span class="el-checkbox__inner"></span>
                                                <input type="checkbox" class="el-checkbox__original">
                                            </span>
                                        </label>
                                    </div>
                                </th>
                                <th class="el-table__cell is-leaf" style="text-align: center;"><div class="cell">ID</div></th>
                                <th class="el-table__cell is-leaf" style="text-align: left;"><div class="cell">人群名称</div></th>
                                <th class="el-table__cell is-leaf" style="text-align: center;"><div class="cell">人群编码</div></th>
                                <th class="el-table__cell is-leaf" style="text-align: center;"><div class="cell">人群类型</div></th>
                                <th class="el-table__cell is-leaf" style="text-align: center;"><div class="cell">总用户数</div></th>
                                <th class="el-table__cell is-leaf" style="text-align: center;"><div class="cell">有效用户数</div></th>
                                <th class="el-table__cell is-leaf" style="text-align: center;"><div class="cell">匹配率</div></th>
                                <th class="el-table__cell is-leaf" style="text-align: center;"><div class="cell">状态</div></th>
                                <th class="el-table__cell is-leaf" style="text-align: center;"><div class="cell">创建时间</div></th>
                                <th class="el-table__cell is-leaf" style="text-align: center;"><div class="cell">操作</div></th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="el-table__body-wrapper" style="overflow: hidden;">
                    <table cellspacing="0" cellpadding="0" border="0" class="el-table__body" style="width: 100%;">
                        <colgroup>
                            <col name="el-table_column_1" width="50">
                            <col name="el-table_column_2" width="80">
                            <col name="el-table_column_3" width="200">
                            <col name="el-table_column_4" width="150">
                            <col name="el-table_column_5" width="100">
                            <col name="el-table_column_6" width="100">
                            <col name="el-table_column_7" width="100">
                            <col name="el-table_column_8" width="80">
                            <col name="el-table_column_9" width="80">
                            <col name="el-table_column_10" width="150">
                            <col name="el-table_column_11" width="200">
                        </colgroup>
                        <tbody>
                            <tr class="el-table__row">
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <label class="el-checkbox">
                                            <span class="el-checkbox__input">
                                                <span class="el-checkbox__inner"></span>
                                                <input type="checkbox" class="el-checkbox__original">
                                            </span>
                                        </label>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">1001</div></td>
                                <td class="el-table__cell" style="text-align: left;">
                                    <div class="cell">
                                        <div style="font-weight: 500;">VIP客户群体</div>
                                        <div style="font-size: 12px; color: var(--el-text-color-secondary); margin-top: 2px;">高价值客户精准营销</div>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">VIP_CUSTOMERS_001</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span class="el-tag el-tag--success el-tag--light">Excel导入</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">1,250</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span style="color: var(--el-color-success); font-weight: 500;">1,180</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span style="color: var(--el-color-success); font-weight: 500;">94.4%</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span class="el-tag el-tag--success el-tag--light">正常</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">2025-01-28 14:30</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <button class="el-button el-button--text el-button--small" type="button">查看</button>
                                        <button class="el-button el-button--text el-button--small" type="button">编辑</button>
                                        <button class="el-button el-button--text el-button--small" type="button">统计</button>
                                        <button class="el-button el-button--text el-button--small is-type-danger" type="button">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="el-table__row">
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <label class="el-checkbox">
                                            <span class="el-checkbox__input">
                                                <span class="el-checkbox__inner"></span>
                                                <input type="checkbox" class="el-checkbox__original">
                                            </span>
                                        </label>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">1002</div></td>
                                <td class="el-table__cell" style="text-align: left;">
                                    <div class="cell">
                                        <div style="font-weight: 500;">新用户推广群</div>
                                        <div style="font-size: 12px; color: var(--el-text-color-secondary); margin-top: 2px;">新注册用户引导转化</div>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">NEW_USERS_002</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span class="el-tag el-tag--success el-tag--light">Excel导入</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">2,800</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span style="color: var(--el-color-success); font-weight: 500;">2,650</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span style="color: var(--el-color-success); font-weight: 500;">94.6%</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span class="el-tag el-tag--success el-tag--light">正常</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">2025-01-27 09:15</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <button class="el-button el-button--text el-button--small" type="button">查看</button>
                                        <button class="el-button el-button--text el-button--small" type="button">编辑</button>
                                        <button class="el-button el-button--text el-button--small" type="button">统计</button>
                                        <button class="el-button el-button--text el-button--small is-type-danger" type="button">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="el-table__row">
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <label class="el-checkbox">
                                            <span class="el-checkbox__input">
                                                <span class="el-checkbox__inner"></span>
                                                <input type="checkbox" class="el-checkbox__original">
                                            </span>
                                        </label>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">1003</div></td>
                                <td class="el-table__cell" style="text-align: left;">
                                    <div class="cell">
                                        <div style="font-weight: 500;">销售部门推广</div>
                                        <div style="font-size: 12px; color: var(--el-text-color-secondary); margin-top: 2px;">销售团队内部推广</div>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">SALES_TEAM_003</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span class="el-tag el-tag--success el-tag--light">Excel导入</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">450</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span style="color: var(--el-color-success); font-weight: 500;">380</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span style="color: var(--el-color-success); font-weight: 500;">84.4%</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span class="el-tag el-tag--warning el-tag--light">停用</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">2025-01-25 16:45</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <button class="el-button el-button--text el-button--small" type="button">查看</button>
                                        <button class="el-button el-button--text el-button--small" type="button">编辑</button>
                                        <button class="el-button el-button--text el-button--small" type="button">统计</button>
                                        <button class="el-button el-button--text el-button--small is-type-danger" type="button">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="el-table__row">
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <label class="el-checkbox">
                                            <span class="el-checkbox__input">
                                                <span class="el-checkbox__inner"></span>
                                                <input type="checkbox" class="el-checkbox__original">
                                            </span>
                                        </label>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">1004</div></td>
                                <td class="el-table__cell" style="text-align: left;">
                                    <div class="cell">
                                        <div style="font-weight: 500;">测试人群数据</div>
                                        <div style="font-size: 12px; color: var(--el-text-color-secondary); margin-top: 2px;">导入处理中...</div>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">TEST_CROWD_004</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span class="el-tag el-tag--success el-tag--light">Excel导入</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">1,000</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span style="color: var(--el-text-color-secondary);">0</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span style="color: var(--el-text-color-secondary);">0%</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <span class="el-tag el-tag--info el-tag--light">处理中</span>
                                    </div>
                                </td>
                                <td class="el-table__cell" style="text-align: center;"><div class="cell">2025-01-30 10:20</div></td>
                                <td class="el-table__cell" style="text-align: center;">
                                    <div class="cell">
                                        <button class="el-button el-button--text el-button--small" type="button">查看</button>
                                        <button class="el-button el-button--text el-button--small" type="button" disabled>编辑</button>
                                        <button class="el-button el-button--text el-button--small" type="button" disabled>统计</button>
                                        <button class="el-button el-button--text el-button--small is-type-danger" type="button">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="pagination-container">
                <div class="el-pagination" style="text-align: right;">
                    <span class="el-pagination__total">共 4 条</span>
                    <div class="el-pagination__sizes" style="display: inline-block; margin-left: 10px;">
                        <select class="el-select__inner" style="width: 100px; height: 28px; border: 1px solid var(--el-border-color); border-radius: 4px;">
                            <option value="10">10 条/页</option>
                            <option value="20">20 条/页</option>
                            <option value="50">50 条/页</option>
                            <option value="100">100 条/页</option>
                        </select>
                    </div>
                    <button class="el-pager__item" style="margin-left: 10px; padding: 0 4px; height: 28px; min-width: 35.5px; border: 1px solid var(--el-border-color); background: var(--el-bg-color); cursor: pointer;" onclick="handleCurrentChange()">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button class="el-pager__item is-active" style="padding: 0 4px; height: 28px; min-width: 35.5px; border: 1px solid var(--el-color-primary-light-7); background: var(--el-color-primary-light-9); color: var(--el-color-primary); cursor: default;">
                        1
                    </button>
                    <button class="el-pager__item" style="padding: 0 4px; height: 28px; min-width: 35.5px; border: 1px solid var(--el-border-color); background: var(--el-bg-color); cursor: pointer;" onclick="handleCurrentChange()">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <div class="el-pagination__jump" style="display: inline-block; margin-left: 10px;">
                        <span style="margin-right: 5px;">前往</span>
                        <input type="number" min="1" max="1" class="el-input__inner" style="width: 50px; height: 28px; text-align: center; border: 1px solid var(--el-border-color); border-radius: 4px;">
                        <span style="margin-left: 5px;">页</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 简化的JavaScript交互 -->
    <script>
        // 基础交互功能
        function handleQuery() {
            console.log('查询功能');
            alert('查询功能演示');
        }

        function resetQuery() {
            document.querySelector('input[placeholder="请输入人群名称"]').value = '';
            console.log('重置查询');
        }

        function handleAdd() {
            window.open('./crowd-create.html', '_blank');
        }

        function handleExport() {
            alert('导出功能演示');
        }

        function handleImport() {
            alert('批量导入功能演示');
        }

        function handleColumnSetting() {
            alert('列设置功能演示');
        }

        function handleView() {
            window.open('./crowd-detail.html', '_blank');
        }

        function handleEdit() {
            alert('编辑功能演示');
        }

        function handleStats() {
            alert('统计功能演示');
        }

        function handleDelete() {
            if (confirm('确定删除该人群吗？')) {
                alert('删除成功');
            }
        }

        // 分页功能
        function handleSizeChange() {
            console.log('改变页面大小');
        }

        function handleCurrentChange() {
            console.log('改变当前页');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('人群列表页面加载完成');

            // 为按钮添加点击事件
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                if (button.textContent.includes('查看')) {
                    button.onclick = handleView;
                } else if (button.textContent.includes('编辑')) {
                    button.onclick = handleEdit;
                } else if (button.textContent.includes('统计')) {
                    button.onclick = handleStats;
                } else if (button.textContent.includes('删除')) {
                    button.onclick = handleDelete;
                }
            });
        });
    </script>
</body>
</html>
