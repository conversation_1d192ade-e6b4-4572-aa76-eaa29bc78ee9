<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人群详情页 - 声量合伙人</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --el-color-primary: #409eff;
            --el-color-success: #67c23a;
            --el-color-warning: #e6a23c;
            --el-color-danger: #f56c6c;
            --el-border-color: #dcdfe6;
            --el-border-color-light: #e4e7ed;
            --el-text-color-primary: #303133;
            --el-text-color-regular: #606266;
            --el-bg-color: #ffffff;
            --el-bg-color-page: #f2f3f5;
        }
        
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            background-color: var(--el-bg-color-page);
        }
        
        .el-card {
            background: var(--el-bg-color);
            border: 1px solid var(--el-border-color-light);
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
        }
        
        .el-button {
            padding: 8px 15px;
            border-radius: 4px;
            border: 1px solid var(--el-border-color);
            background: var(--el-bg-color);
            color: var(--el-text-color-regular);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .el-button--primary {
            background: var(--el-color-primary);
            border-color: var(--el-color-primary);
            color: white;
        }
        
        .el-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .el-table th {
            background: #f8f8f9;
            color: #515a6e;
            font-weight: 500;
            padding: 12px 8px;
            border-bottom: 1px solid var(--el-border-color-light);
        }
        
        .el-table td {
            padding: 12px 8px;
            border-bottom: 1px solid var(--el-border-color-light);
        }
        
        .el-tag {
            display: inline-block;
            height: 24px;
            padding: 0 8px;
            line-height: 22px;
            font-size: 12px;
            border-radius: 4px;
            border: 1px solid;
        }
        
        .el-tag--success {
            background: #f0f9ff;
            border-color: #b3d8ff;
            color: #409eff;
        }
        
        .el-tag--danger {
            background: #fef0f0;
            border-color: #fbc4c4;
            color: #f56c6c;
        }
        
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border: 5px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.1);
            border: 2px dashed #ff4444;
            border-radius: 4px;
            position: relative;
        }
        
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 0.875rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-4">
        <div class="px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="./crowd-list.html" class="text-blue-500 hover:text-blue-700 mr-4">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                    <h1 class="text-lg font-semibold text-gray-900">人群详情</h1>
                    <span class="ml-3 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">正常</span>
                </div>
                <div class="text-sm text-gray-500">管理后台 > 人群管理 > 人群详情</div>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- 人群基础信息 -->
        <div class="el-card mb-6 highlight-new">
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：人群基础信息展示
            </div>
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">VIP客户群体</h3>
                    <div class="flex gap-2">
                        <button class="el-button">
                            <i class="fas fa-edit mr-1"></i>编辑
                        </button>
                        <button class="el-button el-button--primary">
                            <i class="fas fa-tasks mr-1"></i>创建任务
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <div class="space-y-4">
                            <div class="flex">
                                <span class="w-24 text-gray-600">人群编码:</span>
                                <span class="font-mono text-gray-900">VIP_CUSTOMERS_001</span>
                            </div>
                            <div class="flex">
                                <span class="w-24 text-gray-600">人群类型:</span>
                                <span class="el-tag el-tag--success">Excel导入</span>
                            </div>
                            <div class="flex">
                                <span class="w-24 text-gray-600">创建时间:</span>
                                <span class="text-gray-900">2025-01-28 14:30:25</span>
                            </div>
                            <div class="flex">
                                <span class="w-24 text-gray-600">创建人:</span>
                                <span class="text-gray-900">张三 (营销部)</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <span class="w-24 text-gray-600">人群描述:</span>
                                <span class="text-gray-900">高价值VIP客户群体，用于精准营销推广活动</span>
                            </div>
                            <div class="flex items-start">
                                <span class="w-24 text-gray-600">人群标签:</span>
                                <div class="flex flex-wrap gap-2">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">VIP客户</span>
                                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">高价值</span>
                                    <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm">精准营销</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计数据 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div class="el-card">
                <div class="stat-card bg-blue-50">
                    <div class="stat-number text-blue-600">1,250</div>
                    <div class="stat-label text-blue-600">导入总数</div>
                </div>
            </div>
            <div class="el-card">
                <div class="stat-card bg-green-50">
                    <div class="stat-number text-green-600">1,180</div>
                    <div class="stat-label text-green-600">有效用户</div>
                </div>
            </div>
            <div class="el-card">
                <div class="stat-card bg-orange-50">
                    <div class="stat-number text-orange-600">70</div>
                    <div class="stat-label text-orange-600">匹配失败</div>
                </div>
            </div>
            <div class="el-card">
                <div class="stat-card bg-purple-50">
                    <div class="stat-number text-purple-600">94.4%</div>
                    <div class="stat-label text-purple-600">匹配率</div>
                </div>
            </div>
        </div>

        <!-- 用户列表 -->
        <div class="el-card highlight-new">
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：人群用户列表
            </div>
            <div class="p-5 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">用户列表</h3>
                    <div class="flex items-center gap-4">
                        <div class="flex items-center gap-2">
                            <span class="text-sm text-gray-600">匹配状态:</span>
                            <select class="px-3 py-1 border border-gray-300 rounded text-sm">
                                <option value="">全部</option>
                                <option value="1">匹配成功</option>
                                <option value="2">匹配失败</option>
                            </select>
                        </div>
                        <div class="flex gap-2">
                            <button class="el-button">
                                <i class="fas fa-download mr-1"></i>导出
                            </button>
                            <button class="el-button">
                                <i class="fas fa-sync mr-1"></i>重新匹配
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="el-table">
                    <thead>
                        <tr>
                            <th class="text-center w-12">
                                <input type="checkbox" class="rounded">
                            </th>
                            <th class="text-left">导入信息</th>
                            <th class="text-center">匹配状态</th>
                            <th class="text-center">匹配字段</th>
                            <th class="text-left">系统用户</th>
                            <th class="text-center">匹配时间</th>
                            <th class="text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-left">
                                <div class="font-medium">张三</div>
                                <div class="text-sm text-gray-500">138****8001</div>
                                <div class="text-xs text-gray-400">销售部</div>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">匹配成功</span>
                            </td>
                            <td class="text-center">手机号</td>
                            <td class="text-left">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm mr-3">
                                        张
                                    </div>
                                    <div>
                                        <div class="font-medium">张三</div>
                                        <div class="text-sm text-gray-500">销售部 | ID: 10001</div>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center text-gray-500">2025-01-28 14:35</td>
                            <td class="text-center">
                                <button class="el-button text-blue-600 hover:bg-blue-50 px-2 py-1 text-sm">查看</button>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-left">
                                <div class="font-medium">李四</div>
                                <div class="text-sm text-gray-500">139****8002</div>
                                <div class="text-xs text-gray-400">市场部</div>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">匹配成功</span>
                            </td>
                            <td class="text-center">手机号</td>
                            <td class="text-left">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm mr-3">
                                        李
                                    </div>
                                    <div>
                                        <div class="font-medium">李四</div>
                                        <div class="text-sm text-gray-500">市场部 | ID: 10002</div>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center text-gray-500">2025-01-28 14:35</td>
                            <td class="text-center">
                                <button class="el-button text-blue-600 hover:bg-blue-50 px-2 py-1 text-sm">查看</button>
                            </td>
                        </tr>
                        <tr class="bg-red-50">
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-left">
                                <div class="font-medium">王五</div>
                                <div class="text-sm text-gray-500">150****8003</div>
                                <div class="text-xs text-gray-400">技术部</div>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--danger">匹配失败</span>
                            </td>
                            <td class="text-center">-</td>
                            <td class="text-left">
                                <span class="text-gray-500">未找到匹配用户</span>
                            </td>
                            <td class="text-center text-gray-500">2025-01-28 14:35</td>
                            <td class="text-center">
                                <button class="el-button text-orange-600 hover:bg-orange-50 px-2 py-1 text-sm">手动匹配</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="flex items-center justify-between p-4 border-t border-gray-200">
                <div class="text-sm text-gray-500">
                    共 1,250 条记录，第 1/42 页
                </div>
                <div class="flex items-center gap-2">
                    <button class="el-button" disabled>上一页</button>
                    <button class="el-button el-button--primary">1</button>
                    <button class="el-button">2</button>
                    <button class="el-button">3</button>
                    <span class="px-2">...</span>
                    <button class="el-button">42</button>
                    <button class="el-button">下一页</button>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="font-medium text-blue-800 mb-2">
                <i class="fas fa-info-circle mr-2"></i>功能说明
            </h3>
            <div class="text-sm text-blue-700 space-y-1">
                <p><strong>1. 基础信息：</strong>展示人群的详细信息，包括编码、类型、创建信息等</p>
                <p><strong>2. 统计数据：</strong>显示导入总数、有效用户数、匹配失败数和匹配率</p>
                <p><strong>3. 用户列表：</strong>展示人群中的所有用户，包括导入信息和匹配结果</p>
                <p><strong>4. 匹配管理：</strong>支持重新匹配、手动匹配失败的用户</p>
                <p><strong>5. 数据导出：</strong>支持导出用户列表和匹配结果</p>
            </div>
        </div>

        <!-- 开发备注 -->
        <div class="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h3 class="font-medium text-orange-800 mb-2">
                <i class="fas fa-code mr-2"></i>开发备注
            </h3>
            <div class="text-sm text-orange-700 space-y-1">
                <p><strong>API接口：</strong>GET /admin/crowd/detail/:id - 获取人群详情</p>
                <p><strong>用户列表：</strong>GET /admin/crowd/users/:id - 获取人群用户列表</p>
                <p><strong>重新匹配：</strong>POST /admin/crowd/rematch/:id - 重新执行用户匹配</p>
                <p><strong>手动匹配：</strong>POST /admin/crowd/manual-match - 手动匹配指定用户</p>
                <p><strong>数据导出：</strong>GET /admin/crowd/export/:id - 导出人群用户数据</p>
            </div>
        </div>
    </div>
</body>
</html>
