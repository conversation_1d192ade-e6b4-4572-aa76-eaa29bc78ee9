<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务列表 - 声量合伙人H5</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --van-primary-color: #3366cc;
            --van-text-color: rgba(0, 0, 0, 0.85);
            --van-text-color-2: rgba(0, 0, 0, 0.65);
            --van-text-color-3: rgba(0, 0, 0, 0.45);
            --van-border-color: #ebedf0;
            --van-background-color: #f5f5f5;
            --van-background-color-light: #fafafa;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, <PERSON><PERSON>, <PERSON><PERSON>, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
            background-color: var(--van-background-color);
            margin: 0;
            padding: 0;
        }
        
        .van-nav-bar {
            background: var(--van-primary-color);
            color: white;
            height: 46px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-size: 16px;
            font-weight: 500;
        }
        
        .van-nav-bar__left {
            position: absolute;
            left: 16px;
            color: white;
        }
        
        .van-tabs {
            background: white;
            border-bottom: 1px solid var(--van-border-color);
        }
        
        .van-tabs__nav {
            display: flex;
            padding: 0 16px;
        }
        
        .van-tab {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            color: var(--van-text-color-2);
            position: relative;
        }
        
        .van-tab.active {
            color: var(--van-primary-color);
            font-weight: 500;
        }
        
        .van-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--van-primary-color);
        }
        
        .van-cell {
            background: white;
            padding: 12px 16px;
            border-bottom: 1px solid var(--van-border-color);
            display: flex;
            align-items: center;
        }
        
        .van-cell:last-child {
            border-bottom: none;
        }
        
        .van-cell__value {
            flex: 1;
        }
        
        .van-cell__right-icon {
            color: var(--van-text-color-3);
            margin-left: 8px;
        }
        
        .van-tag {
            display: inline-block;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 2px;
            line-height: 1.2;
        }
        
        .van-tag--primary {
            background: #e6f7ff;
            color: var(--van-primary-color);
        }
        
        .van-tag--success {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .van-tag--warning {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .van-tag--danger {
            background: #fff2f0;
            color: #ff4d4f;
        }
        
        .task-card {
            background: white;
            margin: 8px 16px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        
        .task-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        
        .task-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--van-text-color);
            line-height: 1.4;
            flex: 1;
            margin-right: 12px;
        }
        
        .task-reward {
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .task-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            font-size: 12px;
            color: var(--van-text-color-2);
        }
        
        .task-desc {
            font-size: 14px;
            color: var(--van-text-color-2);
            line-height: 1.4;
            margin-bottom: 12px;
        }
        
        .task-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .task-source {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--van-text-color-3);
        }
        
        .task-action {
            background: var(--van-primary-color);
            color: white;
            padding: 6px 16px;
            border-radius: 16px;
            font-size: 14px;
            border: none;
            cursor: pointer;
        }
        
        .task-action:disabled {
            background: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);
        }
        
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -4px;
            border: 4px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.1);
            border: 2px dashed #ff4444;
            border-radius: 8px;
            position: relative;
        }
        
        .source-icon {
            width: 16px;
            height: 16px;
            border-radius: 2px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            font-weight: bold;
        }
        
        .source-org {
            background: #1890ff;
        }
        
        .source-crowd {
            background: #52c41a;
        }
        
        .source-mixed {
            background: #fa8c16;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="van-nav-bar">
        <div class="van-nav-bar__left">
            <a href="./index.html" class="text-white">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        任务列表
    </div>

    <!-- 标签页 -->
    <div class="van-tabs">
        <div class="van-tabs__nav">
            <div class="van-tab active">全部任务</div>
            <div class="van-tab">进行中</div>
            <div class="van-tab">已完成</div>
            <div class="van-tab">已过期</div>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="pb-16">
        <!-- 人群推送任务 -->
        <div class="task-card highlight-new">
            <div class="annotation" style="top: -25px; left: 16px;">
                新增：推送来源标识
            </div>
            <div class="task-header">
                <div class="task-title">VIP客户专享活动推广</div>
                <div class="task-reward">+50积分</div>
            </div>
            <div class="task-meta">
                <span><i class="fas fa-clock mr-1"></i>2025-02-01 ~ 2025-02-15</span>
                <span class="van-tag van-tag--success">进行中</span>
            </div>
            <div class="task-desc">
                针对VIP客户群体的专享活动推广，完成注册和分享即可获得积分奖励
            </div>
            <div class="task-footer">
                <div class="task-source">
                    <div class="source-icon source-crowd">群</div>
                    <span>人群推送 · VIP客户群体</span>
                </div>
                <button class="task-action">立即参与</button>
            </div>
        </div>

        <!-- 组织推送任务 -->
        <div class="task-card">
            <div class="task-header">
                <div class="task-title">品牌宣传视频分享</div>
                <div class="task-reward">+30积分</div>
            </div>
            <div class="task-meta">
                <span><i class="fas fa-clock mr-1"></i>2025-01-25 ~ 2025-02-10</span>
                <span class="van-tag van-tag--success">进行中</span>
            </div>
            <div class="task-desc">
                分享品牌宣传视频到朋友圈，并邀请好友点赞评论
            </div>
            <div class="task-footer">
                <div class="task-source">
                    <div class="source-icon source-org">部</div>
                    <span>组织推送 · 市场部</span>
                </div>
                <button class="task-action">立即参与</button>
            </div>
        </div>

        <!-- 混合推送任务 -->
        <div class="task-card highlight-new">
            <div class="annotation" style="top: -25px; left: 16px;">
                新增：混合推送标识
            </div>
            <div class="task-header">
                <div class="task-title">新春活动问卷调研</div>
                <div class="task-reward">+20积分</div>
            </div>
            <div class="task-meta">
                <span><i class="fas fa-clock mr-1"></i>2025-01-20 ~ 2025-02-05</span>
                <span class="van-tag van-tag--warning">即将截止</span>
            </div>
            <div class="task-desc">
                参与新春活动问卷调研，帮助我们了解用户需求
            </div>
            <div class="task-footer">
                <div class="task-source">
                    <div class="source-icon source-mixed">混</div>
                    <span>混合推送 · 销售部+新用户群</span>
                </div>
                <button class="task-action">立即参与</button>
            </div>
        </div>

        <!-- 已完成任务 -->
        <div class="task-card">
            <div class="task-header">
                <div class="task-title">产品体验反馈</div>
                <div class="task-reward">+40积分</div>
            </div>
            <div class="task-meta">
                <span><i class="fas fa-clock mr-1"></i>2025-01-15 ~ 2025-01-30</span>
                <span class="van-tag van-tag--primary">已完成</span>
            </div>
            <div class="task-desc">
                体验新产品功能并提交使用反馈
            </div>
            <div class="task-footer">
                <div class="task-source">
                    <div class="source-icon source-crowd">群</div>
                    <span>人群推送 · VIP客户群体</span>
                </div>
                <button class="task-action" disabled>已完成</button>
            </div>
        </div>

        <!-- 已过期任务 -->
        <div class="task-card opacity-60">
            <div class="task-header">
                <div class="task-title">年终总结分享</div>
                <div class="task-reward">+25积分</div>
            </div>
            <div class="task-meta">
                <span><i class="fas fa-clock mr-1"></i>2024-12-20 ~ 2025-01-10</span>
                <span class="van-tag van-tag--danger">已过期</span>
            </div>
            <div class="task-desc">
                分享个人年终总结，展示工作成果
            </div>
            <div class="task-footer">
                <div class="task-source">
                    <div class="source-icon source-org">部</div>
                    <span>组织推送 · 全公司</span>
                </div>
                <button class="task-action" disabled>已过期</button>
            </div>
        </div>
    </div>

    <!-- 功能说明浮层 -->
    <div class="fixed bottom-20 left-4 right-4 bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm">
        <div class="font-medium text-blue-800 mb-2">
            <i class="fas fa-info-circle mr-2"></i>功能说明
        </div>
        <div class="text-blue-700 space-y-1">
            <p><strong>推送来源：</strong>显示任务的推送来源，包括组织推送、人群推送、混合推送</p>
            <p><strong>来源标识：</strong>通过不同颜色的图标区分推送类型，便于用户识别</p>
            <p><strong>详细信息：</strong>显示具体的推送部门或人群名称</p>
        </div>
    </div>

    <!-- 开发备注浮层 -->
    <div class="fixed bottom-4 left-4 right-4 bg-orange-50 border border-orange-200 rounded-lg p-3 text-sm">
        <div class="font-medium text-orange-800 mb-2">
            <i class="fas fa-code mr-2"></i>开发备注
        </div>
        <div class="text-orange-700 space-y-1">
            <p><strong>数据字段：</strong>需要在任务数据中增加source_type和source_info字段</p>
            <p><strong>图标设计：</strong>使用不同颜色和文字标识区分推送类型</p>
            <p><strong>筛选功能：</strong>可以按推送类型筛选任务列表</p>
            <p><strong>兼容性：</strong>保持现有任务列表的所有功能不变</p>
        </div>
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.van-tab').forEach((tab, index) => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.van-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
            });
        });

        // 任务卡片点击
        document.querySelectorAll('.task-action').forEach(button => {
            button.addEventListener('click', (e) => {
                if (!button.disabled) {
                    alert('跳转到任务详情页');
                }
            });
        });

        // 隐藏说明浮层
        setTimeout(() => {
            document.querySelectorAll('.fixed').forEach(el => {
                el.style.display = 'none';
            });
        }, 10000);
    </script>
</body>
</html>
