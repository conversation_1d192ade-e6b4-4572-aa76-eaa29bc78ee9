<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 声量合伙人H5</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --van-primary-color: #3366cc;
            --van-text-color: rgba(0, 0, 0, 0.85);
            --van-text-color-2: rgba(0, 0, 0, 0.65);
            --van-text-color-3: rgba(0, 0, 0, 0.45);
            --van-border-color: #ebedf0;
            --van-background-color: #f5f5f5;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, <PERSON><PERSON>, <PERSON><PERSON>, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
            background-color: var(--van-background-color);
            margin: 0;
            padding: 0;
        }
        
        .van-nav-bar {
            background: var(--van-primary-color);
            color: white;
            height: 46px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-size: 16px;
            font-weight: 500;
        }
        
        .van-nav-bar__left {
            position: absolute;
            left: 16px;
            color: white;
        }
        
        .profile-header {
            background: linear-gradient(135deg, var(--van-primary-color), #5a7fd8);
            padding: 20px 16px;
            color: white;
        }
        
        .profile-info {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .profile-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            margin-right: 16px;
        }
        
        .profile-details h3 {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 4px 0;
        }
        
        .profile-details p {
            font-size: 14px;
            opacity: 0.8;
            margin: 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }
        
        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 12px;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .content-card {
            background: white;
            margin: 8px 16px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--van-text-color);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 8px;
            color: var(--van-primary-color);
        }
        
        .crowd-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .crowd-item:last-child {
            border-bottom: none;
        }
        
        .crowd-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: linear-gradient(135deg, #52c41a, #73d13d);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
            margin-right: 12px;
        }
        
        .crowd-info {
            flex: 1;
        }
        
        .crowd-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--van-text-color);
            margin-bottom: 2px;
        }
        
        .crowd-desc {
            font-size: 12px;
            color: var(--van-text-color-3);
        }
        
        .crowd-stats {
            text-align: right;
            font-size: 12px;
            color: var(--van-text-color-2);
        }
        
        .crowd-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
            margin-top: 2px;
        }
        
        .badge-active {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .badge-inactive {
            background: #f5f5f5;
            color: #999;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #f0f0f0;
            color: var(--van-text-color);
            text-decoration: none;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-icon {
            width: 20px;
            margin-right: 12px;
            color: var(--van-primary-color);
        }
        
        .menu-text {
            flex: 1;
            font-size: 14px;
        }
        
        .menu-arrow {
            color: var(--van-text-color-3);
        }
        
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -4px;
            border: 4px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.1);
            border: 2px dashed #ff4444;
            border-radius: 8px;
            position: relative;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="van-nav-bar">
        <div class="van-nav-bar__left">
            <a href="./index.html" class="text-white">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        个人中心
    </div>

    <!-- 个人信息头部 -->
    <div class="profile-header">
        <div class="profile-info">
            <div class="profile-avatar">张</div>
            <div class="profile-details">
                <h3>张三</h3>
                <p>销售部 | 高级销售经理</p>
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">1,250</div>
                <div class="stat-label">总积分</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">45</div>
                <div class="stat-label">完成任务</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">3</div>
                <div class="stat-label">所属人群</div>
            </div>
        </div>
    </div>

    <!-- 我的人群 -->
    <div class="content-card highlight-new">
        <div class="annotation" style="top: -25px; left: 16px;">
            新增：我的人群信息
        </div>
        <div class="section-title">
            <i class="fas fa-users"></i>
            我的人群
        </div>
        
        <div class="crowd-item">
            <div class="crowd-icon">VIP</div>
            <div class="crowd-info">
                <div class="crowd-name">VIP客户群体</div>
                <div class="crowd-desc">高价值客户精准营销 · 匹配方式：手机号</div>
            </div>
            <div class="crowd-stats">
                <div>1,180人</div>
                <div class="crowd-badge badge-active">活跃</div>
            </div>
        </div>
        
        <div class="crowd-item">
            <div class="crowd-icon">新</div>
            <div class="crowd-info">
                <div class="crowd-name">新用户推广群</div>
                <div class="crowd-desc">新注册用户引导转化 · 匹配方式：工号</div>
            </div>
            <div class="crowd-stats">
                <div>2,650人</div>
                <div class="crowd-badge badge-active">活跃</div>
            </div>
        </div>
        
        <div class="crowd-item">
            <div class="crowd-icon">销</div>
            <div class="crowd-info">
                <div class="crowd-name">销售部门推广</div>
                <div class="crowd-desc">销售团队内部推广 · 匹配方式：部门</div>
            </div>
            <div class="crowd-stats">
                <div>380人</div>
                <div class="crowd-badge badge-inactive">停用</div>
            </div>
        </div>
    </div>

    <!-- 人群任务统计 -->
    <div class="content-card highlight-new">
        <div class="annotation" style="top: -25px; left: 16px;">
            新增：人群任务统计
        </div>
        <div class="section-title">
            <i class="fas fa-chart-pie"></i>
            人群任务统计
        </div>
        
        <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-xl font-bold text-blue-600">28</div>
                <div class="text-sm text-blue-600">人群任务</div>
                <div class="text-xs text-gray-500 mt-1">来自人群推送</div>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-xl font-bold text-green-600">17</div>
                <div class="text-sm text-green-600">组织任务</div>
                <div class="text-xs text-gray-500 mt-1">来自部门推送</div>
            </div>
        </div>
        
        <div class="mt-4 p-3 bg-purple-50 rounded-lg">
            <div class="flex items-center justify-between">
                <span class="text-sm text-purple-800">人群任务完成率</span>
                <span class="text-sm font-bold text-purple-800">89.3%</span>
            </div>
            <div class="mt-2 w-full bg-purple-200 rounded-full h-2">
                <div class="bg-purple-600 h-2 rounded-full" style="width: 89.3%"></div>
            </div>
            <div class="text-xs text-purple-600 mt-1">比组织任务完成率高15.2%</div>
        </div>
    </div>

    <!-- 功能菜单 -->
    <div class="content-card">
        <div class="section-title">
            <i class="fas fa-cog"></i>
            功能菜单
        </div>
        
        <a href="#" class="menu-item">
            <i class="fas fa-tasks menu-icon"></i>
            <span class="menu-text">我的任务</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
        </a>
        
        <a href="#" class="menu-item">
            <i class="fas fa-coins menu-icon"></i>
            <span class="menu-text">积分记录</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
        </a>
        
        <a href="#" class="menu-item">
            <i class="fas fa-chart-line menu-icon"></i>
            <span class="menu-text">我的统计</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
        </a>
        
        <a href="#" class="menu-item">
            <i class="fas fa-bell menu-icon"></i>
            <span class="menu-text">消息通知</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
        </a>
        
        <a href="#" class="menu-item">
            <i class="fas fa-user-edit menu-icon"></i>
            <span class="menu-text">个人设置</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
        </a>
    </div>

    <!-- 最近活动 -->
    <div class="content-card">
        <div class="section-title">
            <i class="fas fa-history"></i>
            最近活动
        </div>
        
        <div class="space-y-3">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs mr-3">
                    <i class="fas fa-check"></i>
                </div>
                <div class="flex-1">
                    <div class="text-sm font-medium">完成VIP客户专享活动推广</div>
                    <div class="text-xs text-gray-500">来自人群推送 · 2小时前</div>
                </div>
                <div class="text-sm text-green-600">+50积分</div>
            </div>
            
            <div class="flex items-center">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs mr-3">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="flex-1">
                    <div class="text-sm font-medium">加入新用户推广群</div>
                    <div class="text-xs text-gray-500">系统匹配 · 1天前</div>
                </div>
            </div>
            
            <div class="flex items-center">
                <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs mr-3">
                    <i class="fas fa-star"></i>
                </div>
                <div class="flex-1">
                    <div class="text-sm font-medium">获得VIP客户群体认证</div>
                    <div class="text-xs text-gray-500">人群匹配 · 3天前</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能说明浮层 -->
    <div class="fixed bottom-4 left-4 right-4 bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm">
        <div class="font-medium text-blue-800 mb-2">
            <i class="fas fa-info-circle mr-2"></i>功能说明
        </div>
        <div class="text-blue-700 space-y-1">
            <p><strong>我的人群：</strong>显示用户所属的所有人群，包括人群状态和匹配方式</p>
            <p><strong>任务统计：</strong>区分人群任务和组织任务，展示不同推送方式的效果</p>
            <p><strong>活动记录：</strong>记录用户的人群相关活动，包括加入人群、完成任务等</p>
        </div>
    </div>

    <!-- 开发备注浮层 -->
    <div class="fixed bottom-20 left-4 right-4 bg-orange-50 border border-orange-200 rounded-lg p-3 text-sm">
        <div class="font-medium text-orange-800 mb-2">
            <i class="fas fa-code mr-2"></i>开发备注
        </div>
        <div class="text-orange-700 space-y-1">
            <p><strong>数据来源：</strong>从用户人群关联表获取人群信息</p>
            <p><strong>统计计算：</strong>需要实时计算用户的人群任务统计数据</p>
            <p><strong>权限控制：</strong>只显示用户有权限查看的人群信息</p>
            <p><strong>缓存策略：</strong>个人统计数据可以适当缓存提高性能</p>
        </div>
    </div>

    <script>
        // 菜单项点击
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                alert('跳转到' + item.querySelector('.menu-text').textContent);
            });
        });

        // 隐藏说明浮层
        setTimeout(() => {
            document.querySelectorAll('.fixed').forEach(el => {
                el.style.display = 'none';
            });
        }, 10000);
    </script>
</body>
</html>
