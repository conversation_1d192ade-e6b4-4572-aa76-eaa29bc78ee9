<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务统计页 - 声量合伙人</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --el-color-primary: #409eff;
            --el-color-success: #67c23a;
            --el-color-warning: #e6a23c;
            --el-color-danger: #f56c6c;
            --el-border-color: #dcdfe6;
            --el-border-color-light: #e4e7ed;
            --el-text-color-primary: #303133;
            --el-text-color-regular: #606266;
            --el-bg-color: #ffffff;
            --el-bg-color-page: #f2f3f5;
        }
        
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            background-color: var(--el-bg-color-page);
        }
        
        .el-card {
            background: var(--el-bg-color);
            border: 1px solid var(--el-border-color-light);
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
        }
        
        .el-button {
            padding: 8px 15px;
            border-radius: 4px;
            border: 1px solid var(--el-border-color);
            background: var(--el-bg-color);
            color: var(--el-text-color-regular);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .el-button--primary {
            background: var(--el-color-primary);
            border-color: var(--el-color-primary);
            color: white;
        }
        
        .el-input__inner {
            width: 100%;
            padding: 0 15px;
            border: 1px solid var(--el-border-color);
            border-radius: 4px;
            color: var(--el-text-color-regular);
            background: var(--el-bg-color);
            height: 32px;
            line-height: 32px;
        }
        
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 0.875rem;
            opacity: 0.8;
        }
        
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border: 5px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.1);
            border: 2px dashed #ff4444;
            border-radius: 4px;
            position: relative;
        }
        
        .highlight-modified {
            background: rgba(54, 162, 235, 0.1);
            border: 2px dashed #36a2eb;
            border-radius: 4px;
            position: relative;
        }
        
        .tab-button {
            padding: 8px 16px;
            border: 1px solid var(--el-border-color);
            background: var(--el-bg-color);
            color: var(--el-text-color-regular);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            background: var(--el-color-primary);
            border-color: var(--el-color-primary);
            color: white;
        }
        
        .tab-button:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }
        
        .tab-button:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
        
        .tab-button:not(:first-child) {
            border-left: none;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-4">
        <div class="px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="./index.html" class="text-blue-500 hover:text-blue-700 mr-4">
                        <i class="fas fa-arrow-left"></i> 返回导航
                    </a>
                    <h1 class="text-lg font-semibold text-gray-900">任务统计</h1>
                </div>
                <div class="text-sm text-gray-500">管理后台 > 任务管理 > 任务统计</div>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- 筛选条件 -->
        <div class="el-card mb-6 highlight-modified">
            <div class="annotation" style="top: -35px; left: 20px; background: #36a2eb;">
                改造：新增统计维度选择
            </div>
            <div class="p-5">
                <div class="flex flex-wrap items-end gap-4">
                    <div class="flex-1 min-w-48">
                        <label class="block text-sm font-medium text-gray-700 mb-1">任务名称</label>
                        <input type="text" class="el-input__inner" placeholder="请输入任务名称">
                    </div>
                    <div class="w-40">
                        <label class="block text-sm font-medium text-gray-700 mb-1">时间范围</label>
                        <select class="el-input__inner">
                            <option value="7">最近7天</option>
                            <option value="30">最近30天</option>
                            <option value="90">最近90天</option>
                        </select>
                    </div>
                    <div class="w-40">
                        <label class="block text-sm font-medium text-gray-700 mb-1">统计维度</label>
                        <div class="flex">
                            <button class="tab-button active">综合统计</button>
                            <button class="tab-button">组织维度</button>
                            <button class="tab-button">人群维度</button>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <button class="el-button el-button--primary">
                            <i class="fas fa-search mr-1"></i>查询
                        </button>
                        <button class="el-button">
                            <i class="fas fa-refresh mr-1"></i>重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总体统计 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div class="el-card">
                <div class="stat-card bg-blue-50">
                    <div class="stat-number text-blue-600">156</div>
                    <div class="stat-label text-blue-600">总任务数</div>
                </div>
            </div>
            <div class="el-card">
                <div class="stat-card bg-green-50">
                    <div class="stat-number text-green-600">12,580</div>
                    <div class="stat-label text-green-600">总推送数</div>
                </div>
            </div>
            <div class="el-card">
                <div class="stat-card bg-orange-50">
                    <div class="stat-number text-orange-600">8,945</div>
                    <div class="stat-label text-orange-600">总完成数</div>
                </div>
            </div>
            <div class="el-card">
                <div class="stat-card bg-purple-50">
                    <div class="stat-number text-purple-600">71.1%</div>
                    <div class="stat-label text-purple-600">平均完成率</div>
                </div>
            </div>
        </div>

        <!-- 推送方式对比 -->
        <div class="el-card mb-6 highlight-new">
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：推送方式效果对比
            </div>
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">推送方式效果对比</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 对比图表 -->
                    <div>
                        <div class="chart-container">
                            <canvas id="pushTypeChart"></canvas>
                        </div>
                    </div>
                    <!-- 对比数据 -->
                    <div class="space-y-4">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="font-medium text-gray-900">组织推送</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">传统模式</span>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div class="text-center">
                                    <div class="font-bold text-blue-600">8,200</div>
                                    <div class="text-gray-600">推送数</div>
                                </div>
                                <div class="text-center">
                                    <div class="font-bold text-green-600">5,740</div>
                                    <div class="text-gray-600">完成数</div>
                                </div>
                                <div class="text-center">
                                    <div class="font-bold text-purple-600">70.0%</div>
                                    <div class="text-gray-600">完成率</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="font-medium text-gray-900">人群推送</span>
                                <span class="px-2 py-1 bg-red-100 text-red-800 rounded text-sm">精准模式</span>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div class="text-center">
                                    <div class="font-bold text-blue-600">3,680</div>
                                    <div class="text-gray-600">推送数</div>
                                </div>
                                <div class="text-center">
                                    <div class="font-bold text-green-600">2,835</div>
                                    <div class="text-gray-600">完成数</div>
                                </div>
                                <div class="text-center">
                                    <div class="font-bold text-purple-600">77.0%</div>
                                    <div class="text-gray-600">完成率</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="font-medium text-gray-900">混合推送</span>
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">组合模式</span>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div class="text-center">
                                    <div class="font-bold text-blue-600">700</div>
                                    <div class="text-gray-600">推送数</div>
                                </div>
                                <div class="text-center">
                                    <div class="font-bold text-green-600">370</div>
                                    <div class="text-gray-600">完成数</div>
                                </div>
                                <div class="text-center">
                                    <div class="font-bold text-purple-600">52.9%</div>
                                    <div class="text-gray-600">完成率</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                            <div class="text-sm text-green-800">
                                <i class="fas fa-chart-line mr-2"></i>
                                <strong>效果分析：</strong>人群推送完成率比组织推送高7%，精准营销效果显著
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 人群效果排行 -->
        <div class="el-card mb-6 highlight-new">
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：人群效果排行榜
            </div>
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">人群效果排行</h3>
                    <div class="flex gap-2">
                        <button class="el-button">
                            <i class="fas fa-download mr-1"></i>导出报告
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4">排名</th>
                                <th class="text-left py-3 px-4">人群名称</th>
                                <th class="text-center py-3 px-4">任务数</th>
                                <th class="text-center py-3 px-4">推送数</th>
                                <th class="text-center py-3 px-4">完成数</th>
                                <th class="text-center py-3 px-4">完成率</th>
                                <th class="text-center py-3 px-4">平均完成时间</th>
                                <th class="text-center py-3 px-4">趋势</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-100">
                                <td class="py-3 px-4">
                                    <div class="flex items-center">
                                        <div class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-white text-sm font-bold mr-2">1</div>
                                    </div>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="font-medium">VIP客户群体</div>
                                    <div class="text-sm text-gray-500">1,180个用户</div>
                                </td>
                                <td class="text-center py-3 px-4">15</td>
                                <td class="text-center py-3 px-4">2,350</td>
                                <td class="text-center py-3 px-4">1,880</td>
                                <td class="text-center py-3 px-4">
                                    <span class="font-bold text-green-600">80.0%</span>
                                </td>
                                <td class="text-center py-3 px-4">2.3小时</td>
                                <td class="text-center py-3 px-4">
                                    <i class="fas fa-arrow-up text-green-500"></i>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-100">
                                <td class="py-3 px-4">
                                    <div class="flex items-center">
                                        <div class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center text-white text-sm font-bold mr-2">2</div>
                                    </div>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="font-medium">新用户推广群</div>
                                    <div class="text-sm text-gray-500">2,650个用户</div>
                                </td>
                                <td class="text-center py-3 px-4">8</td>
                                <td class="text-center py-3 px-4">1,330</td>
                                <td class="text-center py-3 px-4">955</td>
                                <td class="text-center py-3 px-4">
                                    <span class="font-bold text-green-600">71.8%</span>
                                </td>
                                <td class="text-center py-3 px-4">4.1小时</td>
                                <td class="text-center py-3 px-4">
                                    <i class="fas fa-arrow-up text-green-500"></i>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-100">
                                <td class="py-3 px-4">
                                    <div class="flex items-center">
                                        <div class="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm font-bold mr-2">3</div>
                                    </div>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="font-medium">销售部门推广</div>
                                    <div class="text-sm text-gray-500">380个用户</div>
                                </td>
                                <td class="text-center py-3 px-4">3</td>
                                <td class="text-center py-3 px-4">380</td>
                                <td class="text-center py-3 px-4">228</td>
                                <td class="text-center py-3 px-4">
                                    <span class="font-bold text-orange-600">60.0%</span>
                                </td>
                                <td class="text-center py-3 px-4">6.5小时</td>
                                <td class="text-center py-3 px-4">
                                    <i class="fas fa-arrow-down text-red-500"></i>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 趋势分析 -->
        <div class="el-card mb-6">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">完成率趋势分析</h3>
            </div>
            <div class="p-6">
                <div class="chart-container">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="font-medium text-blue-800 mb-2">
                <i class="fas fa-info-circle mr-2"></i>功能说明
            </h3>
            <div class="text-sm text-blue-700 space-y-1">
                <p><strong>1. 统计维度：</strong>新增人群维度统计，支持综合、组织、人群三种统计模式</p>
                <p><strong>2. 推送对比：</strong>对比不同推送方式的效果，分析精准营销价值</p>
                <p><strong>3. 人群排行：</strong>展示各人群的任务完成效果，识别高价值人群</p>
                <p><strong>4. 趋势分析：</strong>分析完成率变化趋势，优化营销策略</p>
                <p><strong>5. 数据导出：</strong>支持导出详细统计报告，便于深度分析</p>
            </div>
        </div>

        <!-- 开发备注 -->
        <div class="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h3 class="font-medium text-orange-800 mb-2">
                <i class="fas fa-code mr-2"></i>开发备注
            </h3>
            <div class="text-sm text-orange-700 space-y-1">
                <p><strong>统计算法：</strong>需要实现多维度统计算法，支持实时计算</p>
                <p><strong>图表组件：</strong>使用Chart.js或ECharts实现交互式图表</p>
                <p><strong>数据缓存：</strong>统计数据需要缓存，提高查询性能</p>
                <p><strong>导出功能：</strong>支持Excel格式导出，包含详细数据</p>
                <p><strong>权限控制：</strong>根据用户权限显示对应的统计数据</p>
            </div>
        </div>
    </div>

    <script>
        // 推送方式对比图表
        const pushTypeCtx = document.getElementById('pushTypeChart').getContext('2d');
        new Chart(pushTypeCtx, {
            type: 'doughnut',
            data: {
                labels: ['组织推送', '人群推送', '混合推送'],
                datasets: [{
                    data: [70.0, 77.0, 52.9],
                    backgroundColor: ['#409eff', '#67c23a', '#e6a23c'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // 趋势分析图表
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],
                datasets: [{
                    label: '组织推送完成率',
                    data: [68, 69, 70, 71, 70],
                    borderColor: '#409eff',
                    backgroundColor: 'rgba(64, 158, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: '人群推送完成率',
                    data: [75, 76, 78, 77, 77],
                    borderColor: '#67c23a',
                    backgroundColor: 'rgba(103, 194, 58, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 60,
                        max: 85
                    }
                }
            }
        });
    </script>
</body>
</html>
