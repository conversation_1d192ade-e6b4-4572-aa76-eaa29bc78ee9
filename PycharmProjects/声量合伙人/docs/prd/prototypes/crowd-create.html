<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人群创建页 - 声量合伙人</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --el-color-primary: #409eff;
            --el-color-success: #67c23a;
            --el-color-warning: #e6a23c;
            --el-color-danger: #f56c6c;
            --el-border-color: #dcdfe6;
            --el-border-color-light: #e4e7ed;
            --el-text-color-primary: #303133;
            --el-text-color-regular: #606266;
            --el-bg-color: #ffffff;
            --el-bg-color-page: #f2f3f5;
        }
        
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            background-color: var(--el-bg-color-page);
        }
        
        .el-card {
            background: var(--el-bg-color);
            border: 1px solid var(--el-border-color-light);
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
        }
        
        .el-form-item {
            margin-bottom: 22px;
        }
        
        .el-form-item__label {
            font-weight: 700;
            color: var(--el-text-color-regular);
            width: 150px;
            text-align: right;
            padding-right: 12px;
        }
        
        .el-input__inner, .el-textarea__inner {
            width: 100%;
            padding: 0 15px;
            border: 1px solid var(--el-border-color);
            border-radius: 4px;
            color: var(--el-text-color-regular);
            background: var(--el-bg-color);
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            height: 32px;
            line-height: 32px;
        }
        
        .el-textarea__inner {
            height: 80px;
            line-height: 1.5;
            padding: 8px 15px;
            resize: vertical;
        }
        
        .el-button {
            padding: 8px 15px;
            border-radius: 4px;
            border: 1px solid var(--el-border-color);
            background: var(--el-bg-color);
            color: var(--el-text-color-regular);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .el-button--primary {
            background: var(--el-color-primary);
            border-color: var(--el-color-primary);
            color: white;
        }
        
        .el-button--success {
            background: var(--el-color-success);
            border-color: var(--el-color-success);
            color: white;
        }
        
        .upload-area {
            border: 2px dashed var(--el-border-color);
            border-radius: 6px;
            background: #fafafa;
            text-align: center;
            padding: 40px 20px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: var(--el-color-primary);
            background: #f0f9ff;
        }
        
        .upload-area.dragover {
            border-color: var(--el-color-primary);
            background: #e6f7ff;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--el-color-primary), #79bbff);
            transition: width 0.3s ease;
        }
        
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border: 5px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.1);
            border: 2px dashed #ff4444;
            border-radius: 4px;
            position: relative;
        }
        
        .step-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .step {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .step.active .step-number {
            background: var(--el-color-primary);
            color: white;
        }
        
        .step.completed .step-number {
            background: var(--el-color-success);
            color: white;
        }
        
        .step.pending .step-number {
            background: #f0f0f0;
            color: #999;
        }
        
        .step-line {
            flex: 1;
            height: 2px;
            background: #f0f0f0;
            margin: 0 16px;
        }
        
        .step.completed + .step .step-line {
            background: var(--el-color-success);
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-4">
        <div class="px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="./crowd-list.html" class="text-blue-500 hover:text-blue-700 mr-4">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                    <h1 class="text-lg font-semibold text-gray-900">创建人群</h1>
                </div>
                <div class="text-sm text-gray-500">管理后台 > 人群管理 > 创建人群</div>
            </div>
        </div>
    </div>

    <div class="p-6 max-w-4xl mx-auto">
        <!-- 步骤指示器 -->
        <div class="el-card mb-6 highlight-new">
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：创建流程指引
            </div>
            <div class="p-6">
                <div class="step-indicator">
                    <div class="step completed">
                        <div class="step-number">1</div>
                        <span class="font-medium">基础信息</span>
                    </div>
                    <div class="step-line"></div>
                    <div class="step active">
                        <div class="step-number">2</div>
                        <span class="font-medium">导入用户</span>
                    </div>
                    <div class="step-line"></div>
                    <div class="step pending">
                        <div class="step-number">3</div>
                        <span class="font-medium">匹配结果</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 基础信息表单 -->
        <div class="el-card mb-6">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">基础信息</h3>
            </div>
            <div class="p-6">
                <form class="space-y-6">
                    <div class="el-form-item flex items-start">
                        <label class="el-form-item__label">人群名称 *</label>
                        <div class="flex-1">
                            <input type="text" class="el-input__inner" placeholder="请输入人群名称" value="VIP客户精准营销">
                            <div class="text-xs text-gray-500 mt-1">建议使用有意义的名称，便于后续管理</div>
                        </div>
                    </div>
                    
                    <div class="el-form-item flex items-start">
                        <label class="el-form-item__label">人群编码 *</label>
                        <div class="flex-1">
                            <input type="text" class="el-input__inner" placeholder="系统自动生成或手动输入" value="VIP_MARKETING_001">
                            <div class="text-xs text-gray-500 mt-1">人群的唯一标识，支持字母、数字、下划线</div>
                        </div>
                    </div>
                    
                    <div class="el-form-item flex items-start">
                        <label class="el-form-item__label">人群描述</label>
                        <div class="flex-1">
                            <textarea class="el-textarea__inner" placeholder="请输入人群描述" rows="3">高价值VIP客户群体，用于精准营销推广活动</textarea>
                            <div class="text-xs text-gray-500 mt-1">详细描述人群特征和用途</div>
                        </div>
                    </div>
                    
                    <div class="el-form-item flex items-start">
                        <label class="el-form-item__label">人群标签</label>
                        <div class="flex-1">
                            <div class="flex flex-wrap gap-2 mb-2">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">VIP客户</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">高价值</span>
                                <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">精准营销</span>
                                <button type="button" class="px-3 py-1 border border-dashed border-gray-300 rounded-full text-sm text-gray-500 hover:border-blue-500 hover:text-blue-500">
                                    + 添加标签
                                </button>
                            </div>
                            <div class="text-xs text-gray-500">标签用于快速识别和分类人群</div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Excel导入区域 -->
        <div class="el-card mb-6 highlight-new">
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：Excel导入功能
            </div>
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">导入用户数据</h3>
            </div>
            <div class="p-6">
                <!-- 模板下载 -->
                <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-blue-800">Excel模板</h4>
                            <p class="text-sm text-blue-600 mt-1">请先下载模板，按照格式填写用户数据</p>
                        </div>
                        <button class="el-button el-button--primary">
                            <i class="fas fa-download mr-2"></i>下载模板
                        </button>
                    </div>
                    <div class="mt-3 text-xs text-blue-600">
                        <p><strong>必填字段：</strong>手机号、姓名</p>
                        <p><strong>可选字段：</strong>工号、部门、备注</p>
                        <p><strong>数据限制：</strong>单次最多导入10,000条记录</p>
                    </div>
                </div>

                <!-- 文件上传区域 -->
                <div class="upload-area" id="uploadArea">
                    <div class="text-center">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                        <div class="text-lg font-medium text-gray-700 mb-2">拖拽文件到此处，或点击上传</div>
                        <div class="text-sm text-gray-500 mb-4">支持 .xlsx 和 .xls 格式，文件大小不超过10MB</div>
                        <button type="button" class="el-button el-button--primary">
                            <i class="fas fa-folder-open mr-2"></i>选择文件
                        </button>
                    </div>
                </div>

                <!-- 上传进度（隐藏状态，上传时显示） -->
                <div class="mt-6 hidden" id="uploadProgress">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">用户数据.xlsx</span>
                        <span class="text-sm text-gray-500">2.3MB</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%"></div>
                    </div>
                    <div class="flex items-center justify-between mt-2">
                        <span class="text-xs text-gray-500">上传中... 75%</span>
                        <button class="text-xs text-red-500 hover:text-red-700">取消</button>
                    </div>
                </div>

                <!-- 文件信息（上传成功后显示） -->
                <div class="mt-6" id="fileInfo">
                    <div class="border border-green-200 bg-green-50 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-file-excel text-green-600 text-xl mr-3"></i>
                                <div>
                                    <div class="font-medium text-green-800">用户数据.xlsx</div>
                                    <div class="text-sm text-green-600">文件大小：2.3MB | 上传时间：2025-01-30 14:30</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">上传成功</span>
                                <button class="text-red-500 hover:text-red-700">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据预览和匹配结果 -->
        <div class="el-card mb-6 highlight-new">
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：匹配结果预览
            </div>
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">数据预览与匹配结果</h3>
            </div>
            <div class="p-6">
                <!-- 统计信息 -->
                <div class="grid grid-cols-4 gap-4 mb-6">
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">1,250</div>
                        <div class="text-sm text-blue-600">导入总数</div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">1,180</div>
                        <div class="text-sm text-green-600">匹配成功</div>
                    </div>
                    <div class="text-center p-4 bg-orange-50 rounded-lg">
                        <div class="text-2xl font-bold text-orange-600">70</div>
                        <div class="text-sm text-orange-600">匹配失败</div>
                    </div>
                    <div class="text-center p-4 bg-purple-50 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600">94.4%</div>
                        <div class="text-sm text-purple-600">匹配率</div>
                    </div>
                </div>

                <!-- 匹配详情 -->
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <span class="font-medium text-gray-700">匹配详情</span>
                            <div class="flex gap-2">
                                <button class="text-sm text-blue-600 hover:text-blue-800">查看失败记录</button>
                                <button class="text-sm text-green-600 hover:text-green-800">导出结果</button>
                            </div>
                        </div>
                    </div>
                    <div class="max-h-64 overflow-y-auto">
                        <table class="w-full text-sm">
                            <thead class="bg-gray-50 sticky top-0">
                                <tr>
                                    <th class="px-4 py-2 text-left">导入手机号</th>
                                    <th class="px-4 py-2 text-left">导入姓名</th>
                                    <th class="px-4 py-2 text-left">匹配状态</th>
                                    <th class="px-4 py-2 text-left">匹配字段</th>
                                    <th class="px-4 py-2 text-left">系统用户</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-100">
                                    <td class="px-4 py-2">138****8001</td>
                                    <td class="px-4 py-2">张三</td>
                                    <td class="px-4 py-2">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">匹配成功</span>
                                    </td>
                                    <td class="px-4 py-2">手机号</td>
                                    <td class="px-4 py-2">张三 (销售部)</td>
                                </tr>
                                <tr class="border-b border-gray-100">
                                    <td class="px-4 py-2">139****8002</td>
                                    <td class="px-4 py-2">李四</td>
                                    <td class="px-4 py-2">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">匹配成功</span>
                                    </td>
                                    <td class="px-4 py-2">手机号</td>
                                    <td class="px-4 py-2">李四 (市场部)</td>
                                </tr>
                                <tr class="border-b border-gray-100">
                                    <td class="px-4 py-2">150****8003</td>
                                    <td class="px-4 py-2">王五</td>
                                    <td class="px-4 py-2">
                                        <span class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">匹配失败</span>
                                    </td>
                                    <td class="px-4 py-2">-</td>
                                    <td class="px-4 py-2 text-gray-500">未找到匹配用户</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center justify-between">
            <button class="el-button">
                <i class="fas fa-arrow-left mr-2"></i>上一步
            </button>
            <div class="flex gap-3">
                <button class="el-button">取消</button>
                <button class="el-button">保存草稿</button>
                <button class="el-button el-button--primary">
                    创建人群
                    <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="font-medium text-blue-800 mb-2">
                <i class="fas fa-info-circle mr-2"></i>功能说明
            </h3>
            <div class="text-sm text-blue-700 space-y-1">
                <p><strong>1. 基础信息：</strong>填写人群的基本信息，包括名称、编码、描述和标签</p>
                <p><strong>2. Excel导入：</strong>支持拖拽上传或点击选择Excel文件，自动解析用户数据</p>
                <p><strong>3. 用户匹配：</strong>自动匹配导入用户与系统现有用户，支持手机号、工号、姓名匹配</p>
                <p><strong>4. 结果预览：</strong>实时显示匹配结果和统计信息，支持查看详情和导出</p>
                <p><strong>5. 分步创建：</strong>支持保存草稿，可以分步骤完成人群创建</p>
            </div>
        </div>

        <!-- 开发备注 -->
        <div class="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h3 class="font-medium text-orange-800 mb-2">
                <i class="fas fa-code mr-2"></i>开发备注
            </h3>
            <div class="text-sm text-orange-700 space-y-1">
                <p><strong>文件上传：</strong>支持拖拽上传，需要验证文件格式和大小</p>
                <p><strong>异步处理：</strong>大文件导入需要异步处理，显示进度条</p>
                <p><strong>用户匹配：</strong>匹配算法按优先级：手机号 > 工号 > 姓名</p>
                <p><strong>数据验证：</strong>需要验证Excel格式、必填字段、数据重复等</p>
                <p><strong>错误处理：</strong>上传失败、匹配失败等异常情况的友好提示</p>
            </div>
        </div>
    </div>
</body>
</html>
