<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人群列表页 - 声量合伙人</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Element Plus 原生CSS变量 */
        :root {
            --el-color-primary: #409eff;
            --el-color-primary-light-3: #79bbff;
            --el-color-primary-light-5: #a0cfff;
            --el-color-primary-light-7: #c6e2ff;
            --el-color-primary-light-8: #d9ecff;
            --el-color-primary-light-9: #ecf5ff;
            --el-color-success: #67c23a;
            --el-color-warning: #e6a23c;
            --el-color-danger: #f56c6c;
            --el-color-info: #909399;
            --el-border-color: #dcdfe6;
            --el-border-color-light: #e4e7ed;
            --el-border-color-lighter: #ebeef5;
            --el-border-color-extra-light: #f2f6fc;
            --el-text-color-primary: #303133;
            --el-text-color-regular: #606266;
            --el-text-color-secondary: #909399;
            --el-text-color-placeholder: #a8abb2;
            --el-text-color-disabled: #c0c4cc;
            --el-bg-color: #ffffff;
            --el-bg-color-page: #f2f3f5;
            --el-bg-color-overlay: #ffffff;
            --el-fill-color: #f0f2f5;
            --el-fill-color-light: #f5f7fa;
            --el-fill-color-lighter: #fafafa;
            --el-fill-color-extra-light: #fafcff;
            --el-fill-color-dark: #ebedf0;
            --el-fill-color-darker: #e6e8eb;
            --el-fill-color-blank: #ffffff;
            /* RuoYi 自定义变量 */
            --tableHeaderBg: #f8f8f9;
            --tableHeaderTextColor: #515a6e;
            --brder-color: #e8e8e8;
        }

        /* 基础样式 - 完全按照RuoYi-Vue-Plus风格 */
        body {
            height: 100%;
            margin: 0;
            -moz-osx-font-smoothing: grayscale;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
            background-color: var(--el-bg-color-page);
        }

        /* RuoYi 通用样式类 */
        .p-2 { padding: 8px; }
        .mb-10 { margin-bottom: 10px; }
        .mb5 { margin-bottom: 5px; }
        .mr10 { margin-right: 10px; }
        .ml10 { margin-left: 10px; }

        /* Element Plus 卡片样式 */
        .el-card {
            background: var(--el-bg-color);
            border: 1px solid var(--el-border-color-light);
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
            overflow: hidden;
        }

        .el-card__header {
            padding: 18px 20px;
            border-bottom: 1px solid var(--el-border-color-lighter);
            box-sizing: border-box;
        }

        .el-card__body {
            padding: 20px;
        }

        /* Element Plus 按钮样式 */
        .el-button {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            line-height: 1;
            height: 32px;
            white-space: nowrap;
            cursor: pointer;
            color: var(--el-text-color-regular);
            text-align: center;
            box-sizing: border-box;
            outline: none;
            transition: 0.1s;
            font-weight: 500;
            user-select: none;
            vertical-align: middle;
            -webkit-appearance: none;
            background-color: var(--el-bg-color);
            border: 1px solid var(--el-border-color);
            border-radius: 4px;
            padding: 8px 15px;
            font-size: 14px;
        }

        .el-button:hover {
            color: var(--el-color-primary);
            border-color: var(--el-color-primary-light-7);
            background-color: var(--el-color-primary-light-9);
        }

        .el-button--primary {
            color: #ffffff;
            background-color: var(--el-color-primary);
            border-color: var(--el-color-primary);
        }

        .el-button--primary:hover {
            background: var(--el-color-primary-light-3);
            border-color: var(--el-color-primary-light-3);
            color: #ffffff;
        }

        .el-button--success {
            color: #ffffff;
            background-color: var(--el-color-success);
            border-color: var(--el-color-success);
        }

        .el-button--warning {
            color: #ffffff;
            background-color: var(--el-color-warning);
            border-color: var(--el-color-warning);
        }

        /* Element Plus 输入框样式 */
        .el-input {
            position: relative;
            font-size: 14px;
            display: inline-block;
            width: 100%;
        }

        .el-input__wrapper {
            display: inline-flex;
            flex-grow: 1;
            align-items: center;
            justify-content: center;
            padding: 1px 11px;
            background-color: var(--el-fill-color-blank);
            background-image: none;
            border-radius: 4px;
            cursor: text;
            transition: box-shadow 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            transform: translate3d(0, 0, 0);
            box-shadow: 0 0 0 1px var(--el-border-color) inset;
        }

        .el-input__inner {
            width: 100%;
            flex-grow: 1;
            -webkit-appearance: none;
            color: var(--el-text-color-regular);
            font-size: inherit;
            height: 30px;
            line-height: 30px;
            padding: 0;
            outline: none;
            border: none;
            background: none;
            box-sizing: border-box;
        }

        .el-input__inner::placeholder {
            color: var(--el-text-color-placeholder);
        }

        /* Element Plus 表格样式 */
        .el-table {
            position: relative;
            overflow: hidden;
            box-sizing: border-box;
            flex: 1;
            width: 100%;
            max-width: 100%;
            background-color: var(--el-bg-color);
            font-size: 14px;
            color: var(--el-text-color-regular);
        }

        .el-table__header-wrapper {
            overflow: hidden;
        }

        .el-table__header {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border: 0;
        }

        .el-table__header th {
            background-color: var(--tableHeaderBg);
            color: var(--tableHeaderTextColor);
            font-weight: 500;
            border-bottom: 1px solid var(--brder-color);
            border-right: 1px solid var(--brder-color);
            padding: 12px 0;
            text-align: left;
            vertical-align: middle;
            position: relative;
            word-break: break-word;
            word-wrap: break-word;
        }

        .el-table__header th:last-child {
            border-right: none;
        }

        .el-table__body {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border: 0;
        }

        .el-table__body td {
            border-bottom: 1px solid var(--brder-color);
            border-right: 1px solid var(--brder-color);
            padding: 12px 0;
            vertical-align: middle;
            position: relative;
            word-break: break-word;
            word-wrap: break-word;
        }

        .el-table__body td:last-child {
            border-right: none;
        }

        .el-table__body tr:hover > td {
            background-color: var(--el-fill-color-light);
        }

        /* Element Plus 标签样式 */
        .el-tag {
            background-color: var(--el-color-info-light-9);
            border: 1px solid var(--el-color-info-light-8);
            color: var(--el-color-info);
            display: inline-block;
            height: 24px;
            padding: 0 8px;
            line-height: 22px;
            font-size: 12px;
            border-radius: 4px;
            box-sizing: border-box;
            white-space: nowrap;
        }

        .el-tag--success {
            background-color: var(--el-color-success-light-9);
            border-color: var(--el-color-success-light-8);
            color: var(--el-color-success);
        }

        .el-tag--warning {
            background-color: var(--el-color-warning-light-9);
            border-color: var(--el-color-warning-light-8);
            color: var(--el-color-warning);
        }

        .el-tag--danger {
            background-color: var(--el-color-danger-light-9);
            border-color: var(--el-color-danger-light-8);
            color: var(--el-color-danger);
        }

        .el-tag--info {
            background-color: var(--el-color-info-light-9);
            border-color: var(--el-color-info-light-8);
            color: var(--el-color-info);
        }

        /* Element Plus 分页样式 */
        .el-pagination {
            white-space: nowrap;
            padding: 2px 5px;
            color: var(--el-text-color-regular);
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .el-pagination__total {
            margin-right: 10px;
            font-weight: 400;
            color: var(--el-text-color-regular);
        }

        .el-pager {
            user-select: none;
            list-style: none;
            display: inline-block;
            vertical-align: top;
            font-size: 0;
            padding: 0;
            margin: 0;
        }

        .el-pager li {
            padding: 0 4px;
            background: var(--el-bg-color);
            vertical-align: top;
            display: inline-block;
            font-size: 13px;
            min-width: 35.5px;
            height: 28px;
            line-height: 28px;
            cursor: pointer;
            box-sizing: border-box;
            text-align: center;
            margin: 0;
            border: 1px solid var(--el-border-color);
            border-right: 0;
        }

        .el-pager li:last-child {
            border-right: 1px solid var(--el-border-color);
        }

        .el-pager li.is-active {
            color: var(--el-color-primary);
            cursor: default;
            background-color: var(--el-color-primary-light-9);
            border-color: var(--el-color-primary-light-7);
        }
        
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border: 5px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.1);
            border: 2px dashed #ff4444;
            border-radius: 4px;
            position: relative;
        }
    </style>
</head>
<body>
    <!-- RuoYi 标准页面容器 -->
    <div class="app-container">
        <!-- 面包屑导航 -->
        <div style="margin-bottom: 10px;">
            <span style="color: var(--el-text-color-secondary); font-size: 12px;">
                <a href="./index.html" style="color: var(--el-color-primary); text-decoration: none;">
                    <i class="fas fa-arrow-left" style="margin-right: 5px;"></i>返回导航
                </a>
                <span style="margin: 0 8px;">/</span>
                人群管理
                <span style="margin: 0 8px;">/</span>
                人群列表
            </span>
        </div>

        <!-- 页面内容 -->
        <div class="p-2">
            <!-- 搜索表单 -->
            <div class="el-card mb-10">
                <div class="el-card__body">
                    <form class="el-form el-form--inline">
                        <div class="el-form-item" style="margin-right: 20px;">
                            <label class="el-form-item__label" style="width: 80px;">人群名称</label>
                            <div class="el-form-item__content">
                                <div class="el-input" style="width: 200px;">
                                    <div class="el-input__wrapper">
                                        <input class="el-input__inner" type="text" placeholder="请输入人群名称">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="el-form-item" style="margin-right: 20px;">
                            <label class="el-form-item__label" style="width: 80px;">人群状态</label>
                            <div class="el-form-item__content">
                                <div class="el-select" style="width: 120px;">
                                    <div class="el-input el-input--suffix">
                                        <div class="el-input__wrapper">
                                            <input class="el-input__inner" readonly placeholder="全部状态" style="cursor: pointer;">
                                            <span class="el-input__suffix">
                                                <i class="el-select__caret el-input__icon fas fa-angle-down"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="el-form-item" style="margin-right: 20px;">
                            <label class="el-form-item__label" style="width: 80px;">人群类型</label>
                            <div class="el-form-item__content">
                                <div class="el-select" style="width: 120px;">
                                    <div class="el-input el-input--suffix">
                                        <div class="el-input__wrapper">
                                            <input class="el-input__inner" readonly placeholder="全部类型" style="cursor: pointer;">
                                            <span class="el-input__suffix">
                                                <i class="el-select__caret el-input__icon fas fa-angle-down"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="el-form-item">
                            <div class="el-form-item__content">
                                <button type="button" class="el-button el-button--primary mr10">
                                    <i class="fas fa-search" style="margin-right: 5px;"></i>查询
                                </button>
                                <button type="button" class="el-button">
                                    <i class="fas fa-refresh" style="margin-right: 5px;"></i>重置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 表格操作区域 -->
            <div class="el-card highlight-new">
                <!-- 新增功能标注 -->
                <div class="annotation" style="top: -35px; left: 20px;">
                    新增：人群管理功能
                </div>

                <div class="el-card__header">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <button class="el-button el-button--primary mr10">
                                <i class="fas fa-plus" style="margin-right: 5px;"></i>新建人群
                            </button>
                            <button class="el-button el-button--success mr10">
                                <i class="fas fa-download" style="margin-right: 5px;"></i>导出
                            </button>
                            <button class="el-button el-button--warning mr10">
                                <i class="fas fa-upload" style="margin-right: 5px;"></i>批量导入
                            </button>
                        </div>
                        <div>
                            <button class="el-button" title="刷新">
                                <i class="fas fa-refresh"></i>
                            </button>
                            <button class="el-button ml10" title="列设置">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 表格内容 -->
                <div class="el-card__body" style="padding: 0;">
                    <div class="el-table">
                        <div class="el-table__header-wrapper">
                            <table class="el-table__header">
                                <thead>
                                    <tr>
                                        <th style="width: 50px; text-align: center; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">
                                                <label class="el-checkbox">
                                                    <input type="checkbox" class="el-checkbox__original">
                                                    <span class="el-checkbox__inner"></span>
                                                </label>
                                            </div>
                                        </th>
                                        <th style="width: 80px; text-align: center; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">ID</div>
                                        </th>
                                        <th style="min-width: 200px; text-align: left; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">人群名称</div>
                                        </th>
                                        <th style="width: 150px; text-align: center; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">人群编码</div>
                                        </th>
                                        <th style="width: 100px; text-align: center; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">人群类型</div>
                                        </th>
                                        <th style="width: 100px; text-align: center; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">总用户数</div>
                                        </th>
                                        <th style="width: 100px; text-align: center; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">有效用户数</div>
                                        </th>
                                        <th style="width: 80px; text-align: center; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">匹配率</div>
                                        </th>
                                        <th style="width: 80px; text-align: center; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">状态</div>
                                        </th>
                                        <th style="width: 150px; text-align: center; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">创建时间</div>
                                        </th>
                                        <th style="width: 200px; text-align: center; padding-left: 10px; padding-right: 10px;">
                                            <div class="cell">操作</div>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    <tbody>
                        <tr>
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-center">1001</td>
                            <td class="text-left">
                                <div class="font-medium">VIP客户群体</div>
                                <div class="text-xs text-gray-500">高价值客户精准营销</div>
                            </td>
                            <td class="text-center">VIP_CUSTOMERS_001</td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">Excel导入</span>
                            </td>
                            <td class="text-center">1,250</td>
                            <td class="text-center text-green-600 font-medium">1,180</td>
                            <td class="text-center">
                                <span class="text-green-600 font-medium">94.4%</span>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">正常</span>
                            </td>
                            <td class="text-center text-gray-500">2025-01-28 14:30</td>
                            <td class="text-center">
                                <div class="flex justify-center gap-1">
                                    <button class="el-button text-blue-600 hover:bg-blue-50 px-2 py-1 text-sm">查看</button>
                                    <button class="el-button text-green-600 hover:bg-green-50 px-2 py-1 text-sm">编辑</button>
                                    <button class="el-button text-orange-600 hover:bg-orange-50 px-2 py-1 text-sm">统计</button>
                                    <button class="el-button text-red-600 hover:bg-red-50 px-2 py-1 text-sm">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-center">1002</td>
                            <td class="text-left">
                                <div class="font-medium">新用户推广群</div>
                                <div class="text-xs text-gray-500">新注册用户引导转化</div>
                            </td>
                            <td class="text-center">NEW_USERS_002</td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">Excel导入</span>
                            </td>
                            <td class="text-center">2,800</td>
                            <td class="text-center text-green-600 font-medium">2,650</td>
                            <td class="text-center">
                                <span class="text-green-600 font-medium">94.6%</span>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">正常</span>
                            </td>
                            <td class="text-center text-gray-500">2025-01-27 09:15</td>
                            <td class="text-center">
                                <div class="flex justify-center gap-1">
                                    <button class="el-button text-blue-600 hover:bg-blue-50 px-2 py-1 text-sm">查看</button>
                                    <button class="el-button text-green-600 hover:bg-green-50 px-2 py-1 text-sm">编辑</button>
                                    <button class="el-button text-orange-600 hover:bg-orange-50 px-2 py-1 text-sm">统计</button>
                                    <button class="el-button text-red-600 hover:bg-red-50 px-2 py-1 text-sm">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-center">1003</td>
                            <td class="text-left">
                                <div class="font-medium">销售部门推广</div>
                                <div class="text-xs text-gray-500">销售团队内部推广</div>
                            </td>
                            <td class="text-center">SALES_TEAM_003</td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">Excel导入</span>
                            </td>
                            <td class="text-center">450</td>
                            <td class="text-center text-orange-600 font-medium">380</td>
                            <td class="text-center">
                                <span class="text-orange-600 font-medium">84.4%</span>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--warning">停用</span>
                            </td>
                            <td class="text-center text-gray-500">2025-01-25 16:45</td>
                            <td class="text-center">
                                <div class="flex justify-center gap-1">
                                    <button class="el-button text-blue-600 hover:bg-blue-50 px-2 py-1 text-sm">查看</button>
                                    <button class="el-button text-green-600 hover:bg-green-50 px-2 py-1 text-sm">编辑</button>
                                    <button class="el-button text-orange-600 hover:bg-orange-50 px-2 py-1 text-sm">统计</button>
                                    <button class="el-button text-red-600 hover:bg-red-50 px-2 py-1 text-sm">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="bg-yellow-50">
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-center">1004</td>
                            <td class="text-left">
                                <div class="font-medium">测试人群数据</div>
                                <div class="text-xs text-gray-500">导入处理中...</div>
                            </td>
                            <td class="text-center">TEST_CROWD_004</td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">Excel导入</span>
                            </td>
                            <td class="text-center">1,000</td>
                            <td class="text-center text-gray-500">处理中</td>
                            <td class="text-center">
                                <div class="flex items-center justify-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">65%</span>
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--warning">处理中</span>
                            </td>
                            <td class="text-center text-gray-500">2025-01-30 10:20</td>
                            <td class="text-center">
                                <div class="flex justify-center gap-1">
                                    <button class="el-button text-blue-600 hover:bg-blue-50 px-2 py-1 text-sm">查看进度</button>
                                    <button class="el-button text-red-600 hover:bg-red-50 px-2 py-1 text-sm">取消</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="flex items-center justify-between p-4 border-t border-gray-200">
                <div class="text-sm text-gray-500">
                    共 4 条记录，第 1/1 页
                </div>
                <div class="flex items-center gap-2">
                    <button class="el-button" disabled>上一页</button>
                    <button class="el-button el-button--primary">1</button>
                    <button class="el-button" disabled>下一页</button>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="font-medium text-blue-800 mb-2">
                <i class="fas fa-info-circle mr-2"></i>功能说明
            </h3>
            <div class="text-sm text-blue-700 space-y-1">
                <p><strong>1. 人群列表：</strong>显示所有创建的人群，包括基础信息、用户统计、匹配率等</p>
                <p><strong>2. 搜索筛选：</strong>支持按人群名称、状态、类型进行筛选查询</p>
                <p><strong>3. 批量操作：</strong>支持批量导入、导出、删除等操作</p>
                <p><strong>4. 实时状态：</strong>显示导入进度，处理中的人群会实时更新状态</p>
                <p><strong>5. 匹配率：</strong>显示导入用户与系统用户的匹配成功率</p>
            </div>
        </div>

        <!-- 开发备注 -->
        <div class="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h3 class="font-medium text-orange-800 mb-2">
                <i class="fas fa-code mr-2"></i>开发备注
            </h3>
            <div class="text-sm text-orange-700 space-y-1">
                <p><strong>API接口：</strong>GET /admin/crowd/list - 获取人群列表</p>
                <p><strong>权限控制：</strong>需要 crowd:list 权限</p>
                <p><strong>分页参数：</strong>pageNum, pageSize, crowdName, status, type</p>
                <p><strong>实时更新：</strong>导入进度需要通过WebSocket或轮询更新</p>
                <p><strong>表格排序：</strong>支持按创建时间、用户数量等字段排序</p>
            </div>
        </div>
    </div>
</body>
</html>
