# 人群功能数据库设计方案

## 🎯 设计目标

### 核心原则
- **数据一致性**: 确保人群数据与用户数据的一致性
- **查询性能**: 优化高频查询的性能表现
- **扩展性**: 支持未来功能的扩展需求
- **数据安全**: 保护用户隐私和数据安全

### 设计约束
- 兼容现有数据库结构
- 支持多租户架构
- 支持大数据量存储和查询
- 遵循现有命名规范

## 📊 数据模型设计

### 实体关系图 (ERD)
```mermaid
erDiagram
    CROWD_BASIC ||--o{ CROWD_USER_RELATION : "包含"
    CROWD_BASIC ||--o{ TASK_CROWD_RELATION : "关联"
    TASK_BASIC ||--o{ TASK_CROWD_RELATION : "使用"
    MEMBER_USER ||--o{ CROWD_USER_RELATION : "匹配"
    MEMBER_USER ||--o{ MEMBER_TASK : "执行"
    TASK_BASIC ||--o{ MEMBER_TASK : "分配"
    
    CROWD_BASIC {
        bigint id PK
        varchar crowd_name
        varchar crowd_code UK
        varchar crowd_desc
        tinyint crowd_type
        tinyint crowd_status
        int total_count
        int valid_count
        int invalid_count
        varchar import_file_name
        varchar import_file_url
        json tags
        varchar tenant_id
        datetime create_time
    }
    
    CROWD_USER_RELATION {
        bigint id PK
        bigint crowd_id FK
        bigint user_id FK
        varchar import_phone
        varchar import_name
        varchar import_employee_no
        tinyint match_status
        varchar match_field
        datetime match_time
        tinyint is_valid
        varchar remark
    }
    
    TASK_CROWD_RELATION {
        bigint id PK
        bigint task_id FK
        bigint crowd_id FK
        tinyint push_type
        datetime create_time
    }
```

## 🗄️ 详细表结构设计

### 1. 人群基础信息表 (crowd_basic)

#### 表结构定义
```sql
CREATE TABLE `crowd_basic` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '人群ID',
  `crowd_name` varchar(100) NOT NULL COMMENT '人群名称',
  `crowd_code` varchar(50) NOT NULL COMMENT '人群编码',
  `crowd_desc` varchar(500) DEFAULT NULL COMMENT '人群描述',
  `crowd_type` tinyint(4) DEFAULT '1' COMMENT '人群类型（1:Excel导入 2:条件筛选 3:API导入）',
  `crowd_status` tinyint(4) DEFAULT '1' COMMENT '人群状态（1:正常 2:停用 3:删除）',
  `total_count` int(11) DEFAULT '0' COMMENT '总用户数',
  `valid_count` int(11) DEFAULT '0' COMMENT '有效用户数',
  `invalid_count` int(11) DEFAULT '0' COMMENT '无效用户数',
  `import_file_name` varchar(255) DEFAULT NULL COMMENT '导入文件名',
  `import_file_url` varchar(500) DEFAULT NULL COMMENT '导入文件地址',
  `import_status` tinyint(4) DEFAULT '0' COMMENT '导入状态（0:待处理 1:处理中 2:完成 3:失败）',
  `import_progress` int(11) DEFAULT '0' COMMENT '导入进度（0-100）',
  `import_error_msg` varchar(1000) DEFAULT NULL COMMENT '导入错误信息',
  `tags` json DEFAULT NULL COMMENT '人群标签（JSON格式）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_crowd_code_tenant` (`crowd_code`, `tenant_id`),
  KEY `idx_crowd_status` (`crowd_status`),
  KEY `idx_crowd_type` (`crowd_type`),
  KEY `idx_crowd_create_time` (`create_time`),
  KEY `idx_crowd_create_by` (`create_by`),
  KEY `idx_crowd_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人群基础信息表';
```

#### 字段说明
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | NOT NULL, AUTO_INCREMENT |
| crowd_name | varchar(100) | 人群名称 | NOT NULL |
| crowd_code | varchar(50) | 人群编码 | NOT NULL, 租户内唯一 |
| crowd_desc | varchar(500) | 人群描述 | 可选 |
| crowd_type | tinyint(4) | 人群类型 | 1:Excel导入 2:条件筛选 3:API导入 |
| crowd_status | tinyint(4) | 人群状态 | 1:正常 2:停用 3:删除 |
| total_count | int(11) | 总用户数 | 默认0 |
| valid_count | int(11) | 有效用户数 | 默认0 |
| invalid_count | int(11) | 无效用户数 | 默认0 |
| import_file_name | varchar(255) | 导入文件名 | 可选 |
| import_file_url | varchar(500) | 导入文件地址 | 可选 |
| import_status | tinyint(4) | 导入状态 | 0:待处理 1:处理中 2:完成 3:失败 |
| import_progress | int(11) | 导入进度 | 0-100 |
| tags | json | 人群标签 | JSON格式存储 |

### 2. 人群用户关联表 (crowd_user_relation)

#### 表结构定义
```sql
CREATE TABLE `crowd_user_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `crowd_id` bigint(20) NOT NULL COMMENT '人群ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '系统用户ID（匹配成功时填充）',
  `import_phone` varchar(11) NOT NULL COMMENT '导入的手机号',
  `import_name` varchar(50) DEFAULT NULL COMMENT '导入的姓名',
  `import_employee_no` varchar(50) DEFAULT NULL COMMENT '导入的工号',
  `import_dept_name` varchar(100) DEFAULT NULL COMMENT '导入的部门名称',
  `match_status` tinyint(4) DEFAULT '0' COMMENT '匹配状态（0:未匹配 1:匹配成功 2:匹配失败）',
  `match_field` varchar(20) DEFAULT NULL COMMENT '匹配字段（phone/name/employee_no）',
  `match_time` datetime DEFAULT NULL COMMENT '匹配时间',
  `match_score` decimal(3,2) DEFAULT NULL COMMENT '匹配得分（0.00-1.00）',
  `is_valid` tinyint(4) DEFAULT '1' COMMENT '是否有效（1:有效 0:无效）',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_crowd_user_crowd` (`crowd_id`),
  KEY `idx_crowd_user_user` (`user_id`),
  KEY `idx_crowd_user_phone` (`import_phone`),
  KEY `idx_crowd_user_match` (`match_status`),
  KEY `idx_crowd_user_valid` (`is_valid`),
  CONSTRAINT `fk_crowd_user_crowd` FOREIGN KEY (`crowd_id`) REFERENCES `crowd_basic` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人群用户关联表';
```

#### 分区策略
```sql
-- 按人群ID进行分区，提高查询性能
ALTER TABLE `crowd_user_relation` 
PARTITION BY HASH(`crowd_id`) PARTITIONS 8;
```

### 3. 任务人群关联表 (task_crowd_relation)

#### 表结构定义
```sql
CREATE TABLE `task_crowd_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `crowd_id` bigint(20) NOT NULL COMMENT '人群ID',
  `push_type` tinyint(4) DEFAULT '1' COMMENT '推送类型（1:仅人群 2:人群+组织）',
  `target_user_count` int(11) DEFAULT '0' COMMENT '目标用户数量',
  `actual_push_count` int(11) DEFAULT '0' COMMENT '实际推送数量',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_crowd` (`task_id`, `crowd_id`),
  KEY `idx_task_crowd_task` (`task_id`),
  KEY `idx_task_crowd_crowd` (`crowd_id`),
  KEY `idx_task_crowd_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务人群关联表';
```

### 4. 人群统计汇总表 (crowd_statistics_summary)

#### 表结构定义
```sql
CREATE TABLE `crowd_statistics_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `crowd_id` bigint(20) NOT NULL COMMENT '人群ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_tasks` int(11) DEFAULT '0' COMMENT '总任务数',
  `active_users` int(11) DEFAULT '0' COMMENT '活跃用户数',
  `total_receives` int(11) DEFAULT '0' COMMENT '总领取数',
  `total_submits` int(11) DEFAULT '0' COMMENT '总提交数',
  `total_completes` int(11) DEFAULT '0' COMMENT '总完成数',
  `receive_rate` decimal(5,2) DEFAULT '0.00' COMMENT '领取率',
  `complete_rate` decimal(5,2) DEFAULT '0.00' COMMENT '完成率',
  `total_rewards` decimal(10,2) DEFAULT '0.00' COMMENT '总奖励积分',
  `avg_complete_time` int(11) DEFAULT '0' COMMENT '平均完成时间(分钟)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_crowd_stat_date` (`crowd_id`, `stat_date`),
  KEY `idx_crowd_stat_crowd` (`crowd_id`),
  KEY `idx_crowd_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人群统计汇总表';
```

## 🔧 现有表结构改造

### 1. 任务基础信息表改造 (task_basic)

#### 新增字段
```sql
-- 增加人群推送相关字段
ALTER TABLE `task_basic` 
ADD COLUMN `push_target_type` tinyint(4) DEFAULT '1' COMMENT '推送目标类型（1:组织 2:人群 3:组织+人群）' AFTER `push_range`,
ADD COLUMN `crowd_ids` json DEFAULT NULL COMMENT '关联人群ID列表（JSON格式）' AFTER `push_target_type`,
ADD COLUMN `crowd_user_count` int(11) DEFAULT '0' COMMENT '人群推送用户数' AFTER `crowd_ids`,
ADD COLUMN `total_target_count` int(11) DEFAULT '0' COMMENT '总目标用户数（去重后）' AFTER `crowd_user_count`;

-- 添加索引
ALTER TABLE `task_basic` ADD INDEX `idx_task_push_target_type` (`push_target_type`);
```

### 2. 用户任务表改造 (member_task)

#### 新增字段
```sql
-- 增加来源标识字段
ALTER TABLE `member_task`
ADD COLUMN `source_type` tinyint(4) DEFAULT '1' COMMENT '来源类型（1:组织推送 2:人群推送）' AFTER `dept_id`,
ADD COLUMN `source_crowd_id` bigint(20) DEFAULT NULL COMMENT '来源人群ID' AFTER `source_type`,
ADD COLUMN `complete_duration` int(11) DEFAULT '0' COMMENT '完成耗时(分钟)' AFTER `examine_time`;

-- 添加索引
ALTER TABLE `member_task` ADD INDEX `idx_member_task_source` (`source_type`, `source_crowd_id`);
ALTER TABLE `member_task` ADD INDEX `idx_member_task_crowd` (`source_crowd_id`);
```

## 📈 索引优化策略

### 1. 主要查询场景分析

#### 高频查询场景
```sql
-- 1. 人群列表查询（按状态、类型、创建时间）
SELECT * FROM crowd_basic 
WHERE crowd_status = 1 AND tenant_id = '000000' 
ORDER BY create_time DESC;

-- 2. 人群用户列表查询（按人群ID、匹配状态）
SELECT * FROM crowd_user_relation 
WHERE crowd_id = ? AND match_status = 1 AND is_valid = 1;

-- 3. 任务推送用户查询（按任务ID获取人群用户）
SELECT cur.user_id FROM task_crowd_relation tcr
JOIN crowd_user_relation cur ON tcr.crowd_id = cur.crowd_id
WHERE tcr.task_id = ? AND cur.match_status = 1 AND cur.is_valid = 1;

-- 4. 人群统计查询（按人群ID、日期范围）
SELECT * FROM crowd_statistics_summary 
WHERE crowd_id = ? AND stat_date BETWEEN ? AND ?;
```

### 2. 复合索引设计

#### crowd_basic表索引
```sql
-- 列表查询优化
CREATE INDEX `idx_crowd_status_tenant_time` ON `crowd_basic` (`crowd_status`, `tenant_id`, `create_time`);

-- 搜索查询优化
CREATE INDEX `idx_crowd_name_status` ON `crowd_basic` (`crowd_name`, `crowd_status`);

-- 创建者查询优化
CREATE INDEX `idx_crowd_create_by_time` ON `crowd_basic` (`create_by`, `create_time`);
```

#### crowd_user_relation表索引
```sql
-- 人群用户查询优化
CREATE INDEX `idx_crowd_match_valid` ON `crowd_user_relation` (`crowd_id`, `match_status`, `is_valid`);

-- 用户匹配查询优化
CREATE INDEX `idx_phone_match_status` ON `crowd_user_relation` (`import_phone`, `match_status`);

-- 统计查询优化
CREATE INDEX `idx_crowd_valid_match_time` ON `crowd_user_relation` (`crowd_id`, `is_valid`, `match_time`);
```

#### task_crowd_relation表索引
```sql
-- 任务人群查询优化
CREATE INDEX `idx_task_push_type` ON `task_crowd_relation` (`task_id`, `push_type`);

-- 人群任务查询优化
CREATE INDEX `idx_crowd_create_time` ON `task_crowd_relation` (`crowd_id`, `create_time`);
```

## 🔄 数据迁移方案

### 1. 迁移脚本设计

#### 创建新表
```sql
-- 1. 创建人群相关表
SOURCE /path/to/create_crowd_tables.sql;

-- 2. 修改现有表结构
SOURCE /path/to/alter_existing_tables.sql;

-- 3. 创建索引
SOURCE /path/to/create_indexes.sql;
```

#### 数据初始化
```sql
-- 初始化现有任务的推送目标类型
UPDATE task_basic SET push_target_type = 1 WHERE push_target_type IS NULL;

-- 初始化现有用户任务的来源类型
UPDATE member_task SET source_type = 1 WHERE source_type IS NULL;
```

### 2. 回滚方案
```sql
-- 回滚表结构变更
ALTER TABLE `task_basic` 
DROP COLUMN `push_target_type`,
DROP COLUMN `crowd_ids`,
DROP COLUMN `crowd_user_count`,
DROP COLUMN `total_target_count`;

ALTER TABLE `member_task`
DROP COLUMN `source_type`,
DROP COLUMN `source_crowd_id`,
DROP COLUMN `complete_duration`;

-- 删除新增表
DROP TABLE IF EXISTS `crowd_statistics_summary`;
DROP TABLE IF EXISTS `task_crowd_relation`;
DROP TABLE IF EXISTS `crowd_user_relation`;
DROP TABLE IF EXISTS `crowd_basic`;
```

## 📊 性能优化建议

### 1. 查询优化
- **分页查询**: 大数据量列表使用LIMIT分页
- **索引覆盖**: 尽量使用覆盖索引减少回表
- **查询缓存**: 统计数据使用Redis缓存
- **读写分离**: 统计查询使用只读从库

### 2. 存储优化
- **数据压缩**: 使用ROW_FORMAT=COMPRESSED
- **分区表**: 大表按时间或ID分区
- **归档策略**: 历史数据定期归档

### 3. 维护策略
- **统计信息**: 定期更新表统计信息
- **索引维护**: 定期检查和优化索引
- **数据清理**: 定期清理无效数据

## 🔒 数据安全设计

### 1. 权限控制
- **表级权限**: 基于角色的表访问权限
- **行级权限**: 基于租户的数据隔离
- **字段权限**: 敏感字段访问控制

### 2. 数据保护
- **数据加密**: 敏感字段加密存储
- **备份策略**: 定期数据备份
- **审计日志**: 记录数据变更日志

### 3. 合规要求
- **数据脱敏**: 测试环境数据脱敏
- **数据保留**: 符合数据保留政策
- **隐私保护**: 遵循隐私保护法规

---
*人群功能数据库设计方案 v1.0 - 确保数据存储的高效性和安全性*
