# 数据库设计文档

## 🎯 设计原则

### 命名规范
- **表名**: 小写字母 + 下划线，使用复数形式
- **字段名**: 小写字母 + 下划线，见名知意
- **索引名**: `idx_表名_字段名` 或 `uk_表名_字段名`（唯一索引）
- **外键名**: `fk_表名_关联表名`

### 字段设计原则
- 主键统一使用 `id` 或 `{table}_id`
- 时间字段统一使用 `DATETIME` 类型
- 状态字段使用 `TINYINT` 类型
- 金额字段使用 `DECIMAL(10,2)` 类型
- 文本字段根据长度选择 `VARCHAR` 或 `TEXT`

## 📊 核心表结构

### 1. 用户体系

#### 系统用户表 (sys_user)
```sql
CREATE TABLE `sys_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `user_type` varchar(10) DEFAULT 'sys_user' COMMENT '用户类型',
  `email` varchar(50) DEFAULT NULL COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT NULL COMMENT '手机号码',
  `sex` char(1) DEFAULT '2' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` bigint(20) DEFAULT NULL COMMENT '头像文件ID',
  `password` varchar(100) DEFAULT NULL COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `login_ip` varchar(128) DEFAULT NULL COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_sys_user_username` (`user_name`, `tenant_id`),
  KEY `idx_sys_user_dept` (`dept_id`),
  KEY `idx_sys_user_status` (`status`),
  KEY `idx_sys_user_phone` (`phonenumber`)
) ENGINE=InnoDB COMMENT='用户信息表';
```

#### 会员用户表 (member_user)
```sql
CREATE TABLE `member_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `external_user_id` bigint(20) DEFAULT NULL COMMENT '关联系统用户ID',
  `user_name` varchar(30) DEFAULT NULL COMMENT '用户账号',
  `nick_name` varchar(30) DEFAULT NULL COMMENT '用户昵称',
  `weixin_id` varchar(50) DEFAULT NULL COMMENT '微信号',
  `open_id` varchar(100) DEFAULT NULL COMMENT '微信OpenID',
  `user_type` tinyint(4) DEFAULT '0' COMMENT '用户类型（0-集团员工）',
  `email` varchar(50) DEFAULT NULL COMMENT '用户邮箱',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `phonenumber` varchar(11) DEFAULT NULL COMMENT '手机号码',
  `sex` char(1) DEFAULT '2' COMMENT '用户性别（0男 1女 2未知）',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像地址',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_user_phone` (`phonenumber`),
  UNIQUE KEY `uk_member_user_openid` (`open_id`),
  KEY `idx_member_user_external` (`external_user_id`),
  KEY `idx_member_user_status` (`status`)
) ENGINE=InnoDB COMMENT='会员用户表';
```

#### 员工用户表 (employee_user)
```sql
CREATE TABLE `employee_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `employee_number` varchar(50) DEFAULT NULL COMMENT '员工工号',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '组织ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户姓名',
  `nick_name` varchar(30) DEFAULT NULL COMMENT '用户昵称',
  `email` varchar(50) DEFAULT NULL COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT NULL COMMENT '手机号码',
  `sex` char(1) DEFAULT '2' COMMENT '用户性别',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `entry_date` date DEFAULT NULL COMMENT '入职日期',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_number` (`employee_number`),
  UNIQUE KEY `uk_employee_phone` (`phonenumber`),
  KEY `idx_employee_dept` (`dept_id`)
) ENGINE=InnoDB COMMENT='员工用户表';
```

### 2. 权限体系

#### 角色表 (sys_role)
```sql
CREATE TABLE `sys_role` (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(4) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) DEFAULT '1' COMMENT '数据范围（1：全部 2：自定义 3：本部门 4：本部门及以下 5：仅本人）',
  `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
  `status` char(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`role_id`),
  UNIQUE KEY `uk_sys_role_key` (`role_key`, `tenant_id`),
  KEY `idx_sys_role_status` (`status`)
) ENGINE=InnoDB COMMENT='角色信息表';
```

#### 用户角色关联表 (sys_user_role)
```sql
CREATE TABLE `sys_user_role` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`),
  KEY `fk_sys_user_role_role` (`role_id`)
) ENGINE=InnoDB COMMENT='用户和角色关联表';
```

### 3. 任务体系

#### 任务基础信息表 (task_basic)
```sql
CREATE TABLE `task_basic` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '任务名称',
  `type` tinyint(4) NOT NULL COMMENT '任务类型（0:转发朋友圈 1:原创-其他 2:转发微信 3:互动-视频号）',
  `task_budget` decimal(10,2) DEFAULT NULL COMMENT '任务预算（元）',
  `task_actual_budget` decimal(10,2) DEFAULT '0.00' COMMENT '实际消耗预算（积分）',
  `task_proportion` decimal(10,2) DEFAULT '1.00' COMMENT '兑换比例',
  `defalult_template` tinyint(4) DEFAULT '0' COMMENT '默认文案（0:默认 1:编辑）',
  `template_id` varchar(50) DEFAULT NULL COMMENT '模板ID',
  `task_link` varchar(500) DEFAULT NULL COMMENT '任务链接',
  `template_content` text COMMENT '模板内容',
  `deadline_time` date DEFAULT NULL COMMENT '奖励领取截止时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `sale_type` tinyint(4) DEFAULT '0' COMMENT '上架类型（0:立即 1:定时）',
  `fixed_time` datetime DEFAULT NULL COMMENT '定时上架时间',
  `approve_status` tinyint(4) DEFAULT '1' COMMENT '审核状态（1:审核中 2:不通过 3:通过）',
  `sale_flag` tinyint(4) DEFAULT '0' COMMENT '上架状态（0:未上架 1:已上架 2:待投放）',
  `push_flag` tinyint(4) DEFAULT '0' COMMENT '推送标志（0:推送 1:不推送）',
  `show_flag` tinyint(4) DEFAULT '0' COMMENT '展示标志（0:展示 1:不展示）',
  `examine_flag` tinyint(4) DEFAULT '0' COMMENT '审核标志（0:需要 1:不需要）',
  `reviewers` varchar(500) DEFAULT NULL COMMENT '审核人员',
  `examine_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `task_receive` int(11) DEFAULT '0' COMMENT '任务领取数',
  `task_submit` int(11) DEFAULT '0' COMMENT '任务提交数',
  `task_complete` int(11) DEFAULT '0' COMMENT '任务完成数',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_basic_type` (`type`),
  KEY `idx_task_basic_status` (`approve_status`),
  KEY `idx_task_basic_sale` (`sale_flag`),
  KEY `idx_task_basic_time` (`create_time`),
  KEY `idx_task_basic_deadline` (`deadline_time`)
) ENGINE=InnoDB COMMENT='任务基础信息表';
```

#### 用户任务表 (member_task)
```sql
CREATE TABLE `member_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `type` tinyint(4) NOT NULL COMMENT '任务类型',
  `user_id` bigint(20) NOT NULL COMMENT 'C端用户ID',
  `member_task_status` tinyint(4) DEFAULT '1' COMMENT '任务状态（1:已领取 2:已提交 3:已完成 4:已驳回 5:失效）',
  `approve_status` tinyint(4) DEFAULT '0' COMMENT '审核状态（0:待审核 1:已通过 2:不通过 3:超出预算）',
  `original_approve_status` tinyint(4) DEFAULT NULL COMMENT '原创评审结果（3:基础分 4:一等奖 5:二等奖 6:三等奖 7:无奖项）',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '组织部门ID',
  `rewards` decimal(10,2) DEFAULT '0.00' COMMENT '发放奖励（积分）',
  `thermal` decimal(10,2) DEFAULT '0.00' COMMENT '热力值',
  `examine_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `submit_content` text COMMENT '提交内容',
  `submit_images` text COMMENT '提交图片',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `examine_time` datetime DEFAULT NULL COMMENT '审核时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_member_task_task` (`task_id`),
  KEY `idx_member_task_user` (`user_id`),
  KEY `idx_member_task_status` (`member_task_status`),
  KEY `idx_member_task_approve` (`approve_status`),
  KEY `idx_member_task_dept` (`dept_id`),
  KEY `idx_member_task_submit_time` (`submit_time`)
) ENGINE=InnoDB COMMENT='用户任务表';
```

### 4. 积分体系

#### 会员积分详情表 (member_point_detail)
```sql
CREATE TABLE `member_point_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `point_type` tinyint(4) NOT NULL COMMENT '积分类型（1:任务奖励 2:销售转化）',
  `change_type` tinyint(4) NOT NULL COMMENT '变动类型（1:增加 2:减少）',
  `change_amount` decimal(10,2) NOT NULL COMMENT '变动金额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `business_id` bigint(20) DEFAULT NULL COMMENT '业务ID',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_point_detail_user` (`user_id`),
  KEY `idx_point_detail_type` (`point_type`),
  KEY `idx_point_detail_business` (`business_id`, `business_type`),
  KEY `idx_point_detail_time` (`create_time`)
) ENGINE=InnoDB COMMENT='会员积分详情表';
```

#### 会员热力值详情表 (member_thermal_detail)
```sql
CREATE TABLE `member_thermal_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `change_type` tinyint(4) NOT NULL COMMENT '变动类型（1:增加 2:减少）',
  `change_amount` decimal(10,2) NOT NULL COMMENT '变动金额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `business_id` bigint(20) DEFAULT NULL COMMENT '业务ID',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_thermal_detail_user` (`user_id`),
  KEY `idx_thermal_detail_business` (`business_id`, `business_type`),
  KEY `idx_thermal_detail_time` (`create_time`)
) ENGINE=InnoDB COMMENT='会员热力值详情表';
```

## 🔗 表关系设计

### 用户关系图
```mermaid
erDiagram
    sys_user ||--o{ sys_user_role : "用户角色关联"
    sys_role ||--o{ sys_user_role : "角色用户关联"
    sys_user ||--o{ member_user : "系统用户关联会员"
    member_user ||--o{ employee_user : "会员关联员工"
    sys_dept ||--o{ sys_user : "部门用户关联"
    sys_dept ||--o{ employee_user : "部门员工关联"
```

### 任务关系图
```mermaid
erDiagram
    task_basic ||--o{ member_task : "任务被用户领取"
    member_user ||--o{ member_task : "用户领取任务"
    member_task ||--o{ member_point_detail : "任务完成获得积分"
    member_task ||--o{ member_thermal_detail : "任务完成获得热力值"
    sys_dept ||--o{ member_task : "任务归属部门"
```

## 📋 索引设计策略

### 主要索引
```sql
-- 用户表索引
CREATE INDEX idx_sys_user_dept_status ON sys_user(dept_id, status);
CREATE INDEX idx_sys_user_login_date ON sys_user(login_date);

-- 任务表索引
CREATE INDEX idx_task_basic_type_status ON task_basic(type, approve_status);
CREATE INDEX idx_task_basic_deadline_sale ON task_basic(deadline_time, sale_flag);

-- 用户任务表索引
CREATE INDEX idx_member_task_user_status ON member_task(user_id, member_task_status);
CREATE INDEX idx_member_task_task_approve ON member_task(task_id, approve_status);

-- 积分表索引
CREATE INDEX idx_point_detail_user_time ON member_point_detail(user_id, create_time);
CREATE INDEX idx_thermal_detail_user_time ON member_thermal_detail(user_id, create_time);
```

### 复合索引原则
1. **最左前缀原则**: 查询条件从左到右匹配
2. **选择性高的字段在前**: 区分度高的字段放在前面
3. **覆盖索引**: 尽量让索引包含查询所需的所有字段
4. **避免过多索引**: 平衡查询性能和写入性能

## 🔧 数据库配置

### 字符集配置
```sql
-- 数据库级别
CREATE DATABASE baic_admin 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 表级别
CREATE TABLE example (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  name varchar(100) NOT NULL
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci;
```

### 存储引擎选择
- **InnoDB**: 支持事务、外键、行级锁，适合OLTP场景
- **MyISAM**: 读取速度快，适合只读或读多写少场景
- **Memory**: 数据存储在内存中，适合临时表或缓存

### 分区策略
```sql
-- 按时间分区（日志表）
CREATE TABLE sys_oper_log (
  oper_id bigint(20) NOT NULL AUTO_INCREMENT,
  oper_time datetime NOT NULL,
  -- 其他字段...
  PRIMARY KEY (oper_id, oper_time)
) ENGINE=InnoDB
PARTITION BY RANGE (YEAR(oper_time)) (
  PARTITION p2024 VALUES LESS THAN (2025),
  PARTITION p2025 VALUES LESS THAN (2026),
  PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 📊 性能优化建议

### 查询优化
1. **避免SELECT \***: 只查询需要的字段
2. **合理使用LIMIT**: 分页查询避免大结果集
3. **优化WHERE条件**: 使用索引字段作为查询条件
4. **避免函数操作**: WHERE条件中避免对字段使用函数

### 表结构优化
1. **合理的数据类型**: 选择最小满足需求的数据类型
2. **NOT NULL约束**: 尽量使用NOT NULL约束
3. **适当的字段长度**: VARCHAR字段设置合理长度
4. **分表策略**: 大表考虑水平或垂直分割

## 🔗 相关文档

- [后端开发规范](../development/backend-standards.md)
- [API接口规范](../api/README.md)
- [数据迁移指南](./migration-guide.md)
- [性能优化指南](./performance-optimization.md)

---
*数据库设计文档，确保数据存储的规范性和高效性*
