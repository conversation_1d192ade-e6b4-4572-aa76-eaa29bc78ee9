# API接口规范

## 🎯 接口设计原则

### RESTful设计
- 使用标准HTTP方法（GET、POST、PUT、DELETE）
- 资源导向的URL设计
- 统一的响应格式
- 合理的HTTP状态码使用

### 接口版本管理
- URL版本控制：`/api/v1/users`
- 向后兼容原则
- 废弃接口的优雅处理

## 📋 URL命名规范

### 基础规范
```
# 管理端接口
/admin/{module}/{resource}

# 门户端接口  
/portal/{module}/{resource}

# 系统接口
/system/{resource}

# 公共接口
/common/{resource}
```

### 具体示例
```
# 用户管理
GET    /admin/member/user/list          # 获取用户列表
GET    /admin/member/user/{id}          # 获取单个用户
POST   /admin/member/user               # 创建用户
PUT    /admin/member/user               # 更新用户
DELETE /admin/member/user/{ids}         # 删除用户

# 任务管理
GET    /admin/task/basic/list           # 获取任务列表
POST   /admin/task/basic                # 创建任务
PUT    /admin/task/basic                # 更新任务
DELETE /admin/task/basic/{ids}          # 删除任务

# 门户端任务
GET    /portal/task/basic/list          # 获取可领取任务
POST   /portal/task/basic/receive       # 领取任务
POST   /portal/task/basic/submit        # 提交任务
```

## 📊 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 分页响应
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    // 数据列表
  ],
  "total": 100
}
```

### 错误响应
```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 业务错误响应
```json
{
  "code": 400,
  "msg": "参数校验失败",
  "data": {
    "field": "username",
    "message": "用户名不能为空"
  }
}
```

## 🔧 请求参数规范

### 查询参数
```java
/**
 * 用户查询参数
 */
@Data
public class UserQueryBo extends BaseEntity {
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 手机号
     */
    private String phonenumber;
    
    /**
     * 状态（0正常 1停用）
     */
    private String status;
    
    /**
     * 部门ID
     */
    private Long deptId;
}
```

### 分页参数
```java
/**
 * 分页查询参数
 */
@Data
public class PageQuery {
    
    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码最小值为1")
    private Integer pageNum = 1;
    
    /**
     * 每页数量
     */
    @Range(min = 1, max = 100, message = "每页数量必须在1-100之间")
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String orderByColumn;
    
    /**
     * 排序方向
     */
    private String isAsc = "asc";
}
```

### 请求体参数
```java
/**
 * 用户创建请求
 */
@Data
public class UserCreateBo {
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 30, message = "用户名长度必须在2-30个字符之间")
    private String username;
    
    /**
     * 用户昵称
     */
    @NotBlank(message = "用户昵称不能为空")
    @Size(max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickName;
    
    /**
     * 手机号码
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phonenumber;
    
    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 角色ID数组
     */
    @NotEmpty(message = "用户角色不能为空")
    private Long[] roleIds;
}
```

## 📝 接口文档规范

### Swagger注解使用
```java
@Tag(name = "用户管理", description = "用户管理相关接口")
@RestController
@RequestMapping("/admin/system/user")
public class AdminSysUserController {

    @Operation(summary = "获取用户列表", description = "分页查询用户信息")
    @Parameters({
        @Parameter(name = "username", description = "用户名", example = "admin"),
        @Parameter(name = "status", description = "用户状态", example = "0"),
        @Parameter(name = "pageNum", description = "页码", example = "1"),
        @Parameter(name = "pageSize", description = "每页数量", example = "10")
    })
    @GetMapping("/list")
    public TableDataInfo<SysUserVo> list(
        @Schema(description = "用户查询条件") SysUserBo user,
        @Schema(description = "分页参数") PageQuery pageQuery) {
        return userService.selectPageUserList(user, pageQuery);
    }

    @Operation(summary = "新增用户", description = "创建新的用户账号")
    @ApiResponse(responseCode = "200", description = "操作成功")
    @ApiResponse(responseCode = "400", description = "参数校验失败")
    @PostMapping
    public R<Void> add(@Valid @RequestBody SysUserBo user) {
        return toAjax(userService.insertUser(user));
    }
}
```

### 响应对象文档
```java
@Schema(description = "用户信息")
@Data
public class SysUserVo {

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "用户名", example = "admin")
    private String userName;

    @Schema(description = "用户昵称", example = "管理员")
    private String nickName;

    @Schema(description = "手机号码", example = "13888888888")
    private String phonenumber;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "用户状态", example = "0", allowableValues = {"0", "1"})
    private String status;

    @Schema(description = "创建时间", example = "2025-01-30 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
```

## 🔒 接口安全规范

### 认证授权
```java
// 权限检查
@SaCheckPermission("system:user:list")
@GetMapping("/list")
public TableDataInfo<SysUserVo> list() {
    // 接口实现
}

// 角色检查
@SaCheckRole("admin")
@PostMapping("/admin-only")
public R<Void> adminOnly() {
    // 管理员专用接口
}

// 忽略认证
@SaIgnore
@GetMapping("/public")
public R<String> publicApi() {
    return R.ok("公开接口");
}
```

### 防重复提交
```java
@RepeatSubmit(interval = 5, timeUnit = TimeUnit.SECONDS)
@PostMapping("/submit")
public R<Void> submit(@RequestBody TaskSubmitBo bo) {
    // 防止5秒内重复提交
    return R.ok();
}
```

### 参数校验
```java
@PostMapping("/create")
public R<Void> create(@Validated(AddGroup.class) @RequestBody UserBo user) {
    // 使用分组校验
    return R.ok();
}

@PutMapping("/update")
public R<Void> update(@Validated(EditGroup.class) @RequestBody UserBo user) {
    // 更新时的校验规则
    return R.ok();
}
```

## 📊 错误码规范

### HTTP状态码使用
```
200 OK              - 请求成功
201 Created         - 创建成功
204 No Content      - 删除成功
400 Bad Request     - 请求参数错误
401 Unauthorized    - 未认证
403 Forbidden       - 无权限
404 Not Found       - 资源不存在
409 Conflict        - 资源冲突
422 Unprocessable   - 参数校验失败
500 Internal Error  - 服务器内部错误
```

### 业务错误码
```java
public class ErrorCodes {
    // 用户相关 1000-1999
    public static final int USER_NOT_FOUND = 1001;
    public static final int USER_ALREADY_EXISTS = 1002;
    public static final int USER_PASSWORD_ERROR = 1003;
    
    // 任务相关 2000-2999
    public static final int TASK_NOT_FOUND = 2001;
    public static final int TASK_ALREADY_RECEIVED = 2002;
    public static final int TASK_EXPIRED = 2003;
    
    // 权限相关 3000-3999
    public static final int PERMISSION_DENIED = 3001;
    public static final int ROLE_NOT_FOUND = 3002;
}
```

## 🔄 接口版本控制

### URL版本控制
```java
@RestController
@RequestMapping("/api/v1/users")
public class UserV1Controller {
    // v1版本接口
}

@RestController
@RequestMapping("/api/v2/users")
public class UserV2Controller {
    // v2版本接口
}
```

### 请求头版本控制
```java
@RestController
@RequestMapping("/api/users")
public class UserController {

    @GetMapping(headers = "API-Version=1")
    public R<List<UserV1Vo>> listV1() {
        // v1版本响应
    }

    @GetMapping(headers = "API-Version=2")
    public R<List<UserV2Vo>> listV2() {
        // v2版本响应
    }
}
```

## 📈 性能优化规范

### 分页查询优化
```java
@GetMapping("/list")
public TableDataInfo<UserVo> list(UserBo bo, PageQuery pageQuery) {
    // 限制最大页面大小
    if (pageQuery.getPageSize() > 100) {
        pageQuery.setPageSize(100);
    }
    
    // 使用索引优化查询
    return userService.selectPageUserList(bo, pageQuery);
}
```

### 缓存使用
```java
@Cacheable(cacheNames = "user", key = "#userId")
@GetMapping("/{userId}")
public R<UserVo> getUser(@PathVariable Long userId) {
    UserVo user = userService.selectUserById(userId);
    return R.ok(user);
}

@CacheEvict(cacheNames = "user", key = "#bo.userId")
@PutMapping
public R<Void> updateUser(@RequestBody UserBo bo) {
    return toAjax(userService.updateUser(bo));
}
```

### 异步处理
```java
@PostMapping("/batch-process")
public R<Void> batchProcess(@RequestBody List<Long> ids) {
    // 异步处理大批量数据
    taskService.batchProcessAsync(ids);
    return R.ok("批量处理已开始，请稍后查看结果");
}
```

## 📋 接口测试规范

### 单元测试
```java
@SpringBootTest
@AutoConfigureTestDatabase
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    void testGetUserList() throws Exception {
        mockMvc.perform(get("/admin/system/user/list")
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    void testCreateUser() throws Exception {
        String userJson = """
            {
                "userName": "testuser",
                "nickName": "测试用户",
                "phonenumber": "13888888888",
                "roleIds": [1]
            }
            """;

        mockMvc.perform(post("/admin/system/user")
                .contentType(MediaType.APPLICATION_JSON)
                .content(userJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
```

## 🔗 相关文档

- [后端开发规范](../development/backend-standards.md)
- [数据库设计规范](../database/README.md)
- [前端接口调用规范](../frontend/api-integration.md)
- [接口安全指南](./security-guide.md)

---
*API接口规范，确保接口设计的一致性和可维护性*
