# 项目架构概览

## 🏗️ 整体架构

声量合伙人是一个**多端应用系统**，采用前后端分离的微服务架构设计，支持企业内部营销推广和任务分发管理。

```
┌─────────────────────────────────────────────────────────────┐
│                    声量合伙人系统架构                          │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Frontend Layer)                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 管理后台     │ │   H5移动端   │ │  微信小程序  │           │
│  │ Vue3+Element│ │ Vue3+Vant4  │ │   原生开发   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  网关层 (Gateway Layer)                                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              API Gateway / Nginx                        │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  服务层 (Service Layer)                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  后端服务    │ │  任务调度    │ │   监控服务   │           │
│  │ Spring Boot │ │   XXL-Job   │ │ Spring Admin│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │    MySQL    │ │    Redis    │ │   MinIO/OSS │           │
│  │   主数据库   │ │    缓存     │ │  文件存储    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 📱 前端架构

### 1. 管理后台 (baic-admin-frontend)
- **技术栈**: Vue3 + TypeScript + Element Plus + Vite
- **基础框架**: RuoYi-Vue-Plus 5.X
- **主要功能**: 
  - 用户管理、权限管理、部门管理
  - 任务管理、审核流程
  - 数据统计、报表分析
  - 系统配置、监控管理

### 2. H5移动端 (baic-h5)
- **技术栈**: Vue3 + TypeScript + Vant4 + Tailwindcss + Vite
- **特色功能**:
  - 微信公众号集成
  - 移动端响应式设计
  - PWA支持
  - 任务领取和提交
  - 个人中心管理

### 3. 微信小程序 (baic-wxapp)
- **技术栈**: 原生微信小程序
- **当前页面**: 首页、频道页
- **扩展性**: 支持后续功能模块添加

## 🖥️ 后端架构

### 核心服务 (baic-admin)
```
baic-admin/
├── admin/                    # 主应用模块
├── common/                   # 公共组件模块
│   ├── common-core/         # 核心工具类
│   ├── common-web/          # Web相关配置
│   ├── common-security/     # 安全认证
│   ├── common-satoken/      # SA-Token集成
│   ├── common-mybatis/      # MyBatis配置
│   ├── common-redis/        # Redis配置
│   ├── common-oss/          # 对象存储
│   └── ...                  # 其他公共模块
├── modules/                 # 业务模块
│   ├── business/           # 核心业务逻辑
│   ├── generator/          # 代码生成器
│   ├── job/               # 任务管理
│   └── workflow/          # 工作流
└── extend/                 # 扩展模块
    ├── monitor-admin/     # 监控管理
    └── snailjob-server/   # 任务调度服务
```

### 技术特点
- **Spring Boot 3.4.2**: 最新稳定版本
- **多租户支持**: SaaS模式架构
- **权限管理**: 完整的RBAC体系
- **微服务就绪**: 支持Spring Cloud扩展
- **数据权限**: 部门级数据隔离
- **审计日志**: 完整的操作记录

## 🔄 业务流程架构

### 核心业务实体关系
```
用户体系:
├── 系统用户 (SysUser)      # 管理后台用户
├── 会员用户 (MemberUser)   # C端用户
├── 员工用户 (EmployeeUser) # 企业员工
└── 部门组织 (SysDept)      # 组织架构

任务体系:
├── 任务基础信息 (TaskBasic)
├── 任务详情 (TaskDetail)
├── 会员任务 (MemberTask)   # 用户领取的任务
├── 任务审核 (TaskExamine)
└── 任务奖励 (TaskReward)

权限体系:
├── 角色 (SysRole)
├── 菜单 (SysMenu)
├── 权限 (Permission)
└── 数据权限 (DataScope)
```

## 🔧 技术选型

### 后端技术栈
- **框架**: Spring Boot 3.4.2 + Spring Security
- **数据库**: MySQL 8.0 + MyBatis Plus 3.5.10
- **缓存**: Redis 6.X + Redisson 3.44.0
- **认证**: SA-Token 1.40.0
- **任务调度**: XXL-Job + SnailJob
- **文档**: SpringDoc + Knife4j
- **监控**: Spring Boot Admin

### 前端技术栈
- **构建工具**: Vite 5.X
- **包管理**: pnpm (H5) / npm (管理后台)
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **UI组件**: Element Plus / Vant4

## 📊 部署架构

### 环境要求
- **JDK**: OpenJDK 17/21
- **Node.js**: 18.18+
- **数据库**: MySQL 5.7/8.0
- **缓存**: Redis 6.X+
- **文件存储**: MinIO / 云存储

### 部署方式
- **开发环境**: 本地开发 + Docker Compose
- **测试环境**: Docker容器化部署
- **生产环境**: K8s集群 / 传统服务器部署

## 🔗 相关文档

- [后端开发指南](../backend/README.md)
- [前端开发指南](../frontend/README.md)
- [数据库设计](../database/README.md)
- [API接口文档](../api/README.md)
- [部署运维指南](../deployment/README.md)

---
*架构概览，详细信息请查看各模块的具体文档*
