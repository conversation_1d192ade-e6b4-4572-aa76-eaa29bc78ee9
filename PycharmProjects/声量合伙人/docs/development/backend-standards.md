# 后端开发规范

## 🎯 技术栈规范

### 核心框架
- **Spring Boot**: 3.4.2
- **Spring Security**: 集成SA-Token 1.40.0
- **MyBatis Plus**: 3.5.10
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.X + Redisson 3.44.0
- **任务调度**: XXL-Job + SnailJob
- **文档**: SpringDoc + Knife4j

### 开发环境
- **JDK**: OpenJDK 17/21
- **构建工具**: Maven 3.8.X
- **IDE**: IntelliJ IDEA 2024.3+

## 📁 项目结构规范

### 模块划分
```
baic-admin/
├── admin/                    # 主应用模块
│   └── src/main/java/cn/baic/
│       ├── AdminApplication.java    # 启动类
│       └── web/controller/         # Web控制器
├── common/                   # 公共模块
│   ├── common-core/         # 核心工具类
│   ├── common-web/          # Web配置
│   ├── common-security/     # 安全认证
│   ├── common-mybatis/      # 数据库配置
│   └── ...                  # 其他公共模块
├── modules/                 # 业务模块
│   ├── business/           # 核心业务
│   ├── generator/          # 代码生成
│   └── ...                 # 其他业务模块
└── extend/                 # 扩展模块
```

### 包结构规范
```java
cn.baic.business.{module}/
├── controller/             # 控制器层
│   ├── admin/             # 管理端控制器
│   └── portal/            # 门户端控制器
├── domain/                # 领域模型
│   ├── bo/               # 业务对象
│   ├── vo/               # 视图对象
│   └── {Entity}.java     # 实体类
├── enums/                # 枚举类
├── mapper/               # 数据访问层
├── service/              # 服务层
│   └── impl/            # 服务实现
└── utils/               # 工具类
```

## 🏗️ 代码结构规范

### 控制器规范
```java
/**
 * 任务管理控制器
 *
 * <AUTHOR>
 * @date 创建日期
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/task/basic")
public class AdminTaskBasicController extends BaseController {

    private final ITaskBasicService taskBasicService;

    /**
     * 查询任务列表
     */
    @SaCheckPermission("task:basic:list")
    @GetMapping("/list")
    public TableDataInfo<TaskBasicVo> list(TaskBasicBo bo, PageQuery pageQuery) {
        return taskBasicService.queryPageList(bo, pageQuery);
    }

    /**
     * 新增任务
     */
    @SaCheckPermission("task:basic:add")
    @Log(title = "任务管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TaskBasicBo bo) {
        return toAjax(taskBasicService.insertByBo(bo));
    }

    /**
     * 修改任务
     */
    @SaCheckPermission("task:basic:edit")
    @Log(title = "任务管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TaskBasicBo bo) {
        return toAjax(taskBasicService.updateByBo(bo));
    }

    /**
     * 删除任务
     */
    @SaCheckPermission("task:basic:remove")
    @Log(title = "任务管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                         @PathVariable Long[] ids) {
        return toAjax(taskBasicService.deleteWithValidByIds(List.of(ids)));
    }
}
```

### 服务层规范
```java
/**
 * 任务基础信息服务接口
 *
 * <AUTHOR>
 * @date 创建日期
 */
public interface ITaskBasicService {

    /**
     * 查询任务基础信息
     */
    TaskBasicVo queryById(Long id);

    /**
     * 查询任务基础信息列表
     */
    TableDataInfo<TaskBasicVo> queryPageList(TaskBasicBo bo, PageQuery pageQuery);

    /**
     * 新增任务基础信息
     */
    Boolean insertByBo(TaskBasicBo bo);

    /**
     * 修改任务基础信息
     */
    Boolean updateByBo(TaskBasicBo bo);

    /**
     * 校验并批量删除任务基础信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);
}

/**
 * 任务基础信息服务实现
 *
 * <AUTHOR>
 * @date 创建日期
 */
@RequiredArgsConstructor
@Service
public class TaskBasicServiceImpl implements ITaskBasicService {

    private final TaskBasicMapper baseMapper;

    @Override
    public TaskBasicVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public TableDataInfo<TaskBasicVo> queryPageList(TaskBasicBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TaskBasic> lqw = buildQueryWrapper(bo);
        Page<TaskBasicVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TaskBasicBo bo) {
        TaskBasic add = MapstructUtils.convert(bo, TaskBasic.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(TaskBasicBo bo) {
        TaskBasic update = MapstructUtils.convert(bo, TaskBasic.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TaskBasic entity) {
        // 数据校验逻辑
        if (StringUtils.isBlank(entity.getTitle())) {
            throw new ServiceException("任务标题不能为空");
        }
    }

    private LambdaQueryWrapper<TaskBasic> buildQueryWrapper(TaskBasicBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TaskBasic> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), TaskBasic::getTitle, bo.getTitle());
        lqw.eq(bo.getStatus() != null, TaskBasic::getStatus, bo.getStatus());
        lqw.between(params.get("beginTime") != null && params.get("endTime") != null,
            TaskBasic::getCreateTime, params.get("beginTime"), params.get("endTime"));
        return lqw;
    }
}
```

### 实体类规范
```java
/**
 * 任务基础信息对象 task_basic
 *
 * <AUTHOR>
 * @date 创建日期
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("task_basic")
public class TaskBasic extends TenantEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务内容
     */
    private String content;

    /**
     * 任务状态（0草稿 1待审核 2已发布 3进行中 4已结束）
     */
    private Integer status;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
}

/**
 * 任务基础信息业务对象 TaskBasicBo
 *
 * <AUTHOR>
 * @date 创建日期
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TaskBasic.class, reverseConvertGenerate = false)
public class TaskBasicBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    @Size(max = 100, message = "任务标题长度不能超过100个字符")
    private String title;

    /**
     * 任务内容
     */
    @NotBlank(message = "任务内容不能为空")
    private String content;

    /**
     * 任务状态
     */
    private Integer status;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private Date endTime;
}

/**
 * 任务基础信息视图对象 TaskBasicVo
 *
 * <AUTHOR>
 * @date 创建日期
 */
@Data
@AutoMapper(target = TaskBasic.class)
public class TaskBasicVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务内容
     */
    private String content;

    /**
     * 任务状态
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
```

## 🔧 注解使用规范

### 权限控制注解
```java
// 权限检查
@SaCheckPermission("system:user:list")

// 角色检查
@SaCheckRole("admin")

// 忽略认证
@SaIgnore
```

### 日志记录注解
```java
@Log(title = "用户管理", businessType = BusinessType.INSERT)
@Log(title = "用户管理", businessType = BusinessType.UPDATE)
@Log(title = "用户管理", businessType = BusinessType.DELETE)
@Log(title = "用户管理", businessType = BusinessType.EXPORT)
```

### 防重复提交注解
```java
@RepeatSubmit()  // 默认5秒内防重复
@RepeatSubmit(interval = 10, timeUnit = TimeUnit.SECONDS)  // 自定义间隔
```

### 数据校验注解
```java
// 分组校验
@Validated(AddGroup.class)
@Validated(EditGroup.class)

// 字段校验
@NotBlank(message = "用户名不能为空")
@Size(min = 2, max = 20, message = "用户名长度必须在2-20个字符之间")
@Email(message = "邮箱格式不正确")
@Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
```

## 🚨 异常处理规范

### 业务异常
```java
// 抛出业务异常
throw new ServiceException("用户名已存在");
throw new ServiceException("操作失败", 500);

// 自定义异常
public class TaskException extends ServiceException {
    public TaskException(String message) {
        super(message);
    }
}
```

### 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(ServiceException.class)
    public R<Void> handleServiceException(ServiceException e) {
        log.error("业务异常：{}", e.getMessage());
        return R.fail(e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return R.fail(message);
    }
}
```

## 📊 数据库操作规范

### Mapper接口
```java
/**
 * 任务基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 创建日期
 */
public interface TaskBasicMapper extends BaseMapperPlus<TaskBasic, TaskBasicVo> {

    /**
     * 根据条件查询任务列表
     */
    List<TaskBasicVo> selectTaskList(@Param("bo") TaskBasicBo bo);

    /**
     * 统计任务数量
     */
    Long countTaskByStatus(@Param("status") Integer status);
}
```

### SQL编写规范
```xml
<!-- TaskBasicMapper.xml -->
<select id="selectTaskList" resultType="cn.baic.business.task.domain.vo.TaskBasicVo">
    SELECT 
        t.id,
        t.title,
        t.content,
        t.status,
        t.start_time,
        t.end_time,
        t.create_time
    FROM task_basic t
    <where>
        t.del_flag = '0'
        <if test="bo.title != null and bo.title != ''">
            AND t.title LIKE CONCAT('%', #{bo.title}, '%')
        </if>
        <if test="bo.status != null">
            AND t.status = #{bo.status}
        </if>
        <if test="bo.params.beginTime != null and bo.params.endTime != null">
            AND t.create_time BETWEEN #{bo.params.beginTime} AND #{bo.params.endTime}
        </if>
    </where>
    ORDER BY t.create_time DESC
</select>
```

## 🔄 事务管理规范

```java
// 方法级事务
@Transactional(rollbackFor = Exception.class)
public Boolean updateTask(TaskBasicBo bo) {
    // 业务逻辑
    return true;
}

// 只读事务
@Transactional(readOnly = true)
public List<TaskBasicVo> queryTaskList(TaskBasicBo bo) {
    // 查询逻辑
    return Collections.emptyList();
}

// 事务传播
@Transactional(propagation = Propagation.REQUIRES_NEW)
public void saveLog(String message) {
    // 独立事务
}
```

## 📝 日志记录规范

```java
@Slf4j
@Service
public class TaskServiceImpl {

    public void processTask(Long taskId) {
        log.info("开始处理任务，taskId: {}", taskId);
        
        try {
            // 业务逻辑
            log.debug("任务处理中，当前状态: {}", status);
        } catch (Exception e) {
            log.error("任务处理失败，taskId: {}, 错误信息: {}", taskId, e.getMessage(), e);
            throw new ServiceException("任务处理失败");
        }
        
        log.info("任务处理完成，taskId: {}", taskId);
    }
}
```

## 🔒 安全规范

### 数据权限
```java
// 使用数据权限注解
@DataPermission({
    @DataColumn(key = "deptName", value = "dept_id"),
    @DataColumn(key = "userName", value = "user_id")
})
public List<TaskBasicVo> selectTaskList(TaskBasicBo bo) {
    // 自动添加数据权限过滤
}
```

### 敏感数据处理
```java
// 敏感字段加密
@Sensitive(strategy = SensitiveStrategy.PHONE)
private String phone;

@Sensitive(strategy = SensitiveStrategy.ID_CARD)
private String idCard;
```

## 📋 代码质量规范

### 代码注释
```java
/**
 * 任务管理服务类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-30
 */
public class TaskService {

    /**
     * 创建任务
     * 
     * @param taskBo 任务业务对象
     * @return 创建结果
     * @throws ServiceException 业务异常
     */
    public Boolean createTask(TaskBasicBo taskBo) throws ServiceException {
        // 实现逻辑
        return true;
    }
}
```

### 单元测试
```java
@SpringBootTest
class TaskServiceTest {

    @Autowired
    private ITaskService taskService;

    @Test
    void testCreateTask() {
        TaskBasicBo bo = new TaskBasicBo();
        bo.setTitle("测试任务");
        bo.setContent("测试内容");
        
        Boolean result = taskService.createTask(bo);
        
        Assertions.assertTrue(result);
    }
}
```

---
*后端开发规范，确保代码质量和系统稳定性*
