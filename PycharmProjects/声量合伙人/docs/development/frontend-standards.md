# 前端开发规范

## 🎯 技术栈规范

### H5移动端 (baic-h5)
- **框架**: Vue3 + Composition API + TypeScript
- **UI组件**: Vant4
- **样式**: Tailwindcss + Less
- **状态管理**: Pinia + 持久化插件
- **构建工具**: Vite5 + pnpm
- **代码规范**: ESLint + Prettier + Husky

### 管理后台 (baic-admin-frontend)
- **框架**: Vue3 + Composition API + TypeScript
- **UI组件**: Element Plus
- **样式**: SCSS + UnoCSS
- **状态管理**: Pinia + VueUse
- **构建工具**: Vite5 + npm
- **代码规范**: ESLint + Prettier + Husky

## 📁 目录结构规范

### H5项目结构
```
src/
├── api/                    # API接口
├── assets/                 # 静态资源
├── components/             # 公共组件
├── composables/           # 组合式函数
├── directives/            # 自定义指令
├── layout/                # 布局组件
├── router/                # 路由配置
├── store/                 # 状态管理
├── styles/                # 全局样式
├── utils/                 # 工具函数
├── views/                 # 页面组件
└── main.ts               # 入口文件
```

### 管理后台项目结构
```
src/
├── api/                   # API接口
├── assets/                # 静态资源
├── components/            # 公共组件
├── directive/             # 自定义指令
├── layout/                # 布局组件
├── plugins/               # 插件配置
├── router/                # 路由配置
├── store/                 # 状态管理
├── utils/                 # 工具函数
├── views/                 # 页面组件
└── main.ts               # 入口文件
```

## 🎨 代码风格规范

### Vue组件规范

#### 1. 组件命名
```vue
<!-- ✅ 正确：使用PascalCase -->
<script setup lang="ts">
defineOptions({
  name: "UserProfile"
});
</script>

<!-- ❌ 错误：使用kebab-case -->
<script setup lang="ts">
defineOptions({
  name: "user-profile"
});
</script>
```

#### 2. 组件结构顺序
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入
import { ref, computed } from 'vue';

// 2. 组件选项
defineOptions({
  name: "ComponentName"
});

// 3. Props定义
interface Props {
  title: string;
  count?: number;
}
const props = withDefaults(defineProps<Props>(), {
  count: 0
});

// 4. Emits定义
const emit = defineEmits<{
  change: [value: string];
}>();

// 5. 响应式数据
const loading = ref(false);

// 6. 计算属性
const displayText = computed(() => {
  return `${props.title}: ${props.count}`;
});

// 7. 方法
const handleClick = () => {
  emit('change', 'new-value');
};

// 8. 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>

<style scoped lang="less">
/* 样式内容 */
</style>
```

#### 3. Props和Emits类型定义
```typescript
// ✅ 推荐：使用TypeScript接口
interface Props {
  title: string;
  visible: boolean;
  data?: UserInfo[];
}

interface Emits {
  close: [];
  submit: [data: FormData];
  update: [field: string, value: any];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
});

const emit = defineEmits<Emits>();
```

### TypeScript规范

#### 1. 类型定义
```typescript
// ✅ 使用interface定义对象类型
interface UserInfo {
  id: number;
  name: string;
  email?: string;
  roles: string[];
}

// ✅ 使用type定义联合类型
type Status = 'pending' | 'success' | 'error';

// ✅ 使用泛型
interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}
```

#### 2. API接口类型
```typescript
// api/types/user.ts
export interface LoginParams {
  username: string;
  password: string;
  captcha?: string;
}

export interface LoginResponse {
  token: string;
  userInfo: UserInfo;
}

// api/user.ts
export const login = (params: LoginParams): Promise<LoginResponse> => {
  return request.post('/auth/login', params);
};
```

## 🎨 样式规范

### H5样式规范
```vue
<template>
  <div class="user-profile">
    <!-- 使用Tailwind原子类 -->
    <div class="flex items-center justify-between p-4 bg-white rounded-lg">
      <h2 class="text-lg font-medium text-gray-900">用户信息</h2>
      <button class="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600">
        编辑
      </button>
    </div>
  </div>
</template>

<style scoped lang="less">
// 复杂样式使用Less
.user-profile {
  &__header {
    border-bottom: 1px solid var(--van-border-color);
    
    .title {
      font-size: var(--van-font-size-lg);
      color: var(--van-text-color);
    }
  }
  
  &__content {
    padding: var(--van-padding-md);
  }
}
</style>
```

### 管理后台样式规范
```vue
<template>
  <div class="user-management">
    <!-- 使用UnoCSS原子类 -->
    <div class="flex items-center justify-between mb-4">
      <h1 class="text-xl font-semibold">用户管理</h1>
      <el-button type="primary" @click="handleAdd">新增用户</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 使用SCSS变量和嵌套
.user-management {
  padding: var(--app-content-padding);
  
  .search-form {
    background: var(--el-bg-color);
    border-radius: var(--el-border-radius-base);
    padding: 16px;
    margin-bottom: 16px;
    
    .form-item {
      margin-bottom: 0;
    }
  }
  
  .table-container {
    background: var(--el-bg-color);
    border-radius: var(--el-border-radius-base);
  }
}
</style>
```

## 📦 状态管理规范

### Pinia Store规范
```typescript
// stores/modules/user.ts
import { defineStore } from 'pinia';

interface UserState {
  token: string | null;
  userInfo: UserInfo | null;
  permissions: string[];
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: null,
    userInfo: null,
    permissions: []
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    hasPermission: (state) => (permission: string) => {
      return state.permissions.includes(permission);
    }
  },
  
  actions: {
    async login(params: LoginParams) {
      try {
        const response = await loginApi(params);
        this.setToken(response.token);
        this.setUserInfo(response.userInfo);
        return response;
      } catch (error) {
        throw error;
      }
    },
    
    setToken(token: string) {
      this.token = token;
    },
    
    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo;
    },
    
    logout() {
      this.token = null;
      this.userInfo = null;
      this.permissions = [];
    }
  },
  
  // H5项目使用持久化
  persist: {
    key: 'user-store',
    storage: localStorage
  }
});
```

## 🔧 工具函数规范

### 通用工具函数
```typescript
// utils/validate.ts
/**
 * 验证手机号格式
 */
export const validPhone = (phone: string): boolean => {
  const reg = /^1[3-9]\d{9}$/;
  return reg.test(phone);
};

/**
 * 手机号脱敏
 */
export const phoneDesensitization = (phone: string): string => {
  if (!phone || phone.length !== 11) return phone;
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

// utils/format.ts
/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
```

## 🚀 性能优化规范

### 1. 组件懒加载
```typescript
// router/index.ts
const routes = [
  {
    path: '/user',
    component: () => import('@/views/user/index.vue')
  }
];
```

### 2. 图片懒加载
```vue
<template>
  <!-- H5使用Vant的懒加载 -->
  <van-image
    lazy-load
    :src="imageUrl"
    :loading-icon="loadingIcon"
    :error-icon="errorIcon"
  />
  
  <!-- 管理后台使用Element Plus的懒加载 -->
  <el-image
    :src="imageUrl"
    lazy
    :loading="loadingElement"
    :error="errorElement"
  />
</template>
```

### 3. 列表虚拟滚动
```vue
<template>
  <!-- 大数据量列表使用虚拟滚动 -->
  <van-list
    v-model:loading="loading"
    :finished="finished"
    @load="onLoad"
  >
    <div v-for="item in list" :key="item.id">
      {{ item.name }}
    </div>
  </van-list>
</template>
```

## 📱 响应式设计规范

### H5适配规范
```less
// 使用vmin单位进行适配
.container {
  padding: 4vmin;
  font-size: 3.73vmin; // 14px in 375px design
}

// 使用CSS变量
:root {
  --font-size-sm: 3.2vmin;
  --font-size-base: 3.73vmin;
  --font-size-lg: 4.27vmin;
}
```

### 管理后台响应式
```scss
// 使用媒体查询
.sidebar {
  width: 200px;
  
  @media (max-width: 768px) {
    width: 100%;
    position: fixed;
    z-index: 1000;
  }
}
```

## 🔍 代码检查配置

### ESLint配置要点
```javascript
// eslint.config.js
export default [
  {
    rules: {
      // Vue相关
      'vue/multi-word-component-names': 'off',
      'vue/no-v-model-argument': 'off',
      
      // TypeScript相关
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      
      // Prettier集成
      'prettier/prettier': 'error'
    }
  }
];
```

### Prettier配置
```json
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "none",
  "printWidth": 120,
  "endOfLine": "auto"
}
```

---
*前端开发规范，确保代码质量和团队协作效率*
