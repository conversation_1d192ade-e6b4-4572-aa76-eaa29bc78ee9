# 开发规范总览

## 🎯 核心原则
- **一致性**：保持代码风格、命名规范、项目结构的一致性
- **可维护性**：编写清晰、易懂、易维护的代码
- **可扩展性**：考虑未来功能扩展的需要
- **性能优化**：关注代码性能和用户体验

## 📁 项目结构规范

### 目录命名规范
```
- 文件夹名：小写字母，多单词使用-连接
- 文件名：小写字母，多单词使用-连接
- 组件名：PascalCase（大驼峰）
- 变量名：camelCase（小驼峰）
```

## 🎨 代码风格规范

### 前端代码规范
- **Vue3 Composition API**：统一使用 `<script setup>` 语法
- **TypeScript**：所有新代码必须使用TypeScript
- **ESLint + Prettier**：自动格式化，遵循项目配置
- **组件命名**：使用多单词命名，避免与HTML元素冲突

### 后端代码规范
- **Java 17**：使用现代Java特性
- **Spring Boot 3.x**：遵循Spring Boot最佳实践
- **RESTful API**：统一的API设计规范
- **注释规范**：使用JavaDoc格式

## 🏷️ 命名规范

### 数据库命名
```sql
-- 表名：小写字母 + 下划线
sys_user, sys_role, business_task

-- 字段名：小写字母 + 下划线
user_id, create_time, update_by

-- 索引名：idx_ + 表名 + 字段名
idx_sys_user_username
```

### API接口命名
```
GET    /api/v1/users          # 获取用户列表
GET    /api/v1/users/{id}     # 获取单个用户
POST   /api/v1/users          # 创建用户
PUT    /api/v1/users/{id}     # 更新用户
DELETE /api/v1/users/{id}     # 删除用户
```

## 📝 Git提交规范

### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 提交类型
```
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整
refactor: 重构
test:     测试相关
chore:    构建过程或辅助工具的变动
perf:     性能优化
```

### 示例
```
feat(user): 添加用户头像上传功能

- 支持jpg、png格式
- 文件大小限制2MB
- 自动压缩和裁剪

Closes #123
```

## 🔍 代码审查规范

### 必检项目
- [ ] 代码风格符合规范
- [ ] 功能实现正确
- [ ] 异常处理完善
- [ ] 性能考虑合理
- [ ] 安全性检查
- [ ] 测试覆盖充分

## 📚 详细规范文档

- [前端开发规范](./frontend-standards.md)
- [后端开发规范](./backend-standards.md)
- [数据库设计规范](./database-standards.md)
- [API设计规范](./api-standards.md)
- [测试规范](./testing-standards.md)
- [部署规范](./deployment-standards.md)

## 🛠️ 开发工具配置

### IDE配置
- **IntelliJ IDEA 2024.3+**（推荐）
- **VS Code** + Vue插件

### 必装插件
- ESLint
- Prettier
- GitLens
- Auto Import

---
*快速参考，详细内容请查看对应的子文档*
