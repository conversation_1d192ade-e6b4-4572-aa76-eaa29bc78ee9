---
type: "manual"
---

# 声量合伙人项目文档中心

## 📋 快速导航

### 🚀 开发必读
- [开发规范总览](./development/README.md) - **最常用**，包含代码风格、命名规范、提交规范
- [项目架构概览](./architecture/README.md) - 系统整体架构和模块关系
- [API接口规范](./api/README.md) - 接口设计规范和文档

### 📱 前端开发
- [H5端开发指南](./frontend/h5/README.md) - Vue3 + Vant4 移动端开发
- [管理后台开发指南](./frontend/admin/README.md) - Vue3 + Element Plus 后台开发
- [小程序开发指南](./frontend/wxapp/README.md) - 微信小程序开发

### 🖥️ 后端开发
- [Java后端开发指南](./backend/README.md) - Spring Boot + MyBatis Plus 开发
- [数据库设计文档](./database/README.md) - 数据表结构和关系
- [任务调度系统](./job/README.md) - XXL-Job 使用指南

### 📊 产品文档
- [PRD产品需求文档](./prd/README.md) - 功能需求、流程、状态机
- [业务流程图](./business/README.md) - 核心业务流程分析
- [用户角色权限](./roles/README.md) - 用户体系和权限设计

### 🔧 运维部署
- [环境配置](./deployment/README.md) - 开发、测试、生产环境配置
- [监控告警](./monitoring/README.md) - 系统监控和日志分析

## 📖 文档使用说明

### 文档结构设计原则
1. **树形结构**：顶层为引导型内容，逐层细化
2. **高频优先**：最常用内容放在最显眼位置
3. **快速定位**：通过目录结构快速找到所需内容
4. **降低Token消耗**：避免重复内容，使用引用和链接

### 如何使用文档
1. **新人入门**：按顺序阅读 开发规范总览 → 项目架构概览 → 对应端的开发指南
2. **日常开发**：直接查看对应模块的README.md文件
3. **问题排查**：查看监控告警和业务流程图
4. **需求分析**：参考PRD文档和业务流程

## 🔄 文档更新机制
- 代码变更时同步更新相关文档
- 每个模块的README.md包含最新的关键信息
- 详细内容通过子文档链接查看

## 📝 贡献指南
- 遵循Markdown格式规范
- 保持文档结构的一致性
- 及时更新过时信息
- 添加必要的示例代码

---
*最后更新时间：2025-01-30*
